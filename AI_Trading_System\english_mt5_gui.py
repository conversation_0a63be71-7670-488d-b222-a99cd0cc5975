#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import messagebox, ttk
import threading
import time
from datetime import datetime
from mt5_real_crypto_system import MT5RealCryptoSystem

class EnglishMT5GUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 Smart Trading System - MetaTrader 5")
        self.root.geometry("1000x700")
        self.root.configure(bg='#2b2b2b')
        
        # System
        self.trading_system = None
        self.is_connected = False
        self.is_trading = False
        
        # Control variables
        self.symbol_var = tk.StringVar(value='BTCUSD')
        self.confidence_var = tk.DoubleVar(value=70.0)
        self.demo_var = tk.BooleanVar(value=True)
        
        # Create interface
        self.create_widgets()
        
        # Start data updates
        self.update_data()
        
    def create_widgets(self):
        """Create interface elements"""
        # Title
        title_label = tk.Label(self.root, text="🚀 Cryptocurrency Trading System on MetaTrader 5", 
                              font=('Arial', 16, 'bold'), fg='#00ff88', bg='#2b2b2b')
        title_label.pack(pady=20)
        
        # Control frame
        control_frame = tk.Frame(self.root, bg='#343a40', relief=tk.RAISED, bd=3)
        control_frame.pack(fill=tk.X, padx=20, pady=10)
        
        tk.Label(control_frame, text="🎛️ Control Panel", 
                font=('Arial', 12, 'bold'), fg='white', bg='#343a40').pack(pady=10)
        
        # Connection section
        conn_frame = tk.Frame(control_frame, bg='#343a40')
        conn_frame.pack(pady=10)
        
        self.connect_btn = tk.Button(conn_frame, text="🔌 Connect to MT5", 
                                   command=self.connect_mt5, bg='#007bff', fg='white', 
                                   font=('Arial', 10, 'bold'), width=15)
        self.connect_btn.pack(side=tk.LEFT, padx=10)
        
        self.disconnect_btn = tk.Button(conn_frame, text="🔌 Disconnect", 
                                      command=self.disconnect_system, bg='#6c757d', fg='white', 
                                      font=('Arial', 10, 'bold'), width=15, state='disabled')
        self.disconnect_btn.pack(side=tk.LEFT, padx=10)
        
        # Settings section
        settings_frame = tk.Frame(control_frame, bg='#343a40')
        settings_frame.pack(pady=10)
        
        # Symbol selection
        tk.Label(settings_frame, text="💰 Symbol:", fg='white', bg='#343a40', 
                font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        
        symbol_combo = ttk.Combobox(settings_frame, textvariable=self.symbol_var, 
                                   values=['BTCUSD', 'ETHUSD', 'XRPUSD', 'LTCUSD', 'ADAUSD'], 
                                   width=10, state='readonly')
        symbol_combo.pack(side=tk.LEFT, padx=10)
        
        # Confidence threshold
        tk.Label(settings_frame, text="🎯 Confidence:", fg='white', bg='#343a40', 
                font=('Arial', 10, 'bold')).pack(side=tk.LEFT, padx=(30, 0))
        
        self.confidence_scale = tk.Scale(settings_frame, from_=30, to=100, orient=tk.HORIZONTAL,
                                        variable=self.confidence_var, bg='#343a40', fg='white',
                                        highlightbackground='#343a40', length=150,
                                        command=self.update_confidence_label)
        self.confidence_scale.pack(side=tk.LEFT, padx=10)
        
        self.confidence_label = tk.Label(settings_frame, text="70%", 
                                        fg='#ffc107', bg='#343a40', font=('Arial', 11, 'bold'))
        self.confidence_label.pack(side=tk.LEFT, padx=10)
        
        # Quick confidence buttons
        quick_frame = tk.Frame(control_frame, bg='#343a40')
        quick_frame.pack(pady=5)
        
        tk.Label(quick_frame, text="Quick Set:", fg='white', bg='#343a40', 
                font=('Arial', 9)).pack(side=tk.LEFT)
        
        for conf in [30, 50, 70, 90]:
            btn = tk.Button(quick_frame, text=f"{conf}%", 
                           command=lambda c=conf: self.set_confidence(c),
                           bg='#17a2b8', fg='white', font=('Arial', 8), width=5)
            btn.pack(side=tk.LEFT, padx=2)
        
        # Demo mode
        demo_frame = tk.Frame(control_frame, bg='#343a40')
        demo_frame.pack(pady=5)
        
        demo_check = tk.Checkbutton(demo_frame, text="🛡️ Demo Mode (Safe)", 
                                   variable=self.demo_var, fg='white', bg='#343a40',
                                   selectcolor='#343a40', font=('Arial', 10))
        demo_check.pack()
        
        # Trading controls
        trading_frame = tk.Frame(control_frame, bg='#343a40')
        trading_frame.pack(pady=10)
        
        self.start_btn = tk.Button(trading_frame, text="🚀 Start Smart Trading", 
                                 command=self.start_trading, bg='#28a745', fg='white', 
                                 font=('Arial', 10, 'bold'), width=20)
        self.start_btn.pack(side=tk.LEFT, padx=10)
        
        self.stop_btn = tk.Button(trading_frame, text="⏹️ Stop Trading", 
                                command=self.stop_trading, bg='#dc3545', fg='white', 
                                font=('Arial', 10, 'bold'), width=15, state='disabled')
        self.stop_btn.pack(side=tk.LEFT, padx=10)
        
        # Account information frame
        account_frame = tk.Frame(self.root, bg='#495057', relief=tk.RAISED, bd=3)
        account_frame.pack(fill=tk.X, padx=20, pady=10)
        
        tk.Label(account_frame, text="📊 MetaTrader 5 Account Information", 
                font=('Arial', 12, 'bold'), fg='white', bg='#495057').pack(pady=10)
        
        # Account info
        info_frame = tk.Frame(account_frame, bg='#495057')
        info_frame.pack(pady=10)
        
        # Row 1
        row1 = tk.Frame(info_frame, bg='#495057')
        row1.pack(pady=5)
        
        tk.Label(row1, text="🏢 Company:", fg='white', bg='#495057', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.company_label = tk.Label(row1, text="Not Connected", fg='#17a2b8', bg='#495057', font=('Arial', 10))
        self.company_label.pack(side=tk.LEFT, padx=(10, 30))
        
        tk.Label(row1, text="💰 Balance:", fg='white', bg='#495057', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.balance_label = tk.Label(row1, text="$0.00", fg='#28a745', bg='#495057', font=('Arial', 12, 'bold'))
        self.balance_label.pack(side=tk.LEFT, padx=(10, 30))
        
        tk.Label(row1, text="📊 Profit/Loss:", fg='white', bg='#495057', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.profit_label = tk.Label(row1, text="$0.00", fg='white', bg='#495057', font=('Arial', 12, 'bold'))
        self.profit_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # Row 2
        row2 = tk.Frame(info_frame, bg='#495057')
        row2.pack(pady=5)
        
        tk.Label(row2, text="🖥️ Server:", fg='white', bg='#495057', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.server_label = tk.Label(row2, text="Not Connected", fg='#17a2b8', bg='#495057', font=('Arial', 10))
        self.server_label.pack(side=tk.LEFT, padx=(10, 30))
        
        tk.Label(row2, text="📋 Positions:", fg='white', bg='#495057', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.positions_label = tk.Label(row2, text="0", fg='#ffc107', bg='#495057', font=('Arial', 12, 'bold'))
        self.positions_label.pack(side=tk.LEFT, padx=(10, 30))
        
        tk.Label(row2, text="🔄 Status:", fg='white', bg='#495057', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.system_status_label = tk.Label(row2, text="Stopped", fg='#ffc107', bg='#495057', font=('Arial', 11, 'bold'))
        self.system_status_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # Row 3 - Confidence display
        row3 = tk.Frame(info_frame, bg='#495057')
        row3.pack(pady=5)
        
        tk.Label(row3, text="🎯 Current Confidence:", fg='white', bg='#495057', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.current_confidence_label = tk.Label(row3, text="0%", fg='#ffc107', bg='#495057', font=('Arial', 12, 'bold'))
        self.current_confidence_label.pack(side=tk.LEFT, padx=(10, 30))
        
        tk.Label(row3, text="📊 Required:", fg='white', bg='#495057', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.required_confidence_label = tk.Label(row3, text="70%", fg='#17a2b8', bg='#495057', font=('Arial', 12, 'bold'))
        self.required_confidence_label.pack(side=tk.LEFT, padx=(10, 30))
        
        tk.Label(row3, text="⚡ Entry Status:", fg='white', bg='#495057', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.confidence_status_label = tk.Label(row3, text="Waiting", fg='#6c757d', bg='#495057', font=('Arial', 11, 'bold'))
        self.confidence_status_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # Log frame
        log_frame = tk.Frame(self.root, bg='#1e1e1e', relief=tk.RAISED, bd=3)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        tk.Label(log_frame, text="📝 Events and Analysis Log", 
                font=('Arial', 12, 'bold'), fg='white', bg='#1e1e1e').pack(pady=10)
        
        # Text area
        text_frame = tk.Frame(log_frame, bg='#1e1e1e')
        text_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))
        
        self.log_text = tk.Text(text_frame, bg='#2d2d2d', fg='#ffffff', 
                               font=('Consolas', 10), wrap=tk.WORD, 
                               insertbackground='white')
        
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Welcome message
        welcome_msg = """🚀 Welcome to Smart Trading System

✨ Features:
• Direct connection to MetaTrader 5
• Real account information display
• Cryptocurrency trading (BTC, ETH, etc.)
• Customizable confidence settings (30% - 100%)
• Advanced technical analysis with AI
• Real-time confidence display

📋 Getting Started:
1. Make sure MetaTrader 5 is running and logged in
2. Click "Connect to MT5" to connect
3. Choose currency symbol (BTC, ETH, etc.)
4. Set confidence threshold (30% for testing, 70% recommended)
5. Enable Demo Mode for safe testing
6. Click "Start Smart Trading"

⚠️ Important: Always test with Demo Mode first!

"""
        self.log_text.insert(tk.END, welcome_msg)
        
    def update_confidence_label(self, value=None):
        """Update confidence label in settings"""
        confidence = self.confidence_var.get()
        self.confidence_label.config(text=f"{confidence:.0f}%")
        
        # Update required confidence in account display too
        if hasattr(self, 'required_confidence_label'):
            self.required_confidence_label.config(text=f"{confidence:.0f}%")
        
        # Change color based on percentage
        if confidence >= 90:
            color = '#28a745'  # Green
        elif confidence >= 70:
            color = '#ffc107'  # Yellow
        elif confidence >= 50:
            color = '#fd7e14'  # Orange
        else:
            color = '#dc3545'  # Red
            
        self.confidence_label.config(fg=color)
        
    def set_confidence(self, value):
        """Set confidence quickly"""
        self.confidence_var.set(value)
        self.update_confidence_label()
        
    def log_message(self, message):
        """Add message to log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def connect_mt5(self):
        """Connect to MetaTrader 5"""
        try:
            self.log_message("🔌 Connecting to MetaTrader 5...")

            # Create trading system
            self.trading_system = MT5RealCryptoSystem(demo_mode=self.demo_var.get())

            # Connect to MT5
            if self.trading_system.connect():
                self.is_connected = True
                self.connect_btn.config(state='disabled')
                self.disconnect_btn.config(state='normal')
                self.start_btn.config(state='normal')

                self.log_message("✅ Successfully connected to MetaTrader 5!")
                self.log_message("📊 Getting account information...")

                # Update account info
                self.update_account_info()

            else:
                self.log_message("❌ Failed to connect to MetaTrader 5")
                self.log_message("💡 Make sure MT5 is running and logged in")
                messagebox.showerror("Connection Error", "Failed to connect to MetaTrader 5!\n\nMake sure:\n• MT5 is running\n• You are logged in\n• Algorithm trading is enabled")

        except Exception as e:
            self.log_message(f"❌ Connection error: {str(e)}")
            messagebox.showerror("Error", f"Connection error: {str(e)}")

    def disconnect_system(self):
        """Disconnect from system"""
        try:
            if self.is_trading:
                self.stop_trading()

            if self.trading_system:
                self.trading_system.disconnect()

            self.is_connected = False
            self.connect_btn.config(state='normal')
            self.disconnect_btn.config(state='disabled')
            self.start_btn.config(state='disabled')

            # Reset labels
            self.company_label.config(text="Not Connected")
            self.server_label.config(text="Not Connected")
            self.balance_label.config(text="$0.00")
            self.profit_label.config(text="$0.00", fg='white')
            self.positions_label.config(text="0")
            self.system_status_label.config(text="Disconnected", fg='#6c757d')
            self.current_confidence_label.config(text="0%", fg='#6c757d')
            self.confidence_status_label.config(text="Disconnected", fg='#6c757d')

            self.log_message("🔌 Disconnected from MetaTrader 5")

        except Exception as e:
            self.log_message(f"❌ Disconnection error: {str(e)}")

    def update_account_info(self):
        """Update account information"""
        if not self.is_connected or not self.trading_system:
            return

        try:
            account_info = self.trading_system.get_account_info()

            if account_info:
                self.company_label.config(text=account_info.get('company', 'Not Available'))
                self.server_label.config(text=account_info.get('server', 'Not Available'))

                balance = account_info.get('balance', 0)
                self.balance_label.config(text=f"${balance:,.2f}")

                profit = account_info.get('profit', 0)
                if profit > 0:
                    self.profit_label.config(text=f"+${profit:,.2f}", fg='#28a745')
                elif profit < 0:
                    self.profit_label.config(text=f"${profit:,.2f}", fg='#dc3545')
                else:
                    self.profit_label.config(text="$0.00", fg='white')

                positions = self.trading_system.get_open_positions()
                self.positions_label.config(text=str(len(positions)))

        except Exception as e:
            self.log_message(f"❌ Error updating account info: {str(e)}")

    def update_confidence_display(self, current_confidence, required_confidence, decision):
        """Update confidence display in interface"""
        try:
            # Update current confidence
            self.current_confidence_label.config(text=f"{current_confidence:.1f}%")

            # Update required confidence
            self.required_confidence_label.config(text=f"{required_confidence:.0f}%")

            # Update entry status
            if decision == 'error':
                self.confidence_status_label.config(text="Error", fg='#dc3545')
                self.current_confidence_label.config(fg='#dc3545')
            elif decision == 'hold':
                self.confidence_status_label.config(text="Waiting", fg='#6c757d')
                self.current_confidence_label.config(fg='#6c757d')
            elif current_confidence >= required_confidence:
                if decision in ['buy', 'sell']:
                    self.confidence_status_label.config(text="Ready to Enter", fg='#28a745')
                    self.current_confidence_label.config(fg='#28a745')
                else:
                    self.confidence_status_label.config(text="Waiting", fg='#ffc107')
                    self.current_confidence_label.config(fg='#ffc107')
            else:
                self.confidence_status_label.config(text="Insufficient", fg='#ffc107')
                self.current_confidence_label.config(fg='#ffc107')

        except Exception as e:
            print(f"Error updating confidence display: {str(e)}")

    def start_trading(self):
        """Start trading"""
        if not self.is_connected:
            messagebox.showwarning("Warning", "Must connect to MT5 first!")
            return

        symbol = self.symbol_var.get()
        confidence_threshold = self.confidence_var.get()

        # Confirmation
        mode_text = "Demo (Safe)" if self.demo_var.get() else "Live"
        confirm_msg = f"""Do you want to start smart trading?

Settings:
• Symbol: {symbol}
• Mode: {mode_text}
• Required Confidence: {confidence_threshold:.0f}%

⚠️ Make sure settings are correct before proceeding!"""

        if not messagebox.askyesno("Confirm Start Trading", confirm_msg):
            return

        self.is_trading = True
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.system_status_label.config(text="🚀 Running", fg='#28a745')

        # Apply settings
        self.trading_system.demo_mode = self.demo_var.get()
        self.trading_system.set_current_pair(symbol)
        self.trading_system.set_confidence_threshold(confidence_threshold)

        self.log_message(f"🚀 Starting smart trading:")
        self.log_message(f"   💰 Symbol: {symbol}")
        self.log_message(f"   🛡️ Mode: {mode_text}")
        self.log_message(f"   🎯 Confidence: {confidence_threshold:.0f}%")

        # Start trading in separate thread
        threading.Thread(target=self.trading_loop, daemon=True).start()

    def stop_trading(self):
        """Stop trading"""
        self.is_trading = False
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.system_status_label.config(text="⏹️ Stopped", fg='#ffc107')
        self.log_message("⏹️ Smart trading stopped")

    def trading_loop(self):
        """Smart trading loop"""
        while self.is_trading and self.is_connected:
            try:
                symbol = self.symbol_var.get()
                self.log_message(f"🔍 Analyzing {symbol} with AI...")

                # Market analysis
                analysis = self.trading_system.analyze_market(symbol)

                if 'error' not in analysis:
                    decision = analysis['decision']
                    confidence = analysis['confidence']
                    price = analysis['price']
                    threshold = self.trading_system.confidence_threshold

                    # Update confidence display in interface
                    self.root.after(0, lambda: self.update_confidence_display(confidence, threshold, decision))

                    self.log_message(f"📊 Analysis Result:")
                    self.log_message(f"   🎯 Decision: {decision}")
                    self.log_message(f"   📈 Confidence: {confidence:.1f}%")
                    self.log_message(f"   💰 Price: ${price:,.2f}")
                    self.log_message(f"   🎯 Required: {threshold:.0f}%")

                    # Check entry conditions
                    if decision in ['buy', 'sell'] and confidence >= threshold:
                        self.log_message(f"✅ Entry conditions met! Confidence: {confidence:.1f}% >= Required: {threshold:.0f}%")

                        # Try to execute trade
                        if self.trading_system.execute_trade_mt5(analysis):
                            self.log_message("🎉 Trade executed successfully!")
                            self.log_message("🧠 System learning from this trade...")
                        else:
                            self.log_message("❌ Failed to execute trade")
                    else:
                        if decision == 'hold':
                            self.log_message(f"⏳ Hold decision - no clear signal")
                        else:
                            self.log_message(f"⏳ Insufficient confidence: {confidence:.1f}% < Required: {threshold:.0f}%")
                else:
                    self.log_message(f"❌ Analysis error: {analysis['error']}")
                    # Update confidence display with error state
                    self.root.after(0, lambda: self.update_confidence_display(0, self.trading_system.confidence_threshold, 'error'))

                # Update account info
                self.root.after(0, self.update_account_info)

                # Wait before next analysis (30 seconds)
                for i in range(30):
                    if not self.is_trading:
                        break
                    time.sleep(1)

            except Exception as e:
                self.log_message(f"❌ Trading loop error: {str(e)}")
                time.sleep(10)

    def update_data(self):
        """Update data periodically"""
        if self.is_connected:
            self.update_account_info()

        # Schedule next update (every 5 seconds)
        self.root.after(5000, self.update_data)

    def run(self):
        """Run the application"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log_message("⏹️ Application stopped")
        finally:
            if self.trading_system:
                self.trading_system.disconnect()

    def on_closing(self):
        """Handle window closing"""
        if self.is_trading:
            if messagebox.askokcancel("Quit", "Trading is active. Do you want to stop and quit?"):
                self.disconnect_system()
                self.root.destroy()
        else:
            self.disconnect_system()
            self.root.destroy()

if __name__ == "__main__":
    try:
        app = EnglishMT5GUI()
        app.run()
    except Exception as e:
        print(f"❌ Application error: {e}")
