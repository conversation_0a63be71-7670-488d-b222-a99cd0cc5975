# دليل الإعداد الشامل لنظام إدارة الصيدلية
# Complete Setup Guide for Pharmacy Management System

## 🚀 الإعداد السريع | Quick Setup

### الطريقة الأسهل | Easiest Method
```bash
# تشغيل الإعداد الشامل
# Run complete setup
complete_system_setup.bat
```

### الإعداد اليدوي | Manual Setup

#### 1. إعداد قاعدة البيانات | Database Setup
```bash
# إعداد قاعدة البيانات فقط
# Database setup only
setup_pharmacy_store_database.bat
```

#### 2. إصلاح مشاكل البناء | Fix Build Issues
```bash
# إصلاح مشاكل التجميع
# Fix compilation issues
fix_build_issues.bat
```

## 📋 المتطلبات | Requirements

### قاعدة البيانات | Database
- SQL Server 2019 أو أحدث | SQL Server 2019 or newer
- SQL Server Express (مجاني) | SQL Server Express (free)

### بيئة التطوير | Development Environment
- Visual Studio 2019/2022
- .NET Framework 4.7.2
- Windows 10/11

## 🗄️ هيكل قاعدة البيانات | Database Structure

### قاعدة البيانات الموحدة | Unified Database: `UnifiedPharmacy`

#### الجداول الأساسية | Core Tables
- `users` - المستخدمون | Users
- `pharmacies` - الصيدليات | Pharmacies  
- `medic` - الأدوية | Medicines
- `sales` - المبيعات | Sales
- `employee_sessions` - جلسات الموظفين | Employee Sessions

#### جداول متجر الصيدلية | Pharmacy Store Tables
- `published_medicines` - الأدوية المنشورة | Published Medicines
- `purchase_requests` - طلبات الشراء | Purchase Requests
- `pharmacy_messages` - رسائل الصيدليات | Pharmacy Messages

#### جداول الإعدادات | Settings Tables
- `print_settings` - إعدادات الطباعة | Print Settings

## 👤 معلومات تسجيل الدخول الافتراضية | Default Login Credentials

### المدير | Administrator
- **اسم المستخدم | Username:** `admin`
- **كلمة المرور | Password:** `admin123`

### موظف | Employee
- **اسم المستخدم | Username:** `employee1`
- **كلمة المرور | Password:** `emp123`

## 🔧 استكشاف الأخطاء | Troubleshooting

### مشاكل قاعدة البيانات | Database Issues

#### خطأ الاتصال | Connection Error
```
Cannot connect to SQL Server
```
**الحل | Solution:**
1. تأكد من تشغيل SQL Server | Ensure SQL Server is running
2. تحقق من اسم الخادم | Check server name
3. تأكد من الصلاحيات | Verify permissions

#### خطأ قاعدة البيانات غير موجودة | Database Not Found
```
Database 'UnifiedPharmacy' does not exist
```
**الحل | Solution:**
```bash
# تشغيل إعداد قاعدة البيانات
# Run database setup
setup_pharmacy_store_database.bat
```

### مشاكل البناء | Build Issues

#### خطأ MSBuild غير موجود | MSBuild Not Found
```
MSBuild is not recognized
```
**الحل | Solution:**
1. تثبيت Visual Studio | Install Visual Studio
2. تثبيت Build Tools | Install Build Tools
3. تشغيل من Developer Command Prompt

#### خطأ GenerateResource | GenerateResource Error
```
Could not run the "GenerateResource" task
```
**الحل | Solution:**
```bash
# تشغيل إصلاح مشاكل البناء
# Run build fix
fix_build_issues.bat
```

## 🌟 الميزات الجديدة | New Features

### متجر الصيدلية | Pharmacy Store
- نشر الأدوية للبيع | Publish medicines for sale
- طلبات الشراء بين الصيدليات | Inter-pharmacy purchase requests
- نظام الرسائل | Messaging system
- البحث والتصفية | Search and filtering

### إدارة الجلسات | Session Management
- تتبع دخول/خروج الموظفين | Track employee login/logout
- تقارير الحضور | Attendance reports

### إعدادات الطباعة | Print Settings
- تخصيص تصميم التقارير | Customize report design
- معاينة مباشرة | Live preview

## 📞 الدعم | Support

إذا واجهت أي مشاكل، يرجى:
If you encounter any issues, please:

1. تحقق من هذا الدليل | Check this guide
2. تشغيل ملفات الإصلاح | Run fix scripts
3. التحقق من ملفات السجل | Check log files

---

**تم إنشاؤه بواسطة | Created by:** Augment Agent  
**التاريخ | Date:** 2025-06-30  
**الإصدار | Version:** 2.0
