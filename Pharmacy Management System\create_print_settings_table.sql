-- إنشاء جدول إعدادات الطباعة
USE pharmacy;
GO

-- حذ<PERSON> الجدول إذا كان موجوداً (للتأكد من البداية النظيفة)
IF EXISTS (SELECT * FROM sysobjects WHERE name='print_settings' AND xtype='U')
BEGIN
    DROP TABLE print_settings;
END
GO

-- إن<PERSON>اء جدول إعدادات الطباعة الجديد
CREATE TABLE print_settings (
    id INT IDENTITY(1,1) PRIMARY KEY,
    reportType VARCHAR(100) NOT NULL,
    paperSize VARCHAR(50) DEFAULT 'A4',
    orientation VARCHAR(50) DEFAULT 'عمودي',
    marginTop INT DEFAULT 20,
    marginBottom INT DEFAULT 20,
    marginLeft INT DEFAULT 15,
    marginRight INT DEFAULT 15,
    titleText VARCHAR(500) DEFAULT 'تقرير الصيدلية',
    titleFont INT DEFAULT 18,
    titleAlignment VARCHAR(50) DEFAULT 'وسط',
    showDateTime BIT DEFAULT 1,
    dateFormat VARCHAR(50) DEFAULT 'dd/MM/yyyy',
    datePosition VARCHAR(50) DEFAULT 'أعلى يمين',
    tableFont INT DEFAULT 10,
    borderWidth INT DEFAULT 1,
    footerText VARCHAR(500) DEFAULT 'نظام إدارة الصيدلية',
    showPageNumbers BIT DEFAULT 1,
    titleColor INT DEFAULT -16777216, -- أسود
    tableHeaderColor INT DEFAULT -3355444, -- رمادي فاتح
    tableTextColor INT DEFAULT -16777216, -- أسود
    createdDate DATETIME DEFAULT GETDATE(),
    lastModified DATETIME DEFAULT GETDATE(),
    UNIQUE(reportType)
);
GO

-- إدراج الإعدادات الافتراضية لأنواع التقارير المختلفة
INSERT INTO print_settings (reportType, titleText) VALUES 
('عام', 'تقرير الصيدلية'),
('مبيعات الأدوية', 'تقرير مبيعات الأدوية'),
('تقرير المبيعات', 'تقرير المبيعات'),
('جلسات الموظفين', 'تقرير جلسات الموظفين'),
('جرد الأدوية', 'تقرير جرد الأدوية'),
('صلاحية الأدوية', 'تقرير صلاحية الأدوية');
GO

-- التحقق من إنشاء الجدول بنجاح
SELECT 'تم إنشاء جدول print_settings بنجاح' AS Status;
SELECT COUNT(*) AS 'عدد السجلات المدرجة' FROM print_settings;
GO

-- عرض البيانات المدرجة
SELECT * FROM print_settings;
GO
