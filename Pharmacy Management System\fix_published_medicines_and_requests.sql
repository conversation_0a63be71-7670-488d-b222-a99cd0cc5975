-- إصلاح جدول published_medicines وطلبات الشراء
USE UnifiedPharmacy;

PRINT '=== إصلاح جداول متجر الصيدلية ===';

-- 0. إن<PERSON>اء جدول pharmacies إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacies')
BEGIN
    CREATE TABLE pharmacies (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacy_name NVARCHAR(255) NOT NULL,
        phone NVARCHAR(50),
        city NVARCHAR(100),
        address NVARCHAR(500),
        created_date DATETIME DEFAULT GETDATE()
    );

    -- إدراج بيانات تجريبية للصيدليات
    INSERT INTO pharmacies (pharmacy_name, phone, city, address) VALUES
    ('الصيدلية المركزية', '01234567890', 'القاهرة', 'شارع التحرير'),
    ('صيدلية النهضة', '01987654321', 'الجيزة', 'شارع الهرم'),
    ('صيدلية الشفاء', '01122334455', 'الإسكندرية', 'شارع الكورنيش'),
    ('صيدلية الأمل', '01555666777', 'المنصورة', 'شارع الجمهورية');

    PRINT '✅ تم إنشاء جدول pharmacies وإدراج البيانات';
END
ELSE
BEGIN
    PRINT '⚠️ جدول pharmacies موجود بالفعل';
END

-- 1. إنشاء جدول published_medicines بالبنية الصحيحة
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'published_medicines')
BEGIN
    CREATE TABLE published_medicines (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacy_id INT NOT NULL,
        medicine_name NVARCHAR(255) NOT NULL,
        medicine_number NVARCHAR(100),
        dosage NVARCHAR(100),
        quantity_available INT NOT NULL,
        price_per_unit DECIMAL(10,2) NOT NULL,
        manufacturing_date DATE,
        expiry_date DATE,
        description NVARCHAR(1000),
        is_available BIT DEFAULT 1,
        published_date DATETIME DEFAULT GETDATE(),
        updated_date DATETIME DEFAULT GETDATE()
    );
    PRINT '✅ تم إنشاء جدول published_medicines';
END
ELSE
BEGIN
    PRINT '⚠️ جدول published_medicines موجود بالفعل';
END

-- 2. إنشاء جدول pharmacy_messages للمراسلات
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacy_messages')
BEGIN
    CREATE TABLE pharmacy_messages (
        id INT IDENTITY(1,1) PRIMARY KEY,
        sender_pharmacy_id INT NOT NULL,
        receiver_pharmacy_id INT NOT NULL,
        subject NVARCHAR(255),
        message_content NVARCHAR(1000) NOT NULL,
        sent_date DATETIME DEFAULT GETDATE(),
        is_read BIT DEFAULT 0,
        read_date DATETIME NULL,
        related_request_id INT NULL
    );
    PRINT '✅ تم إنشاء جدول pharmacy_messages';
END
ELSE
BEGIN
    PRINT '⚠️ جدول pharmacy_messages موجود بالفعل';
END

-- 3. حذف البيانات الموجودة وإدراج بيانات جديدة
DELETE FROM purchase_requests;
DELETE FROM published_medicines WHERE id > 0;
DELETE FROM pharmacy_messages WHERE id > 0;

PRINT 'تم حذف البيانات الموجودة';

-- 4. إدراج أدوية منشورة تجريبية
INSERT INTO published_medicines 
(pharmacy_id, medicine_name, medicine_number, dosage, quantity_available, price_per_unit, manufacturing_date, expiry_date, description, is_available)
VALUES 
(1, 'باراسيتامول', 'MED001', '500 مجم', 100, 5.50, '2024-01-01', '2026-01-01', 'مسكن للألم وخافض للحرارة', 1),
(1, 'أموكسيسيلين', 'MED002', '250 مجم', 50, 12.00, '2024-02-01', '2025-12-01', 'مضاد حيوي واسع المجال', 1),
(1, 'فيتامين د', 'MED003', '1000 وحدة', 75, 25.00, '2024-03-01', '2026-03-01', 'مكمل غذائي لتقوية العظام', 1),
(2, 'إيبوبروفين', 'MED004', '400 مجم', 60, 8.00, '2024-01-15', '2025-11-15', 'مضاد للالتهاب ومسكن', 1),
(2, 'أسبرين', 'MED005', '100 مجم', 80, 3.50, '2024-02-15', '2026-02-15', 'مضاد للتجلط ومسكن', 1);

PRINT '✅ تم إدراج الأدوية المنشورة';

-- 5. إدراج طلبات شراء تجريبية (باستخدام معرفات من جدول medic)
-- أولاً نحصل على معرفات الأدوية الموجودة في جدول medic
DECLARE @med1_id INT, @med2_id INT, @med3_id INT;

-- الحصول على أول 3 أدوية من جدول medic
SELECT TOP 1 @med1_id = id FROM medic ORDER BY id;
SELECT TOP 1 @med2_id = id FROM medic WHERE id > @med1_id ORDER BY id;
SELECT TOP 1 @med3_id = id FROM medic WHERE id > @med2_id ORDER BY id;

-- إذا لم توجد أدوية، استخدم قيم افتراضية
IF @med1_id IS NULL SET @med1_id = 1;
IF @med2_id IS NULL SET @med2_id = 2;
IF @med3_id IS NULL SET @med3_id = 3;

INSERT INTO purchase_requests
(buyer_pharmacy_id, seller_pharmacy_id, medicine_id, requested_quantity, offered_price, request_message, status, request_date)
VALUES
(2, 1, @med1_id, 10, 50.00, 'أحتاج هذا الدواء بسرعة', 'pending', GETDATE()),
(3, 1, @med2_id, 5, 60.00, 'طلب دواء عاجل', 'pending', GETDATE()),
(4, 1, @med3_id, 8, 200.00, 'للمرضى المزمنين', 'pending', GETDATE()),
(2, 1, @med1_id, 15, 75.00, 'طلب شهري', 'pending', GETDATE()),
(3, 1, @med2_id, 3, 36.00, 'للأطفال', 'pending', GETDATE());

PRINT '✅ تم إدراج طلبات الشراء';

-- 6. إدراج رسائل تجريبية
INSERT INTO pharmacy_messages
(sender_pharmacy_id, receiver_pharmacy_id, subject, message_content, related_request_id)
VALUES
(2, 1, 'استفسار عن باراسيتامول', 'مرحباً، هل يمكنني الحصول على باراسيتامول؟', 1),
(1, 2, 'رد على استفسار باراسيتامول', 'أهلاً وسهلاً، نعم متوفر لدينا', 1),
(3, 1, 'طلب عاجل', 'أحتاج أموكسيسيلين بسرعة', 2),
(4, 1, 'استفسار عن فيتامين د', 'هل فيتامين د متوفر؟', 3),
(1, 4, 'رد على استفسار فيتامين د', 'نعم متوفر، كم الكمية المطلوبة؟', 3);

PRINT '✅ تم إدراج الرسائل التجريبية';

-- 7. عرض النتائج
PRINT '=== إحصائيات النتائج ===';
SELECT 'published_medicines' as 'الجدول', COUNT(*) as 'عدد السجلات' FROM published_medicines
UNION ALL
SELECT 'purchase_requests', COUNT(*) FROM purchase_requests
UNION ALL
SELECT 'pharmacy_messages', COUNT(*) FROM pharmacy_messages;

-- 8. عرض الطلبات للصيدلية رقم 1
PRINT '=== طلبات الصيدلية رقم 1 ===';
SELECT
    pr.id,
    pr.buyer_pharmacy_id as 'الصيدلية الطالبة',
    m.mname as 'اسم الدواء',
    m.mnumber as 'رقم الدواء',
    pr.requested_quantity as 'الكمية المطلوبة',
    pr.offered_price as 'السعر المعروض',
    pr.request_message as 'رسالة الطلب',
    pr.status as 'الحالة'
FROM purchase_requests pr
INNER JOIN medic m ON pr.medicine_id = m.id
WHERE pr.seller_pharmacy_id = 1
ORDER BY pr.request_date DESC;

PRINT '=== تم إكمال الإصلاح بنجاح ===';
