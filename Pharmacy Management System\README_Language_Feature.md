# ميزة اختيار اللغة في نظام إدارة الصيدلية

## الوصف
تم إضافة ميزة اختيار اللغة إلى صفحة تسجيل الدخول في نظام إدارة الصيدلية. يمكن للمستخدمين الآن اختيار بين اللغة العربية والإنجليزية.

## الميزات المضافة

### 1. أزرار اختيار اللغة
- زر "العربية" باللون الأزرق
- زر "English" باللون الأخضر
- موضعان في أسفل صفحة تسجيل الدخول

### 2. نظام إدارة اللغات (LanguageManager)
- كلاس مخصص لإدارة الترجمات
- دعم للغة العربية والإنجليزية
- ترجمة تلقائية لجميع النصوص في الواجهة

### 3. الترجمات المتاحة
#### صفحة تسجيل الدخول:
- اسم المستخدم / User Name
- كلمة المرور / Password
- تسجيل الدخول / Sign in
- نظام إدارة الصيدلية / Pharmacy Management System

#### رسائل الخطأ:
- يرجى إدخال اسم المستخدم / Please enter username
- يرجى إدخال كلمة المرور / Please enter password
- اسم المستخدم أو كلمة المرور خاطئة / Wrong Username or Password
- هل أنت متأكد من إغلاق التطبيق؟ / Are you sure you want to exit?

### 4. دعم اتجاه النص
- اتجاه من اليمين لليسار للعربية (RTL)
- اتجاه من اليسار لليمين للإنجليزية (LTR)

## كيفية الاستخدام

1. **تشغيل البرنامج**: عند فتح البرنامج، ستظهر صفحة تسجيل الدخول باللغة العربية افتراضياً
2. **تغيير اللغة**: اضغط على زر "English" للتبديل إلى الإنجليزية أو "العربية" للعودة للعربية
3. **التطبيق الفوري**: تتغير جميع النصوص فوراً عند اختيار اللغة الجديدة

## الملفات المعدلة

### ملفات جديدة:
- `LanguageManager.cs` - كلاس إدارة اللغات والترجمات

### ملفات معدلة:
- `Form1.cs` - إضافة وظائف أزرار اللغة وتطبيق الترجمات
- `Form1.Designer.cs` - إضافة أزرار اختيار اللغة
- `Pharmacy Management System.csproj` - إضافة ملف LanguageManager

## المميزات التقنية

1. **نظام ترجمة مرن**: يمكن إضافة لغات جديدة بسهولة
2. **ذاكرة اللغة**: يحتفظ البرنامج باللغة المختارة أثناء الجلسة
3. **واجهة سهلة**: أزرار واضحة ومميزة لكل لغة
4. **تطبيق شامل**: يؤثر على جميع النصوص والرسائل

## إمكانيات التطوير المستقبلية

1. حفظ اختيار اللغة في ملف إعدادات
2. إضافة لغات إضافية (فرنسية، ألمانية، إلخ)
3. ترجمة باقي صفحات البرنامج
4. دعم الخطوط المخصصة لكل لغة

## ملاحظات للمطورين

- جميع النصوص الجديدة يجب إضافتها إلى `LanguageManager.cs`
- استخدم `LanguageManager.GetText("key")` للحصول على النص المترجم
- استخدم `LanguageManager.IsRightToLeft()` للتحقق من اتجاه النص
- استخدم `LanguageManager.SetLanguage("ar")` أو `LanguageManager.SetLanguage("en")` لتغيير اللغة
