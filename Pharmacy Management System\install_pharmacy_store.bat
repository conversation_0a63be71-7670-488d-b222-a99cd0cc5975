@echo off
echo ========================================
echo   إعداد قاعدة بيانات متجر الأدوية
echo   Setup Pharmacy Store Database
echo ========================================
echo.

echo 🏪 إضافة ميزات متجر الأدوية إلى قاعدة البيانات الموحدة...
echo.

echo المميزات التي سيتم إضافتها:
echo ✅ نشر الأدوية للبيع بين الصيدليات
echo ✅ إرسال طلبات الشراء
echo ✅ نظام الرسائل بين الصيدليات
echo ✅ الإشعارات والتنبيهات
echo ✅ تقييم الصيدليات
echo ✅ البحث الذكي عن الأدوية
echo.

echo 🚀 بدء التنفيذ...
echo.

echo 📊 الخطوة 1: إنشاء الجداول...
sqlcmd -S NARUTO -E -i add_pharmacy_store_to_unified_database.sql

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في إنشاء الجداول!
    goto :error
)

echo.
echo 🔧 الخطوة 2: إنشاء الإجراءات المخزنة...
sqlcmd -S NARUTO -E -i add_pharmacy_store_procedures_unified.sql

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في إنشاء الإجراءات المخزنة!
    goto :error
)

echo.
echo ========================================
echo   ✅ تم إعداد متجر الأدوية بنجاح!
echo ========================================
echo.

echo 🎯 الميزات المتاحة الآن:
echo.
echo 📋 للصيدليات:
echo   • نشر الأدوية للبيع
echo   • البحث عن الأدوية من صيدليات أخرى
echo   • إرسال طلبات الشراء
echo   • الرد على طلبات الشراء
echo   • إرسال واستقبال الرسائل
echo   • مراجعة الإشعارات
echo   • تقييم الصيدليات الأخرى
echo.
echo 🔍 البحث المتقدم:
echo   • البحث بالاسم
echo   • التصفية حسب المدينة
echo   • التصفية حسب الكمية
echo   • التصفية حسب السعر
echo   • استبعاد الأدوية المنتهية الصلاحية
echo.
echo 📱 نظام الإشعارات:
echo   • إشعارات طلبات الشراء
echo   • إشعارات الرسائل الجديدة
echo   • إشعارات النظام
echo.
echo 🧪 اختبر الآن:
echo 1. شغل برنامج إدارة الصيدلية
echo 2. اذهب لواجهة الموظف
echo 3. اضغط على "متجر الصيدلية"
echo 4. جرب نشر دواء للبيع
echo 5. ابحث عن أدوية من صيدليات أخرى
echo 6. أرسل طلب شراء
echo.

goto :success

:error
echo.
echo ========================================
echo   ❌ حدث خطأ أثناء الإعداد!
echo ========================================
echo.
echo تأكد من:
echo 1. تشغيل SQL Server
echo 2. وجود قاعدة البيانات UnifiedPharmacy
echo 3. صحة اسم الخادم (NARUTO)
echo 4. وجود صلاحيات الإدارة
echo.
echo إذا كانت قاعدة البيانات غير موجودة:
echo 1. افتح SQL Server Management Studio
echo 2. اتصل بالخادم NARUTO
echo 3. شغل الأمر: CREATE DATABASE UnifiedPharmacy
echo 4. ثم شغل هذا الملف مرة أخرى
echo.
goto :end

:success
echo 💡 نصائح للاستخدام:
echo.
echo • تأكد من تسجيل صيدليتك أولاً
echo • استخدم أوصاف واضحة للأدوية
echo • حدد أسعار منافسة
echo • رد على طلبات الشراء بسرعة
echo • حافظ على تحديث معلومات الأدوية
echo.

:end
echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
