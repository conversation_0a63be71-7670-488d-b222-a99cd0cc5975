using System;
using System.Windows.Forms;

namespace Pharmacy_Management_System
{
    public partial class TestTranslations : Form
    {
        public TestTranslations()
        {
            InitializeComponent();
            TestAllTranslations();
        }

        private void TestAllTranslations()
        {
            try
            {
                // اختبار الترجمات العربية
                LanguageManager.SetLanguage("ar");
                Console.WriteLine("=== Arabic Translations ===");
                Console.WriteLine(string.Format("Employee Sessions: {0}", LanguageManager.GetText("Employee Sessions")));
                Console.WriteLine(string.Format("Sales Report: {0}", LanguageManager.GetText("Sales Report")));
                Console.WriteLine(string.Format("Employee Name: {0}", LanguageManager.GetText("Employee Name")));
                Console.WriteLine(string.Format("Medicine Name: {0}", LanguageManager.GetText("Medicine Name")));
                Console.WriteLine(string.Format("Login Time: {0}", LanguageManager.GetText("Login Time")));
                Console.WriteLine(string.Format("Logout Time: {0}", LanguageManager.GetText("Logout Time")));
                Console.WriteLine(string.Format("Refresh: {0}", LanguageManager.GetText("Refresh")));
                Console.WriteLine(string.Format("Filter by Date: {0}", LanguageManager.GetText("Filter by Date")));
                Console.WriteLine(string.Format("Search Employee: {0}", LanguageManager.GetText("Search Employee")));
                Console.WriteLine(string.Format("Select Date: {0}", LanguageManager.GetText("Select Date")));
                Console.WriteLine(string.Format("ID: {0}", LanguageManager.GetText("ID")));
                Console.WriteLine(string.Format("Username: {0}", LanguageManager.GetText("Username")));
                Console.WriteLine(string.Format("Session Date: {0}", LanguageManager.GetText("Session Date")));
                Console.WriteLine(string.Format("Print Report: {0}", LanguageManager.GetText("Print Report")));

                // اختبار الترجمات الإنجليزية
                LanguageManager.SetLanguage("en");
                Console.WriteLine("\n=== English Translations ===");
                Console.WriteLine(string.Format("Employee Sessions: {0}", LanguageManager.GetText("Employee Sessions")));
                Console.WriteLine(string.Format("Sales Report: {0}", LanguageManager.GetText("Sales Report")));
                Console.WriteLine(string.Format("Employee Name: {0}", LanguageManager.GetText("Employee Name")));
                Console.WriteLine(string.Format("Medicine Name: {0}", LanguageManager.GetText("Medicine Name")));
                Console.WriteLine(string.Format("Login Time: {0}", LanguageManager.GetText("Login Time")));
                Console.WriteLine(string.Format("Logout Time: {0}", LanguageManager.GetText("Logout Time")));
                Console.WriteLine(string.Format("Refresh: {0}", LanguageManager.GetText("Refresh")));
                Console.WriteLine(string.Format("Filter by Date: {0}", LanguageManager.GetText("Filter by Date")));
                Console.WriteLine(string.Format("Search Employee: {0}", LanguageManager.GetText("Search Employee")));
                Console.WriteLine(string.Format("Select Date: {0}", LanguageManager.GetText("Select Date")));
                Console.WriteLine(string.Format("ID: {0}", LanguageManager.GetText("ID")));
                Console.WriteLine(string.Format("Username: {0}", LanguageManager.GetText("Username")));
                Console.WriteLine(string.Format("Session Date: {0}", LanguageManager.GetText("Session Date")));
                Console.WriteLine(string.Format("Print Report: {0}", LanguageManager.GetText("Print Report")));

                MessageBox.Show("Translation test completed successfully! Check console output.", "Test Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("Translation test failed: {0}", ex.Message), "Test Failed", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // TestTranslations
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(284, 261);
            this.Name = "TestTranslations";
            this.Text = "Test Translations";
            this.ResumeLayout(false);
        }
    }
}
