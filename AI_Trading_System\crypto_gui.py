#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة نظام تداول العملات الرقمية
Cryptocurrency Trading System GUI
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from datetime import datetime
import json
from crypto_trading_system import CryptoTradingSystem

class CryptoTradingGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 نظام تداول العملات الرقمية الذكي")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1e1e1e')
        
        # النظام الأساسي
        self.trading_system = CryptoTradingSystem(demo_mode=True)
        self.is_trading = False
        self.trading_thread = None
        
        # إعداد الواجهة
        self.setup_styles()
        self.create_widgets()
        self.update_display()
        
        # تحديث دوري للواجهة
        self.root.after(1000, self.periodic_update)
        
    def setup_styles(self):
        """إعداد أنماط الواجهة"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # ألوان داكنة
        style.configure('Dark.TFrame', background='#2d2d2d')
        style.configure('Dark.TLabel', background='#2d2d2d', foreground='white')
        style.configure('Dark.TButton', background='#404040', foreground='white')
        style.configure('Success.TButton', background='#28a745', foreground='white')
        style.configure('Danger.TButton', background='#dc3545', foreground='white')
        style.configure('Warning.TButton', background='#ffc107', foreground='black')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, style='Dark.TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # شريط العنوان
        title_frame = ttk.Frame(main_frame, style='Dark.TFrame')
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_label = ttk.Label(title_frame, text="🚀 نظام تداول العملات الرقمية الذكي", 
                               font=('Arial', 16, 'bold'), style='Dark.TLabel')
        title_label.pack(side=tk.LEFT)
        
        # حالة الاتصال
        self.status_label = ttk.Label(title_frame, text="🔴 غير متصل", 
                                     font=('Arial', 12), style='Dark.TLabel')
        self.status_label.pack(side=tk.RIGHT)
        
        # الإطار العلوي - معلومات الحساب والتحكم
        top_frame = ttk.Frame(main_frame, style='Dark.TFrame')
        top_frame.pack(fill=tk.X, pady=(0, 10))
        
        # معلومات الحساب
        account_frame = ttk.LabelFrame(top_frame, text="معلومات الحساب", style='Dark.TFrame')
        account_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        self.balance_label = ttk.Label(account_frame, text="الرصيد: $0.00", 
                                      font=('Arial', 12, 'bold'), style='Dark.TLabel')
        self.balance_label.pack(anchor=tk.W, padx=10, pady=5)
        
        self.positions_label = ttk.Label(account_frame, text="الصفقات المفتوحة: 0", 
                                        style='Dark.TLabel')
        self.positions_label.pack(anchor=tk.W, padx=10, pady=2)
        
        self.trades_label = ttk.Label(account_frame, text="إجمالي الصفقات: 0", 
                                     style='Dark.TLabel')
        self.trades_label.pack(anchor=tk.W, padx=10, pady=2)
        
        self.learning_label = ttk.Label(account_frame, text="نقاط التعلم: 0", 
                                       style='Dark.TLabel')
        self.learning_label.pack(anchor=tk.W, padx=10, pady=2)
        
        # لوحة التحكم
        control_frame = ttk.LabelFrame(top_frame, text="لوحة التحكم", style='Dark.TFrame')
        control_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # اختيار العملة
        currency_frame = ttk.Frame(control_frame, style='Dark.TFrame')
        currency_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(currency_frame, text="العملة:", style='Dark.TLabel').pack(side=tk.LEFT)
        
        self.currency_var = tk.StringVar(value=self.trading_system.current_pair)
        currency_combo = ttk.Combobox(currency_frame, textvariable=self.currency_var,
                                     values=self.trading_system.supported_pairs,
                                     state='readonly', width=15)
        currency_combo.pack(side=tk.LEFT, padx=(5, 0))
        currency_combo.bind('<<ComboboxSelected>>', self.on_currency_change)
        
        # وضع التداول
        mode_frame = ttk.Frame(control_frame, style='Dark.TFrame')
        mode_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(mode_frame, text="الوضع:", style='Dark.TLabel').pack(side=tk.LEFT)
        
        self.mode_var = tk.StringVar(value="محاكاة")
        mode_combo = ttk.Combobox(mode_frame, textvariable=self.mode_var,
                                 values=["محاكاة", "حقيقي"], state='readonly', width=15)
        mode_combo.pack(side=tk.LEFT, padx=(5, 0))
        mode_combo.bind('<<ComboboxSelected>>', self.on_mode_change)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(control_frame, style='Dark.TFrame')
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.start_button = ttk.Button(buttons_frame, text="🚀 بدء التداول", 
                                      command=self.start_trading, style='Success.TButton')
        self.start_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_button = ttk.Button(buttons_frame, text="⏹️ إيقاف التداول", 
                                     command=self.stop_trading, style='Danger.TButton',
                                     state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        self.train_button = ttk.Button(buttons_frame, text="🧠 تدريب النموذج", 
                                      command=self.train_model, style='Warning.TButton')
        self.train_button.pack(side=tk.LEFT, padx=5)
        
        # الإطار الأوسط - الرسوم البيانية والتحليل
        middle_frame = ttk.Frame(main_frame, style='Dark.TFrame')
        middle_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # تحليل السوق
        analysis_frame = ttk.LabelFrame(middle_frame, text="تحليل السوق", style='Dark.TFrame')
        analysis_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        self.analysis_text = scrolledtext.ScrolledText(analysis_frame, height=15, width=40,
                                                      bg='#1e1e1e', fg='white', 
                                                      font=('Consolas', 10))
        self.analysis_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # الصفقات المفتوحة
        positions_frame = ttk.LabelFrame(middle_frame, text="الصفقات المفتوحة", style='Dark.TFrame')
        positions_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # جدول الصفقات
        columns = ('الرمز', 'النوع', 'الحجم', 'سعر الدخول', 'الربح/الخسارة')
        self.positions_tree = ttk.Treeview(positions_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.positions_tree.heading(col, text=col)
            self.positions_tree.column(col, width=100)
            
        scrollbar = ttk.Scrollbar(positions_frame, orient=tk.VERTICAL, command=self.positions_tree.yview)
        self.positions_tree.configure(yscrollcommand=scrollbar.set)
        
        self.positions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        # الإطار السفلي - سجل الأحداث
        log_frame = ttk.LabelFrame(main_frame, text="سجل الأحداث", style='Dark.TFrame')
        log_frame.pack(fill=tk.X, pady=(0, 0))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, 
                                                 bg='#1e1e1e', fg='#00ff00', 
                                                 font=('Consolas', 9))
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
    def on_currency_change(self, event):
        """تغيير العملة المختارة"""
        new_pair = self.currency_var.get()
        self.trading_system.current_pair = new_pair
        self.log_message(f"🔄 تم تغيير العملة إلى: {new_pair}")
        
    def on_mode_change(self, event):
        """تغيير وضع التداول"""
        mode = self.mode_var.get()
        demo_mode = (mode == "محاكاة")
        self.trading_system.switch_trading_mode(demo_mode)
        self.log_message(f"🔄 تم التبديل إلى وضع: {mode}")
        
    def start_trading(self):
        """بدء التداول"""
        if self.is_trading:
            return
            
        self.is_trading = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        
        # بدء التداول في thread منفصل
        self.trading_thread = threading.Thread(target=self.trading_loop, daemon=True)
        self.trading_thread.start()
        
        self.log_message("🚀 تم بدء التداول...")
        
    def stop_trading(self):
        """إيقاف التداول"""
        self.is_trading = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        
        self.log_message("⏹️ تم إيقاف التداول")
        
    def trading_loop(self):
        """حلقة التداول الرئيسية"""
        try:
            while self.is_trading:
                # فحص الصفقات المفتوحة
                self.trading_system.check_positions()
                
                # تحليل السوق
                analysis = self.trading_system.analyze_and_trade()
                
                # عرض التحليل
                self.root.after(0, lambda: self.display_analysis(analysis))
                
                # تنفيذ التداول
                if analysis['decision'] in ['buy', 'sell'] and analysis['confidence'] >= self.trading_system.min_confidence:
                    if self.trading_system.demo_mode:
                        success = self.trading_system.execute_demo_trade(analysis)
                    else:
                        success = self.trading_system.execute_real_crypto_trade(analysis)
                        
                    if success:
                        self.root.after(0, lambda: self.log_message(f"✅ تم تنفيذ صفقة {analysis['decision']}"))
                
                # انتظار قبل التحليل التالي
                time.sleep(60)  # دقيقة واحدة
                
        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"❌ خطأ في التداول: {e}"))
            
    def train_model(self):
        """تدريب النموذج"""
        def train_thread():
            try:
                self.log_message("🧠 بدء تدريب النموذج...")
                success = self.trading_system.train_ml_model()
                
                if success:
                    self.root.after(0, lambda: self.log_message("✅ تم تدريب النموذج بنجاح"))
                else:
                    self.root.after(0, lambda: self.log_message("❌ فشل في تدريب النموذج"))
                    
            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"❌ خطأ في التدريب: {e}"))
                
        threading.Thread(target=train_thread, daemon=True).start()
        
    def display_analysis(self, analysis):
        """عرض تحليل السوق"""
        try:
            self.analysis_text.delete(1.0, tk.END)
            
            analysis_text = f"""
📊 تحليل السوق - {datetime.now().strftime('%H:%M:%S')}
{'='*40}

🎯 القرار: {analysis['decision'].upper()}
📈 مستوى الثقة: {analysis['confidence']:.2%}
📊 نقاط الاتجاه: {analysis.get('trend_score', 0)}
⚡ نقاط الزخم: {analysis.get('momentum_score', 0)}
📈 RSI: {analysis.get('rsi', 0):.2f}
💰 السعر الحالي: ${analysis.get('price', 0):.4f}
📊 التقلبات: {analysis.get('volatility', 0):.2f}%

"""
            
            if 'ml_prediction' in analysis:
                analysis_text += f"🤖 توقع ML: {analysis['ml_prediction']:.4f}\n"
                
            if 'reason' in analysis:
                analysis_text += f"💡 السبب: {analysis['reason']}\n"
                
            self.analysis_text.insert(tk.END, analysis_text)
            
        except Exception as e:
            self.log_message(f"❌ خطأ في عرض التحليل: {e}")
            
    def update_positions_table(self):
        """تحديث جدول الصفقات"""
        try:
            # مسح الجدول
            for item in self.positions_tree.get_children():
                self.positions_tree.delete(item)
                
            # إضافة الصفقات المفتوحة
            for trade_id, position in self.trading_system.positions.items():
                # حساب الربح/الخسارة الحالي
                price_data = self.trading_system.get_crypto_price(position['symbol'])
                if price_data:
                    current_price = price_data['price']
                    if position['type'] == 'buy':
                        pnl = (current_price - position['entry_price']) * position['size']
                    else:
                        pnl = (position['entry_price'] - current_price) * position['size']
                        
                    pnl_text = f"${pnl:.2f}"
                    if pnl > 0:
                        pnl_text = f"+{pnl_text}"
                        
                    self.positions_tree.insert('', tk.END, values=(
                        position['symbol'],
                        position['type'].upper(),
                        f"{position['size']:.6f}",
                        f"${position['entry_price']:.4f}",
                        pnl_text
                    ))
                    
        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث الجدول: {e}")
            
    def update_display(self):
        """تحديث عرض المعلومات"""
        try:
            # تحديث معلومات الحساب
            summary = self.trading_system.get_account_summary()
            
            self.balance_label.config(text=f"الرصيد: ${summary['balance']:.2f}")
            self.positions_label.config(text=f"الصفقات المفتوحة: {summary['open_positions']}")
            self.trades_label.config(text=f"إجمالي الصفقات: {summary['total_trades']}")
            self.learning_label.config(text=f"نقاط التعلم: {summary['learning_points']}")
            
            # تحديث حالة الاتصال
            self.status_label.config(text="🟢 متصل")
            
            # تحديث جدول الصفقات
            self.update_positions_table()
            
        except Exception as e:
            self.log_message(f"❌ خطأ في التحديث: {e}")
            
    def periodic_update(self):
        """تحديث دوري للواجهة"""
        self.update_display()
        self.root.after(5000, self.periodic_update)  # كل 5 ثوان
        
    def log_message(self, message):
        """إضافة رسالة للسجل"""
        try:
            timestamp = datetime.now().strftime('%H:%M:%S')
            log_entry = f"[{timestamp}] {message}\n"
            
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)
            
            # الاحتفاظ بآخر 100 سطر فقط
            lines = self.log_text.get(1.0, tk.END).split('\n')
            if len(lines) > 100:
                self.log_text.delete(1.0, f"{len(lines)-100}.0")
                
        except Exception as e:
            print(f"خطأ في السجل: {e}")
            
    def run(self):
        """تشغيل الواجهة"""
        try:
            self.log_message("🚀 مرحباً بك في نظام تداول العملات الرقمية الذكي")
            self.log_message("💡 اختر العملة والوضع ثم اضغط 'بدء التداول'")
            self.root.mainloop()
        except Exception as e:
            print(f"خطأ في تشغيل الواجهة: {e}")

def main():
    """الدالة الرئيسية"""
    try:
        app = CryptoTradingGUI()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")

if __name__ == "__main__":
    main()
