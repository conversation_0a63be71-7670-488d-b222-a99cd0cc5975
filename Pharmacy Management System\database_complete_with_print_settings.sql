-- إنشاء قاعدة البيانات إذا لم تكن موجودة
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'pharmacy')
BEGIN
    CREATE DATABASE pharmacy;
END
GO

USE pharmacy;
GO

-- إنشاء جدول المستخدمين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
BEGIN
    CREATE TABLE users(
        id int identity (1,1) primary key,
        userRole varchar(50) not null,
        name varchar (250) not null,
        dob varchar (250) not null,
        mobile bigint not null,
        email varchar (250) not null,
        username varchar (250) unique not null,
        pass varchar(250) not null
    );
END
GO

-- إنشاء جدول الأدوية
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='medic' AND xtype='U')
BEGIN
    CREATE TABLE medic(
        id int identity(1,1) primary key,
        mid varchar(250) not null,
        mname varchar (250) not null,
        mnumber varchar (250) not null,
        mDate varchar (250) not null,
        eDate varchar(250) not null,
        quantity bigint not null,
        perUnit bigint not null,
        lu varchar(250) not null,
        br varchar(250) not null
    );
END
GO

-- إضافة الأعمدة الجديدة لجدول الأدوية إذا لم تكن موجودة
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'newEDate')
    ALTER TABLE medic ADD newEDate VARCHAR(250) NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'newQuantity')
    ALTER TABLE medic ADD newQuantity BIGINT NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'allqun')
    ALTER TABLE medic ADD allqun BIGINT NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'mnumber_qty')
    ALTER TABLE medic ADD mnumber_qty INT NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'newMDate')
    ALTER TABLE medic ADD newMDate VARCHAR(250) NULL;

-- إضافة أعمدة الجرعات
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'dos2')
    ALTER TABLE medic ADD dos2 BIGINT NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'dos2_qty')
    ALTER TABLE medic ADD dos2_qty INT NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'dos3')
    ALTER TABLE medic ADD dos3 BIGINT NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'dos3_qty')
    ALTER TABLE medic ADD dos3_qty INT NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'dos4')
    ALTER TABLE medic ADD dos4 BIGINT NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'dos4_qty')
    ALTER TABLE medic ADD dos4_qty INT NULL;

-- إضافة أعمدة الكميات الأصلية
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'originalQuantity')
    ALTER TABLE medic ADD originalQuantity BIGINT DEFAULT 0;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'originalNewQuantity')
    ALTER TABLE medic ADD originalNewQuantity BIGINT DEFAULT 0;

-- إنشاء جدول المبيعات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='sales' AND xtype='U')
BEGIN
    CREATE TABLE sales (
        id INT IDENTITY(1,1) PRIMARY KEY,
        mid VARCHAR(250) NOT NULL,
        medicineName VARCHAR(250) NOT NULL,
        dosage VARCHAR(100) NOT NULL,
        quantity INT NOT NULL,
        pricePerUnit BIGINT NOT NULL,
        totalPrice BIGINT NOT NULL,
        employeeUsername VARCHAR(250) NOT NULL,
        employeeName VARCHAR(250) NOT NULL,
        saleDate DATETIME DEFAULT GETDATE()
    );
END
GO

-- إنشاء جدول جلسات الموظفين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='employee_sessions' AND xtype='U')
BEGIN
    CREATE TABLE employee_sessions (
        id INT IDENTITY(1,1) PRIMARY KEY,
        username VARCHAR(250),
        employeeName VARCHAR(250),
        loginTime DATETIME,
        logoutTime DATETIME NULL,
        sessionDate DATE
    );
END
GO

-- إنشاء جدول إعدادات الطباعة الجديد
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='print_settings' AND xtype='U')
BEGIN
    CREATE TABLE print_settings (
        id INT IDENTITY(1,1) PRIMARY KEY,
        reportType VARCHAR(100) NOT NULL,
        paperSize VARCHAR(50) DEFAULT 'A4',
        orientation VARCHAR(50) DEFAULT 'عمودي',
        marginTop INT DEFAULT 20,
        marginBottom INT DEFAULT 20,
        marginLeft INT DEFAULT 15,
        marginRight INT DEFAULT 15,
        titleText VARCHAR(500) DEFAULT 'تقرير الصيدلية',
        titleFont INT DEFAULT 18,
        titleAlignment VARCHAR(50) DEFAULT 'وسط',
        showDateTime BIT DEFAULT 1,
        dateFormat VARCHAR(50) DEFAULT 'dd/MM/yyyy',
        datePosition VARCHAR(50) DEFAULT 'أعلى يمين',
        tableFont INT DEFAULT 10,
        borderWidth INT DEFAULT 1,
        footerText VARCHAR(500) DEFAULT 'نظام إدارة الصيدلية',
        showPageNumbers BIT DEFAULT 1,
        titleColor INT DEFAULT -16777216, -- أسود
        tableHeaderColor INT DEFAULT -3355444, -- رمادي فاتح
        tableTextColor INT DEFAULT -16777216, -- أسود
        createdDate DATETIME DEFAULT GETDATE(),
        lastModified DATETIME DEFAULT GETDATE(),
        UNIQUE(reportType)
    );
END
GO

-- إدراج الإعدادات الافتراضية لأنواع التقارير المختلفة
INSERT INTO print_settings (reportType, titleText) VALUES 
('عام', 'تقرير الصيدلية'),
('مبيعات الأدوية', 'تقرير مبيعات الأدوية'),
('تقرير المبيعات', 'تقرير المبيعات'),
('جلسات الموظفين', 'تقرير جلسات الموظفين'),
('جرد الأدوية', 'تقرير جرد الأدوية'),
('صلاحية الأدوية', 'تقرير صلاحية الأدوية')
ON DUPLICATE KEY UPDATE titleText = VALUES(titleText);

-- تحديث الكميات الأصلية للأدوية الموجودة
UPDATE medic SET originalQuantity = quantity WHERE originalQuantity = 0 OR originalQuantity IS NULL;
UPDATE medic SET originalNewQuantity = ISNULL(newQuantity, 0) WHERE originalNewQuantity = 0 OR originalNewQuantity IS NULL;

-- إنشاء فهارس لتحسين الأداء
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_print_settings_reportType')
    CREATE INDEX IX_print_settings_reportType ON print_settings(reportType);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_sales_saleDate')
    CREATE INDEX IX_sales_saleDate ON sales(saleDate);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_employee_sessions_sessionDate')
    CREATE INDEX IX_employee_sessions_sessionDate ON employee_sessions(sessionDate);

PRINT 'تم إنشاء قاعدة البيانات وجميع الجداول بنجاح!';
PRINT 'تم إضافة جدول إعدادات الطباعة الجديد لحل مشكلة عدم حفظ الإعدادات.';
