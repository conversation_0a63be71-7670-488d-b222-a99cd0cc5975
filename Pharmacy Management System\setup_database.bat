@echo off
echo ========================================
echo   إعداد قاعدة بيانات نظام إدارة الصيدلية
echo ========================================
echo.

echo 🔧 تحديث مخطط قاعدة البيانات...
echo.

REM تشغيل سكريبت تحديث قاعدة البيانات
sqlcmd -S NARUTO -E -i update_database_schema.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم تحديث قاعدة البيانات بنجاح!
    echo.
    echo الجداول المتاحة:
    echo - users: جدول المستخدمين
    echo - medic: جدول الأدوية مع جميع الأعمدة
    echo - sales: جدول المبيعات
    echo - employee_sessions: جدول جلسات الموظفين
    echo.
    echo يمكنك الآن تشغيل نظام إدارة الصيدلية.
) else (
    echo.
    echo ❌ حدث خطأ أثناء تحديث قاعدة البيانات!
    echo.
    echo تأكد من:
    echo 1. تشغيل SQL Server
    echo 2. صحة اسم الخادم (NARUTO)
    echo 3. وجود صلاحيات الإدارة
    echo 4. وجود ملف update_database_schema.sql
)

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
