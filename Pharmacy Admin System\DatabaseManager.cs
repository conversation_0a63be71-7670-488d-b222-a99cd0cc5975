using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

namespace Pharmacy_Admin_System
{
    /// <summary>
    /// مدير قاعدة البيانات للنظام الإداري
    /// Database Manager for Admin System
    /// </summary>
    public class DatabaseManager
    {
        private readonly string adminConnectionString;
        private readonly string pharmacyConnectionString;

        public DatabaseManager()
        {
            // قاعدة بيانات الإدارة المنفصلة
            adminConnectionString = ConfigurationManager.ConnectionStrings["AdminDatabase"]?.ConnectionString 
                ?? "data source=NARUTO;database=PharmacyAdminSystem;integrated security=True";
            
            // قاعدة بيانات الصيدليات للقراءة فقط
            pharmacyConnectionString = ConfigurationManager.ConnectionStrings["PharmacyDatabase"]?.ConnectionString 
                ?? "data source=NARUTO;database=UnifiedPharmacy;integrated security=True";
        }

        #region Admin Database Operations

        /// <summary>
        /// الحصول على اتصال بقاعدة بيانات الإدارة
        /// </summary>
        public SqlConnection GetAdminConnection()
        {
            return new SqlConnection(adminConnectionString);
        }

        /// <summary>
        /// تنفيذ استعلام على قاعدة بيانات الإدارة
        /// </summary>
        public DataTable ExecuteAdminQuery(string query, Dictionary<string, object> parameters = null)
        {
            try
            {
                using (var connection = GetAdminConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        if (parameters != null)
                        {
                            foreach (var param in parameters)
                            {
                                command.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                            }
                        }

                        using (var adapter = new SqlDataAdapter(command))
                        {
                            var dataTable = new DataTable();
                            adapter.Fill(dataTable);
                            return dataTable;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الاستعلام: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تنفيذ أمر على قاعدة بيانات الإدارة
        /// </summary>
        public int ExecuteAdminCommand(string command, Dictionary<string, object> parameters = null)
        {
            try
            {
                using (var connection = GetAdminConnection())
                {
                    connection.Open();
                    using (var cmd = new SqlCommand(command, connection))
                    {
                        if (parameters != null)
                        {
                            foreach (var param in parameters)
                            {
                                cmd.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                            }
                        }

                        return cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الأمر: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تنفيذ أمر وإرجاع قيمة واحدة
        /// </summary>
        public object ExecuteAdminScalar(string command, Dictionary<string, object> parameters = null)
        {
            try
            {
                using (var connection = GetAdminConnection())
                {
                    connection.Open();
                    using (var cmd = new SqlCommand(command, connection))
                    {
                        if (parameters != null)
                        {
                            foreach (var param in parameters)
                            {
                                cmd.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                            }
                        }

                        return cmd.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الأمر: {ex.Message}", ex);
            }
        }

        #endregion

        #region Pharmacy Database Operations (Read-Only)

        /// <summary>
        /// الحصول على اتصال بقاعدة بيانات الصيدليات (للقراءة فقط)
        /// </summary>
        public SqlConnection GetPharmacyConnection()
        {
            return new SqlConnection(pharmacyConnectionString);
        }

        /// <summary>
        /// تنفيذ استعلام على قاعدة بيانات الصيدليات (للقراءة فقط)
        /// </summary>
        public DataTable ExecutePharmacyQuery(string query, Dictionary<string, object> parameters = null)
        {
            try
            {
                using (var connection = GetPharmacyConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        if (parameters != null)
                        {
                            foreach (var param in parameters)
                            {
                                command.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                            }
                        }

                        using (var adapter = new SqlDataAdapter(command))
                        {
                            var dataTable = new DataTable();
                            adapter.Fill(dataTable);
                            return dataTable;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في قراءة بيانات الصيدليات: {ex.Message}", ex);
            }
        }

        #endregion

        #region Authentication

        /// <summary>
        /// التحقق من صحة بيانات تسجيل دخول المدير العام
        /// </summary>
        public DataRow ValidateAdminLogin(string username, string password)
        {
            try
            {
                string query = @"
                    SELECT id, username, fullName, email, phone, role, lastLogin
                    FROM admin_users 
                    WHERE username = @username AND password = @password AND isActive = 1";

                var parameters = new Dictionary<string, object>
                {
                    {"@username", username},
                    {"@password", password}
                };

                var result = ExecuteAdminQuery(query, parameters);
                
                if (result.Rows.Count > 0)
                {
                    // تحديث آخر تسجيل دخول
                    UpdateLastLogin(Convert.ToInt32(result.Rows[0]["id"]));
                    return result.Rows[0];
                }

                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في التحقق من بيانات تسجيل الدخول: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث آخر تسجيل دخول للمدير
        /// </summary>
        private void UpdateLastLogin(int adminId)
        {
            try
            {
                string command = "UPDATE admin_users SET lastLogin = GETDATE() WHERE id = @adminId";
                var parameters = new Dictionary<string, object> { {"@adminId", adminId} };
                ExecuteAdminCommand(command, parameters);
            }
            catch
            {
                // تجاهل الأخطاء في تحديث آخر تسجيل دخول
            }
        }

        #endregion

        #region Activity Logging

        /// <summary>
        /// تسجيل نشاط في سجل النشاطات
        /// </summary>
        public void LogActivity(int? adminUserId, int? pharmacyId, string activityType, string description, string details = null)
        {
            try
            {
                string command = @"
                    INSERT INTO activity_log (adminUserId, pharmacyId, activityType, description, details, activityDate)
                    VALUES (@adminUserId, @pharmacyId, @activityType, @description, @details, GETDATE())";

                var parameters = new Dictionary<string, object>
                {
                    {"@adminUserId", adminUserId},
                    {"@pharmacyId", pharmacyId},
                    {"@activityType", activityType},
                    {"@description", description},
                    {"@details", details}
                };

                ExecuteAdminCommand(command, parameters);
            }
            catch
            {
                // تجاهل أخطاء تسجيل النشاطات لتجنب توقف النظام
            }
        }

        #endregion

        #region Database Initialization

        /// <summary>
        /// التحقق من وجود قاعدة البيانات وإنشاؤها إذا لم تكن موجودة
        /// </summary>
        public bool EnsureAdminDatabaseExists()
        {
            try
            {
                // محاولة الاتصال بقاعدة البيانات
                using (var connection = GetAdminConnection())
                {
                    connection.Open();
                    return true;
                }
            }
            catch
            {
                // إذا فشل الاتصال، قاعدة البيانات غير موجودة
                return false;
            }
        }

        /// <summary>
        /// إنشاء قاعدة البيانات الإدارية
        /// </summary>
        public void CreateAdminDatabase()
        {
            try
            {
                // قراءة سكريبت إنشاء قاعدة البيانات وتنفيذه
                string masterConnectionString = adminConnectionString.Replace("database=PharmacyAdminSystem", "database=master");
                
                using (var connection = new SqlConnection(masterConnectionString))
                {
                    connection.Open();
                    
                    // إنشاء قاعدة البيانات
                    string createDbCommand = @"
                        IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'PharmacyAdminSystem')
                        BEGIN
                            CREATE DATABASE PharmacyAdminSystem;
                        END";
                    
                    using (var command = new SqlCommand(createDbCommand, connection))
                    {
                        command.ExecuteNonQuery();
                    }
                }

                // الآن إنشاء الجداول
                CreateAdminTables();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء قاعدة البيانات الإدارية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إنشاء جداول قاعدة البيانات الإدارية
        /// </summary>
        private void CreateAdminTables()
        {
            // سيتم تنفيذ هذا من خلال سكريبت SQL منفصل
            // يمكن تحسينه لاحقاً لتنفيذ السكريبت برمجياً
        }

        #endregion

        #region System Settings

        /// <summary>
        /// الحصول على قيمة إعداد النظام
        /// </summary>
        public string GetSystemSetting(string key, string defaultValue = null)
        {
            try
            {
                string query = "SELECT settingValue FROM system_settings WHERE settingKey = @key";
                var parameters = new Dictionary<string, object> { {"@key", key} };
                
                var result = ExecuteAdminScalar(query, parameters);
                return result?.ToString() ?? defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }

        /// <summary>
        /// تحديث قيمة إعداد النظام
        /// </summary>
        public void UpdateSystemSetting(string key, string value, int modifiedBy)
        {
            try
            {
                string command = @"
                    UPDATE system_settings 
                    SET settingValue = @value, lastModified = GETDATE(), modifiedBy = @modifiedBy 
                    WHERE settingKey = @key";

                var parameters = new Dictionary<string, object>
                {
                    {"@key", key},
                    {"@value", value},
                    {"@modifiedBy", modifiedBy}
                };

                ExecuteAdminCommand(command, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث إعداد النظام: {ex.Message}", ex);
            }
        }

        #endregion
    }
}
