@echo off
chcp 65001 > nul
title 🧠 اختبار قرارات التداول - Trading Decisions Test

echo.
echo ================================================================
echo 🧠 اختبار قرارات التداول
echo TRADING DECISIONS TEST
echo ================================================================
echo.

echo 🎯 هذا الاختبار سيتحقق من:
echo    ✅ حد الثقة المطلوب للتداول
echo    ✅ قدرة النظام على اتخاذ قرارات تداول
echo    ✅ عدم البقاء في وضع الانتظار فقط
echo    ✅ جاهزية النظام لفتح صفقات حقيقية
echo.

echo 💡 متطلبات الاختبار:
echo    🔌 MetaTrader 5 يعمل ومسجل دخول
echo    ⚙️ التداول الآلي مفعل
echo    📄 ملف config.ini محدث ببيانات حسابك
echo.

echo ⚠️ ملاحظة:
echo    🧠 النظام قد يحتاج عدة دورات لاتخاذ قرار
echo    📊 هذا طبيعي للتداول الذكي الآمن
echo    🔄 الاختبار سيجرب 5 دورات تحليل
echo.

pause

echo 🔄 بدء اختبار قرارات التداول...
echo.

python test_trading_decisions.py

echo.
echo ================================================================
echo ✅ انتهى الاختبار
echo ================================================================
echo.

echo 💡 إذا نجح الاختبار:
echo    🚀 شغّل النظام: run_intelligent_gui_v2.bat
echo    ⏰ اتركه يعمل لفترة أطول (30+ دقيقة)
echo    📊 راقب السجلات للقرارات
echo    💰 سيفتح صفقات حقيقية عند وجود فرص
echo.

echo ⚠️ إذا كان النظام محافظ جداً:
echo    🔄 هذا طبيعي للتداول الآمن
echo    ⏰ اتركه يعمل وقت أطول
echo    📈 سيتداول عند وجود فرص قوية
echo.

pause
