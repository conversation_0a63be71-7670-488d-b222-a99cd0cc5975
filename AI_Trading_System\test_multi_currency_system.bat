@echo off
title Test Multi-Currency Intelligent System V3

echo.
echo ================================================================
echo   TESTING MULTI-CURRENCY INTELLIGENT TRADING SYSTEM V3
echo   Comprehensive System Testing and Validation
echo ================================================================
echo.

echo 🧪 TESTING COMPONENTS:
echo ✅ System Initialization
echo ✅ Currency Pair Setup (12+ pairs)
echo ✅ Strategy Configuration (6 strategies)
echo ✅ Timeframe Setup (9 timeframes)
echo ✅ Self-Learning System
echo ✅ Technical Indicators Calculation
echo ✅ Market Condition Analysis
echo ✅ Strategy Signal Generation
echo.

echo Installing test dependencies...
pip install pandas numpy scikit-learn --quiet

echo.
echo 🚀 Running Comprehensive System Test...
echo.

python test_multi_currency_system.py

if errorlevel 1 (
    echo.
    echo ❌ Some tests failed!
    echo 💡 Check the error messages above
    echo 💡 Make sure all dependencies are installed
    echo.
) else (
    echo.
    echo ✅ All tests passed successfully!
    echo 🚀 System is ready for trading!
    echo.
    echo 💡 Next steps:
    echo    1. Configure your MT5 credentials in config.ini
    echo    2. Run: run_multi_currency_system.bat
    echo    3. Start with Demo account first
    echo.
)

pause
