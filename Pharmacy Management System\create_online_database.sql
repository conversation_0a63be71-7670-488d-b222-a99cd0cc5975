-- إنشاء قاعدة البيانات الأونلاين للصيدليات
USE master;
GO

-- حذف قاعدة البيانات إذا كانت موجودة
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'PharmacyNetworkOnline')
BEGIN
    ALTER DATABASE PharmacyNetworkOnline SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    DROP DATABASE PharmacyNetworkOnline;
END
GO

-- إنشاء قاعدة البيانات الجديدة
CREATE DATABASE PharmacyNetworkOnline;
GO

USE PharmacyNetworkOnline;
GO

-- جدول الصيدليات المسجلة
CREATE TABLE pharmacies (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacy_code NVARCHAR(50) UNIQUE NOT NULL,
    pharmacy_name NVARCHAR(200) NOT NULL,
    owner_name NVARCHAR(100) NOT NULL,
    phone NVARCHAR(20),
    email NVARCHAR(100),
    address NVARCHAR(500),
    city NVARCHAR(100),
    registration_date DATETIME DEFAULT GETDATE(),
    is_active BIT DEFAULT 1,
    last_online DATETIME DEFAULT GETDATE()
);

-- جدول المستخدمين للصيدليات
CREATE TABLE pharmacy_users (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacy_id INT FOREIGN KEY REFERENCES pharmacies(id),
    username NVARCHAR(50) NOT NULL,
    password_hash NVARCHAR(255) NOT NULL,
    user_role NVARCHAR(20) DEFAULT 'Employee',
    full_name NVARCHAR(100),
    email NVARCHAR(100),
    is_active BIT DEFAULT 1,
    created_date DATETIME DEFAULT GETDATE()
);

-- جدول الأدوية المشتركة
CREATE TABLE shared_medicines (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacy_id INT FOREIGN KEY REFERENCES pharmacies(id),
    medicine_id NVARCHAR(50) NOT NULL,
    medicine_name NVARCHAR(200) NOT NULL,
    manufacturer NVARCHAR(100),
    quantity INT NOT NULL,
    price_per_unit DECIMAL(10,2) NOT NULL,
    wholesale_price DECIMAL(10,2),
    expiry_date DATE,
    dosage NVARCHAR(100),
    description NVARCHAR(500),
    is_available BIT DEFAULT 1,
    shared_date DATETIME DEFAULT GETDATE(),
    last_updated DATETIME DEFAULT GETDATE()
);

-- جدول طلبات الأدوية
CREATE TABLE medicine_orders (
    id INT IDENTITY(1,1) PRIMARY KEY,
    requesting_pharmacy_id INT FOREIGN KEY REFERENCES pharmacies(id),
    supplying_pharmacy_id INT FOREIGN KEY REFERENCES pharmacies(id),
    medicine_id INT FOREIGN KEY REFERENCES shared_medicines(id),
    requested_quantity INT NOT NULL,
    agreed_price DECIMAL(10,2),
    order_status NVARCHAR(20) DEFAULT 'Pending', -- Pending, Approved, Rejected, Completed
    order_date DATETIME DEFAULT GETDATE(),
    response_date DATETIME,
    completion_date DATETIME,
    notes NVARCHAR(500)
);

-- جدول المحادثات بين الصيدليات
CREATE TABLE pharmacy_chats (
    id INT IDENTITY(1,1) PRIMARY KEY,
    sender_pharmacy_id INT FOREIGN KEY REFERENCES pharmacies(id),
    receiver_pharmacy_id INT FOREIGN KEY REFERENCES pharmacies(id),
    message_text NVARCHAR(1000) NOT NULL,
    message_type NVARCHAR(20) DEFAULT 'Text', -- Text, Order, Medicine
    related_order_id INT FOREIGN KEY REFERENCES medicine_orders(id),
    sent_date DATETIME DEFAULT GETDATE(),
    is_read BIT DEFAULT 0
);

-- جدول إحصائيات الشبكة
CREATE TABLE network_stats (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacy_id INT FOREIGN KEY REFERENCES pharmacies(id),
    total_orders_sent INT DEFAULT 0,
    total_orders_received INT DEFAULT 0,
    total_medicines_shared INT DEFAULT 0,
    total_sales_amount DECIMAL(15,2) DEFAULT 0,
    last_activity DATETIME DEFAULT GETDATE(),
    stats_date DATE DEFAULT CAST(GETDATE() AS DATE)
);

-- إدراج بيانات تجريبية
INSERT INTO pharmacies (pharmacy_code, pharmacy_name, owner_name, phone, email, address, city) VALUES
('PH001', 'صيدلية النور', 'أحمد محمد', '01234567890', '<EMAIL>', 'شارع الجامعة', 'القاهرة'),
('PH002', 'صيدلية الشفاء', 'فاطمة علي', '01987654321', '<EMAIL>', 'شارع المعز', 'الإسكندرية'),
('PH003', 'صيدلية الصحة', 'محمد حسن', '01122334455', '<EMAIL>', 'شارع النيل', 'الجيزة');

-- إدراج مستخدمين تجريبيين
INSERT INTO pharmacy_users (pharmacy_id, username, password_hash, user_role, full_name) VALUES
(1, 'admin1', 'admin123', 'Admin', 'أحمد محمد'),
(2, 'admin2', 'admin123', 'Admin', 'فاطمة علي'),
(3, 'admin3', 'admin123', 'Admin', 'محمد حسن');

-- إدراج أدوية تجريبية
INSERT INTO shared_medicines (pharmacy_id, medicine_id, medicine_name, manufacturer, quantity, price_per_unit, wholesale_price, expiry_date, dosage) VALUES
(1, 'MED001', 'باراسيتامول', 'شركة الدواء', 100, 5.50, 4.00, '2025-12-31', '500mg'),
(1, 'MED002', 'أسبرين', 'شركة الصحة', 50, 8.00, 6.50, '2025-10-15', '100mg'),
(2, 'MED003', 'أموكسيسيلين', 'شركة المضادات', 75, 15.00, 12.00, '2025-08-20', '250mg'),
(2, 'MED004', 'فيتامين سي', 'شركة الفيتامينات', 200, 12.00, 9.00, '2026-01-10', '1000mg'),
(3, 'MED005', 'إيبوبروفين', 'شركة المسكنات', 80, 10.00, 7.50, '2025-11-30', '400mg');

PRINT 'تم إنشاء قاعدة البيانات الأونلاين بنجاح!';
