@echo off
chcp 65001 >nul
echo ═══════════════════════════════════════════════════════════════
echo                    اختبار سريع لنظام إدارة الصيدلية
echo                   Quick Test Pharmacy Management System
echo ═══════════════════════════════════════════════════════════════
echo.

echo الخطوة 1: التحقق من قاعدة البيانات
echo Step 1: Checking database
echo ───────────────────────────────────────────────────────────────
sqlcmd -S . -E -Q "USE UnifiedPharmacy; SELECT COUNT(*) as published_medicines FROM published_medicines WHERE is_available = 1;" >nul 2>&1

if %ERRORLEVEL% EQU 0 (
    echo ✅ قاعدة البيانات متصلة وتحتوي على أدوية منشورة
    echo ✅ Database connected and contains published medicines
) else (
    echo ❌ مشكلة في قاعدة البيانات
    echo ❌ Database issue
    pause
    exit /b 1
)

echo.
echo الخطوة 2: بناء المشروع
echo Step 2: Building project
echo ───────────────────────────────────────────────────────────────

REM البحث عن MSBuild
set MSBUILD_PATH=""
if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
) else (
    echo ⚠️ MSBuild غير موجود، محاولة استخدام dotnet build
    echo ⚠️ MSBuild not found, trying dotnet build
    dotnet build "Pharmacy Management System.csproj" --configuration Debug >nul 2>&1
    goto CHECK_BUILD
)

%MSBUILD_PATH% "Pharmacy Management System.csproj" /p:Configuration=Debug /p:Platform="Any CPU" /nologo /verbosity:quiet

:CHECK_BUILD
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء المشروع بنجاح
    echo ✅ Project built successfully
) else (
    echo ❌ فشل في بناء المشروع
    echo ❌ Project build failed
    pause
    exit /b 1
)

echo.
echo الخطوة 3: تشغيل النظام
echo Step 3: Running system
echo ───────────────────────────────────────────────────────────────

if exist "bin\Debug\Pharmacy Management System.exe" (
    echo 🚀 تشغيل نظام إدارة الصيدلية...
    echo 🚀 Starting Pharmacy Management System...
    echo.
    echo 📋 معلومات تسجيل الدخول:
    echo 📋 Login credentials:
    echo • المدير: admin / admin123
    echo • Administrator: admin / admin123
    echo • الموظف: employee1 / emp123
    echo • Employee: employee1 / emp123
    echo.
    echo 🧪 خطوات الاختبار:
    echo 🧪 Testing steps:
    echo 1. سجل دخول كموظف (employee1 / emp123)
    echo 1. Login as employee (employee1 / emp123)
    echo 2. اضغط على "Pharmacy Store" في الشريط الجانبي
    echo 2. Click "Pharmacy Store" in the sidebar
    echo 3. تحقق من التبويبات الثلاثة:
    echo 3. Check the three tabs:
    echo    • الأدوية المحلية (Local Medicines)
    echo    • الأدوية المنشورة (Published Medicines)
    echo    • أدويتي المعروضة (My Published Medicines)
    echo.
    echo النظام سيفتح الآن...
    echo System will open now...
    start "" "bin\Debug\Pharmacy Management System.exe"
    echo.
    echo ✅ تم تشغيل النظام بنجاح!
    echo ✅ System started successfully!
    echo.
    echo 📊 إحصائيات قاعدة البيانات:
    echo 📊 Database statistics:
    sqlcmd -S . -E -Q "USE UnifiedPharmacy; SELECT 'Published Medicines' as Type, COUNT(*) as Count FROM published_medicines WHERE is_available = 1 UNION ALL SELECT 'Pharmacies' as Type, COUNT(*) as Count FROM pharmacies;"
) else (
    echo ❌ ملف التشغيل غير موجود
    echo ❌ Executable file not found
    echo يرجى التحقق من بناء المشروع
    echo Please check project build
)

echo.
echo ═══════════════════════════════════════════════════════════════
echo                        🎉 انتهى الاختبار!
echo                     🎉 Testing Completed!
echo ═══════════════════════════════════════════════════════════════
echo.
echo إذا لم تظهر الأدوية المنشورة:
echo If published medicines don't appear:
echo 1. تحقق من رسائل الخطأ في النظام
echo 1. Check error messages in the system
echo 2. تأكد من تسجيل الدخول بشكل صحيح
echo 2. Make sure you're logged in correctly
echo 3. جرب إعادة تشغيل النظام
echo 3. Try restarting the system
echo.
pause
