@echo off
chcp 65001 >nul
echo ═══════════════════════════════════════════════════════════════
echo                    اختبار وتشغيل نظام إدارة الصيدلية
echo                Test and Run Pharmacy Management System
echo ═══════════════════════════════════════════════════════════════
echo.

echo الخطوة 1: إنهاء أي عمليات تشغيل سابقة
echo Step 1: Terminating any running processes
echo ───────────────────────────────────────────────────────────────
taskkill /f /im "Pharmacy Management System.exe" >nul 2>&1
echo ✅ تم إنهاء العمليات السابقة
echo ✅ Previous processes terminated

echo.
echo الخطوة 2: تنظيف مجلدات البناء
echo Step 2: Cleaning build folders
echo ───────────────────────────────────────────────────────────────
if exist "bin" rmdir /s /q "bin" >nul 2>&1
if exist "obj" rmdir /s /q "obj" >nul 2>&1
echo ✅ تم تنظيف مجلدات البناء
echo ✅ Build folders cleaned

echo.
echo الخطوة 3: البحث عن MSBuild
echo Step 3: Looking for MSBuild
echo ───────────────────────────────────────────────────────────────

set MSBUILD_PATH=""

REM تحقق من Visual Studio 2022
if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    echo ✅ تم العثور على MSBuild 2022
    echo ✅ Found MSBuild 2022
    goto BUILD
)

REM تحقق من Visual Studio 2019
if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
    echo ✅ تم العثور على MSBuild 2019
    echo ✅ Found MSBuild 2019
    goto BUILD
)

REM تحقق من .NET Framework MSBuild
if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2017\BuildTools\MSBuild\15.0\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2017\BuildTools\MSBuild\15.0\Bin\MSBuild.exe"
    echo ✅ تم العثور على MSBuild 2017
    echo ✅ Found MSBuild 2017
    goto BUILD
)

echo ❌ لم يتم العثور على MSBuild
echo ❌ MSBuild not found
echo يرجى تثبيت Visual Studio أو Build Tools
echo Please install Visual Studio or Build Tools
pause
exit /b 1

:BUILD
echo.
echo الخطوة 4: بناء المشروع
echo Step 4: Building project
echo ───────────────────────────────────────────────────────────────
%MSBUILD_PATH% "Pharmacy Management System.csproj" /p:Configuration=Debug /p:Platform="Any CPU" /p:TargetFrameworkVersion=v4.7.2

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء المشروع بنجاح!
    echo ✅ Project built successfully!
) else (
    echo ❌ فشل في بناء المشروع
    echo ❌ Project build failed
    echo يرجى التحقق من الأخطاء أعلاه
    echo Please check the errors above
    pause
    exit /b 1
)

echo.
echo الخطوة 5: اختبار قاعدة البيانات
echo Step 5: Testing database
echo ───────────────────────────────────────────────────────────────
sqlcmd -S . -E -Q "USE UnifiedPharmacy; SELECT COUNT(*) as published_medicines_count FROM published_medicines WHERE is_available = 1;" >nul 2>&1

if %ERRORLEVEL% EQU 0 (
    echo ✅ قاعدة البيانات متصلة وتعمل
    echo ✅ Database connected and working
) else (
    echo ⚠️ مشكلة في الاتصال بقاعدة البيانات
    echo ⚠️ Database connection issue
    echo تشغيل إعداد قاعدة البيانات...
    echo Running database setup...
    call setup_pharmacy_store_database.bat
)

echo.
echo الخطوة 6: تشغيل النظام
echo Step 6: Running system
echo ───────────────────────────────────────────────────────────────

if exist "bin\Debug\Pharmacy Management System.exe" (
    echo 🚀 تشغيل نظام إدارة الصيدلية...
    echo 🚀 Starting Pharmacy Management System...
    echo.
    echo معلومات تسجيل الدخول:
    echo Login credentials:
    echo • المدير: admin / admin123
    echo • Administrator: admin / admin123
    echo • الموظف: employee1 / emp123
    echo • Employee: employee1 / emp123
    echo.
    echo النظام سيفتح الآن...
    echo System will open now...
    start "" "bin\Debug\Pharmacy Management System.exe"
    echo.
    echo ✅ تم تشغيل النظام بنجاح!
    echo ✅ System started successfully!
) else (
    echo ❌ ملف التشغيل غير موجود
    echo ❌ Executable file not found
    echo يرجى التحقق من بناء المشروع
    echo Please check project build
)

echo.
echo ═══════════════════════════════════════════════════════════════
echo                        🎉 انتهى الاختبار!
echo                     🎉 Testing Completed!
echo ═══════════════════════════════════════════════════════════════
echo.
echo للاختبار:
echo For testing:
echo 1. سجل دخول كموظف (employee1 / emp123)
echo 1. Login as employee (employee1 / emp123)
echo 2. اذهب لمتجر الصيدلية
echo 2. Go to Pharmacy Store
echo 3. جرب نشر دواء جديد
echo 3. Try publishing a new medicine
echo 4. تحقق من ظهوره في "أدويتي المعروضة"
echo 4. Check if it appears in "My Published Medicines"
echo.
pause
