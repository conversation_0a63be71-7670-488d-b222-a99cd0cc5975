#!/usr/bin/env python3
"""
Intelligent Trading System V2 - Advanced GUI
واجهة المستخدم الرسومية المتطورة للنظام الذكي
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from datetime import datetime, timedelta
import json
from typing import Dict, List, Optional
import warnings
warnings.filterwarnings('ignore')

# إضافة المسار للبحث عن الوحدات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    from matplotlib.figure import Figure
    import matplotlib.dates as mdates
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

try:
    import pandas as pd
    import numpy as np
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

try:
    from intelligent_trading_system_v2 import IntelligentTradingSystemV2
    TRADING_SYSTEM_AVAILABLE = True
except ImportError:
    TRADING_SYSTEM_AVAILABLE = False

class IntelligentTradingGUI:
    """
    واجهة المستخدم الرسومية المتطورة للنظام الذكي
    """
    
    def __init__(self):
        """
        تهيئة الواجهة الرسومية
        """
        self.root = tk.Tk()
        self.root.title("🚀 نظام التداول الذكي المتطور - الإصدار الثاني")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        
        # تطبيق الثيم الداكن
        self.setup_dark_theme()
        
        # متغيرات النظام
        self.trading_system = None
        self.is_trading = False
        self.is_connected = False
        self.update_thread = None
        self.chart_data = []
        self.trades_data = []
        self.logs_data = []

        # إعدادات التداول - البدء بالوضع الحقيقي
        self.demo_mode = False  # False = تداول فعلي، True = محاكاة
        self.real_positions = {}  # الصفقات الفعلية من MT5
        self.mt5_connected = False  # حالة الاتصال الفعلي بـ MT5
        self.force_real_trading = True  # إجبار التداول الحقيقي
        
        # متغيرات الواجهة
        self.status_vars = {
            'connection': tk.StringVar(value="❌ غير متصل"),
            'trading': tk.StringVar(value="⏸️ متوقف"),
            'balance': tk.StringVar(value="$0.00"),
            'equity': tk.StringVar(value="$0.00"),
            'profit_today': tk.StringVar(value="$0.00"),
            'symbol': tk.StringVar(value="EURUSD"),
            'last_update': tk.StringVar(value="--:--:--")
        }
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تهيئة النظام
        self.initialize_system()
        
        # بدء تحديث البيانات
        self.start_updates()
    
    def setup_dark_theme(self):
        """
        إعداد الثيم الداكن
        """
        # ألوان الثيم الداكن
        self.colors = {
            'bg_primary': '#1e1e1e',      # خلفية رئيسية
            'bg_secondary': '#2d2d2d',    # خلفية ثانوية
            'bg_tertiary': '#3d3d3d',     # خلفية ثالثية
            'text_primary': '#ffffff',     # نص رئيسي
            'text_secondary': '#cccccc',   # نص ثانوي
            'accent_green': '#00ff88',     # أخضر للربح
            'accent_red': '#ff4444',       # أحمر للخسارة
            'accent_blue': '#4488ff',      # أزرق للمعلومات
            'accent_yellow': '#ffaa00',    # أصفر للتحذير
            'border': '#555555'            # حدود
        }
        
        # تطبيق الألوان على النافذة الرئيسية
        self.root.configure(bg=self.colors['bg_primary'])
        
        # إعداد أنماط ttk
        style = ttk.Style()
        style.theme_use('clam')
        
        # تخصيص الأنماط
        style.configure('Dark.TFrame', background=self.colors['bg_secondary'])
        style.configure('Dark.TLabel', background=self.colors['bg_secondary'], 
                       foreground=self.colors['text_primary'])
        style.configure('Dark.TButton', background=self.colors['bg_tertiary'],
                       foreground=self.colors['text_primary'])
        style.configure('Success.TButton', background=self.colors['accent_green'],
                       foreground='black')
        style.configure('Danger.TButton', background=self.colors['accent_red'],
                       foreground='white')
        style.configure('Info.TButton', background=self.colors['accent_blue'],
                       foreground='white')
    
    def create_widgets(self):
        """
        إنشاء عناصر الواجهة
        """
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, style='Dark.TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # شريط الحالة العلوي
        self.create_status_bar(main_frame)
        
        # الإطار الأوسط (الرسوم البيانية والتحكم)
        middle_frame = ttk.Frame(main_frame, style='Dark.TFrame')
        middle_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # الجانب الأيسر (الرسم البياني)
        self.create_chart_section(middle_frame)
        
        # الجانب الأيمن (التحكم والمعلومات)
        self.create_control_section(middle_frame)
        
        # الإطار السفلي (الصفقات والسجلات)
        self.create_bottom_section(main_frame)
    
    def create_status_bar(self, parent):
        """
        إنشاء شريط الحالة العلوي
        """
        status_frame = ttk.Frame(parent, style='Dark.TFrame')
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        # العنوان
        title_label = ttk.Label(status_frame, text="🚀 نظام التداول الذكي المتطور", 
                               font=('Arial', 16, 'bold'), style='Dark.TLabel')
        title_label.pack(side=tk.LEFT)
        
        # معلومات الحالة
        status_info_frame = ttk.Frame(status_frame, style='Dark.TFrame')
        status_info_frame.pack(side=tk.RIGHT)
        
        # حالة الاتصال
        connection_label = ttk.Label(status_info_frame, textvariable=self.status_vars['connection'],
                                   font=('Arial', 10, 'bold'), style='Dark.TLabel')
        connection_label.pack(side=tk.LEFT, padx=(0, 20))
        
        # حالة التداول
        trading_label = ttk.Label(status_info_frame, textvariable=self.status_vars['trading'],
                                font=('Arial', 10, 'bold'), style='Dark.TLabel')
        trading_label.pack(side=tk.LEFT, padx=(0, 20))
        
        # آخر تحديث
        update_label = ttk.Label(status_info_frame, textvariable=self.status_vars['last_update'],
                               font=('Arial', 9), style='Dark.TLabel')
        update_label.pack(side=tk.LEFT)
    
    def create_chart_section(self, parent):
        """
        إنشاء قسم الرسم البياني
        """
        chart_frame = ttk.Frame(parent, style='Dark.TFrame')
        chart_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # عنوان الرسم البياني
        chart_title = ttk.Label(chart_frame, text="📈 الرسم البياني المباشر", 
                               font=('Arial', 12, 'bold'), style='Dark.TLabel')
        chart_title.pack(pady=(0, 10))
        
        if MATPLOTLIB_AVAILABLE:
            # إنشاء الرسم البياني
            self.fig = Figure(figsize=(8, 6), dpi=100, facecolor=self.colors['bg_secondary'])
            self.ax = self.fig.add_subplot(111, facecolor=self.colors['bg_tertiary'])
            
            # تخصيص الرسم البياني
            self.ax.tick_params(colors=self.colors['text_primary'])
            self.ax.spines['bottom'].set_color(self.colors['border'])
            self.ax.spines['top'].set_color(self.colors['border'])
            self.ax.spines['right'].set_color(self.colors['border'])
            self.ax.spines['left'].set_color(self.colors['border'])
            
            # إضافة الرسم البياني للواجهة
            self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
            self.canvas.draw()
            self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
            # شريط أدوات الرسم البياني
            chart_toolbar_frame = ttk.Frame(chart_frame, style='Dark.TFrame')
            chart_toolbar_frame.pack(fill=tk.X, pady=(5, 0))
            
            # أزرار الإطارات الزمنية
            timeframes = ['M15', 'H1', 'H4', 'D1']
            for tf in timeframes:
                btn = ttk.Button(chart_toolbar_frame, text=tf, width=6,
                               command=lambda t=tf: self.change_timeframe(t))
                btn.pack(side=tk.LEFT, padx=(0, 5))
        else:
            # رسالة في حالة عدم توفر matplotlib
            no_chart_label = ttk.Label(chart_frame, 
                                     text="📊 الرسم البياني غير متوفر\nيرجى تثبيت matplotlib",
                                     font=('Arial', 12), style='Dark.TLabel')
            no_chart_label.pack(expand=True)
    
    def create_control_section(self, parent):
        """
        إنشاء قسم التحكم والمعلومات
        """
        control_frame = ttk.Frame(parent, style='Dark.TFrame')
        control_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        control_frame.configure(width=350)
        
        # معلومات الحساب
        self.create_account_info(control_frame)
        
        # أزرار التحكم
        self.create_control_buttons(control_frame)
        
        # تقرير المخاطر
        self.create_risk_report(control_frame)
        
        # الإعدادات السريعة
        self.create_quick_settings(control_frame)
    
    def create_account_info(self, parent):
        """
        إنشاء قسم معلومات الحساب
        """
        account_frame = ttk.LabelFrame(parent, text="💰 معلومات الحساب", style='Dark.TFrame')
        account_frame.pack(fill=tk.X, pady=(0, 10))
        
        # الرصيد
        balance_frame = ttk.Frame(account_frame, style='Dark.TFrame')
        balance_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(balance_frame, text="الرصيد:", style='Dark.TLabel').pack(side=tk.LEFT)
        balance_label = ttk.Label(balance_frame, textvariable=self.status_vars['balance'],
                                font=('Arial', 12, 'bold'), style='Dark.TLabel')
        balance_label.pack(side=tk.RIGHT)
        
        # الأسهم
        equity_frame = ttk.Frame(account_frame, style='Dark.TFrame')
        equity_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(equity_frame, text="الأسهم:", style='Dark.TLabel').pack(side=tk.LEFT)
        equity_label = ttk.Label(equity_frame, textvariable=self.status_vars['equity'],
                               font=('Arial', 12, 'bold'), style='Dark.TLabel')
        equity_label.pack(side=tk.RIGHT)
        
        # الربح اليومي
        profit_frame = ttk.Frame(account_frame, style='Dark.TFrame')
        profit_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(profit_frame, text="الربح اليوم:", style='Dark.TLabel').pack(side=tk.LEFT)
        profit_label = ttk.Label(profit_frame, textvariable=self.status_vars['profit_today'],
                               font=('Arial', 12, 'bold'), style='Dark.TLabel')
        profit_label.pack(side=tk.RIGHT)
        
        # الرمز
        symbol_frame = ttk.Frame(account_frame, style='Dark.TFrame')
        symbol_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(symbol_frame, text="الرمز:", style='Dark.TLabel').pack(side=tk.LEFT)
        symbol_label = ttk.Label(symbol_frame, textvariable=self.status_vars['symbol'],
                               font=('Arial', 12, 'bold'), style='Dark.TLabel')
        symbol_label.pack(side=tk.RIGHT)
    
    def create_control_buttons(self, parent):
        """
        إنشاء أزرار التحكم
        """
        control_frame = ttk.LabelFrame(parent, text="🎮 التحكم", style='Dark.TFrame')
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # زر التبديل بين الأوضاع
        self.mode_btn = ttk.Button(control_frame, text="🔥 وضع فعلي",
                                 command=self.toggle_mode, style='Warning.TButton')
        self.mode_btn.pack(fill=tk.X, padx=10, pady=5)

        # زر الاتصال
        self.connect_btn = ttk.Button(control_frame, text="🔌 اتصال",
                                    command=self.toggle_connection, style='Info.TButton')
        self.connect_btn.pack(fill=tk.X, padx=10, pady=5)

        # زر بدء/إيقاف التداول
        self.trading_btn = ttk.Button(control_frame, text="▶️ بدء التداول",
                                    command=self.toggle_trading, style='Success.TButton')
        self.trading_btn.pack(fill=tk.X, padx=10, pady=5)
        
        # زر دورة واحدة
        single_cycle_btn = ttk.Button(control_frame, text="🔄 دورة واحدة", 
                                    command=self.run_single_cycle)
        single_cycle_btn.pack(fill=tk.X, padx=10, pady=5)
        
        # زر الاختبار التاريخي
        backtest_btn = ttk.Button(control_frame, text="🧪 اختبار تاريخي", 
                                command=self.run_backtest)
        backtest_btn.pack(fill=tk.X, padx=10, pady=5)
        
        # زر الإعدادات
        settings_btn = ttk.Button(control_frame, text="⚙️ الإعدادات", 
                                command=self.open_settings)
        settings_btn.pack(fill=tk.X, padx=10, pady=5)
    
    def create_risk_report(self, parent):
        """
        إنشاء تقرير المخاطر
        """
        risk_frame = ttk.LabelFrame(parent, text="📊 تقرير المخاطر", style='Dark.TFrame')
        risk_frame.pack(fill=tk.X, pady=(0, 10))
        
        # المخاطر اليومية
        daily_frame = ttk.Frame(risk_frame, style='Dark.TFrame')
        daily_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(daily_frame, text="يومية:", style='Dark.TLabel').pack(side=tk.LEFT)
        self.daily_risk_var = tk.StringVar(value="0.0% / 5.0%")
        ttk.Label(daily_frame, textvariable=self.daily_risk_var, style='Dark.TLabel').pack(side=tk.RIGHT)
        
        # شريط المخاطر اليومية
        self.daily_progress = ttk.Progressbar(risk_frame, length=300, mode='determinate')
        self.daily_progress.pack(fill=tk.X, padx=10, pady=(0, 5))
        
        # المخاطر الأسبوعية
        weekly_frame = ttk.Frame(risk_frame, style='Dark.TFrame')
        weekly_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(weekly_frame, text="أسبوعية:", style='Dark.TLabel').pack(side=tk.LEFT)
        self.weekly_risk_var = tk.StringVar(value="0.0% / 15.0%")
        ttk.Label(weekly_frame, textvariable=self.weekly_risk_var, style='Dark.TLabel').pack(side=tk.RIGHT)
        
        # شريط المخاطر الأسبوعية
        self.weekly_progress = ttk.Progressbar(risk_frame, length=300, mode='determinate')
        self.weekly_progress.pack(fill=tk.X, padx=10, pady=(0, 5))
        
        # المخاطر الشهرية
        monthly_frame = ttk.Frame(risk_frame, style='Dark.TFrame')
        monthly_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(monthly_frame, text="شهرية:", style='Dark.TLabel').pack(side=tk.LEFT)
        self.monthly_risk_var = tk.StringVar(value="0.0% / 30.0%")
        ttk.Label(monthly_frame, textvariable=self.monthly_risk_var, style='Dark.TLabel').pack(side=tk.RIGHT)
        
        # شريط المخاطر الشهرية
        self.monthly_progress = ttk.Progressbar(risk_frame, length=300, mode='determinate')
        self.monthly_progress.pack(fill=tk.X, padx=10, pady=(0, 5))
    
    def create_quick_settings(self, parent):
        """
        إنشاء الإعدادات السريعة
        """
        settings_frame = ttk.LabelFrame(parent, text="⚡ إعدادات سريعة", style='Dark.TFrame')
        settings_frame.pack(fill=tk.X, pady=(0, 10))
        
        # مستوى الثقة
        confidence_frame = ttk.Frame(settings_frame, style='Dark.TFrame')
        confidence_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(confidence_frame, text="الثقة الدنيا:", style='Dark.TLabel').pack(side=tk.LEFT)
        self.confidence_var = tk.DoubleVar(value=65.0)
        confidence_scale = ttk.Scale(confidence_frame, from_=50, to=90, 
                                   variable=self.confidence_var, orient=tk.HORIZONTAL)
        confidence_scale.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
        
        # عرض قيمة الثقة
        confidence_value_label = ttk.Label(settings_frame, text="65%", style='Dark.TLabel')
        confidence_value_label.pack(pady=(0, 5))
        
        # ربط التحديث
        def update_confidence_label(*args):
            confidence_value_label.config(text=f"{self.confidence_var.get():.0f}%")
        self.confidence_var.trace('w', update_confidence_label)
        
        # المخاطر لكل صفقة
        risk_frame = ttk.Frame(settings_frame, style='Dark.TFrame')
        risk_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(risk_frame, text="مخاطر الصفقة:", style='Dark.TLabel').pack(side=tk.LEFT)
        self.risk_var = tk.DoubleVar(value=2.0)
        risk_scale = ttk.Scale(risk_frame, from_=1, to=5, 
                             variable=self.risk_var, orient=tk.HORIZONTAL)
        risk_scale.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
        
        # عرض قيمة المخاطر
        risk_value_label = ttk.Label(settings_frame, text="2.0%", style='Dark.TLabel')
        risk_value_label.pack(pady=(0, 5))
        
        # ربط التحديث
        def update_risk_label(*args):
            risk_value_label.config(text=f"{self.risk_var.get():.1f}%")
        self.risk_var.trace('w', update_risk_label)

    def create_bottom_section(self, parent):
        """
        إنشاء القسم السفلي (الصفقات والسجلات)
        """
        bottom_frame = ttk.Frame(parent, style='Dark.TFrame')
        bottom_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # إنشاء Notebook للتبويبات
        notebook = ttk.Notebook(bottom_frame)
        notebook.pack(fill=tk.BOTH, expand=True)

        # تبويب الصفقات المفتوحة
        self.create_open_trades_tab(notebook)

        # تبويب تاريخ الصفقات
        self.create_trades_history_tab(notebook)

        # تبويب السجلات
        self.create_logs_tab(notebook)

        # تبويب الإحصائيات
        self.create_statistics_tab(notebook)

    def create_open_trades_tab(self, notebook):
        """
        إنشاء تبويب الصفقات المفتوحة
        """
        trades_frame = ttk.Frame(notebook, style='Dark.TFrame')
        notebook.add(trades_frame, text="📋 الصفقات المفتوحة")

        # جدول الصفقات المفتوحة
        columns = ('الرمز', 'النوع', 'الحجم', 'سعر الفتح', 'السعر الحالي', 'الربح/الخسارة', 'الوقت')
        self.open_trades_tree = ttk.Treeview(trades_frame, columns=columns, show='headings', height=8)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.open_trades_tree.heading(col, text=col)
            self.open_trades_tree.column(col, width=120, anchor='center')

        # شريط التمرير
        trades_scrollbar = ttk.Scrollbar(trades_frame, orient=tk.VERTICAL, command=self.open_trades_tree.yview)
        self.open_trades_tree.configure(yscrollcommand=trades_scrollbar.set)

        # تخطيط الجدول
        self.open_trades_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        trades_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # أزرار التحكم في الصفقات
        trades_control_frame = ttk.Frame(trades_frame, style='Dark.TFrame')
        trades_control_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))

        close_all_btn = ttk.Button(trades_control_frame, text="❌ إغلاق الكل",
                                 command=self.close_all_trades, style='Danger.TButton')
        close_all_btn.pack(side=tk.LEFT, padx=(0, 5))

        refresh_trades_btn = ttk.Button(trades_control_frame, text="🔄 تحديث",
                                      command=self.refresh_trades)
        refresh_trades_btn.pack(side=tk.LEFT)

    def create_trades_history_tab(self, notebook):
        """
        إنشاء تبويب تاريخ الصفقات
        """
        history_frame = ttk.Frame(notebook, style='Dark.TFrame')
        notebook.add(history_frame, text="📈 تاريخ الصفقات")

        # جدول تاريخ الصفقات
        history_columns = ('الوقت', 'الرمز', 'النوع', 'الحجم', 'سعر الفتح', 'سعر الإغلاق', 'الربح/الخسارة', 'المدة')
        self.history_tree = ttk.Treeview(history_frame, columns=history_columns, show='headings', height=8)

        # تعيين عناوين الأعمدة
        for col in history_columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=100, anchor='center')

        # شريط التمرير
        history_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=history_scrollbar.set)

        # تخطيط الجدول
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # أزرار التحكم في التاريخ
        history_control_frame = ttk.Frame(history_frame, style='Dark.TFrame')
        history_control_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))

        export_btn = ttk.Button(history_control_frame, text="📊 تصدير",
                              command=self.export_history)
        export_btn.pack(side=tk.LEFT, padx=(0, 5))

        clear_history_btn = ttk.Button(history_control_frame, text="🗑️ مسح",
                                     command=self.clear_history)
        clear_history_btn.pack(side=tk.LEFT)

    def create_logs_tab(self, notebook):
        """
        إنشاء تبويب السجلات
        """
        logs_frame = ttk.Frame(notebook, style='Dark.TFrame')
        notebook.add(logs_frame, text="📝 السجلات")

        # منطقة النص للسجلات
        self.logs_text = scrolledtext.ScrolledText(logs_frame, height=10,
                                                  bg=self.colors['bg_tertiary'],
                                                  fg=self.colors['text_primary'],
                                                  insertbackground=self.colors['text_primary'])
        self.logs_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # أزرار التحكم في السجلات
        logs_control_frame = ttk.Frame(logs_frame, style='Dark.TFrame')
        logs_control_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))

        clear_logs_btn = ttk.Button(logs_control_frame, text="🗑️ مسح السجلات",
                                  command=self.clear_logs)
        clear_logs_btn.pack(side=tk.LEFT, padx=(0, 5))

        save_logs_btn = ttk.Button(logs_control_frame, text="💾 حفظ السجلات",
                                 command=self.save_logs)
        save_logs_btn.pack(side=tk.LEFT)

        # مستوى السجلات
        ttk.Label(logs_control_frame, text="مستوى السجل:", style='Dark.TLabel').pack(side=tk.RIGHT, padx=(20, 5))
        self.log_level_var = tk.StringVar(value="INFO")
        log_level_combo = ttk.Combobox(logs_control_frame, textvariable=self.log_level_var,
                                     values=['DEBUG', 'INFO', 'WARNING', 'ERROR'], width=10)
        log_level_combo.pack(side=tk.RIGHT)

    def create_statistics_tab(self, notebook):
        """
        إنشاء تبويب الإحصائيات
        """
        stats_frame = ttk.Frame(notebook, style='Dark.TFrame')
        notebook.add(stats_frame, text="📊 الإحصائيات")

        # إطار الإحصائيات الرئيسية
        main_stats_frame = ttk.LabelFrame(stats_frame, text="📈 الإحصائيات الرئيسية", style='Dark.TFrame')
        main_stats_frame.pack(fill=tk.X, padx=10, pady=10)

        # إنشاء شبكة للإحصائيات
        stats_grid = ttk.Frame(main_stats_frame, style='Dark.TFrame')
        stats_grid.pack(fill=tk.X, padx=10, pady=10)

        # الإحصائيات
        self.stats_vars = {
            'total_trades': tk.StringVar(value="0"),
            'winning_trades': tk.StringVar(value="0"),
            'losing_trades': tk.StringVar(value="0"),
            'win_rate': tk.StringVar(value="0.0%"),
            'total_profit': tk.StringVar(value="$0.00"),
            'avg_profit': tk.StringVar(value="$0.00"),
            'avg_loss': tk.StringVar(value="$0.00"),
            'max_drawdown': tk.StringVar(value="0.0%"),
            'profit_factor': tk.StringVar(value="0.00"),
            'sharpe_ratio': tk.StringVar(value="0.00")
        }

        # عرض الإحصائيات في شبكة
        stats_labels = [
            ("إجمالي الصفقات:", 'total_trades'),
            ("الصفقات الرابحة:", 'winning_trades'),
            ("الصفقات الخاسرة:", 'losing_trades'),
            ("معدل الفوز:", 'win_rate'),
            ("إجمالي الربح:", 'total_profit'),
            ("متوسط الربح:", 'avg_profit'),
            ("متوسط الخسارة:", 'avg_loss'),
            ("أقصى انخفاض:", 'max_drawdown'),
            ("عامل الربح:", 'profit_factor'),
            ("نسبة شارب:", 'sharpe_ratio')
        ]

        for i, (label_text, var_name) in enumerate(stats_labels):
            row = i // 2
            col = i % 2

            stat_frame = ttk.Frame(stats_grid, style='Dark.TFrame')
            stat_frame.grid(row=row, column=col, sticky='ew', padx=10, pady=5)

            ttk.Label(stat_frame, text=label_text, style='Dark.TLabel').pack(side=tk.LEFT)
            ttk.Label(stat_frame, textvariable=self.stats_vars[var_name],
                     font=('Arial', 10, 'bold'), style='Dark.TLabel').pack(side=tk.RIGHT)

        # تكوين الأعمدة
        stats_grid.columnconfigure(0, weight=1)
        stats_grid.columnconfigure(1, weight=1)

        # إطار الرسوم البيانية للإحصائيات
        if MATPLOTLIB_AVAILABLE:
            charts_frame = ttk.LabelFrame(stats_frame, text="📊 الرسوم البيانية", style='Dark.TFrame')
            charts_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # رسم بياني للأرباح والخسائر
            self.stats_fig = Figure(figsize=(10, 4), dpi=100, facecolor=self.colors['bg_secondary'])

            # رسم بياني للأرباح اليومية
            self.profit_ax = self.stats_fig.add_subplot(121, facecolor=self.colors['bg_tertiary'])
            self.profit_ax.set_title("الأرباح اليومية", color=self.colors['text_primary'])
            self.profit_ax.tick_params(colors=self.colors['text_primary'])

            # رسم بياني لتوزيع الصفقات
            self.trades_ax = self.stats_fig.add_subplot(122, facecolor=self.colors['bg_tertiary'])
            self.trades_ax.set_title("توزيع الصفقات", color=self.colors['text_primary'])
            self.trades_ax.tick_params(colors=self.colors['text_primary'])

            # إضافة الرسوم البيانية للواجهة
            self.stats_canvas = FigureCanvasTkAgg(self.stats_fig, charts_frame)
            self.stats_canvas.draw()
            self.stats_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def initialize_system(self):
        """
        تهيئة النظام التجاري
        """
        try:
            if TRADING_SYSTEM_AVAILABLE:
                # تمرير وضع التداول للنظام
                self.trading_system = IntelligentTradingSystemV2(demo_mode=self.demo_mode)
                self.log_message("✅ تم تهيئة النظام التجاري بنجاح")

                # محاولة الاتصال الفعلي بـ MT5 إذا كان في الوضع الفعلي
                if not self.demo_mode:
                    self.connect_to_mt5()
            else:
                self.log_message("❌ النظام التجاري غير متوفر")
                self.demo_mode = True
                messagebox.showerror("خطأ", "النظام التجاري غير متوفر\nتأكد من وجود ملف intelligent_trading_system_v2.py")
        except Exception as e:
            self.log_message(f"❌ خطأ في تهيئة النظام: {e}")
            messagebox.showerror("خطأ", f"فشل في تهيئة النظام:\n{e}")

    def connect_to_mt5(self):
        """
        الاتصال الفعلي بـ MetaTrader 5 - إجباري للتداول الحقيقي
        """
        try:
            import MetaTrader5 as mt5

            self.log_message("🔥 محاولة الاتصال الحقيقي بـ MetaTrader 5...")

            # محاولة الاتصال
            if not mt5.initialize():
                error = mt5.last_error()
                self.log_message(f"❌ فشل في تهيئة MT5: {error}")
                self.log_message("💡 تأكد من:")
                self.log_message("   - تشغيل MetaTrader 5")
                self.log_message("   - تسجيل الدخول إلى حسابك")
                self.log_message("   - تفعيل التداول الآلي")
                return False

            # تسجيل الدخول بالبيانات من config.ini
            import configparser
            config = configparser.ConfigParser()
            config.read('config.ini')

            login = int(config.get('MT5_CONNECTION', 'login', fallback='0'))
            password = config.get('MT5_CONNECTION', 'password', fallback='')
            server = config.get('MT5_CONNECTION', 'server', fallback='')

            self.log_message(f"🔐 محاولة تسجيل الدخول - حساب: {login}, خادم: {server}")

            if not mt5.login(login, password, server):
                error = mt5.last_error()
                self.log_message(f"❌ فشل في تسجيل الدخول: {error}")
                self.log_message("💡 تحقق من بيانات الحساب في config.ini")
                return False

            # التحقق من معلومات الحساب
            account_info = mt5.account_info()
            if account_info is None:
                self.log_message("❌ لا يمكن الحصول على معلومات الحساب")
                return False

            # التحقق من إذن التداول
            if not account_info.trade_allowed:
                self.log_message("❌ التداول غير مسموح في هذا الحساب")
                self.log_message("💡 تحقق من إعدادات الحساب في MT5")
                return False

            # التحقق من التداول الآلي
            terminal_info = mt5.terminal_info()
            if not terminal_info or not terminal_info.trade_allowed:
                self.log_message("❌ التداول الآلي معطل في الطرفية")
                self.log_message("💡 فعّل التداول الآلي: Tools → Options → Expert Advisors")
                return False

            # نجح الاتصال - إجبار الوضع الحقيقي
            self.mt5_connected = True
            self.demo_mode = False  # إجبار التداول الحقيقي

            self.log_message("🎉 تم الاتصال الحقيقي بـ MT5 بنجاح!")
            self.log_message(f"📊 حساب: {account_info.login}")
            self.log_message(f"💰 رصيد: ${account_info.balance:.2f}")
            self.log_message(f"📈 أسهم: ${account_info.equity:.2f}")
            self.log_message(f"🏢 شركة: {account_info.company}")
            self.log_message("🔥 وضع التداول الحقيقي مفعل!")

            # اختبار أوضاع التنفيذ
            symbol = config.get('TRADING_SETTINGS', 'symbol', fallback='EURUSD')
            self.test_filling_modes_gui(symbol)

            return True

        except ImportError:
            self.log_message("❌ مكتبة MetaTrader5 غير مثبتة")
            self.log_message("💡 قم بتثبيتها: pip install MetaTrader5")
            return False
        except Exception as e:
            self.log_message(f"❌ خطأ في الاتصال بـ MT5: {e}")
            return False

    def test_filling_modes_gui(self, symbol):
        """
        اختبار أوضاع التنفيذ للرمز في الواجهة
        """
        try:
            import MetaTrader5 as mt5

            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                self.log_message(f"⚠️ لا يمكن الحصول على معلومات الرمز {symbol}")
                return

            filling_mode = symbol_info.filling_mode
            self.log_message(f"🔍 فحص أوضاع التنفيذ للرمز {symbol}:")
            self.log_message(f"   قيمة وضع التنفيذ: {filling_mode}")

            # اختبار كل وضع
            modes = [
                (1, "FOK (Fill or Kill)", "ORDER_FILLING_FOK"),
                (2, "IOC (Immediate or Cancel)", "ORDER_FILLING_IOC"),
                (4, "RETURN", "ORDER_FILLING_RETURN")
            ]

            supported_count = 0
            for bit, name, const_name in modes:
                if filling_mode & bit:
                    self.log_message(f"   ✅ {name} مدعوم")
                    supported_count += 1
                else:
                    self.log_message(f"   ❌ {name} غير مدعوم")

            if supported_count > 0:
                self.log_message(f"✅ تم العثور على {supported_count} وضع تنفيذ مدعوم")
            else:
                self.log_message("⚠️ لم يتم العثور على أوضاع تنفيذ مدعومة")

        except Exception as e:
            self.log_message(f"❌ خطأ في اختبار أوضاع التنفيذ: {e}")

    def start_updates(self):
        """
        بدء تحديث البيانات
        """
        self.update_data()
        # جدولة التحديث التالي
        self.root.after(5000, self.start_updates)  # كل 5 ثوان

    def update_data(self):
        """
        تحديث البيانات في الواجهة
        """
        try:
            # تحديث وقت آخر تحديث
            self.status_vars['last_update'].set(datetime.now().strftime("%H:%M:%S"))

            if self.trading_system and self.is_connected:
                # تحديث معلومات الحساب الفعلية إذا كان متصل
                if self.mt5_connected:
                    self.update_real_account_info()
                else:
                    # الحصول على حالة النظام التجريبي
                    status = self.trading_system.get_system_status()

                    # تحديث معلومات الحساب التجريبي
                    if 'account_info' in status:
                        account = status['account_info']
                        self.status_vars['balance'].set(f"${account['balance']:.2f}")
                        self.status_vars['equity'].set(f"${account['equity']:.2f}")

                        # حساب الربح اليومي (مبسط)
                        profit_today = account['equity'] - account['balance']
                        self.status_vars['profit_today'].set(f"${profit_today:.2f}")

                    # تحديث تقرير المخاطر
                    if 'risk_report' in status:
                        self.update_risk_report(status['risk_report'])

                # تحديث الرسم البياني
                self.update_chart()

                # تحديث الصفقات
                self.update_trades()

                # تحديث الإحصائيات
                self.update_statistics()

        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث البيانات: {e}")

    def update_risk_report(self, risk_report):
        """
        تحديث تقرير المخاطر
        """
        try:
            # المخاطر اليومية
            daily_used = risk_report['daily_risk']['used']
            daily_limit = risk_report['daily_risk']['limit']
            self.daily_risk_var.set(f"{daily_used:.1f}% / {daily_limit:.1f}%")
            self.daily_progress['value'] = (daily_used / daily_limit) * 100

            # المخاطر الأسبوعية
            weekly_used = risk_report['weekly_risk']['used']
            weekly_limit = risk_report['weekly_risk']['limit']
            self.weekly_risk_var.set(f"{weekly_used:.1f}% / {weekly_limit:.1f}%")
            self.weekly_progress['value'] = (weekly_used / weekly_limit) * 100

            # المخاطر الشهرية
            monthly_used = risk_report['monthly_risk']['used']
            monthly_limit = risk_report['monthly_risk']['limit']
            self.monthly_risk_var.set(f"{monthly_used:.1f}% / {monthly_limit:.1f}%")
            self.monthly_progress['value'] = (monthly_used / monthly_limit) * 100

        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث تقرير المخاطر: {e}")

    def update_chart(self):
        """
        تحديث الرسم البياني
        """
        try:
            if not MATPLOTLIB_AVAILABLE or not hasattr(self, 'ax'):
                return

            # محاكاة بيانات الرسم البياني
            if not self.chart_data:
                # إنشاء بيانات تجريبية
                base_price = 1.0850
                times = [datetime.now() - timedelta(minutes=i*15) for i in range(100, 0, -1)]
                prices = [base_price + (i * 0.0001) + (np.random.random() * 0.002 - 0.001) for i in range(100)]
                self.chart_data = list(zip(times, prices))

            # إضافة نقطة جديدة
            new_time = datetime.now()
            last_price = self.chart_data[-1][1] if self.chart_data else 1.0850
            new_price = last_price + (np.random.random() * 0.002 - 0.001)
            self.chart_data.append((new_time, new_price))

            # الاحتفاظ بآخر 100 نقطة فقط
            if len(self.chart_data) > 100:
                self.chart_data = self.chart_data[-100:]

            # رسم البيانات
            self.ax.clear()
            times, prices = zip(*self.chart_data)
            self.ax.plot(times, prices, color=self.colors['accent_blue'], linewidth=2)

            # تنسيق الرسم البياني
            self.ax.set_title(f"{self.status_vars['symbol'].get()} - Live Chart",
                            color=self.colors['text_primary'])
            self.ax.tick_params(colors=self.colors['text_primary'])
            self.ax.grid(True, alpha=0.3)

            # تنسيق محور الوقت
            self.ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
            self.ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=30))

            # تحديث الرسم
            self.canvas.draw()

        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث الرسم البياني: {e}")

    def update_trades(self):
        """
        تحديث جدول الصفقات
        """
        try:
            # مسح البيانات الحالية
            for item in self.open_trades_tree.get_children():
                self.open_trades_tree.delete(item)

            # محاكاة صفقات مفتوحة
            if self.is_trading and len(self.trades_data) < 3:
                # إضافة صفقة تجريبية
                trade = {
                    'symbol': 'EURUSD',
                    'type': 'شراء' if np.random.random() > 0.5 else 'بيع',
                    'volume': 0.01,
                    'open_price': 1.0850 + (np.random.random() * 0.01 - 0.005),
                    'current_price': 1.0850 + (np.random.random() * 0.01 - 0.005),
                    'profit': np.random.random() * 20 - 10,
                    'time': datetime.now().strftime("%H:%M:%S")
                }
                self.trades_data.append(trade)

            # عرض الصفقات
            for trade in self.trades_data:
                profit_color = 'green' if trade['profit'] >= 0 else 'red'
                self.open_trades_tree.insert('', 'end', values=(
                    trade['symbol'],
                    trade['type'],
                    f"{trade['volume']:.2f}",
                    f"{trade['open_price']:.5f}",
                    f"{trade['current_price']:.5f}",
                    f"${trade['profit']:.2f}",
                    trade['time']
                ), tags=(profit_color,))

            # تلوين الصفوف
            self.open_trades_tree.tag_configure('green', foreground=self.colors['accent_green'])
            self.open_trades_tree.tag_configure('red', foreground=self.colors['accent_red'])

        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث الصفقات: {e}")

    def update_statistics(self):
        """
        تحديث الإحصائيات
        """
        try:
            # محاكاة إحصائيات
            total_trades = len(self.trades_data) + np.random.randint(10, 50)
            winning_trades = int(total_trades * 0.65)
            losing_trades = total_trades - winning_trades
            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

            # تحديث المتغيرات
            self.stats_vars['total_trades'].set(str(total_trades))
            self.stats_vars['winning_trades'].set(str(winning_trades))
            self.stats_vars['losing_trades'].set(str(losing_trades))
            self.stats_vars['win_rate'].set(f"{win_rate:.1f}%")
            self.stats_vars['total_profit'].set(f"${np.random.uniform(100, 500):.2f}")
            self.stats_vars['avg_profit'].set(f"${np.random.uniform(5, 15):.2f}")
            self.stats_vars['avg_loss'].set(f"${np.random.uniform(-10, -3):.2f}")
            self.stats_vars['max_drawdown'].set(f"{np.random.uniform(2, 8):.1f}%")
            self.stats_vars['profit_factor'].set(f"{np.random.uniform(1.2, 2.5):.2f}")
            self.stats_vars['sharpe_ratio'].set(f"{np.random.uniform(0.8, 2.0):.2f}")

        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث الإحصائيات: {e}")

    def log_message(self, message):
        """
        إضافة رسالة للسجلات
        """
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"

            if hasattr(self, 'logs_text'):
                self.logs_text.insert(tk.END, log_entry)
                self.logs_text.see(tk.END)

                # الاحتفاظ بآخر 1000 سطر فقط
                lines = self.logs_text.get("1.0", tk.END).split('\n')
                if len(lines) > 1000:
                    self.logs_text.delete("1.0", f"{len(lines)-1000}.0")

            # إضافة للسجلات المحفوظة
            self.logs_data.append(log_entry)
            if len(self.logs_data) > 1000:
                self.logs_data = self.logs_data[-1000:]

        except Exception as e:
            print(f"خطأ في السجلات: {e}")

    # وظائف التحكم
    def toggle_connection(self):
        """
        تبديل حالة الاتصال
        """
        try:
            if not self.is_connected:
                # محاولة الاتصال
                self.log_message("🔄 محاولة الاتصال بـ MetaTrader 5...")

                # محاولة الاتصال
                if self.trading_system:
                    success = self.trading_system.connect_to_mt5()
                    if success:
                        self.is_connected = True
                        self.mt5_connected = True
                        self.demo_mode = False  # إجبار التداول الحقيقي
                        self.status_vars['connection'].set("✅ متصل (فعلي)")
                        self.connect_btn.config(text="🔌 قطع الاتصال", style='Danger.TButton')
                        self.log_message("🔥 تم الاتصال الحقيقي بنجاح!")

                        # إجبار النظام التجاري على الوضع الحقيقي
                        self.trading_system.set_trading_mode(False)
                        self.trading_system.demo_mode = False

                        # تحديث معلومات الحساب الفعلية
                        self.update_real_account_info()

                        # تحديث زر الوضع
                        self.mode_btn.config(text="🧪 وضع تجريبي", style='Info.TButton')
                    else:
                        self.log_message("❌ فشل في الاتصال بـ MT5")
                        messagebox.showerror("خطأ في الاتصال",
                                           "لا يمكن الاتصال بـ MetaTrader 5\n\n"
                                           "تأكد من:\n"
                                           "• تشغيل MetaTrader 5\n"
                                           "• تسجيل الدخول إلى حسابك\n"
                                           "• تفعيل التداول الآلي\n"
                                           "• صحة بيانات config.ini")
                        return
                else:
                    self.log_message("❌ النظام التجاري غير متوفر")
                    messagebox.showerror("خطأ", "النظام التجاري غير متوفر")
                    return
            else:
                # قطع الاتصال
                self.log_message("🔄 قطع الاتصال...")
                if self.trading_system:
                    self.trading_system.disconnect()

                self.is_connected = False
                self.is_trading = False
                self.mt5_connected = False
                # لا نغير demo_mode عند قطع الاتصال
                self.status_vars['connection'].set("❌ غير متصل")
                self.status_vars['trading'].set("⏸️ متوقف")
                self.connect_btn.config(text="🔌 اتصال", style='Info.TButton')
                self.trading_btn.config(text="▶️ بدء التداول", style='Success.TButton')
                self.log_message("✅ تم قطع الاتصال")

        except Exception as e:
            self.log_message(f"❌ خطأ في تبديل الاتصال: {e}")
            messagebox.showerror("خطأ", f"خطأ في الاتصال:\n{e}")

    def update_real_account_info(self):
        """
        تحديث معلومات الحساب الفعلية من MT5
        """
        try:
            if not self.mt5_connected:
                return

            import MetaTrader5 as mt5

            # الحصول على معلومات الحساب
            account_info = mt5.account_info()
            if account_info:
                self.status_vars['balance'].set(f"${account_info.balance:.2f}")
                self.status_vars['equity'].set(f"${account_info.equity:.2f}")

                # حساب الربح اليومي (تقريبي)
                daily_profit = account_info.equity - account_info.balance
                self.status_vars['profit_today'].set(f"${daily_profit:.2f}")

            # الحصول على الصفقات المفتوحة
            positions = mt5.positions_get()
            if positions:
                self.real_positions = {}
                for pos in positions:
                    self.real_positions[pos.ticket] = {
                        'symbol': pos.symbol,
                        'type': 'buy' if pos.type == 0 else 'sell',
                        'volume': pos.volume,
                        'price_open': pos.price_open,
                        'price_current': pos.price_current,
                        'profit': pos.profit,
                        'time': pos.time
                    }

                self.log_message(f"📊 تم تحديث معلومات الحساب - صفقات مفتوحة: {len(positions)}")

        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث معلومات الحساب: {e}")

    def toggle_mode(self):
        """
        التبديل بين وضع المحاكاة والتداول الفعلي
        """
        try:
            if self.is_trading:
                messagebox.showwarning("تحذير", "لا يمكن تغيير الوضع أثناء التداول\nيرجى إيقاف التداول أولاً")
                return

            if self.demo_mode:
                # التبديل إلى الوضع الفعلي
                if self.connect_to_mt5():
                    self.demo_mode = False
                    self.mt5_connected = True
                    self.mode_btn.config(text="🧪 وضع تجريبي", style='Info.TButton')
                    self.log_message("🔥 تم التبديل إلى الوضع الفعلي")

                    # تحديث وضع النظام التجاري
                    if self.trading_system:
                        self.trading_system.set_trading_mode(False)

                    messagebox.showinfo("تم", "تم التبديل إلى وضع التداول الفعلي\nسيتم تنفيذ الصفقات على حسابك الحقيقي!")
                else:
                    messagebox.showerror("خطأ", "فشل في الاتصال بـ MT5\nسيتم البقاء في الوضع التجريبي")
            else:
                # التبديل إلى وضع المحاكاة
                self.demo_mode = True
                self.mt5_connected = False
                self.mode_btn.config(text="🔥 وضع فعلي", style='Warning.TButton')
                self.log_message("🧪 تم التبديل إلى الوضع التجريبي")

                # تحديث وضع النظام التجاري
                if self.trading_system:
                    self.trading_system.set_trading_mode(True)

                messagebox.showinfo("تم", "تم التبديل إلى وضع المحاكاة\nلن يتم تنفيذ صفقات حقيقية")

                # قطع الاتصال بـ MT5 إذا كان متصل
                try:
                    import MetaTrader5 as mt5
                    mt5.shutdown()
                except:
                    pass

        except Exception as e:
            self.log_message(f"❌ خطأ في تبديل الوضع: {e}")
            messagebox.showerror("خطأ", f"خطأ في تبديل الوضع:\n{e}")

    def toggle_trading(self):
        """
        تبديل حالة التداول
        """
        try:
            if not self.is_connected:
                messagebox.showwarning("تحذير", "يجب الاتصال أولاً قبل بدء التداول")
                return

            if not self.is_trading:
                # بدء التداول
                if self.mt5_connected:
                    self.log_message("🚀 بدء التداول الفعلي...")
                    self.status_vars['trading'].set("🔥 فعلي")
                else:
                    self.log_message("🚀 بدء التداول التجريبي...")
                    self.status_vars['trading'].set("🧪 تجريبي")

                self.is_trading = True
                self.trading_btn.config(text="⏸️ إيقاف التداول", style='Danger.TButton')

                # بدء التداول في خيط منفصل
                self.start_trading_thread()

            else:
                # إيقاف التداول
                self.log_message("⏸️ إيقاف التداول...")
                self.is_trading = False
                self.status_vars['trading'].set("⏸️ متوقف")
                self.trading_btn.config(text="▶️ بدء التداول", style='Success.TButton')

                if self.trading_system:
                    self.trading_system.stop_trading()

        except Exception as e:
            self.log_message(f"❌ خطأ في تبديل التداول: {e}")
            messagebox.showerror("خطأ", f"خطأ في التداول:\n{e}")

    def start_trading_thread(self):
        """
        بدء التداول في خيط منفصل
        """
        def trading_worker():
            try:
                while self.is_trading and self.is_connected:
                    if self.trading_system:
                        # تحديث معلومات الحساب إذا كان متصل فعلياً
                        if self.mt5_connected:
                            self.update_real_account_info()

                        # تشغيل دورة تداول واحدة
                        success = self.trading_system.run_intelligent_trading_cycle()

                        if success:
                            if self.mt5_connected:
                                self.log_message("✅ تمت دورة التداول الفعلي بنجاح")
                            else:
                                self.log_message("✅ تمت دورة التداول التجريبي بنجاح")
                        else:
                            self.log_message("⚠️ لم يتم تنفيذ صفقة في هذه الدورة")

                    # انتظار 5 دقائق قبل الدورة التالية
                    for _ in range(300):  # 300 ثانية = 5 دقائق
                        if not self.is_trading:
                            break
                        time.sleep(1)

            except Exception as e:
                self.log_message(f"❌ خطأ في خيط التداول: {e}")

        # بدء الخيط
        self.update_thread = threading.Thread(target=trading_worker, daemon=True)
        self.update_thread.start()

    def run_single_cycle(self):
        """
        تشغيل دورة تداول واحدة
        """
        try:
            if not self.is_connected:
                messagebox.showwarning("تحذير", "يجب الاتصال أولاً")
                return

            self.log_message("🔄 تشغيل دورة تداول واحدة...")

            def single_cycle_worker():
                try:
                    if self.trading_system:
                        success = self.trading_system.run_intelligent_trading_cycle()
                        if success:
                            self.log_message("✅ تمت الدورة بنجاح")
                        else:
                            self.log_message("⚠️ لم يتم تنفيذ صفقة")
                except Exception as e:
                    self.log_message(f"❌ خطأ في الدورة: {e}")

            # تشغيل في خيط منفصل
            thread = threading.Thread(target=single_cycle_worker, daemon=True)
            thread.start()

        except Exception as e:
            self.log_message(f"❌ خطأ في تشغيل الدورة: {e}")

    def run_backtest(self):
        """
        تشغيل اختبار تاريخي
        """
        try:
            if not self.trading_system:
                messagebox.showerror("خطأ", "النظام التجاري غير متوفر")
                return

            # نافذة اختيار فترة الاختبار
            dialog = tk.Toplevel(self.root)
            dialog.title("اختبار تاريخي")
            dialog.geometry("300x150")
            dialog.configure(bg=self.colors['bg_primary'])
            dialog.transient(self.root)
            dialog.grab_set()

            ttk.Label(dialog, text="عدد الأيام للاختبار:", style='Dark.TLabel').pack(pady=10)

            days_var = tk.IntVar(value=30)
            days_spinbox = tk.Spinbox(dialog, from_=7, to=365, textvariable=days_var, width=10)
            days_spinbox.pack(pady=5)

            def start_backtest():
                days = days_var.get()
                dialog.destroy()

                self.log_message(f"🧪 بدء اختبار تاريخي لآخر {days} يوم...")

                def backtest_worker():
                    try:
                        result = self.trading_system.run_backtest(days=days)
                        if result:
                            self.log_message(f"📈 نتائج الاختبار التاريخي:")
                            self.log_message(f"   العائد: {result['total_return']:.2f}%")
                            self.log_message(f"   الصفقات: {result['total_trades']}")
                            self.log_message(f"   معدل الفوز: {result['statistics']['win_rate']:.2f}%")
                        else:
                            self.log_message("❌ فشل في الاختبار التاريخي")
                    except Exception as e:
                        self.log_message(f"❌ خطأ في الاختبار التاريخي: {e}")

                thread = threading.Thread(target=backtest_worker, daemon=True)
                thread.start()

            ttk.Button(dialog, text="بدء الاختبار", command=start_backtest).pack(pady=10)
            ttk.Button(dialog, text="إلغاء", command=dialog.destroy).pack()

        except Exception as e:
            self.log_message(f"❌ خطأ في نافذة الاختبار التاريخي: {e}")

    def open_settings(self):
        """
        فتح نافذة الإعدادات
        """
        try:
            settings_window = tk.Toplevel(self.root)
            settings_window.title("⚙️ الإعدادات")
            settings_window.geometry("500x600")
            settings_window.configure(bg=self.colors['bg_primary'])
            settings_window.transient(self.root)

            # إنشاء نافذة الإعدادات (مبسطة)
            ttk.Label(settings_window, text="⚙️ إعدادات النظام",
                     font=('Arial', 14, 'bold'), style='Dark.TLabel').pack(pady=10)

            # إعدادات التداول
            trading_frame = ttk.LabelFrame(settings_window, text="إعدادات التداول", style='Dark.TFrame')
            trading_frame.pack(fill=tk.X, padx=20, pady=10)

            # الرمز
            symbol_frame = ttk.Frame(trading_frame, style='Dark.TFrame')
            symbol_frame.pack(fill=tk.X, padx=10, pady=5)
            ttk.Label(symbol_frame, text="الرمز:", style='Dark.TLabel').pack(side=tk.LEFT)
            symbol_combo = ttk.Combobox(symbol_frame, values=['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD'])
            symbol_combo.set(self.status_vars['symbol'].get())
            symbol_combo.pack(side=tk.RIGHT)

            # حجم الصفقة
            lot_frame = ttk.Frame(trading_frame, style='Dark.TFrame')
            lot_frame.pack(fill=tk.X, padx=10, pady=5)
            ttk.Label(lot_frame, text="حجم الصفقة:", style='Dark.TLabel').pack(side=tk.LEFT)
            lot_var = tk.DoubleVar(value=0.01)
            lot_spinbox = tk.Spinbox(lot_frame, from_=0.01, to=1.0, increment=0.01,
                                   textvariable=lot_var, width=10)
            lot_spinbox.pack(side=tk.RIGHT)

            # أزرار الحفظ والإلغاء
            buttons_frame = ttk.Frame(settings_window, style='Dark.TFrame')
            buttons_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=20, pady=20)

            def save_settings():
                try:
                    self.status_vars['symbol'].set(symbol_combo.get())
                    self.log_message("✅ تم حفظ الإعدادات")
                    settings_window.destroy()
                except Exception as e:
                    self.log_message(f"❌ خطأ في حفظ الإعدادات: {e}")

            ttk.Button(buttons_frame, text="💾 حفظ", command=save_settings,
                      style='Success.TButton').pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(buttons_frame, text="❌ إلغاء", command=settings_window.destroy,
                      style='Danger.TButton').pack(side=tk.LEFT)

        except Exception as e:
            self.log_message(f"❌ خطأ في فتح الإعدادات: {e}")

    # وظائف إضافية
    def change_timeframe(self, timeframe):
        """
        تغيير الإطار الزمني للرسم البياني
        """
        self.log_message(f"📊 تغيير الإطار الزمني إلى {timeframe}")
        # هنا يمكن إضافة منطق تغيير الإطار الزمني

    def close_all_trades(self):
        """
        إغلاق جميع الصفقات
        """
        try:
            if not self.trades_data:
                messagebox.showinfo("معلومات", "لا توجد صفقات مفتوحة")
                return

            result = messagebox.askyesno("تأكيد", "هل تريد إغلاق جميع الصفقات المفتوحة؟")
            if result:
                self.trades_data.clear()
                self.log_message("❌ تم إغلاق جميع الصفقات")
                self.update_trades()

        except Exception as e:
            self.log_message(f"❌ خطأ في إغلاق الصفقات: {e}")

    def refresh_trades(self):
        """
        تحديث جدول الصفقات
        """
        self.log_message("🔄 تحديث الصفقات...")
        self.update_trades()

    def export_history(self):
        """
        تصدير تاريخ الصفقات
        """
        try:
            from tkinter import filedialog

            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )

            if filename:
                # محاكاة تصدير البيانات
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("الوقت,الرمز,النوع,الحجم,سعر الفتح,سعر الإغلاق,الربح/الخسارة,المدة\n")
                    # إضافة بيانات تجريبية
                    for i in range(10):
                        f.write(f"2024-01-{i+1:02d} 10:00,EURUSD,شراء,0.01,1.0850,1.0860,+10.00,30 دقيقة\n")

                self.log_message(f"📊 تم تصدير التاريخ إلى {filename}")
                messagebox.showinfo("نجح", f"تم تصدير التاريخ إلى:\n{filename}")

        except Exception as e:
            self.log_message(f"❌ خطأ في التصدير: {e}")
            messagebox.showerror("خطأ", f"فشل في التصدير:\n{e}")

    def clear_history(self):
        """
        مسح تاريخ الصفقات
        """
        try:
            result = messagebox.askyesno("تأكيد", "هل تريد مسح تاريخ الصفقات؟")
            if result:
                # مسح البيانات من الجدول
                for item in self.history_tree.get_children():
                    self.history_tree.delete(item)

                self.log_message("🗑️ تم مسح تاريخ الصفقات")

        except Exception as e:
            self.log_message(f"❌ خطأ في مسح التاريخ: {e}")

    def clear_logs(self):
        """
        مسح السجلات
        """
        try:
            result = messagebox.askyesno("تأكيد", "هل تريد مسح السجلات؟")
            if result:
                self.logs_text.delete("1.0", tk.END)
                self.logs_data.clear()
                self.log_message("🗑️ تم مسح السجلات")

        except Exception as e:
            self.log_message(f"❌ خطأ في مسح السجلات: {e}")

    def save_logs(self):
        """
        حفظ السجلات
        """
        try:
            from tkinter import filedialog

            filename = filedialog.asksaveasfilename(
                defaultextension=".log",
                filetypes=[("Log files", "*.log"), ("Text files", "*.txt"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.logs_text.get("1.0", tk.END))

                self.log_message(f"💾 تم حفظ السجلات إلى {filename}")
                messagebox.showinfo("نجح", f"تم حفظ السجلات إلى:\n{filename}")

        except Exception as e:
            self.log_message(f"❌ خطأ في حفظ السجلات: {e}")
            messagebox.showerror("خطأ", f"فشل في حفظ السجلات:\n{e}")

    def on_closing(self):
        """
        عند إغلاق النافذة
        """
        try:
            if self.is_trading:
                result = messagebox.askyesno("تأكيد", "التداول نشط. هل تريد الإغلاق؟")
                if not result:
                    return

            # إيقاف التداول
            self.is_trading = False

            # قطع الاتصال
            if self.trading_system and self.is_connected:
                self.trading_system.disconnect()

            # إغلاق النافذة
            self.root.destroy()

        except Exception as e:
            print(f"خطأ في الإغلاق: {e}")
            self.root.destroy()

    def run(self):
        """
        تشغيل الواجهة الرسومية
        """
        try:
            # ربط حدث الإغلاق
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

            # رسالة ترحيب
            self.log_message("🚀 مرحباً بك في النظام الذكي المتطور للتداول")
            self.log_message("💡 اضغط 'اتصال' للبدء")

            # تشغيل الحلقة الرئيسية
            self.root.mainloop()

        except Exception as e:
            print(f"خطأ في تشغيل الواجهة: {e}")
            messagebox.showerror("خطأ", f"خطأ في تشغيل الواجهة:\n{e}")

def main():
    """
    الدالة الرئيسية لتشغيل الواجهة الرسومية
    """
    try:
        print("🚀 بدء تشغيل الواجهة الرسومية للنظام الذكي المتطور...")

        # فحص المتطلبات
        missing_modules = []

        if not PANDAS_AVAILABLE:
            missing_modules.append("pandas numpy")

        if not TRADING_SYSTEM_AVAILABLE:
            missing_modules.append("intelligent_trading_system_v2")

        if missing_modules:
            print(f"⚠️ مكتبات مفقودة: {', '.join(missing_modules)}")
            print("💡 تثبيت: pip install " + " ".join(missing_modules))

        # إنشاء وتشغيل الواجهة
        app = IntelligentTradingGUI()
        app.run()

    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        try:
            import tkinter.messagebox as mb
            mb.showerror("خطأ", f"فشل في تشغيل التطبيق:\n{e}")
        except:
            pass

if __name__ == "__main__":
    main()
