-- إصلاح مشاكل قاعدة البيانات الأونلاين
-- Fix Online Database Issues

USE PharmacyNetworkOnline;
GO

PRINT '========================================';
PRINT 'إصلاح مشاكل قاعدة البيانات الأونلاين';
PRINT '========================================';

-- ===================================
-- 1. إصلاح مشكلة الجدولين للأدوية
-- ===================================

-- حذف الجدول القديم network_medicines إذا كان فارغاً
IF EXISTS (SELECT * FROM sysobjects WHERE name='network_medicines' AND xtype='U')
BEGIN
    DECLARE @count INT;
    SELECT @count = COUNT(*) FROM network_medicines;
    
    IF @count = 0
    BEGIN
        -- حذف المراجع أولاً
        IF EXISTS (SELECT * FROM sysobjects WHERE name='inter_pharmacy_orders' AND xtype='U')
        BEGIN
            -- حذف جميع القيود المرجعية
            DECLARE @sql NVARCHAR(MAX) = '';
            SELECT @sql = @sql + 'ALTER TABLE ' + OBJECT_SCHEMA_NAME(parent_object_id) + '.' + OBJECT_NAME(parent_object_id) + 
                         ' DROP CONSTRAINT ' + name + '; '
            FROM sys.foreign_keys 
            WHERE referenced_object_id = OBJECT_ID('network_medicines');
            
            IF @sql != ''
                EXEC sp_executesql @sql;
            
            PRINT 'تم حذف المراجع من inter_pharmacy_orders';
        END
        
        DROP TABLE network_medicines;
        PRINT 'تم حذف الجدول القديم network_medicines';
    END
    ELSE
    BEGIN
        -- نسخ البيانات إلى الجدول الجديد
        INSERT INTO networkmedicines (
            pharmacyId, localMedicineId, medicineName, genericName, brandName,
            manufacturer, category, dosageForm, strength, availableQuantity,
            unitPrice, wholesalePrice, expiryDate, isAvailableForSale,
            requiresPrescription, createdAt, updatedAt
        )
        SELECT 
            pharmacyId, localMedicineId, medicineName, genericName, brandName,
            manufacturer, category, dosageForm, strength, availableQuantity,
            unitPrice, wholesalePrice, expiryDate, isAvailableForSale,
            requiresPrescription, createdAt, updatedAt
        FROM network_medicines;
        
        PRINT 'تم نسخ البيانات من network_medicines إلى networkmedicines';
        
        -- حذف المراجع
        DECLARE @sql2 NVARCHAR(MAX) = '';
        SELECT @sql2 = @sql2 + 'ALTER TABLE ' + OBJECT_SCHEMA_NAME(parent_object_id) + '.' + OBJECT_NAME(parent_object_id) + 
                     ' DROP CONSTRAINT ' + name + '; '
        FROM sys.foreign_keys 
        WHERE referenced_object_id = OBJECT_ID('network_medicines');
        
        IF @sql2 != ''
            EXEC sp_executesql @sql2;
        
        DROP TABLE network_medicines;
        PRINT 'تم حذف الجدول القديم network_medicines';
    END
END

-- ===================================
-- 2. إعادة إنشاء جدول inter_pharmacy_orders بالمراجع الصحيحة
-- ===================================

IF EXISTS (SELECT * FROM sysobjects WHERE name='inter_pharmacy_orders' AND xtype='U')
BEGIN
    DROP TABLE inter_pharmacy_orders;
    PRINT 'تم حذف جدول inter_pharmacy_orders القديم';
END

CREATE TABLE inter_pharmacy_orders (
    id INT IDENTITY(1,1) PRIMARY KEY,
    orderNumber VARCHAR(50) UNIQUE NOT NULL,
    buyerPharmacyId INT NOT NULL,
    sellerPharmacyId INT NOT NULL,
    medicineId INT NOT NULL,
    requestedQuantity INT NOT NULL,
    unitPrice DECIMAL(10, 2) NOT NULL,
    totalAmount DECIMAL(10, 2) NOT NULL,
    orderStatus VARCHAR(50) DEFAULT 'Pending',
    orderDate DATETIME DEFAULT GETDATE(),
    responseDate DATETIME NULL,
    deliveryDate DATETIME NULL,
    notes NVARCHAR(1000) NULL,
    FOREIGN KEY (buyerPharmacyId) REFERENCES pharmacies(id),
    FOREIGN KEY (sellerPharmacyId) REFERENCES pharmacies(id),
    FOREIGN KEY (medicineId) REFERENCES networkmedicines(id)
);

PRINT 'تم إعادة إنشاء جدول inter_pharmacy_orders بالمراجع الصحيحة';

-- ===================================
-- 3. إنشاء الإجراءات المخزنة المطلوبة
-- ===================================

-- إجراء تسجيل صيدلية جديدة
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_RegisterPharmacy')
BEGIN
    DROP PROCEDURE sp_RegisterPharmacy;
END

CREATE PROCEDURE sp_RegisterPharmacy
    @pharmacyName NVARCHAR(250),
    @ownerName NVARCHAR(250),
    @licenseNumber VARCHAR(100),
    @address NVARCHAR(500),
    @city NVARCHAR(100),
    @region NVARCHAR(100),
    @phone VARCHAR(20),
    @email VARCHAR(250),
    @adminName NVARCHAR(250),
    @adminUsername VARCHAR(250),
    @adminPassword VARCHAR(500)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @pharmacyId INT;
    DECLARE @pharmacyCode VARCHAR(20);
    DECLARE @errorMessage NVARCHAR(500);

    -- التحقق من عدم وجود رقم الترخيص مسبقاً
    IF EXISTS (SELECT 1 FROM pharmacies WHERE licenseNumber = @licenseNumber)
    BEGIN
        SELECT 0 as PharmacyId, '' as PharmacyCode, 'رقم الترخيص موجود مسبقاً' as Message;
        RETURN;
    END

    -- التحقق من عدم وجود اسم المستخدم مسبقاً
    IF EXISTS (SELECT 1 FROM network_users WHERE username = @adminUsername)
    BEGIN
        SELECT 0 as PharmacyId, '' as PharmacyCode, 'اسم المستخدم موجود مسبقاً' as Message;
        RETURN;
    END

    -- إنشاء كود فريد للصيدلية
    DECLARE @nextNumber INT;
    SELECT @nextNumber = ISNULL(MAX(CAST(SUBSTRING(pharmacyCode, 3, 3) AS INT)), 0) + 1 FROM pharmacies;
    SET @pharmacyCode = 'PH' + RIGHT('000' + CAST(@nextNumber AS VARCHAR), 3);

    BEGIN TRANSACTION;

    BEGIN TRY
        -- إدراج الصيدلية
        INSERT INTO pharmacies (
            pharmacyCode, pharmacyName, ownerName, licenseNumber, 
            address, city, region, phone, email, isActive, 
            registrationDate, lastOnline, subscriptionType
        )
        VALUES (
            @pharmacyCode, @pharmacyName, @ownerName, @licenseNumber,
            @address, @city, @region, @phone, @email, 1,
            GETDATE(), GETDATE(), 'Basic'
        );

        SET @pharmacyId = SCOPE_IDENTITY();

        -- إدراج المدير
        INSERT INTO network_users (
            pharmacyId, userRole, name, username, passwordHash, 
            email, isActive, createdAt, updatedAt
        )
        VALUES (
            @pharmacyId, 'Admin', @adminName, @adminUsername, @adminPassword,
            @email, 1, GETDATE(), GETDATE()
        );

        COMMIT TRANSACTION;

        -- إرجاع النتيجة الناجحة
        SELECT @pharmacyId as PharmacyId, @pharmacyCode as PharmacyCode, 'تم تسجيل الصيدلية بنجاح' as Message;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        
        SET @errorMessage = ERROR_MESSAGE();
        SELECT 0 as PharmacyId, '' as PharmacyCode, 'خطأ في التسجيل: ' + @errorMessage as Message;
        
    END CATCH
END;
GO

PRINT 'تم إنشاء إجراء sp_RegisterPharmacy';

-- إجراء البحث عن الأدوية
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_SearchMedicines')
BEGIN
    DROP PROCEDURE sp_SearchMedicines;
END

CREATE PROCEDURE sp_SearchMedicines
    @searchTerm NVARCHAR(250) = '',
    @pharmacyId INT = NULL,
    @category NVARCHAR(100) = '',
    @minPrice DECIMAL(10,2) = 0,
    @maxPrice DECIMAL(10,2) = 999999,
    @pageNumber INT = 1,
    @pageSize INT = 20
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @offset INT = (@pageNumber - 1) * @pageSize;

    SELECT
        m.id,
        m.pharmacyId,
        p.pharmacyName,
        p.pharmacyCode,
        p.city,
        p.region,
        p.phone,
        m.medicineName,
        m.genericName,
        m.brandName,
        m.manufacturer,
        m.category,
        m.dosageForm,
        m.strength,
        m.availableQuantity,
        m.unitPrice,
        m.wholesalePrice,
        m.expiryDate,
        DATEDIFF(day, GETDATE(), m.expiryDate) as daysToExpiry
    FROM networkmedicines m
    INNER JOIN pharmacies p ON m.pharmacyId = p.id
    WHERE m.isAvailableForSale = 1
        AND m.availableQuantity > 0
        AND m.expiryDate > GETDATE()
        AND p.isActive = 1
        AND (@pharmacyId IS NULL OR m.pharmacyId != @pharmacyId)
        AND (@searchTerm = '' OR m.medicineName LIKE '%' + @searchTerm + '%'
             OR m.genericName LIKE '%' + @searchTerm + '%'
             OR m.brandName LIKE '%' + @searchTerm + '%'
             OR m.manufacturer LIKE '%' + @searchTerm + '%')
        AND (@category = '' OR m.category = @category)
        AND m.unitPrice BETWEEN @minPrice AND @maxPrice
    ORDER BY m.medicineName
    OFFSET @offset ROWS
    FETCH NEXT @pageSize ROWS ONLY;
END;
GO

PRINT 'تم إنشاء إجراء sp_SearchMedicines';

-- إجراء الحصول على الصيدليات النشطة
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetActivePharmacies')
BEGIN
    DROP PROCEDURE sp_GetActivePharmacies;
END

CREATE PROCEDURE sp_GetActivePharmacies
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        p.id,
        p.pharmacyCode,
        p.pharmacyName,
        p.ownerName,
        p.city,
        p.region,
        p.phone,
        p.email,
        p.lastOnline,
        p.subscriptionType,
        COUNT(DISTINCT u.id) as totalUsers,
        COUNT(DISTINCT m.id) as totalMedicines,
        0 as totalOrdersSent,
        0 as totalOrdersReceived,
        0 as averageRating
    FROM pharmacies p
    LEFT JOIN network_users u ON p.id = u.pharmacyId AND u.isActive = 1
    LEFT JOIN networkmedicines m ON p.id = m.pharmacyId AND m.isAvailableForSale = 1
    WHERE p.isActive = 1
    GROUP BY p.id, p.pharmacyCode, p.pharmacyName, p.ownerName, p.city, p.region, p.phone, p.email, p.lastOnline, p.subscriptionType
    ORDER BY p.pharmacyName;
END;
GO

PRINT 'تم إنشاء إجراء sp_GetActivePharmacies';

PRINT '========================================';
PRINT 'تم إصلاح جميع مشاكل قاعدة البيانات';
PRINT '========================================';

PRINT 'تم الانتهاء من الإصلاح بنجاح!';
