using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy_Management_System
{
    public partial class PharmacyNetworkLoginForm : Form
    {
        private UnifiedPharmacyFunction unifiedDb;
        private Button buttonConnect;
        private Label labelTitle;
        private Guna.UI2.WinForms.Guna2TextBox textBoxUsername;
        private Label labelStatus;
        private Button buttonTestConnection;
        private Guna.UI2.WinForms.Guna2TextBox textBoxPassword;

        public PharmacyNetworkLoginForm()
        {
            InitializeComponent();
            unifiedDb = new UnifiedPharmacyFunction();
            ApplyLanguage();
        }

        private void InitializeComponent()
        {
            this.buttonConnect = new System.Windows.Forms.Button();
            this.labelTitle = new System.Windows.Forms.Label();
            this.textBoxUsername = new Guna.UI2.WinForms.Guna2TextBox();
            this.textBoxPassword = new Guna.UI2.WinForms.Guna2TextBox();
            this.labelStatus = new System.Windows.Forms.Label();
            this.buttonTestConnection = new System.Windows.Forms.Button();
            this.SuspendLayout();
            // 
            // buttonConnect
            // 
            this.buttonConnect.BackColor = System.Drawing.SystemColors.GradientActiveCaption;
            this.buttonConnect.Cursor = System.Windows.Forms.Cursors.Hand;
            this.buttonConnect.FlatAppearance.BorderSize = 0;
            this.buttonConnect.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.buttonConnect.Font = new System.Drawing.Font("Segoe UI", 11.25F, ((System.Drawing.FontStyle)((System.Drawing.FontStyle.Bold | System.Drawing.FontStyle.Italic))), System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.buttonConnect.ForeColor = System.Drawing.Color.White;
            this.buttonConnect.Location = new System.Drawing.Point(246, 259);
            this.buttonConnect.Margin = new System.Windows.Forms.Padding(2);
            this.buttonConnect.Name = "buttonConnect";
            this.buttonConnect.Size = new System.Drawing.Size(150, 32);
            this.buttonConnect.TabIndex = 5;
            this.buttonConnect.Text = "Connect";
            this.buttonConnect.UseVisualStyleBackColor = false;
            this.buttonConnect.Click += new System.EventHandler(this.buttonConnect_Click);
            // 
            // labelTitle
            // 
            this.labelTitle.AutoSize = true;
            this.labelTitle.Font = new System.Drawing.Font("Segoe UI", 18F, System.Drawing.FontStyle.Bold);
            this.labelTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(123)))), ((int)(((byte)(255)))));
            this.labelTitle.Location = new System.Drawing.Point(66, 24);
            this.labelTitle.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.labelTitle.Name = "labelTitle";
            this.labelTitle.Size = new System.Drawing.Size(302, 32);
            this.labelTitle.TabIndex = 0;
            this.labelTitle.Text = "Pharmacy Network Login";
            this.labelTitle.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // textBoxUsername
            // 
            this.textBoxUsername.BorderRadius = 20;
            this.textBoxUsername.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.textBoxUsername.DefaultText = "";
            this.textBoxUsername.DisabledState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(208)))), ((int)(((byte)(208)))), ((int)(((byte)(208)))));
            this.textBoxUsername.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(226)))), ((int)(((byte)(226)))), ((int)(((byte)(226)))));
            this.textBoxUsername.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.textBoxUsername.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.textBoxUsername.FocusedState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.textBoxUsername.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.textBoxUsername.HoverState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.textBoxUsername.Location = new System.Drawing.Point(105, 117);
            this.textBoxUsername.Name = "textBoxUsername";
            this.textBoxUsername.PlaceholderText = "Username";
            this.textBoxUsername.SelectedText = "";
            this.textBoxUsername.Size = new System.Drawing.Size(200, 36);
            this.textBoxUsername.TabIndex = 8;
            this.textBoxUsername.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // textBoxPassword
            // 
            this.textBoxPassword.BorderRadius = 20;
            this.textBoxPassword.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.textBoxPassword.DefaultText = "";
            this.textBoxPassword.DisabledState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(208)))), ((int)(((byte)(208)))), ((int)(((byte)(208)))));
            this.textBoxPassword.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(226)))), ((int)(((byte)(226)))), ((int)(((byte)(226)))));
            this.textBoxPassword.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.textBoxPassword.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.textBoxPassword.FocusedState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.textBoxPassword.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.textBoxPassword.HoverState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.textBoxPassword.Location = new System.Drawing.Point(105, 198);
            this.textBoxPassword.Name = "textBoxPassword";
            this.textBoxPassword.PlaceholderText = "Password";
            this.textBoxPassword.SelectedText = "";
            this.textBoxPassword.Size = new System.Drawing.Size(200, 36);
            this.textBoxPassword.TabIndex = 9;
            this.textBoxPassword.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // labelStatus
            // 
            this.labelStatus.AutoSize = true;
            this.labelStatus.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.labelStatus.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(76)))), ((int)(((byte)(175)))), ((int)(((byte)(80)))));
            this.labelStatus.Location = new System.Drawing.Point(195, 408);
            this.labelStatus.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.labelStatus.Name = "labelStatus";
            this.labelStatus.Size = new System.Drawing.Size(99, 15);
            this.labelStatus.TabIndex = 7;
            this.labelStatus.Text = "Ready to connect";
            // 
            // buttonTestConnection
            // 
            this.buttonTestConnection.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(152)))), ((int)(((byte)(0)))));
            this.buttonTestConnection.Cursor = System.Windows.Forms.Cursors.Hand;
            this.buttonTestConnection.FlatAppearance.BorderSize = 0;
            this.buttonTestConnection.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.buttonTestConnection.Font = new System.Drawing.Font("Segoe UI", 11F, System.Drawing.FontStyle.Bold);
            this.buttonTestConnection.ForeColor = System.Drawing.Color.White;
            this.buttonTestConnection.Location = new System.Drawing.Point(11, 398);
            this.buttonTestConnection.Margin = new System.Windows.Forms.Padding(2);
            this.buttonTestConnection.Name = "buttonTestConnection";
            this.buttonTestConnection.Size = new System.Drawing.Size(150, 32);
            this.buttonTestConnection.TabIndex = 6;
            this.buttonTestConnection.Text = "Test Connection";
            this.buttonTestConnection.UseVisualStyleBackColor = false;
            this.buttonTestConnection.Click += new System.EventHandler(this.buttonTestConnection_Click);
            // 
            // PharmacyNetworkLoginForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.SystemColors.Control;
            this.ClientSize = new System.Drawing.Size(407, 441);
            this.Controls.Add(this.textBoxPassword);
            this.Controls.Add(this.textBoxUsername);
            this.Controls.Add(this.labelTitle);
            this.Controls.Add(this.buttonConnect);
            this.Controls.Add(this.buttonTestConnection);
            this.Controls.Add(this.labelStatus);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.Margin = new System.Windows.Forms.Padding(2);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "PharmacyNetworkLoginForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Connect to Pharmacy Network";
            this.Load += new System.EventHandler(this.PharmacyNetworkLoginForm_Load);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        private void ApplyLanguage()
        {
            // تطبيق اللغة المختارة
            labelTitle.Text = LanguageManager.GetText("Pharmacy Network Login");
            buttonConnect.Text = LanguageManager.GetText("Connect");
            buttonTestConnection.Text = LanguageManager.GetText("Test Connection");
            labelStatus.Text = LanguageManager.GetText("Ready to connect");
        }

        private void buttonConnect_Click(object sender, EventArgs e)
        {
            PerformLogin();
        }

        private void PerformLogin()
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(textBoxUsername.Text) || 
                    string.IsNullOrWhiteSpace(textBoxPassword.Text))
                {
                    labelStatus.Text = LanguageManager.GetText("Please fill all required fields");
                    labelStatus.ForeColor = Color.Red;
                    return;
                }

                labelStatus.Text = LanguageManager.GetText("Connecting...");
                labelStatus.ForeColor = Color.Blue;
                Application.DoEvents();

                // محاولة تسجيل الدخول
                DataSet loginResult = unifiedDb.ValidateLogin(textBoxUsername.Text, textBoxPassword.Text);

                if (loginResult.Tables.Count > 0 && loginResult.Tables[0].Rows.Count > 0)
                {
                    DataRow userRow = loginResult.Tables[0].Rows[0];
                    
                    // تسجيل جلسة الدخول
                    SessionManager.Login(
                        Convert.ToInt32(userRow["id"]),
                        userRow["username"].ToString(),
                        userRow["name"].ToString(),
                        userRow["userRole"].ToString(),
                        Convert.ToInt32(userRow["pharmacyId"]),
                        userRow["pharmacyName"].ToString(),
                        userRow["pharmacyCode"].ToString()
                    );

                    // تسجيل جلسة في قاعدة البيانات
                    unifiedDb.RecordLoginSession(
                        Convert.ToInt32(userRow["pharmacyId"]),
                        Convert.ToInt32(userRow["id"]),
                        userRow["username"].ToString(),
                        userRow["name"].ToString()
                    );

                    labelStatus.Text = LanguageManager.GetText("Connected successfully!");
                    labelStatus.ForeColor = Color.Green;

                    // فتح الواجهة المناسبة
                    string role = userRow["userRole"].ToString();
                    if (role == "Administrator")
                    {
                        Adminstrator adminForm = new Adminstrator(userRow["name"].ToString());
                        adminForm.Show();
                    }
                    else
                    {
                        Pharmacist pharmacistForm = new Pharmacist(userRow["name"].ToString(), userRow["userRole"].ToString());
                        pharmacistForm.Show();
                    }

                    this.Hide();
                }
                else
                {
                    labelStatus.Text = LanguageManager.GetText("Wrong Username or Password");
                    labelStatus.ForeColor = Color.Red;
                }
            }
            catch (Exception ex)
            {
                labelStatus.Text = "Connection failed: " + ex.Message;
                labelStatus.ForeColor = Color.Red;
            }
        }



        private void buttonTestConnection_Click(object sender, EventArgs e)
        {
            try
            {
                labelStatus.Text = LanguageManager.GetText("Testing connection...");
                labelStatus.ForeColor = Color.Blue;
                Application.DoEvents();

                // اختبار الاتصال بقاعدة البيانات
                DataSet testResult = unifiedDb.GetActivePharmacies();
                
                if (testResult.Tables.Count > 0)
                {
                    labelStatus.Text = LanguageManager.GetText("Connection test successful!");
                    labelStatus.ForeColor = Color.Green;
                }
                else
                {
                    labelStatus.Text = LanguageManager.GetText("Connection test failed!");
                    labelStatus.ForeColor = Color.Red;
                }
            }
            catch (Exception ex)
            {
                labelStatus.Text = "Connection test failed: " + ex.Message;
                labelStatus.ForeColor = Color.Red;
            }
        }

        private void PharmacyNetworkLoginForm_Load(object sender, EventArgs e)
        {

        }
    }
}
