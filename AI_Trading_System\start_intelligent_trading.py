#!/usr/bin/env python3
"""
Start Intelligent Trading System V2
تشغيل النظام الذكي المتطور بسهولة
"""

import sys
import os
import time
from datetime import datetime

# إضافة المسار للبحث عن الوحدات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def print_banner():
    """
    عرض شعار النظام
    """
    print("🚀" + "="*58 + "🚀")
    print("🚀" + " "*20 + "INTELLIGENT TRADING SYSTEM V2" + " "*8 + "🚀")
    print("🚀" + " "*15 + "نظام التداول الذكي المتطور - الإصدار الثاني" + " "*8 + "🚀")
    print("🚀" + "="*58 + "🚀")
    print()

def check_requirements():
    """
    فحص المتطلبات
    """
    print("🔍 فحص المتطلبات...")
    
    missing_modules = []
    
    try:
        import MetaTrader5 as mt5
        print("   ✅ MetaTrader5")
    except ImportError:
        missing_modules.append("MetaTrader5")
        print("   ❌ MetaTrader5")
    
    try:
        import pandas as pd
        import numpy as np
        print("   ✅ Pandas & Numpy")
    except ImportError:
        missing_modules.append("pandas numpy")
        print("   ❌ Pandas & Numpy")
    
    try:
        from sklearn.ensemble import RandomForestClassifier
        print("   ✅ Scikit-learn")
    except ImportError:
        missing_modules.append("scikit-learn")
        print("   ❌ Scikit-learn")
    
    # فحص الوحدات المطورة
    try:
        from intelligent_trading_system_v2 import IntelligentTradingSystemV2
        print("   ✅ النظام الذكي المتطور")
    except ImportError:
        print("   ❌ النظام الذكي المتطور")
        return False
    
    if missing_modules:
        print(f"\n⚠️ مكتبات مفقودة: {', '.join(missing_modules)}")
        print("💡 تثبيت: pip install " + " ".join(missing_modules))
        return False
    
    return True

def get_user_choice():
    """
    الحصول على اختيار المستخدم
    """
    print("\n📋 اختر نوع التشغيل:")
    print("1️⃣ اختبار سريع للنظام")
    print("2️⃣ تشغيل دورة تداول واحدة")
    print("3️⃣ تشغيل تداول مستمر (30 دقيقة)")
    print("4️⃣ تشغيل تداول مستمر (1 ساعة)")
    print("5️⃣ تشغيل تداول مستمر (مخصص)")
    print("6️⃣ اختبار تاريخي (7 أيام)")
    print("7️⃣ اختبار تاريخي (30 يوم)")
    print("8️⃣ عرض حالة النظام فقط")
    print("0️⃣ خروج")
    
    while True:
        try:
            choice = input("\n👆 اختر رقم (0-8): ").strip()
            if choice in ['0', '1', '2', '3', '4', '5', '6', '7', '8']:
                return choice
            else:
                print("❌ اختيار غير صحيح. جرب مرة أخرى.")
        except KeyboardInterrupt:
            return '0'

def run_quick_test(system):
    """
    تشغيل اختبار سريع
    """
    print("\n🧪 تشغيل اختبار سريع...")
    
    # فحص الاتصال
    if not system.connect_to_mt5():
        print("❌ فشل في الاتصال بـ MT5")
        return False
    
    # عرض حالة النظام
    status = system.get_system_status()
    print(f"✅ النظام متصل ويعمل")
    print(f"📊 الرمز: {status['symbol']}")
    
    if 'account_info' in status:
        account = status['account_info']
        print(f"💰 الرصيد: ${account['balance']:.2f}")
        print(f"🏢 الشركة: {account['company']}")
    
    print("✅ الاختبار السريع مكتمل")
    return True

def run_single_cycle(system):
    """
    تشغيل دورة واحدة
    """
    print("\n🔄 تشغيل دورة تداول واحدة...")
    
    if not system.connect_to_mt5():
        print("❌ فشل في الاتصال بـ MT5")
        return False
    
    success = system.run_intelligent_trading_cycle()
    
    if success:
        print("✅ تمت الدورة بنجاح")
    else:
        print("⚠️ لم يتم تنفيذ صفقة في هذه الدورة")
    
    return success

def run_continuous_trading(system, hours):
    """
    تشغيل تداول مستمر
    """
    print(f"\n🚀 تشغيل تداول مستمر لمدة {hours} ساعة...")
    print("⏹️ اضغط Ctrl+C للإيقاف")
    
    if not system.connect_to_mt5():
        print("❌ فشل في الاتصال بـ MT5")
        return False
    
    try:
        result = system.run_continuous_intelligent_trading(
            duration_hours=hours,
            analysis_interval=300  # 5 دقائق
        )
        
        if 'error' in result:
            print(f"❌ خطأ: {result['error']}")
            return False
        
        if 'stopped_by_user' in result:
            print("⏹️ تم الإيقاف بواسطة المستخدم")
            return True
        
        print(f"\n🏁 انتهاء التداول المستمر:")
        print(f"   الدورات: {result['total_cycles']}")
        print(f"   النجاح: {result['success_rate']:.1f}%")
        print(f"   المدة: {result['total_duration']/60:.1f} دقيقة")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التداول بواسطة المستخدم")
        system.stop_trading()
        return True

def run_backtest(system, days):
    """
    تشغيل اختبار تاريخي
    """
    print(f"\n🧪 تشغيل اختبار تاريخي لآخر {days} يوم...")
    
    if not system.connect_to_mt5():
        print("❌ فشل في الاتصال بـ MT5")
        return False
    
    result = system.run_backtest(days=days)
    
    if not result:
        print("❌ فشل في الاختبار التاريخي")
        return False
    
    print(f"\n📈 نتائج الاختبار التاريخي ({days} يوم):")
    print(f"   العائد الإجمالي: {result['total_return']:.2f}%")
    print(f"   إجمالي الصفقات: {result['total_trades']}")
    print(f"   معدل الفوز: {result['statistics']['win_rate']:.2f}%")
    print(f"   أقصى انخفاض: {result['statistics']['max_drawdown']:.2f}%")
    print(f"   نسبة شارب: {result['statistics']['sharpe_ratio']:.2f}")
    
    return True

def show_system_status(system):
    """
    عرض حالة النظام
    """
    print("\n📊 حالة النظام:")
    
    status = system.get_system_status()
    
    print(f"   الوقت: {status['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   الاتصال: {'✅ متصل' if status['is_connected'] else '❌ غير متصل'}")
    print(f"   التداول: {'🟢 نشط' if status['is_trading'] else '🔴 متوقف'}")
    print(f"   الرمز: {status['symbol']}")
    print(f"   الحد الأدنى للرصيد: ${status['min_balance']}")
    print(f"   المكونات المتقدمة: {'✅' if status['advanced_modules_available'] else '❌'}")
    print(f"   MT5: {'✅' if status['mt5_available'] else '❌'}")
    
    if 'account_info' in status:
        account = status['account_info']
        print(f"\n💰 معلومات الحساب:")
        print(f"   الحساب: {account['login']}")
        print(f"   الرصيد: ${account['balance']:.2f}")
        print(f"   الأسهم: ${account['equity']:.2f}")
        print(f"   الهامش: ${account['margin']:.2f}")
        print(f"   الهامش الحر: ${account['free_margin']:.2f}")
        print(f"   مستوى الهامش: {account['margin_level']:.2f}%")
        print(f"   الشركة: {account['company']}")
    
    if 'risk_report' in status:
        risk = status['risk_report']
        print(f"\n📊 تقرير المخاطر:")
        print(f"   حالة المخاطر: {risk['risk_status']}")
        print(f"   نوع الحساب: {risk['account_type']}")
        print(f"   المخاطر اليومية: {risk['daily_risk']['used']:.1f}% / {risk['daily_risk']['limit']:.1f}%")
        print(f"   المخاطر الأسبوعية: {risk['weekly_risk']['used']:.1f}% / {risk['weekly_risk']['limit']:.1f}%")
        print(f"   المخاطر الشهرية: {risk['monthly_risk']['used']:.1f}% / {risk['monthly_risk']['limit']:.1f}%")

def main():
    """
    الدالة الرئيسية
    """
    print_banner()
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ المتطلبات غير مكتملة. يرجى تثبيت المكتبات المفقودة.")
        input("اضغط Enter للخروج...")
        return
    
    print("✅ جميع المتطلبات متوفرة")
    
    # إنشاء النظام
    try:
        from intelligent_trading_system_v2 import IntelligentTradingSystemV2
        system = IntelligentTradingSystemV2()
        print("✅ تم إنشاء النظام الذكي بنجاح")
    except Exception as e:
        print(f"❌ خطأ في إنشاء النظام: {e}")
        input("اضغط Enter للخروج...")
        return
    
    # حلقة التشغيل الرئيسية
    while True:
        choice = get_user_choice()
        
        if choice == '0':
            print("👋 وداعاً!")
            break
        
        elif choice == '1':
            run_quick_test(system)
        
        elif choice == '2':
            run_single_cycle(system)
        
        elif choice == '3':
            run_continuous_trading(system, 0.5)  # 30 دقيقة
        
        elif choice == '4':
            run_continuous_trading(system, 1.0)  # ساعة واحدة
        
        elif choice == '5':
            try:
                hours = float(input("كم ساعة؟ "))
                if hours > 0:
                    run_continuous_trading(system, hours)
                else:
                    print("❌ يجب أن يكون الرقم أكبر من صفر")
            except ValueError:
                print("❌ رقم غير صحيح")
        
        elif choice == '6':
            run_backtest(system, 7)
        
        elif choice == '7':
            run_backtest(system, 30)
        
        elif choice == '8':
            show_system_status(system)
        
        print("\n" + "-"*50)
        input("اضغط Enter للمتابعة...")
    
    # تنظيف الموارد
    try:
        system.disconnect()
    except:
        pass

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء البرنامج")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
