using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Win32;

namespace Pharmacy_Management_System
{
    /// <summary>
    /// واجهة تسجيل الدخول الجديدة مع كود الصيدلية
    /// New Login Form with Pharmacy Code
    /// </summary>
    public partial class PharmacyCodeLoginForm : Form
    {
        private UnifiedPharmacyFunction unifiedDb;
        private Panel mainPanel;
        private Panel loginPanel;
        private Label titleLabel;
        private Label pharmacyCodeLabel;
        private TextBox pharmacyCodeTextBox;
        private Label usernameLabel;
        private TextBox usernameTextBox;
        private Label passwordLabel;
        private TextBox passwordTextBox;
        private Button loginButton;
        private Button exitButton;
        private Label statusLabel;
        private int selectedPharmacyId = 0;
        private string selectedPharmacyName = "";

        // مفتاح الريجستري لحفظ آخر كود صيدلية
        private const string REGISTRY_KEY = @"SOFTWARE\PharmacyManagementSystem\LoginSettings";

        public PharmacyCodeLoginForm()
        {
            InitializeComponent();
            unifiedDb = new UnifiedPharmacyFunction();
            SetupForm();
            LoadLastPharmacyCode(); // تحميل آخر كود صيدلية
        }

        private void SetupForm()
        {
            // Form properties
            this.Size = new Size(800, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.FromArgb(45, 45, 48);
            this.Text = "تسجيل الدخول - نظام إدارة الصيدلية";

            // Main panel
            mainPanel = new Panel
            {
                Size = new Size(800, 700),
                Location = new Point(0, 0),
                BackColor = Color.FromArgb(45, 45, 48)
            };
            this.Controls.Add(mainPanel);

            // Login panel
            loginPanel = new Panel
            {
                Size = new Size(450, 550),
                Location = new Point(175, 75),
                BackColor = Color.FromArgb(62, 62, 66),
                BorderStyle = BorderStyle.FixedSingle
            };
            mainPanel.Controls.Add(loginPanel);

            // Title
            titleLabel = new Label
            {
                Text = "نظام إدارة الصيدلية\nتسجيل الدخول بكود الصيدلية",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(350, 60),
                Location = new Point(50, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };
            loginPanel.Controls.Add(titleLabel);

            // Pharmacy Code label
            pharmacyCodeLabel = new Label
            {
                Text = "كود الصيدلية:",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(120, 25),
                Location = new Point(50, 120),
                TextAlign = ContentAlignment.MiddleLeft
            };
            loginPanel.Controls.Add(pharmacyCodeLabel);

            // Pharmacy Code textbox
            pharmacyCodeTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 12),
                Size = new Size(350, 35),
                Location = new Point(50, 150),
                BackColor = Color.FromArgb(69, 73, 74),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Text = "" // سيتم تحميل آخر كود تلقائياً
            };
            loginPanel.Controls.Add(pharmacyCodeTextBox);

            // Username label
            usernameLabel = new Label
            {
                Text = "اسم المستخدم:",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(120, 25),
                Location = new Point(50, 200),
                TextAlign = ContentAlignment.MiddleLeft
            };
            loginPanel.Controls.Add(usernameLabel);

            // Username textbox
            usernameTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 12),
                Size = new Size(350, 35),
                Location = new Point(50, 230),
                BackColor = Color.FromArgb(69, 73, 74),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Text = "admin" // Default for testing
            };
            loginPanel.Controls.Add(usernameTextBox);

            // Password label
            passwordLabel = new Label
            {
                Text = "كلمة المرور:",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(120, 25),
                Location = new Point(50, 280),
                TextAlign = ContentAlignment.MiddleLeft
            };
            loginPanel.Controls.Add(passwordLabel);

            // Password textbox
            passwordTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 12),
                Size = new Size(350, 35),
                Location = new Point(50, 310),
                BackColor = Color.FromArgb(69, 73, 74),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                UseSystemPasswordChar = true,
                Text = "admin123" // Default for testing
            };
            loginPanel.Controls.Add(passwordTextBox);

            // Login button
            loginButton = new Button
            {
                Text = "تسجيل الدخول",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                Size = new Size(160, 45),
                Location = new Point(50, 370),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            loginButton.FlatAppearance.BorderSize = 0;
            loginButton.Click += LoginButton_Click;
            loginPanel.Controls.Add(loginButton);

            // Exit button
            exitButton = new Button
            {
                Text = "إغلاق",
                Font = new Font("Segoe UI", 14),
                Size = new Size(160, 45),
                Location = new Point(230, 370),
                BackColor = Color.FromArgb(180, 50, 50),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            exitButton.FlatAppearance.BorderSize = 0;
            exitButton.Click += (s, e) => Application.Exit();
            loginPanel.Controls.Add(exitButton);

            // Status label
            statusLabel = new Label
            {
                Text = "أدخل كود الصيدلية ثم بيانات تسجيل الدخول",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.LightGray,
                Size = new Size(350, 50),
                Location = new Point(50, 440),
                TextAlign = ContentAlignment.MiddleCenter
            };
            loginPanel.Controls.Add(statusLabel);

            // Add Enter key support
            pharmacyCodeTextBox.KeyDown += (s, e) => { if (e.KeyCode == Keys.Enter) usernameTextBox.Focus(); };
            usernameTextBox.KeyDown += (s, e) => { if (e.KeyCode == Keys.Enter) passwordTextBox.Focus(); };
            passwordTextBox.KeyDown += (s, e) => { if (e.KeyCode == Keys.Enter) LoginButton_Click(null, null); };
        }

        private void LoginButton_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من إدخال جميع البيانات المطلوبة
                if (string.IsNullOrWhiteSpace(pharmacyCodeTextBox.Text) ||
                    string.IsNullOrWhiteSpace(usernameTextBox.Text) || 
                    string.IsNullOrWhiteSpace(passwordTextBox.Text))
                {
                    statusLabel.Text = "يرجى إدخال جميع البيانات المطلوبة";
                    statusLabel.ForeColor = Color.Red;
                    return;
                }

                statusLabel.Text = "جاري التحقق من كود الصيدلية...";
                statusLabel.ForeColor = Color.Yellow;
                Application.DoEvents();

                // التحقق من صحة كود الصيدلية أولاً
                if (!ValidatePharmacyCode(pharmacyCodeTextBox.Text.Trim()))
                {
                    statusLabel.Text = "كود الصيدلية غير صحيح أو غير نشط";
                    statusLabel.ForeColor = Color.Red;
                    return;
                }

                statusLabel.Text = "جاري التحقق من بيانات المستخدم...";
                statusLabel.ForeColor = Color.Yellow;
                Application.DoEvents();

                // التحقق من صحة بيانات المستخدم
                DataSet loginResult = unifiedDb.ValidateLoginWithPharmacy(
                    usernameTextBox.Text.Trim(), 
                    passwordTextBox.Text, 
                    selectedPharmacyId);

                if (loginResult.Tables.Count > 0 && loginResult.Tables[0].Rows.Count > 0)
                {
                    DataRow userRow = loginResult.Tables[0].Rows[0];
                    
                    statusLabel.Text = "تم تسجيل الدخول بنجاح!";
                    statusLabel.ForeColor = Color.Green;

                    // تسجيل جلسة الدخول
                    unifiedDb.RecordLoginSession(
                        selectedPharmacyId,
                        Convert.ToInt32(userRow["id"]),
                        userRow["username"].ToString(),
                        userRow["name"].ToString()
                    );

                    // تحديث SessionManager مع معلومات الجلسة
                    SessionManager.Login(
                        Convert.ToInt32(userRow["id"]),
                        userRow["username"].ToString(),
                        userRow["name"].ToString(),
                        userRow["userRole"].ToString(),
                        selectedPharmacyId,
                        selectedPharmacyName,
                        pharmacyCodeTextBox.Text.Trim()
                    );

                    // فتح الواجهة المناسبة
                    string role = userRow["userRole"].ToString();
                    if (role == "Administrator")
                    {
                        Adminstrator adminForm = new Adminstrator(userRow["name"].ToString());
                        adminForm.Show();
                    }
                    else
                    {
                        Pharmacist pharmacistForm = new Pharmacist(userRow["name"].ToString(), userRow["userRole"].ToString());
                        pharmacistForm.Show();
                    }

                    // حفظ كود الصيدلية الحالي
                    SaveLastPharmacyCode(pharmacyCodeTextBox.Text.Trim());

                    this.Hide();
                }
                else
                {
                    statusLabel.Text = "اسم المستخدم أو كلمة المرور غير صحيحة";
                    statusLabel.ForeColor = Color.Red;
                    passwordTextBox.Clear();
                    passwordTextBox.Focus();
                }
            }
            catch (Exception ex)
            {
                statusLabel.Text = $"خطأ في تسجيل الدخول: {ex.Message}";
                statusLabel.ForeColor = Color.Red;
            }
        }

        private bool ValidatePharmacyCode(string pharmacyCode)
        {
            try
            {
                DataSet pharmacyResult = unifiedDb.GetPharmacyByCode(pharmacyCode);

                if (pharmacyResult.Tables.Count > 0 && pharmacyResult.Tables[0].Rows.Count > 0)
                {
                    DataRow pharmacyRow = pharmacyResult.Tables[0].Rows[0];
                    selectedPharmacyId = Convert.ToInt32(pharmacyRow["id"]);
                    selectedPharmacyName = pharmacyRow["pharmacyName"].ToString();

                    // التحقق من أن الصيدلية نشطة
                    bool isActive = Convert.ToBoolean(pharmacyRow["isActive"]);
                    return isActive;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من كود الصيدلية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حفظ آخر كود صيدلية في الريجستري
        /// </summary>
        private void SaveLastPharmacyCode(string pharmacyCode)
        {
            try
            {
                using (RegistryKey key = Registry.CurrentUser.CreateSubKey(REGISTRY_KEY))
                {
                    if (key != null)
                    {
                        key.SetValue("LastPharmacyCode", pharmacyCode);
                        System.Diagnostics.Debug.WriteLine($"تم حفظ كود الصيدلية: {pharmacyCode}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ كود الصيدلية: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل آخر كود صيدلية من الريجستري
        /// </summary>
        private void LoadLastPharmacyCode()
        {
            try
            {
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY))
                {
                    if (key != null)
                    {
                        string lastCode = key.GetValue("LastPharmacyCode")?.ToString();
                        if (!string.IsNullOrEmpty(lastCode))
                        {
                            pharmacyCodeTextBox.Text = lastCode;
                            System.Diagnostics.Debug.WriteLine($"تم تحميل كود الصيدلية: {lastCode}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل كود الصيدلية: {ex.Message}");
            }
        }

        /// <summary>
        /// دالة عامة لحفظ كود الصيدلية (يمكن استدعاؤها من الخارج)
        /// </summary>
        public static void SavePharmacyCode(string pharmacyCode)
        {
            try
            {
                using (RegistryKey key = Registry.CurrentUser.CreateSubKey(REGISTRY_KEY))
                {
                    if (key != null)
                    {
                        key.SetValue("LastPharmacyCode", pharmacyCode);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ كود الصيدلية: {ex.Message}");
            }
        }

        /// <summary>
        /// دالة عامة لتحميل آخر كود صيدلية (يمكن استدعاؤها من الخارج)
        /// </summary>
        public static string GetLastPharmacyCode()
        {
            try
            {
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY))
                {
                    if (key != null)
                    {
                        return key.GetValue("LastPharmacyCode")?.ToString() ?? "";
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل كود الصيدلية: {ex.Message}");
            }
            return "";
        }
    }
}
