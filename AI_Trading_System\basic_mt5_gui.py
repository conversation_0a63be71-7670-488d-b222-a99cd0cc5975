#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import messagebox
import threading
import time
from datetime import datetime
import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from mt5_real_crypto_system import MT5RealCryptoSystem
except ImportError as e:
    print(f"خطأ في استيراد النظام: {e}")
    sys.exit(1)

class BasicMT5GUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام تداول العملات الرقمية على MetaTrader 5")
        self.root.geometry("1000x700")
        self.root.configure(bg='#2b2b2b')
        
        # النظام
        self.trading_system = None
        self.is_connected = False
        self.is_trading = False
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # بدء تحديث البيانات
        self.update_data()
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_label = tk.Label(self.root, text="🚀 نظام تداول العملات الرقمية على MetaTrader 5", 
                              font=('Arial', 16, 'bold'), fg='#00ff88', bg='#2b2b2b')
        title_label.pack(pady=20)
        
        # إطار التحكم
        control_frame = tk.Frame(self.root, bg='#343a40', relief=tk.RAISED, bd=3)
        control_frame.pack(fill=tk.X, padx=20, pady=10)
        
        tk.Label(control_frame, text="🎛️ لوحة التحكم", 
                font=('Arial', 12, 'bold'), fg='white', bg='#343a40').pack(pady=10)
        
        # صف الاتصال
        conn_frame = tk.Frame(control_frame, bg='#343a40')
        conn_frame.pack(pady=10)
        
        self.connect_btn = tk.Button(conn_frame, text="🔌 اتصال بـ MT5", command=self.connect_system,
                                    bg='#28a745', fg='white', font=('Arial', 10, 'bold'), width=15)
        self.connect_btn.pack(side=tk.LEFT, padx=10)
        
        self.disconnect_btn = tk.Button(conn_frame, text="🔌 قطع الاتصال", command=self.disconnect_system,
                                       bg='#dc3545', fg='white', font=('Arial', 10, 'bold'), 
                                       width=15, state='disabled')
        self.disconnect_btn.pack(side=tk.LEFT, padx=10)
        
        self.status_label = tk.Label(conn_frame, text="❌ غير متصل بـ MT5", 
                                    fg='#dc3545', bg='#343a40', font=('Arial', 11, 'bold'))
        self.status_label.pack(side=tk.LEFT, padx=20)
        
        # صف الرمز والسعر
        symbol_frame = tk.Frame(control_frame, bg='#343a40')
        symbol_frame.pack(pady=10)
        
        tk.Label(symbol_frame, text="💰 رمز العملة:", fg='white', bg='#343a40',
                font=('Arial', 10, 'bold')).pack(side=tk.LEFT)

        # Symbol search frame
        search_frame = tk.Frame(symbol_frame, bg='#343a40')
        search_frame.pack(side=tk.LEFT, padx=10)

        # Symbol entry with search
        self.symbol_var = tk.StringVar(value="BTCUSD")
        self.symbol_entry = tk.Entry(search_frame, textvariable=self.symbol_var,
                                    width=10, font=('Arial', 10))
        self.symbol_entry.pack(side=tk.TOP)
        self.symbol_entry.bind('<KeyRelease>', self.on_symbol_search)

        # Quick symbol buttons
        quick_symbols_frame = tk.Frame(search_frame, bg='#343a40')
        quick_symbols_frame.pack(side=tk.TOP, pady=2)

        quick_symbols = ['BTCUSD', 'ETHUSD', 'EURUSD', 'GBPUSD', 'AUDUSD', 'XAUUSD']
        for i, symbol in enumerate(quick_symbols):
            if i % 3 == 0:
                row_frame = tk.Frame(quick_symbols_frame, bg='#343a40')
                row_frame.pack()
            btn = tk.Button(row_frame, text=symbol,
                           command=lambda s=symbol: self.set_symbol(s),
                           bg='#17a2b8', fg='white', font=('Arial', 7), width=7)
            btn.pack(side=tk.LEFT, padx=1, pady=1)

        # Search button
        search_btn = tk.Button(search_frame, text="🔍 بحث في MT5",
                              command=self.search_mt5_symbols, bg='#28a745', fg='white',
                              font=('Arial', 8), width=12)
        search_btn.pack(side=tk.TOP, pady=2)

        # Available symbols (initially hidden)
        self.symbols_listbox = None

        # Default available symbols
        self.available_symbols = [
            # Crypto
            'BTCUSD', 'ETHUSD', 'XRPUSD', 'LTCUSD', 'ADAUSD', 'DOTUSD', 'LINKUSD',
            # Major Forex
            'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',
            # Cross pairs
            'AUDCAD', 'AUDCHF', 'AUDJPY', 'AUDNZD', 'CADCHF', 'CADJPY', 'CHFJPY',
            'EURAUD', 'EURCAD', 'EURCHF', 'EURGBP', 'EURJPY', 'EURNZD',
            'GBPAUD', 'GBPCAD', 'GBPCHF', 'GBPJPY', 'GBPNZD',
            'NZDCAD', 'NZDCHF', 'NZDJPY',
            # Metals
            'XAUUSD', 'XAGUSD', 'XAUEUR', 'XAGEUR',
            # Indices
            'US30', 'US500', 'NAS100', 'GER30', 'UK100', 'FRA40', 'JPN225'
        ]
        
        self.refresh_btn = tk.Button(symbol_frame, text="🔄 تحديث السعر", command=self.refresh_price,
                                    bg='#007bff', fg='white', font=('Arial', 9), width=12)
        self.refresh_btn.pack(side=tk.LEFT, padx=10)
        
        self.price_label = tk.Label(symbol_frame, text="السعر: غير متوفر", 
                                   fg='#ffc107', bg='#343a40', font=('Arial', 11, 'bold'))
        self.price_label.pack(side=tk.LEFT, padx=20)
        
        # صف الإعدادات
        settings_frame = tk.Frame(control_frame, bg='#343a40')
        settings_frame.pack(pady=10)
        
        tk.Label(settings_frame, text="🎯 نسبة الثقة:", fg='white', bg='#343a40', 
                font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        
        self.confidence_var = tk.DoubleVar(value=70.0)
        self.confidence_scale = tk.Scale(settings_frame, from_=30, to=100, orient=tk.HORIZONTAL,
                                        variable=self.confidence_var, bg='#343a40', fg='white',
                                        highlightbackground='#343a40', length=150,
                                        command=self.update_confidence_label)
        self.confidence_scale.pack(side=tk.LEFT, padx=10)
        
        self.confidence_label = tk.Label(settings_frame, text="70%", 
                                        fg='#ffc107', bg='#343a40', font=('Arial', 12, 'bold'))
        self.confidence_label.pack(side=tk.LEFT, padx=10)
        
        # أزرار سريعة
        tk.Button(settings_frame, text="50%", command=lambda: self.set_confidence(50),
                 bg='#ffc107', fg='black', font=('Arial', 8), width=4).pack(side=tk.LEFT, padx=2)
        tk.Button(settings_frame, text="70%", command=lambda: self.set_confidence(70),
                 bg='#007bff', fg='white', font=('Arial', 8), width=4).pack(side=tk.LEFT, padx=2)
        tk.Button(settings_frame, text="90%", command=lambda: self.set_confidence(90),
                 bg='#28a745', fg='white', font=('Arial', 8), width=4).pack(side=tk.LEFT, padx=2)
        tk.Button(settings_frame, text="100%", command=lambda: self.set_confidence(100),
                 bg='#6f42c1', fg='white', font=('Arial', 8), width=4).pack(side=tk.LEFT, padx=2)
        
        # صف التداول
        trading_frame = tk.Frame(control_frame, bg='#343a40')
        trading_frame.pack(pady=15)
        
        self.demo_var = tk.BooleanVar(value=True)
        self.demo_check = tk.Checkbutton(trading_frame, text="🛡️ وضع تجريبي آمن", 
                                        variable=self.demo_var, fg='white', bg='#343a40',
                                        selectcolor='#343a40', font=('Arial', 10))
        self.demo_check.pack(side=tk.LEFT, padx=20)
        
        self.start_btn = tk.Button(trading_frame, text="🚀 بدء التداول", command=self.start_trading,
                                  bg='#28a745', fg='white', font=('Arial', 10, 'bold'), 
                                  width=15, state='disabled')
        self.start_btn.pack(side=tk.LEFT, padx=10)
        
        self.stop_btn = tk.Button(trading_frame, text="⏹️ إيقاف التداول", command=self.stop_trading,
                                 bg='#dc3545', fg='white', font=('Arial', 10, 'bold'), 
                                 width=15, state='disabled')
        self.stop_btn.pack(side=tk.LEFT, padx=10)
        
        # إطار معلومات الحساب
        account_frame = tk.Frame(self.root, bg='#495057', relief=tk.RAISED, bd=3)
        account_frame.pack(fill=tk.X, padx=20, pady=10)
        
        tk.Label(account_frame, text="📊 معلومات حساب MetaTrader 5", 
                font=('Arial', 12, 'bold'), fg='white', bg='#495057').pack(pady=10)
        
        # معلومات الحساب
        info_frame = tk.Frame(account_frame, bg='#495057')
        info_frame.pack(pady=10)
        
        # الصف الأول
        row1 = tk.Frame(info_frame, bg='#495057')
        row1.pack(pady=5)
        
        tk.Label(row1, text="🏢 الشركة:", fg='white', bg='#495057', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.company_label = tk.Label(row1, text="غير متصل", fg='#17a2b8', bg='#495057', font=('Arial', 10))
        self.company_label.pack(side=tk.LEFT, padx=(10, 30))
        
        tk.Label(row1, text="💰 الرصيد:", fg='white', bg='#495057', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.balance_label = tk.Label(row1, text="$0.00", fg='#28a745', bg='#495057', font=('Arial', 12, 'bold'))
        self.balance_label.pack(side=tk.LEFT, padx=(10, 30))
        
        tk.Label(row1, text="📊 الربح/الخسارة:", fg='white', bg='#495057', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.profit_label = tk.Label(row1, text="$0.00", fg='white', bg='#495057', font=('Arial', 12, 'bold'))
        self.profit_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # الصف الثاني
        row2 = tk.Frame(info_frame, bg='#495057')
        row2.pack(pady=5)
        
        tk.Label(row2, text="🖥️ الخادم:", fg='white', bg='#495057', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.server_label = tk.Label(row2, text="غير متصل", fg='#17a2b8', bg='#495057', font=('Arial', 10))
        self.server_label.pack(side=tk.LEFT, padx=(10, 30))
        
        tk.Label(row2, text="📋 الصفقات:", fg='white', bg='#495057', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.positions_label = tk.Label(row2, text="0", fg='#ffc107', bg='#495057', font=('Arial', 12, 'bold'))
        self.positions_label.pack(side=tk.LEFT, padx=(10, 30))
        
        tk.Label(row2, text="🔄 الحالة:", fg='white', bg='#495057', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.system_status_label = tk.Label(row2, text="متوقف", fg='#ffc107', bg='#495057', font=('Arial', 11, 'bold'))
        self.system_status_label.pack(side=tk.LEFT, padx=(10, 0))

        # الصف الثالث - نسبة الثقة
        row3 = tk.Frame(info_frame, bg='#495057')
        row3.pack(pady=5)

        tk.Label(row3, text="🎯 نسبة الثقة الحالية:", fg='white', bg='#495057', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.current_confidence_label = tk.Label(row3, text="0%", fg='#ffc107', bg='#495057', font=('Arial', 12, 'bold'))
        self.current_confidence_label.pack(side=tk.LEFT, padx=(10, 30))

        tk.Label(row3, text="📊 المطلوب:", fg='white', bg='#495057', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.required_confidence_label = tk.Label(row3, text="70%", fg='#17a2b8', bg='#495057', font=('Arial', 12, 'bold'))
        self.required_confidence_label.pack(side=tk.LEFT, padx=(10, 30))

        tk.Label(row3, text="⚡ حالة الدخول:", fg='white', bg='#495057', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.confidence_status_label = tk.Label(row3, text="انتظار", fg='#6c757d', bg='#495057', font=('Arial', 11, 'bold'))
        self.confidence_status_label.pack(side=tk.LEFT, padx=(10, 0))

        # السجل
        log_frame = tk.Frame(self.root, bg='#1e1e1e', relief=tk.RAISED, bd=3)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        tk.Label(log_frame, text="📝 سجل الأحداث والتحليلات", 
                font=('Arial', 12, 'bold'), fg='white', bg='#1e1e1e').pack(pady=10)
        
        # منطقة النص
        text_frame = tk.Frame(log_frame, bg='#1e1e1e')
        text_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))
        
        self.log_text = tk.Text(text_frame, height=12, 
                               bg='#1e1e1e', fg='#00ff88', 
                               font=('Consolas', 9),
                               insertbackground='#00ff88',
                               wrap=tk.WORD)
        
        # شريط التمرير
        scrollbar = tk.Scrollbar(text_frame, command=self.log_text.yview)
        self.log_text.config(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # رسالة ترحيب
        welcome_msg = """🚀 مرحباً بك في نظام تداول العملات الرقمية على MetaTrader 5!

✨ المميزات:
• الاتصال المباشر بـ MetaTrader 5
• عرض معلومات الحساب الحقيقية
• تداول العملات الرقمية (BTC, ETH, إلخ)
• إعدادات نسبة الثقة قابلة للتخصيص (30% - 100%)
• تحليل فني متقدم مع الذكاء الاصطناعي

📋 خطوات البدء:
1. تأكد من تشغيل MetaTrader 5 وتسجيل الدخول
2. اضغط "اتصال بـ MT5" للاتصال
3. اختر رمز العملة (BTC, ETH, إلخ)
4. حدد نسبة الثقة المطلوبة للدخول
5. اضغط "بدء التداول" لبدء التداول الذكي

⚠️ تنبيه: ابدأ دائماً بالوضع التجريبي!
"""
        self.log_text.insert(tk.END, welcome_msg)
        
    def update_confidence_label(self, value=None):
        """تحديث تسمية نسبة الثقة في الإعدادات"""
        confidence = self.confidence_var.get()
        self.confidence_label.config(text=f"{confidence:.0f}%")

        # تحديث النسبة المطلوبة في عرض الحساب أيضاً
        if hasattr(self, 'required_confidence_label'):
            self.required_confidence_label.config(text=f"{confidence:.0f}%")

        # تغيير اللون حسب النسبة
        if confidence >= 90:
            color = '#28a745'  # أخضر
        elif confidence >= 70:
            color = '#007bff'  # أزرق
        elif confidence >= 50:
            color = '#ffc107'  # أصفر
        else:
            color = '#dc3545'  # أحمر

        self.confidence_label.config(fg=color)
        
    def set_confidence(self, value):
        """تحديد نسبة الثقة بسرعة"""
        self.confidence_var.set(value)
        self.update_confidence_label()
        
    def log_message(self, message):
        """إضافة رسالة للسجل"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, full_message)
        self.log_text.see(tk.END)
        
    def connect_system(self):
        """الاتصال بالنظام"""
        try:
            self.log_message("🔌 محاولة الاتصال بـ MetaTrader 5...")
            
            demo_mode = self.demo_var.get()
            self.trading_system = MT5RealCryptoSystem(demo_mode=demo_mode)
            
            if self.trading_system.connect():
                self.is_connected = True
                self.status_label.config(text="✅ متصل بـ MT5", fg='#28a745')
                self.connect_btn.config(state='disabled')
                self.disconnect_btn.config(state='normal')
                self.start_btn.config(state='normal')
                self.refresh_btn.config(state='normal')
                
                # تحديث معلومات الحساب
                self.update_account_info()
                
                # تحديث الرموز المتوفرة
                self.refresh_symbols()
                
                self.log_message("✅ تم الاتصال بـ MT5 بنجاح!")
                
            else:
                self.log_message("❌ فشل في الاتصال بـ MT5!")
                messagebox.showerror("خطأ", "فشل في الاتصال بـ MetaTrader 5\nتأكد من:\n• تشغيل MT5\n• تسجيل الدخول\n• تفعيل التداول الآلي")
                
        except Exception as e:
            self.log_message(f"❌ خطأ في الاتصال: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في الاتصال: {str(e)}")
            
    def disconnect_system(self):
        """قطع الاتصال"""
        if self.is_trading:
            self.stop_trading()
            
        if self.trading_system:
            self.trading_system.disconnect()
            
        self.is_connected = False
        self.status_label.config(text="❌ غير متصل بـ MT5", fg='#dc3545')
        self.connect_btn.config(state='normal')
        self.disconnect_btn.config(state='disabled')
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='disabled')
        self.refresh_btn.config(state='disabled')
        
        # مسح المعلومات
        self.company_label.config(text="غير متصل")
        self.server_label.config(text="غير متصل")
        self.balance_label.config(text="$0.00")
        self.profit_label.config(text="$0.00", fg='white')
        self.positions_label.config(text="0")
        self.system_status_label.config(text="متوقف", fg='#ffc107')
        self.price_label.config(text="السعر: غير متوفر")
        
        self.log_message("🔌 تم قطع الاتصال")
        
    def refresh_symbols(self):
        """تحديث الرموز المتوفرة"""
        if not self.is_connected:
            return
            
        try:
            symbols = self.trading_system.get_mt5_symbols()
            if symbols:
                # إضافة الرموز الخارجية
                all_symbols = symbols + ['BTCUSD', 'ETHUSD', 'LTCUSD', 'XRPUSD']
                
                # تحديث القائمة المنسدلة
                menu = self.symbol_menu['menu']
                menu.delete(0, 'end')
                for symbol in all_symbols:
                    menu.add_command(label=symbol, command=tk._setit(self.symbol_var, symbol))
                
                self.log_message(f"🔄 تم تحديث الرموز: {len(symbols)} رمز من MT5")
            else:
                self.log_message("⚠️ لم يتم العثور على رموز عملات رقمية في MT5")
                
        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث الرموز: {str(e)}")
            
    def refresh_price(self):
        """تحديث السعر الحالي"""
        if not self.is_connected:
            return
            
        try:
            symbol = self.symbol_var.get()
            price_info = self.trading_system.get_best_price(symbol)
            
            if price_info['final_price']:
                price = price_info['final_price']
                source = price_info['source']
                self.price_label.config(text=f"السعر: ${price:,.2f} ({source})")
                self.log_message(f"💰 {symbol}: ${price:,.2f} (المصدر: {source})")
            else:
                self.price_label.config(text="السعر: غير متوفر")
                self.log_message(f"❌ فشل في الحصول على سعر {symbol}")
                
        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث السعر: {str(e)}")
            
    def update_account_info(self):
        """تحديث معلومات الحساب"""
        if not self.is_connected or not self.trading_system:
            return
            
        try:
            account_info = self.trading_system.get_account_info()
            
            if account_info:
                self.company_label.config(text=account_info.get('company', 'غير متوفر'))
                self.server_label.config(text=account_info.get('server', 'غير متوفر'))
                
                balance = account_info.get('balance', 0)
                self.balance_label.config(text=f"${balance:,.2f}")
                
                profit = account_info.get('profit', 0)
                if profit > 0:
                    self.profit_label.config(text=f"+${profit:,.2f}", fg='#28a745')
                elif profit < 0:
                    self.profit_label.config(text=f"${profit:,.2f}", fg='#dc3545')
                else:
                    self.profit_label.config(text="$0.00", fg='white')
                
                positions = self.trading_system.get_open_positions()
                self.positions_label.config(text=str(len(positions)))
                
        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث معلومات الحساب: {str(e)}")

    def update_confidence_display(self, current_confidence, required_confidence, decision):
        """تحديث عرض نسبة الثقة في الواجهة"""
        try:
            # تحديث نسبة الثقة الحالية
            self.current_confidence_label.config(text=f"{current_confidence:.1f}%")

            # تحديث نسبة الثقة المطلوبة
            self.required_confidence_label.config(text=f"{required_confidence:.0f}%")

            # تحديث حالة الدخول
            if decision == 'error':
                self.confidence_status_label.config(text="خطأ", fg='#dc3545')
                self.current_confidence_label.config(fg='#dc3545')
            elif decision == 'hold':
                self.confidence_status_label.config(text="انتظار", fg='#6c757d')
                self.current_confidence_label.config(fg='#6c757d')
            elif current_confidence >= required_confidence:
                if decision in ['buy', 'sell']:
                    self.confidence_status_label.config(text="جاهز للدخول", fg='#28a745')
                    self.current_confidence_label.config(fg='#28a745')
                else:
                    self.confidence_status_label.config(text="انتظار", fg='#ffc107')
                    self.current_confidence_label.config(fg='#ffc107')
            else:
                self.confidence_status_label.config(text="غير كافي", fg='#ffc107')
                self.current_confidence_label.config(fg='#ffc107')

        except Exception as e:
            print(f"خطأ في تحديث عرض الثقة: {str(e)}")

    def set_symbol(self, symbol):
        """تحديد رمز العملة بسرعة"""
        self.symbol_var.set(symbol)
        self.refresh_price()

    def on_symbol_search(self, event=None):
        """البحث أثناء الكتابة"""
        search_text = self.symbol_var.get().upper()
        if len(search_text) >= 2:
            matches = [s for s in self.available_symbols if search_text in s]
            if matches and not hasattr(self, 'symbols_listbox_visible'):
                self.show_symbol_suggestions(matches[:10])
        elif hasattr(self, 'symbols_listbox_visible'):
            self.hide_symbol_suggestions()

    def show_symbol_suggestions(self, matches):
        """عرض اقتراحات الرموز"""
        if not self.symbols_listbox:
            # Create listbox if it doesn't exist
            self.symbols_listbox = tk.Listbox(self.symbol_entry.master, height=min(len(matches), 6),
                                             width=12, font=('Arial', 9))
            self.symbols_listbox.bind('<Double-Button-1>', self.select_symbol_from_list)
            self.symbols_listbox.bind('<Return>', self.select_symbol_from_list)

        # Clear and populate
        self.symbols_listbox.delete(0, tk.END)
        for match in matches:
            self.symbols_listbox.insert(tk.END, match)

        # Show listbox
        self.symbols_listbox.pack(pady=2)
        self.symbols_listbox_visible = True

    def hide_symbol_suggestions(self):
        """إخفاء اقتراحات الرموز"""
        if self.symbols_listbox:
            self.symbols_listbox.pack_forget()
        if hasattr(self, 'symbols_listbox_visible'):
            delattr(self, 'symbols_listbox_visible')

    def select_symbol_from_list(self, event=None):
        """اختيار رمز من القائمة"""
        if self.symbols_listbox and self.symbols_listbox.curselection():
            selected = self.symbols_listbox.get(self.symbols_listbox.curselection()[0])
            self.symbol_var.set(selected)
            self.hide_symbol_suggestions()
            self.refresh_price()

    def search_mt5_symbols(self):
        """البحث في رموز MT5 المتاحة"""
        if not self.is_connected or not self.trading_system:
            messagebox.showwarning("تحذير", "يجب الاتصال بـ MT5 أولاً للبحث في الرموز المتاحة!")
            return

        try:
            self.log_message("🔍 البحث في رموز MT5 المتاحة...")

            # Get all symbols from MT5
            import MetaTrader5 as mt5
            symbols = mt5.symbols_get()

            if symbols:
                # Filter and categorize symbols
                forex_symbols = []
                crypto_symbols = []
                metal_symbols = []
                index_symbols = []
                other_symbols = []

                for symbol in symbols:
                    name = symbol.name
                    if any(crypto in name for crypto in ['BTC', 'ETH', 'XRP', 'LTC', 'ADA', 'DOT', 'LINK']):
                        crypto_symbols.append(name)
                    elif any(metal in name for metal in ['XAU', 'XAG', 'GOLD', 'SILVER']):
                        metal_symbols.append(name)
                    elif any(index in name for index in ['US30', 'US500', 'NAS100', 'GER30', 'UK100', 'FRA40', 'JPN225']):
                        index_symbols.append(name)
                    elif len(name) == 6 and name[:3] != name[3:]:  # Forex pairs
                        forex_symbols.append(name)
                    else:
                        other_symbols.append(name)

                # Update available symbols
                self.available_symbols = (sorted(crypto_symbols) + sorted(forex_symbols) +
                                        sorted(metal_symbols) + sorted(index_symbols) +
                                        sorted(other_symbols))

                # Show results window
                self.show_symbols_window()

                self.log_message(f"✅ تم العثور على {len(self.available_symbols)} رمز متاح في MT5")

            else:
                self.log_message("❌ لم يتم العثور على رموز في MT5")

        except Exception as e:
            self.log_message(f"❌ خطأ في البحث: {str(e)}")

    def show_symbols_window(self):
        """عرض نافذة الرموز المتاحة"""
        symbols_window = tk.Toplevel(self.root)
        symbols_window.title("🔍 الرموز المتاحة في MT5")
        symbols_window.geometry("600x500")
        symbols_window.configure(bg='#2b2b2b')

        # Search frame
        search_frame = tk.Frame(symbols_window, bg='#343a40')
        search_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(search_frame, text="🔍 البحث:", fg='white', bg='#343a40',
                font=('Arial', 10, 'bold')).pack(side=tk.LEFT)

        search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=search_var, width=20, font=('Arial', 10))
        search_entry.pack(side=tk.LEFT, padx=10)

        # Categories frame
        categories_frame = tk.Frame(symbols_window, bg='#2b2b2b')
        categories_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create notebook for categories
        from tkinter import ttk
        notebook = ttk.Notebook(categories_frame)
        notebook.pack(fill=tk.BOTH, expand=True)

        # Categorize symbols
        categories = {
            "العملات الرقمية": [s for s in self.available_symbols if any(crypto in s for crypto in ['BTC', 'ETH', 'XRP', 'LTC', 'ADA'])],
            "العملات الرئيسية": [s for s in self.available_symbols if s in ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD']],
            "الأزواج المتقاطعة": [s for s in self.available_symbols if len(s) == 6 and 'USD' not in s and any(curr in s for curr in ['EUR', 'GBP', 'AUD', 'CAD', 'CHF', 'JPY', 'NZD'])],
            "المعادن": [s for s in self.available_symbols if any(metal in s for metal in ['XAU', 'XAG', 'GOLD', 'SILVER'])],
            "المؤشرات": [s for s in self.available_symbols if any(index in s for index in ['US30', 'US500', 'NAS100', 'GER30', 'UK100'])],
            "الكل": self.available_symbols
        }

        for category, symbols in categories.items():
            if symbols:
                frame = tk.Frame(notebook, bg='#2b2b2b')
                notebook.add(frame, text=f"{category} ({len(symbols)})")

                # Listbox with scrollbar
                list_frame = tk.Frame(frame, bg='#2b2b2b')
                list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

                listbox = tk.Listbox(list_frame, bg='#343a40', fg='white',
                                   font=('Arial', 10), selectmode=tk.SINGLE)
                scrollbar = tk.Scrollbar(list_frame, orient=tk.VERTICAL, command=listbox.yview)
                listbox.configure(yscrollcommand=scrollbar.set)

                for symbol in sorted(symbols):
                    listbox.insert(tk.END, symbol)

                listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
                scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

                # Double-click to select
                listbox.bind('<Double-Button-1>', lambda e, lb=listbox: self.select_symbol_from_window(lb, symbols_window))

        # Buttons frame
        buttons_frame = tk.Frame(symbols_window, bg='#2b2b2b')
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        select_btn = tk.Button(buttons_frame, text="✅ اختيار",
                              command=lambda: self.select_current_symbol(notebook, symbols_window),
                              bg='#28a745', fg='white', font=('Arial', 10, 'bold'))
        select_btn.pack(side=tk.LEFT, padx=5)

        close_btn = tk.Button(buttons_frame, text="❌ إغلاق",
                             command=symbols_window.destroy,
                             bg='#dc3545', fg='white', font=('Arial', 10, 'bold'))
        close_btn.pack(side=tk.LEFT, padx=5)

    def select_symbol_from_window(self, listbox, window):
        """اختيار رمز من نافذة الرموز"""
        if listbox.curselection():
            selected = listbox.get(listbox.curselection()[0])
            self.symbol_var.set(selected)
            window.destroy()
            self.refresh_price()
            self.log_message(f"✅ تم اختيار الرمز: {selected}")

    def select_current_symbol(self, notebook, window):
        """اختيار الرمز المحدد حالياً"""
        current_tab = notebook.select()
        current_frame = notebook.nametowidget(current_tab)

        # Find listbox in current frame
        for widget in current_frame.winfo_children():
            if isinstance(widget, tk.Frame):
                for child in widget.winfo_children():
                    if isinstance(child, tk.Listbox) and child.curselection():
                        selected = child.get(child.curselection()[0])
                        self.symbol_var.set(selected)
                        window.destroy()
                        self.refresh_price()
                        self.log_message(f"✅ تم اختيار الرمز: {selected}")
                        return

        messagebox.showwarning("تحذير", "يرجى اختيار رمز من القائمة أولاً!")

    def start_trading(self):
        """بدء التداول"""
        if not self.is_connected:
            messagebox.showwarning("تحذير", "يجب الاتصال بـ MT5 أولاً!")
            return
            
        symbol = self.symbol_var.get()
        confidence_threshold = self.confidence_var.get()
        
        # تأكيد البدء
        mode_text = "تجريبي آمن" if self.demo_var.get() else "حقيقي"
        confirm_msg = f"""هل تريد بدء التداول الذكي؟

الإعدادات:
• الرمز: {symbol}
• الوضع: {mode_text}
• نسبة الثقة المطلوبة: {confidence_threshold:.0f}%

⚠️ تأكد من الإعدادات قبل المتابعة!"""
        
        if not messagebox.askyesno("تأكيد بدء التداول", confirm_msg):
            return
            
        self.is_trading = True
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.system_status_label.config(text="🚀 يعمل", fg='#28a745')
        
        # تطبيق الإعدادات
        self.trading_system.demo_mode = self.demo_var.get()
        self.trading_system.set_current_pair(symbol)
        self.trading_system.set_confidence_threshold(confidence_threshold)
        
        self.log_message(f"🚀 بدء التداول الذكي:")
        self.log_message(f"   💰 الرمز: {symbol}")
        self.log_message(f"   🛡️ الوضع: {mode_text}")
        self.log_message(f"   🎯 نسبة الثقة: {confidence_threshold:.0f}%")
        
        # بدء التداول في خيط منفصل
        threading.Thread(target=self.trading_loop, daemon=True).start()
        
    def stop_trading(self):
        """إيقاف التداول"""
        self.is_trading = False
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.system_status_label.config(text="⏹️ متوقف", fg='#ffc107')
        self.log_message("⏹️ تم إيقاف التداول الذكي")
        
    def trading_loop(self):
        """حلقة التداول الذكية"""
        while self.is_trading and self.is_connected:
            try:
                symbol = self.symbol_var.get()
                self.log_message(f"🔍 تحليل {symbol} بالذكاء الاصطناعي...")
                
                # تحليل السوق
                analysis = self.trading_system.analyze_market(symbol)
                
                if 'error' not in analysis:
                    decision = analysis['decision']
                    confidence = analysis['confidence']
                    price = analysis['price']
                    threshold = self.trading_system.confidence_threshold

                    # تحديث نسبة الثقة في الواجهة
                    self.root.after(0, lambda: self.update_confidence_display(confidence, threshold, decision))

                    self.log_message(f"📊 نتيجة التحليل:")
                    self.log_message(f"   🎯 القرار: {decision}")
                    self.log_message(f"   📈 الثقة: {confidence:.1f}%")
                    self.log_message(f"   💰 السعر: ${price:,.2f}")
                    self.log_message(f"   🎯 المطلوب: {threshold:.0f}%")

                    # فحص شروط الدخول
                    if decision in ['buy', 'sell'] and confidence >= threshold:
                        self.log_message(f"✅ شروط الدخول متوفرة! الثقة: {confidence:.1f}% >= المطلوب: {threshold:.0f}%")

                        # محاولة تنفيذ التداول
                        if self.trading_system.execute_trade_mt5(analysis):
                            self.log_message("🎉 تم تنفيذ الصفقة بنجاح!")
                            self.log_message("🧠 النظام يتعلم من هذه الصفقة...")
                        else:
                            self.log_message("❌ فشل في تنفيذ الصفقة")
                    else:
                        if decision == 'hold':
                            self.log_message(f"⏳ قرار الانتظار - لا توجد إشارة واضحة")
                        else:
                            self.log_message(f"⏳ نسبة الثقة غير كافية: {confidence:.1f}% < المطلوب: {threshold:.0f}%")
                else:
                    self.log_message(f"❌ خطأ في التحليل: {analysis['error']}")
                    # تحديث عرض الثقة بحالة خطأ
                    self.root.after(0, lambda: self.update_confidence_display(0, self.trading_system.confidence_threshold, 'error'))
                
                # تحديث معلومات الحساب
                self.root.after(0, self.update_account_info)
                
                # انتظار قبل التحليل التالي (30 ثانية)
                for i in range(30):
                    if not self.is_trading:
                        break
                    time.sleep(1)
                
            except Exception as e:
                self.log_message(f"❌ خطأ في التداول: {str(e)}")
                time.sleep(10)
                
    def update_data(self):
        """تحديث البيانات دورياً"""
        if self.is_connected:
            self.update_account_info()
            
        # جدولة التحديث التالي (كل 5 ثوانٍ)
        self.root.after(5000, self.update_data)
        
    def run(self):
        """تشغيل التطبيق"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log_message("⏹️ تم إيقاف التطبيق")
        finally:
            if self.trading_system:
                self.trading_system.disconnect()
                
    def on_closing(self):
        """عند إغلاق التطبيق"""
        if self.is_trading:
            if messagebox.askokcancel("تأكيد الإغلاق", "النظام يعمل حالياً. هل تريد إيقافه والخروج؟"):
                self.stop_trading()
                time.sleep(1)
                self.disconnect_system()
                self.root.destroy()
        else:
            self.disconnect_system()
            self.root.destroy()

if __name__ == "__main__":
    try:
        app = BasicMT5GUI()
        app.run()
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")
