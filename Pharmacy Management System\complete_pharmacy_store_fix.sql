-- إصلاح شامل لمتجر الصيدلية
-- Complete Pharmacy Store Fix
USE UnifiedPharmacy;

PRINT '=== بدء الإصلاح الشامل لمتجر الصيدلية ===';

-- 1. إ<PERSON><PERSON><PERSON><PERSON> جدول الصيدليات
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacies')
BEGIN
    CREATE TABLE pharmacies (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacy_name NVARCHAR(255) NOT NULL,
        phone NVARCHAR(50),
        city NVARCHAR(100),
        address NVARCHAR(500),
        created_date DATETIME DEFAULT GETDATE()
    );
    PRINT '✅ تم إنشاء جدول pharmacies';
END

-- 2. إن<PERSON>ا<PERSON> جدول الأدوية المنشورة
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'published_medicines')
BEGIN
    CREATE TABLE published_medicines (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacy_id INT NOT NULL,
        medicine_name NVARCHAR(255) NOT NULL,
        medicine_number NVARCHAR(100),
        available_quantity INT NOT NULL,
        price_per_unit DECIMAL(10,2) NOT NULL,
        description NVARCHAR(1000),
        publish_date DATETIME DEFAULT GETDATE(),
        is_active BIT DEFAULT 1,
        FOREIGN KEY (pharmacy_id) REFERENCES pharmacies(id)
    );
    PRINT '✅ تم إنشاء جدول published_medicines';
END

-- 3. إنشاء جدول طلبات الشراء
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'purchase_requests')
BEGIN
    CREATE TABLE purchase_requests (
        id INT IDENTITY(1,1) PRIMARY KEY,
        buyer_pharmacy_id INT NOT NULL,
        seller_pharmacy_id INT NOT NULL,
        published_medicine_id INT NOT NULL,
        requested_quantity INT NOT NULL,
        offered_price DECIMAL(10,2),
        status NVARCHAR(50) DEFAULT 'pending',
        request_date DATETIME DEFAULT GETDATE(),
        response_date DATETIME,
        response_message NVARCHAR(1000),
        FOREIGN KEY (buyer_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (seller_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (published_medicine_id) REFERENCES published_medicines(id)
    );
    PRINT '✅ تم إنشاء جدول purchase_requests';
END

-- 4. إدراج بيانات تجريبية للصيدليات
IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacy_name = 'الصيدلية المركزية')
BEGIN
    INSERT INTO pharmacies (pharmacy_name, phone, city, address) VALUES
    ('الصيدلية المركزية', '01234567890', 'القاهرة', 'شارع التحرير'),
    ('صيدلية النهضة', '01987654321', 'الجيزة', 'شارع الهرم'),
    ('صيدلية الشفاء', '01122334455', 'الإسكندرية', 'شارع الكورنيش');
    PRINT '✅ تم إدراج بيانات الصيدليات';
END

-- 5. إدراج أدوية منشورة تجريبية
DECLARE @pharmacy1_id INT = (SELECT id FROM pharmacies WHERE pharmacy_name = 'الصيدلية المركزية');
DECLARE @pharmacy2_id INT = (SELECT id FROM pharmacies WHERE pharmacy_name = 'صيدلية النهضة');
DECLARE @pharmacy3_id INT = (SELECT id FROM pharmacies WHERE pharmacy_name = 'صيدلية الشفاء');

IF NOT EXISTS (SELECT * FROM published_medicines WHERE medicine_name = 'باراسيتامول 500 مجم')
BEGIN
    INSERT INTO published_medicines (pharmacy_id, medicine_name, medicine_number, available_quantity, price_per_unit, description) VALUES
    (@pharmacy1_id, 'باراسيتامول 500 مجم', 'MED001', 100, 5.50, 'مسكن للألم وخافض للحرارة'),
    (@pharmacy2_id, 'أموكسيسيلين 250 مجم', 'MED002', 50, 12.00, 'مضاد حيوي واسع المجال'),
    (@pharmacy3_id, 'فيتامين د 1000 وحدة', 'MED003', 75, 25.00, 'مكمل غذائي لتقوية العظام');
    PRINT '✅ تم إدراج الأدوية المنشورة';
END

-- 6. إدراج طلبات شراء تجريبية
DECLARE @published_med1_id INT = (SELECT id FROM published_medicines WHERE medicine_name = 'باراسيتامول 500 مجم');
DECLARE @published_med2_id INT = (SELECT id FROM published_medicines WHERE medicine_name = 'أموكسيسيلين 250 مجم');
DECLARE @published_med3_id INT = (SELECT id FROM published_medicines WHERE medicine_name = 'فيتامين د 1000 وحدة');

-- حذف الطلبات الموجودة لتجنب التكرار
DELETE FROM purchase_requests;

-- إدراج طلبات جديدة
INSERT INTO purchase_requests (buyer_pharmacy_id, seller_pharmacy_id, published_medicine_id, requested_quantity, offered_price, status) VALUES
(@pharmacy2_id, @pharmacy1_id, @published_med1_id, 20, 5.25, 'pending'),
(@pharmacy3_id, @pharmacy1_id, @published_med1_id, 15, 5.00, 'pending'),
(@pharmacy3_id, @pharmacy2_id, @published_med2_id, 10, 12.50, 'pending'),
(@pharmacy1_id, @pharmacy3_id, @published_med3_id, 5, 24.00, 'pending'),
(@pharmacy2_id, @pharmacy3_id, @published_med3_id, 8, 25.50, 'pending');

PRINT '✅ تم إدراج طلبات الشراء التجريبية';

-- 7. عرض النتائج النهائية
PRINT '=== إحصائيات قاعدة البيانات ===';
SELECT 'pharmacies' as 'الجدول', COUNT(*) as 'عدد السجلات' FROM pharmacies
UNION ALL
SELECT 'published_medicines', COUNT(*) FROM published_medicines
UNION ALL
SELECT 'purchase_requests', COUNT(*) FROM purchase_requests;

-- 8. عرض طلبات كل صيدلية
PRINT '=== طلبات كل صيدلية ===';
SELECT 
    p.pharmacy_name as 'اسم الصيدلية',
    COUNT(pr.id) as 'عدد الطلبات المستلمة'
FROM pharmacies p
LEFT JOIN purchase_requests pr ON p.id = pr.seller_pharmacy_id
GROUP BY p.id, p.pharmacy_name
ORDER BY p.id;

PRINT '=== تم إكمال الإصلاح بنجاح ===';
