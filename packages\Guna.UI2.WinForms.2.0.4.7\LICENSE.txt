GunaFramework EULA:


License Type
Activation Code is per developer meaning each developer should have a unique activation code. Each code allows up-to two(2) activations (two machines/devices/PCs). If you are collaborating you can have two devices activated using the same activation code.

Subscription
All renewals are paid annually from date of purchase

No run time limitations
When the activation code expires,compiled projects including deployes systems, software, projects developed using our framework will continue to function without any hindrances.

Feature updates
Free feature updates will be made available to all specific products within an active subscription period.

Source code
We do not provide the product (Framework) source code.

Royalties and distribution
You will not pay any royalties for applications you make and deploy using our framework. You can sell and distribute applications you make using our framework, royalty free! No attribution is required.

Resetting and transfers
If you bought a new PC or need to transfer your license to another machine, you can use the framework manager to manage (activate, deactivate and reactivate) your activation codes. Wehave made this for your convenience.

Post expiry
Your compiled applications will continue running even after expiry of license. However, your will not be able to edit your project / solution in design time.

Bug fixing
Will be handled on case by case basis which will dictate the timeframe for resolving bugs.

Free Trial
We provide a 14-day free trial for all users. Upon trial expiry, you will need to purchase to continue using the product.

Activation process
All activations are online. This means you will need internet to activate. There are no offline activations. During activation process we will collect personal info such as your email so that we activate your code. Please see our privacy policy.

Licensing changes
We reserve the right to modify the licensing from time to time without notice.Please visit this section of the website to keep up-to-date with latest terms.

Last updated 29 May, 2021

