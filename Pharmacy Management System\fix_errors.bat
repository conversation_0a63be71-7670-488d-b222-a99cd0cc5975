@echo off
echo ========================================
echo   حل أخطاء مشروع الصيدلية
echo ========================================
echo.

echo 🗑️  تنظيف الملفات المؤقتة...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if exist ".vs" rmdir /s /q ".vs"

echo.
echo 🔨 البحث عن MSBuild...

REM البحث عن Visual Studio 2022
set "MSBUILD_PATH="
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
)
if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
)

REM البحث عن Visual Studio 2019
if "%MSBUILD_PATH%"=="" (
    if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
    )
)

if "%MSBUILD_PATH%"=="" (
    echo ❌ لم يتم العثور على MSBuild
    echo يرجى تثبيت Visual Studio أو Build Tools
    pause
    exit /b 1
)

echo ✅ تم العثور على MSBuild: %MSBUILD_PATH%
echo.

echo 🔨 إعادة بناء المشروع...
"%MSBUILD_PATH%" "Pharmacy Management System.sln" /p:Configuration=Debug /p:Platform="Any CPU" /t:Rebuild /verbosity:minimal

echo.
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء المشروع بنجاح!
    echo.
    echo 🚀 تشغيل البرنامج...
    if exist "bin\Debug\Pharmacy Management System.exe" (
        start "" "bin\Debug\Pharmacy Management System.exe"
    ) else (
        echo ⚠️  لم يتم العثور على الملف التنفيذي
    )
) else (
    echo ❌ فشل في بناء المشروع!
    echo تحقق من الأخطاء في Visual Studio
)

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
