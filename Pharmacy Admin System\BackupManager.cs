using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Data.SqlClient;

namespace Pharmacy_Admin_System
{
    /// <summary>
    /// مدير النسخ الاحتياطية - إدارة النسخ الاحتياطية لقواعد بيانات الصيدليات
    /// Backup Manager - Manages backup operations for pharmacy databases
    /// </summary>
    public class BackupManager
    {
        private DatabaseManager dbManager;
        private readonly string backupBasePath;

        public BackupManager()
        {
            dbManager = new DatabaseManager();
            backupBasePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), 
                "Pharmacy Backups");
            
            // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
            if (!Directory.Exists(backupBasePath))
            {
                Directory.CreateDirectory(backupBasePath);
            }
        }

        #region Backup Operations

        /// <summary>
        /// إنشاء نسخة احتياطية لقاعدة بيانات صيدلية
        /// </summary>
        public string CreatePharmacyBackup(int pharmacyId, string backupType, int createdBy)
        {
            try
            {
                // الحصول على معلومات الصيدلية
                var pharmacyInfo = GetPharmacyInfo(pharmacyId);
                if (pharmacyInfo == null)
                {
                    throw new Exception("الصيدلية غير موجودة");
                }

                string pharmacyCode = pharmacyInfo["pharmacyCode"].ToString();
                string pharmacyName = pharmacyInfo["pharmacyName"].ToString();
                
                // إنشاء مجلد خاص بالصيدلية
                string pharmacyBackupPath = Path.Combine(backupBasePath, pharmacyCode);
                if (!Directory.Exists(pharmacyBackupPath))
                {
                    Directory.CreateDirectory(pharmacyBackupPath);
                }

                // تحديد اسم ملف النسخة الاحتياطية
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string backupFileName = $"{pharmacyCode}_{backupType}_{timestamp}.bak";
                string backupFilePath = Path.Combine(pharmacyBackupPath, backupFileName);

                // تنفيذ النسخة الاحتياطية
                bool success = PerformDatabaseBackup("UnifiedPharmacy", backupFilePath, backupType);

                if (success)
                {
                    // حساب حجم الملف
                    long fileSize = new FileInfo(backupFilePath).Length;

                    // تسجيل النسخة الاحتياطية في قاعدة البيانات
                    RecordBackupHistory(pharmacyId, backupType, backupFilePath, fileSize, "Completed", null, createdBy);

                    // تحديث تاريخ آخر نسخة احتياطية للصيدلية
                    UpdateLastBackupDate(pharmacyId);

                    // تسجيل النشاط
                    dbManager.LogActivity(createdBy, pharmacyId, "Backup Created", 
                        $"تم إنشاء نسخة احتياطية {backupType} للصيدلية {pharmacyName}");

                    return backupFilePath;
                }
                else
                {
                    throw new Exception("فشل في إنشاء النسخة الاحتياطية");
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ في قاعدة البيانات
                RecordBackupHistory(pharmacyId, backupType, "", 0, "Failed", ex.Message, createdBy);
                throw new Exception($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تنفيذ النسخة الاحتياطية لقاعدة البيانات
        /// </summary>
        private bool PerformDatabaseBackup(string databaseName, string backupPath, string backupType)
        {
            try
            {
                string backupCommand = "";
                
                switch (backupType.ToLower())
                {
                    case "full":
                        backupCommand = $@"
                            BACKUP DATABASE [{databaseName}] 
                            TO DISK = '{backupPath}' 
                            WITH FORMAT, INIT, COMPRESSION, 
                            NAME = 'Full Backup of {databaseName}',
                            DESCRIPTION = 'Full backup created by Pharmacy Admin System'";
                        break;
                        
                    case "differential":
                        backupCommand = $@"
                            BACKUP DATABASE [{databaseName}] 
                            TO DISK = '{backupPath}' 
                            WITH DIFFERENTIAL, FORMAT, INIT, COMPRESSION,
                            NAME = 'Differential Backup of {databaseName}',
                            DESCRIPTION = 'Differential backup created by Pharmacy Admin System'";
                        break;
                        
                    case "log":
                        backupCommand = $@"
                            BACKUP LOG [{databaseName}] 
                            TO DISK = '{backupPath}' 
                            WITH FORMAT, INIT, COMPRESSION,
                            NAME = 'Log Backup of {databaseName}',
                            DESCRIPTION = 'Transaction log backup created by Pharmacy Admin System'";
                        break;
                        
                    default:
                        throw new Exception($"نوع النسخة الاحتياطية غير مدعوم: {backupType}");
                }

                using (var connection = dbManager.GetPharmacyConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(backupCommand, connection))
                    {
                        command.CommandTimeout = 300; // 5 دقائق
                        command.ExecuteNonQuery();
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تنفيذ النسخة الاحتياطية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// استعادة قاعدة بيانات من نسخة احتياطية
        /// </summary>
        public bool RestorePharmacyBackup(int pharmacyId, string backupFilePath, int restoredBy, bool replaceExisting = false)
        {
            try
            {
                // التحقق من وجود ملف النسخة الاحتياطية
                if (!File.Exists(backupFilePath))
                {
                    throw new Exception("ملف النسخة الاحتياطية غير موجود");
                }

                // الحصول على معلومات الصيدلية
                var pharmacyInfo = GetPharmacyInfo(pharmacyId);
                if (pharmacyInfo == null)
                {
                    throw new Exception("الصيدلية غير موجودة");
                }

                string databaseName = "UnifiedPharmacy";
                
                // تنفيذ الاستعادة
                string restoreCommand = $@"
                    RESTORE DATABASE [{databaseName}] 
                    FROM DISK = '{backupFilePath}' 
                    WITH {(replaceExisting ? "REPLACE," : "")} 
                    STATS = 10";

                using (var connection = dbManager.GetAdminConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(restoreCommand, connection))
                    {
                        command.CommandTimeout = 600; // 10 دقائق
                        command.ExecuteNonQuery();
                    }
                }

                // تسجيل النشاط
                dbManager.LogActivity(restoredBy, pharmacyId, "Database Restored", 
                    $"تم استعادة قاعدة البيانات من النسخة الاحتياطية: {Path.GetFileName(backupFilePath)}");

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استعادة النسخة الاحتياطية: {ex.Message}", ex);
            }
        }

        #endregion

        #region Backup History

        /// <summary>
        /// تسجيل تاريخ النسخة الاحتياطية
        /// </summary>
        private void RecordBackupHistory(int pharmacyId, string backupType, string backupPath, 
            long backupSize, string status, string errorMessage, int createdBy)
        {
            try
            {
                string command = @"
                    INSERT INTO backup_history 
                    (pharmacyId, backupType, backupPath, backupSize, backupDate, status, errorMessage, createdBy)
                    VALUES 
                    (@pharmacyId, @backupType, @backupPath, @backupSize, GETDATE(), @status, @errorMessage, @createdBy)";

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", pharmacyId},
                    {"@backupType", backupType},
                    {"@backupPath", backupPath},
                    {"@backupSize", backupSize},
                    {"@status", status},
                    {"@errorMessage", errorMessage},
                    {"@createdBy", createdBy}
                };

                dbManager.ExecuteAdminCommand(command, parameters);
            }
            catch
            {
                // تجاهل أخطاء تسجيل التاريخ
            }
        }

        /// <summary>
        /// الحصول على تاريخ النسخ الاحتياطية لصيدلية
        /// </summary>
        public DataTable GetPharmacyBackupHistory(int pharmacyId)
        {
            try
            {
                string query = @"
                    SELECT 
                        bh.id,
                        bh.backupType,
                        bh.backupPath,
                        bh.backupSize,
                        bh.backupDate,
                        bh.status,
                        bh.errorMessage,
                        au.fullName as createdByName,
                        rp.pharmacyName
                    FROM backup_history bh
                    LEFT JOIN admin_users au ON bh.createdBy = au.id
                    LEFT JOIN registered_pharmacies rp ON bh.pharmacyId = rp.id
                    WHERE bh.pharmacyId = @pharmacyId
                    ORDER BY bh.backupDate DESC";

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", pharmacyId}
                };

                return dbManager.ExecuteAdminQuery(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب تاريخ النسخ الاحتياطية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على جميع النسخ الاحتياطية
        /// </summary>
        public DataTable GetAllBackupHistory()
        {
            try
            {
                string query = @"
                    SELECT 
                        bh.id,
                        rp.pharmacyName,
                        rp.pharmacyCode,
                        bh.backupType,
                        bh.backupSize,
                        bh.backupDate,
                        bh.status,
                        au.fullName as createdByName
                    FROM backup_history bh
                    LEFT JOIN registered_pharmacies rp ON bh.pharmacyId = rp.id
                    LEFT JOIN admin_users au ON bh.createdBy = au.id
                    ORDER BY bh.backupDate DESC";

                return dbManager.ExecuteAdminQuery(query);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب تاريخ النسخ الاحتياطية: {ex.Message}", ex);
            }
        }

        #endregion

        #region Maintenance

        /// <summary>
        /// تنظيف النسخ الاحتياطية القديمة
        /// </summary>
        public int CleanupOldBackups(int retentionDays = 90)
        {
            try
            {
                int deletedCount = 0;

                // الحصول على النسخ الاحتياطية القديمة
                string query = @"
                    SELECT id, backupPath 
                    FROM backup_history 
                    WHERE backupDate < DATEADD(day, -@retentionDays, GETDATE())
                      AND status = 'Completed'";

                var parameters = new Dictionary<string, object>
                {
                    {"@retentionDays", retentionDays}
                };

                var oldBackups = dbManager.ExecuteAdminQuery(query, parameters);

                foreach (DataRow row in oldBackups.Rows)
                {
                    try
                    {
                        string backupPath = row["backupPath"].ToString();
                        int backupId = Convert.ToInt32(row["id"]);

                        // حذف الملف إذا كان موجوداً
                        if (File.Exists(backupPath))
                        {
                            File.Delete(backupPath);
                        }

                        // حذف السجل من قاعدة البيانات
                        string deleteCommand = "DELETE FROM backup_history WHERE id = @backupId";
                        var deleteParams = new Dictionary<string, object> { {"@backupId", backupId} };
                        dbManager.ExecuteAdminCommand(deleteCommand, deleteParams);

                        deletedCount++;
                    }
                    catch
                    {
                        // تجاهل أخطاء حذف الملفات الفردية
                    }
                }

                return deletedCount;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنظيف النسخ الاحتياطية القديمة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث تاريخ آخر نسخة احتياطية للصيدلية
        /// </summary>
        private void UpdateLastBackupDate(int pharmacyId)
        {
            try
            {
                string command = @"
                    UPDATE registered_pharmacies 
                    SET lastBackupDate = GETDATE() 
                    WHERE id = @pharmacyId";

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", pharmacyId}
                };

                dbManager.ExecuteAdminCommand(command, parameters);
            }
            catch
            {
                // تجاهل أخطاء التحديث
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// الحصول على معلومات الصيدلية
        /// </summary>
        private DataRow GetPharmacyInfo(int pharmacyId)
        {
            try
            {
                string query = @"
                    SELECT id, pharmacyName, pharmacyCode, databaseName 
                    FROM registered_pharmacies 
                    WHERE id = @pharmacyId";

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", pharmacyId}
                };

                var result = dbManager.ExecuteAdminQuery(query, parameters);
                return result.Rows.Count > 0 ? result.Rows[0] : null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// تحويل حجم الملف إلى نص قابل للقراءة
        /// </summary>
        public static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// الحصول على مسار مجلد النسخ الاحتياطية
        /// </summary>
        public string GetBackupBasePath()
        {
            return backupBasePath;
        }

        #endregion

        #region Statistics

        /// <summary>
        /// الحصول على إحصائيات النسخ الاحتياطية
        /// </summary>
        public BackupStatistics GetBackupStatistics()
        {
            try
            {
                var stats = new BackupStatistics();

                // إجمالي النسخ الاحتياطية
                var totalQuery = "SELECT COUNT(*) FROM backup_history WHERE status = 'Completed'";
                stats.TotalBackups = Convert.ToInt32(dbManager.ExecuteAdminScalar(totalQuery));

                // النسخ الاحتياطية هذا الشهر
                var monthlyQuery = @"
                    SELECT COUNT(*) FROM backup_history 
                    WHERE status = 'Completed' 
                      AND MONTH(backupDate) = MONTH(GETDATE()) 
                      AND YEAR(backupDate) = YEAR(GETDATE())";
                stats.MonthlyBackups = Convert.ToInt32(dbManager.ExecuteAdminScalar(monthlyQuery));

                // النسخ الاحتياطية الفاشلة
                var failedQuery = "SELECT COUNT(*) FROM backup_history WHERE status = 'Failed'";
                stats.FailedBackups = Convert.ToInt32(dbManager.ExecuteAdminScalar(failedQuery));

                // إجمالي حجم النسخ الاحتياطية
                var sizeQuery = "SELECT ISNULL(SUM(backupSize), 0) FROM backup_history WHERE status = 'Completed'";
                stats.TotalBackupSize = Convert.ToInt64(dbManager.ExecuteAdminScalar(sizeQuery));

                return stats;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب إحصائيات النسخ الاحتياطية: {ex.Message}", ex);
            }
        }

        #endregion
    }

    #region Data Classes

    /// <summary>
    /// إحصائيات النسخ الاحتياطية
    /// </summary>
    public class BackupStatistics
    {
        public int TotalBackups { get; set; }
        public int MonthlyBackups { get; set; }
        public int FailedBackups { get; set; }
        public long TotalBackupSize { get; set; }
    }

    #endregion
}
