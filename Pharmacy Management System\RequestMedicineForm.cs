using System;
using System.Drawing;
using System.Windows.Forms;

namespace Pharmacy_Management_System
{
    public partial class RequestMedicineForm : Form
    {
        public int RequestQuantity { get; private set; }
        public string RequestMessage { get; private set; }

        private string medicineName;
        private int availableQuantity;
        private string pharmacyName;
        private string pharmacyPhone;

        public RequestMedicineForm(string medicineName, int availableQuantity, string pharmacyName, string pharmacyPhone)
        {
            InitializeComponent();
            
            this.medicineName = medicineName;
            this.availableQuantity = availableQuantity;
            this.pharmacyName = pharmacyName;
            this.pharmacyPhone = pharmacyPhone;
            
            InitializeForm();
            ApplyLanguage();
            ApplyModernDesign();
        }

        private void InitializeForm()
        {
            // إعداد النموذج
            this.Text = "طلب دواء من صيدلية";
            this.Size = new Size(550, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.WindowState = FormWindowState.Normal;

            // عرض معلومات الدواء والصيدلية
            lblMedicineName.Text = $"اسم الدواء: {medicineName}";
            lblQuantityAvailable.Text = $"الكمية المتاحة: {availableQuantity}";
            lblPharmacyName.Text = $"الصيدلية: {pharmacyName}";
            lblPharmacyPhone.Text = $"رقم التواصل: {pharmacyPhone}";

            // إعداد عناصر التحكم
            numQuantityRequested.Maximum = availableQuantity;
            numQuantityRequested.Minimum = 1;
            numQuantityRequested.Value = Math.Min(5, availableQuantity);
        }

        private void ApplyLanguage()
        {
            try
            {
                this.Text = LanguageManager.GetText("Request Medicine");
                lblTitle.Text = LanguageManager.GetText("Request Medicine");
                lblQuantityRequested.Text = LanguageManager.GetText("Request Quantity");
                lblMessageLabel.Text = LanguageManager.GetText("Request Message (Optional)");
                btnSendRequest.Text = LanguageManager.GetText("Send Request");
                btnCancel.Text = LanguageManager.GetText("Cancel");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق اللغة: {ex.Message}");
            }
        }

        private void ApplyModernDesign()
        {
            try
            {
                // تطبيق التصميم العصري
                this.BackColor = Color.White;
                this.ForeColor = Color.Black;

                // تطبيق التصميم على التسميات
                foreach (Control control in this.Controls)
                {
                    if (control is Label)
                    {
                        control.ForeColor = Color.Black;
                    }
                    else if (control is Panel)
                    {
                        control.BackColor = Color.White;
                        foreach (Control subControl in control.Controls)
                        {
                            if (subControl is Label)
                            {
                                subControl.ForeColor = Color.Black;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق التصميم العصري: {ex.Message}");
            }
        }

        private void btnSendRequest_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (numQuantityRequested.Value <= 0)
                {
                    MessageBox.Show("يجب أن تكون الكمية المطلوبة أكبر من صفر", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (numQuantityRequested.Value > availableQuantity)
                {
                    MessageBox.Show($"الكمية المطلوبة لا يمكن أن تكون أكبر من {availableQuantity}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // حفظ البيانات
                RequestQuantity = (int)numQuantityRequested.Value;
                RequestMessage = txtMessage.Text.Trim();

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إرسال الطلب: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }



        private void numQuantityRequested_ValueChanged(object sender, EventArgs e)
        {
            // تحديث معلومات الكمية
            if (numQuantityRequested.Value > availableQuantity)
            {
                numQuantityRequested.Value = availableQuantity;
            }
        }

        private void panelMain_Paint(object sender, PaintEventArgs e)
        {

        }
    }
}
