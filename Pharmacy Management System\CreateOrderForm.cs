using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy_Management_System
{
    public partial class CreateOrderForm : Form
    {
        private OnlineNetworkManager networkManager;
        private int medicineId;
        private string medicineName;
        private string pharmacyName;
        private bool isLoading = false;

        public CreateOrderForm(int medicineId, string medicineName, string pharmacyName)
        {
            InitializeComponent();
            this.medicineId = medicineId;
            this.medicineName = medicineName;
            this.pharmacyName = pharmacyName;
            
            networkManager = OnlineNetworkManager.Instance;
            
            // تطبيق التصميم العصري
            ApplyModernDesign();
            
            // تطبيق اللغة
            ApplyLanguage();
        }

        private void CreateOrderForm_Load(object sender, EventArgs e)
        {
            // تحديث معلومات الدواء
            lblMedicineName.Text = string.Format("{0}: {1}", LanguageManager.GetText("Medicine"), medicineName);
            lblPharmacyName.Text = string.Format("{0}: {1}", LanguageManager.GetText("From Pharmacy"), pharmacyName);
            
            // تركيز على حقل الكمية
            numericUpDownQuantity.Focus();
        }

        private async void btnCreateOrder_Click(object sender, EventArgs e)
        {
            await CreateOrder();
        }

        private async Task CreateOrder()
        {
            if (isLoading) return;

            // التحقق من صحة البيانات
            if (numericUpDownQuantity.Value <= 0)
            {
                MessageBox.Show(LanguageManager.GetText("Please enter valid quantity"), 
                    LanguageManager.GetText("Warning"), MessageBoxButtons.OK, MessageBoxIcon.Warning);
                numericUpDownQuantity.Focus();
                return;
            }

            try
            {
                isLoading = true;
                SetLoadingState(true);

                // إنشاء الطلب (هنا يمكن إضافة الكود لحفظ الطلب في قاعدة البيانات)
                string orderNumber = string.Format("ORD_{0:yyyyMMddHHmmss}", DateTime.Now);

                // محاكاة إنشاء الطلب
                await Task.Delay(1000);

                // إرسال إشعار للصيدلية البائعة
                string notificationMessage = string.Format("{0}\n{1}: {2}\n{3}: {4}\n{5}: {6}",
                    LanguageManager.GetText("New order received"),
                    LanguageManager.GetText("Medicine"), medicineName,
                    LanguageManager.GetText("Quantity"), numericUpDownQuantity.Value,
                    LanguageManager.GetText("Notes"), txtNotes.Text);

                // تسجيل النشاط
                await networkManager.LogActivity("Order", string.Format("إنشاء طلب جديد للدواء: {0}", medicineName), "Medicine", medicineId);

                MessageBox.Show(string.Format("{0}\n{1}: {2}",
                              LanguageManager.GetText("Order created successfully"),
                              LanguageManager.GetText("Order Number"), orderNumber),
                              LanguageManager.GetText("Success"), MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Error creating order") + ": " + ex.Message, 
                    LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                isLoading = false;
                SetLoadingState(false);
            }
        }

        private void SetLoadingState(bool loading)
        {
            btnCreateOrder.Enabled = !loading;
            btnCancel.Enabled = !loading;
            numericUpDownQuantity.Enabled = !loading;
            txtNotes.Enabled = !loading;

            if (loading)
            {
                btnCreateOrder.Text = LanguageManager.GetText("Creating...");
                this.Cursor = Cursors.WaitCursor;
            }
            else
            {
                btnCreateOrder.Text = LanguageManager.GetText("Create Order");
                this.Cursor = Cursors.Default;
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void ApplyModernDesign()
        {
            try
            {
                // تطبيق التصميم العصري
                this.BackColor = ModernTheme.BackgroundColor;
                this.ForeColor = ModernTheme.PrimaryTextColor;
            }
            catch (Exception)
            {
                // تجاهل أخطاء التصميم
            }
        }

        private void ApplyLanguage()
        {
            try
            {
                // تحديث النصوص حسب اللغة المختارة
                this.Text = LanguageManager.GetText("Create Order");
                
                if (lblTitle != null) lblTitle.Text = LanguageManager.GetText("Create New Order");
                if (lblQuantity != null) lblQuantity.Text = LanguageManager.GetText("Quantity");
                if (lblNotes != null) lblNotes.Text = LanguageManager.GetText("Notes");
                if (txtNotes != null) txtNotes.PlaceholderText = LanguageManager.GetText("Enter any notes or special requirements...");
                if (btnCreateOrder != null) btnCreateOrder.Text = LanguageManager.GetText("Create Order");
                if (btnCancel != null) btnCancel.Text = LanguageManager.GetText("Cancel");

                // الحفاظ على التصميم من اليسار لليمين في جميع اللغات
                this.RightToLeft = RightToLeft.No;
            }
            catch (Exception)
            {
                // تجاهل أخطاء الترجمة
            }
        }

        private void CreateOrderForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (isLoading)
            {
                e.Cancel = true;
                MessageBox.Show(LanguageManager.GetText("Please wait for order creation to complete"), 
                    LanguageManager.GetText("Please Wait"), MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void panelMain_Paint(object sender, PaintEventArgs e)
        {

        }
    }
}
