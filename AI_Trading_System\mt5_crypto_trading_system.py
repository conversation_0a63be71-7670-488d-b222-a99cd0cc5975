#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام تداول العملات الرقمية على MetaTrader 5
MT5 Cryptocurrency Trading System
"""

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import configparser
import logging
import time
import json
from typing import Dict, List, Optional
import ta
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
import pickle
import os

class MT5CryptoTradingSystem:
    def __init__(self, demo_mode: bool = True):
        self.demo_mode = demo_mode
        self.connected = False
        self.account_info = None
        
        # العملات الرقمية المدعومة في MT5 (الرموز الحقيقية المتوفرة)
        self.crypto_symbols = [
            'BTC',      # Bitcoin - متوفر للتداول
            'ETH',      # Ethereum - متوفر للتداول
            'BTCL',     # Bitcoin Long
            'BTCO',     # Bitcoin Options ETF
            'BTCW',     # Bitcoin Weekly ETF
            'BTCZ',     # Bitcoin Zero
            'AETH',     # Ethereum Alternative ETF
            'BETH',     # Ethereum Beta ETF
            'EETH',     # Ethereum Extended ETF
            'ETHD',     # Ethereum Daily
            'ETHE',     # Grayscale Ethereum Trust
            'ETHO',     # Ethereum Climate ETF
            'ETHT',     # Ethereum Trust
            'ETHU',     # Ethereum Strategy ETF
            'ETHV',     # Ethereum Volatility
            'ETHW',     # Ethereum Weekly
            'FBTC',     # Fidelity Bitcoin Fund
            'FETH',     # Fidelity Ethereum
            'GBTC',     # Grayscale Bitcoin Trust
            'NETH25'    # Ethereum 25
        ]

        # عملات بديلة للاختبار إذا لم تكن العملات الرقمية متوفرة
        self.fallback_symbols = [
            'EURUSD',   # Euro/US Dollar
            'GBPUSD',   # British Pound/US Dollar
            'USDJPY',   # US Dollar/Japanese Yen
            'USDCHF',   # US Dollar/Swiss Franc
            'AUDUSD',   # Australian Dollar/US Dollar
            'USDCAD',   # US Dollar/Canadian Dollar
            'NZDUSD',   # New Zealand Dollar/US Dollar
            'EURJPY',   # Euro/Japanese Yen
            'GBPJPY',   # British Pound/Japanese Yen
            'EURGBP'    # Euro/British Pound
        ]
        
        self.current_symbol = 'BTCUSD'
        self.positions = {}
        self.trade_history = []
        self.learning_data = []
        
        # نماذج التعلم الآلي
        self.price_model = None
        self.scaler = StandardScaler()
        self.model_trained = False
        
        # إعداد السجلات
        self.logger = self._setup_logger()
        self.load_config()
        self.load_learning_data()
        
    def _setup_logger(self):
        """إعداد نظام السجلات"""
        logger = logging.getLogger('MT5CryptoTrading')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            os.makedirs('logs', exist_ok=True)
            handler = logging.FileHandler(f'logs/mt5_crypto_trading_{datetime.now().strftime("%Y%m%d")}.log')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
        
    def load_config(self):
        """تحميل الإعدادات"""
        config = configparser.ConfigParser()

        if os.path.exists('mt5_crypto_config.ini'):
            config.read('mt5_crypto_config.ini', encoding='utf-8')
        else:
            self.create_default_config()
            config.read('mt5_crypto_config.ini', encoding='utf-8')
            
        # إعدادات MT5
        self.login = int(config.get('MT5_CONNECTION', 'login', fallback='96406085'))
        self.password = config.get('MT5_CONNECTION', 'password', fallback='D!2qKdJy')
        self.server = config.get('MT5_CONNECTION', 'server', fallback='MetaQuotes-Demo')
        
        # إعدادات التداول
        self.lot_size = float(config.get('TRADING', 'lot_size', fallback='0.01'))
        self.min_confidence = float(config.get('TRADING', 'min_confidence', fallback='0.6'))
        self.max_positions = int(config.get('TRADING', 'max_positions', fallback='5'))
        self.stop_loss_pips = int(config.get('TRADING', 'stop_loss_pips', fallback='500'))
        self.take_profit_pips = int(config.get('TRADING', 'take_profit_pips', fallback='1000'))
        self.risk_per_trade = float(config.get('TRADING', 'risk_per_trade', fallback='0.02'))
        
    def create_default_config(self):
        """إنشاء ملف إعدادات افتراضي"""
        config = configparser.ConfigParser()
        
        # إعدادات MT5
        config.add_section('MT5_CONNECTION')
        config.set('MT5_CONNECTION', 'login', '96406085')
        config.set('MT5_CONNECTION', 'password', 'D!2qKdJy')
        config.set('MT5_CONNECTION', 'server', 'MetaQuotes-Demo')
        
        # إعدادات التداول
        config.add_section('TRADING')
        config.set('TRADING', 'lot_size', '0.01')
        config.set('TRADING', 'min_confidence', '0.6')
        config.set('TRADING', 'max_positions', '5')
        config.set('TRADING', 'stop_loss_pips', '500')
        config.set('TRADING', 'take_profit_pips', '1000')
        config.set('TRADING', 'risk_per_trade', '0.02')
        
        # إعدادات التعلم
        config.add_section('LEARNING')
        config.set('LEARNING', 'learning_enabled', 'true')
        config.set('LEARNING', 'retrain_interval', '24')
        config.set('LEARNING', 'min_data_points', '50')
        
        with open('mt5_crypto_config.ini', 'w', encoding='utf-8') as f:
            config.write(f)
            
    def connect_mt5(self) -> bool:
        """الاتصال بـ MetaTrader 5"""
        try:
            self.logger.info("🔌 محاولة الاتصال بـ MetaTrader 5...")
            
            # تهيئة MT5
            if not mt5.initialize():
                error = mt5.last_error()
                self.logger.error(f"❌ فشل في تهيئة MT5: {error}")
                return False
                
            # تسجيل الدخول
            if not mt5.login(self.login, self.password, self.server):
                error = mt5.last_error()
                self.logger.error(f"❌ فشل في تسجيل الدخول: {error}")
                return False
                
            # الحصول على معلومات الحساب
            self.account_info = mt5.account_info()
            if not self.account_info:
                self.logger.error("❌ لا يمكن الحصول على معلومات الحساب")
                return False
                
            # التحقق من صلاحيات التداول
            if not self.account_info.trade_allowed:
                self.logger.error("❌ التداول غير مسموح في هذا الحساب")
                return False
                
            self.connected = True
            self.logger.info("✅ تم الاتصال بـ MT5 بنجاح!")
            self.logger.info(f"📊 حساب: {self.account_info.login}")
            self.logger.info(f"💰 رصيد: ${self.account_info.balance:.2f}")
            self.logger.info(f"🏢 شركة: {self.account_info.company}")
            
            # فحص العملات الرقمية المتوفرة
            self.check_available_crypto_symbols()
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في الاتصال: {e}")
            return False
            
    def check_available_crypto_symbols(self):
        """فحص العملات الرقمية المتوفرة"""
        try:
            available_symbols = []

            # فحص العملات الرقمية أولاً
            self.logger.info("🔍 فحص العملات الرقمية...")
            for symbol in self.crypto_symbols:
                symbol_info = mt5.symbol_info(symbol)
                if symbol_info:
                    available_symbols.append(symbol)
                    self.logger.info(f"✅ {symbol} متوفر")
                else:
                    # محاولة إضافة الرمز
                    if mt5.symbol_select(symbol, True):
                        available_symbols.append(symbol)
                        self.logger.info(f"✅ {symbol} تم إضافته")
                    else:
                        self.logger.warning(f"⚠️ {symbol} غير متوفر")

            # إذا لم تكن العملات الرقمية متوفرة، استخدم العملات البديلة
            if not available_symbols:
                self.logger.warning("⚠️ لا توجد عملات رقمية متوفرة، جاري فحص العملات البديلة...")

                for symbol in self.fallback_symbols:
                    symbol_info = mt5.symbol_info(symbol)
                    if symbol_info:
                        available_symbols.append(symbol)
                        self.logger.info(f"✅ {symbol} (بديل) متوفر")
                    else:
                        if mt5.symbol_select(symbol, True):
                            available_symbols.append(symbol)
                            self.logger.info(f"✅ {symbol} (بديل) تم إضافته")
                        else:
                            self.logger.warning(f"⚠️ {symbol} (بديل) غير متوفر")

                if available_symbols:
                    self.logger.info("💡 سيتم استخدام العملات البديلة للتداول والاختبار")

            self.crypto_symbols = available_symbols
            self.logger.info(f"📊 الرموز المتوفرة للتداول: {len(self.crypto_symbols)}")

            if not self.crypto_symbols:
                self.logger.error("❌ لا توجد رموز متوفرة للتداول")
                return False

            # تعيين الرمز الافتراضي
            if self.current_symbol not in self.crypto_symbols and self.crypto_symbols:
                self.current_symbol = self.crypto_symbols[0]
                self.logger.info(f"🔄 تم تغيير الرمز الافتراضي إلى: {self.current_symbol}")

            return True

        except Exception as e:
            self.logger.error(f"❌ خطأ في فحص الرموز: {e}")
            return False
            
    def get_crypto_data(self, symbol: str, timeframe=mt5.TIMEFRAME_H1, count: int = 100) -> Optional[pd.DataFrame]:
        """الحصول على بيانات العملة الرقمية"""
        try:
            if not self.connected:
                self.logger.error("❌ غير متصل بـ MT5")
                return None
                
            # الحصول على البيانات
            rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, count)
            
            if rates is None or len(rates) == 0:
                self.logger.error(f"❌ لا يمكن الحصول على بيانات {symbol}")
                return None
                
            # تحويل إلى DataFrame
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            df.set_index('time', inplace=True)
            
            # إعادة تسمية الأعمدة
            df.rename(columns={
                'open': 'open',
                'high': 'high', 
                'low': 'low',
                'close': 'close',
                'tick_volume': 'volume'
            }, inplace=True)
            
            return df[['open', 'high', 'low', 'close', 'volume']]
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في الحصول على البيانات: {e}")
            return None
            
    def calculate_crypto_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """حساب المؤشرات الفنية للعملات الرقمية"""
        try:
            # المتوسطات المتحركة
            df['sma_20'] = ta.trend.sma_indicator(df['close'], window=20)
            df['sma_50'] = ta.trend.sma_indicator(df['close'], window=50)
            df['ema_12'] = ta.trend.ema_indicator(df['close'], window=12)
            df['ema_26'] = ta.trend.ema_indicator(df['close'], window=26)
            
            # MACD
            df['macd'] = ta.trend.macd_diff(df['close'])
            df['macd_signal'] = ta.trend.macd_signal(df['close'])
            
            # RSI
            df['rsi'] = ta.momentum.rsi(df['close'], window=14)
            
            # Bollinger Bands
            bb = ta.volatility.BollingerBands(df['close'])
            df['bb_upper'] = bb.bollinger_hband()
            df['bb_lower'] = bb.bollinger_lband()
            df['bb_middle'] = bb.bollinger_mavg()
            
            # Stochastic
            df['stoch_k'] = ta.momentum.stoch(df['high'], df['low'], df['close'])
            df['stoch_d'] = ta.momentum.stoch_signal(df['high'], df['low'], df['close'])
            
            # ATR للتقلبات
            df['atr'] = ta.volatility.average_true_range(df['high'], df['low'], df['close'])
            
            # Volume indicators
            df['volume_sma'] = df['volume'].rolling(window=20).mean()
            
            # Price changes
            df['price_change'] = df['close'].pct_change()
            df['price_change_5'] = df['close'].pct_change(periods=5)
            
            # Crypto-specific indicators
            df['volatility'] = df['close'].rolling(window=20).std()
            df['momentum'] = df['close'] / df['close'].shift(10) - 1
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في حساب المؤشرات: {e}")
            return df
            
    def analyze_crypto_market(self, symbol: str) -> Dict:
        """تحليل سوق العملة الرقمية"""
        try:
            # الحصول على البيانات
            df = self.get_crypto_data(symbol, mt5.TIMEFRAME_H1, 100)
            if df is None or df.empty:
                return {'decision': 'hold', 'confidence': 0.0, 'reason': 'لا توجد بيانات'}
                
            # حساب المؤشرات
            df = self.calculate_crypto_indicators(df)
            
            # التحقق من اكتمال البيانات
            if df.isnull().iloc[-1].any():
                return {'decision': 'hold', 'confidence': 0.0, 'reason': 'بيانات ناقصة'}
                
            latest = df.iloc[-1]
            
            # تحليل الاتجاه
            trend_score = 0
            if latest['close'] > latest['sma_20']:
                trend_score += 1
            if latest['sma_20'] > latest['sma_50']:
                trend_score += 1
            if latest['macd'] > latest['macd_signal']:
                trend_score += 1
            if latest['close'] > latest['bb_middle']:
                trend_score += 1
                
            # تحليل الزخم
            momentum_score = 0
            if latest['rsi'] < 30:  # oversold
                momentum_score += 2
            elif latest['rsi'] > 70:  # overbought
                momentum_score -= 2
            elif 40 <= latest['rsi'] <= 60:  # neutral
                momentum_score += 1
                
            if latest['stoch_k'] < 20:
                momentum_score += 1
            elif latest['stoch_k'] > 80:
                momentum_score -= 1
                
            # تحليل التقلبات (مهم للعملات الرقمية)
            volatility = latest['volatility'] / latest['close'] * 100
            volatility_score = 0
            if volatility > 5:  # تقلبات عالية
                volatility_score -= 1
            elif volatility < 2:  # تقلبات منخفضة
                volatility_score += 1
                
            # تحليل الحجم
            volume_score = 0
            if latest['volume'] > latest['volume_sma']:
                volume_score += 1
                
            # حساب النتيجة الإجمالية
            total_score = trend_score + momentum_score + volatility_score + volume_score
            max_score = 8  # أقصى نتيجة ممكنة
            
            # حساب الثقة
            confidence = (total_score + max_score) / (2 * max_score)  # normalize to 0-1
            confidence = max(0, min(1, confidence))
            
            # تحديد القرار
            if confidence >= self.min_confidence:
                if trend_score >= 3 and momentum_score >= 0:
                    decision = 'buy'
                elif trend_score <= 1 and momentum_score <= -1:
                    decision = 'sell'
                else:
                    decision = 'hold'
            else:
                decision = 'hold'
                
            # استخدام التعلم الآلي إذا كان متوفراً
            ml_prediction = None
            if self.model_trained:
                features = self.prepare_ml_features(df)
                if features.size > 0:
                    ml_prediction = self.predict_price_movement(features)
                    if ml_prediction is not None:
                        # تعديل الثقة بناءً على توقع ML
                        if ml_prediction > 0.02 and decision in ['buy', 'hold']:
                            confidence = min(1.0, confidence + 0.1)
                            if decision == 'hold':
                                decision = 'buy'
                        elif ml_prediction < -0.02 and decision in ['sell', 'hold']:
                            confidence = min(1.0, confidence + 0.1)
                            if decision == 'hold':
                                decision = 'sell'
                                
            return {
                'decision': decision,
                'confidence': confidence,
                'symbol': symbol,
                'price': latest['close'],
                'trend_score': trend_score,
                'momentum_score': momentum_score,
                'volatility': volatility,
                'rsi': latest['rsi'],
                'macd': latest['macd'],
                'volume_ratio': latest['volume'] / latest['volume_sma'] if latest['volume_sma'] > 0 else 1,
                'ml_prediction': ml_prediction,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في تحليل السوق: {e}")
            return {'decision': 'hold', 'confidence': 0.0, 'reason': f'خطأ: {str(e)}'}
            
    def execute_crypto_trade(self, analysis: Dict) -> bool:
        """تنفيذ صفقة العملة الرقمية"""
        try:
            if not self.connected:
                self.logger.error("❌ غير متصل بـ MT5")
                return False
                
            if analysis['decision'] == 'hold':
                return True
                
            symbol = analysis['symbol']
            current_price = analysis['price']
            
            # التحقق من معلومات الرمز
            symbol_info = mt5.symbol_info(symbol)
            if not symbol_info:
                self.logger.error(f"❌ معلومات الرمز {symbol} غير متوفرة")
                return False
                
            # حساب حجم الصفقة
            account_info = mt5.account_info()
            if not account_info:
                return False
                
            balance = account_info.balance
            risk_amount = balance * self.risk_per_trade
            
            # حساب حجم اللوت بناءً على المخاطر
            pip_value = symbol_info.trade_tick_value
            stop_loss_amount = self.stop_loss_pips * pip_value
            
            if stop_loss_amount > 0:
                lot_size = min(self.lot_size, risk_amount / stop_loss_amount)
            else:
                lot_size = self.lot_size
                
            # تحديد نوع الأمر والسعر
            if analysis['decision'] == 'buy':
                order_type = mt5.ORDER_TYPE_BUY
                price = mt5.symbol_info_tick(symbol).ask
                sl = price - (self.stop_loss_pips * symbol_info.point)
                tp = price + (self.take_profit_pips * symbol_info.point)
            else:  # sell
                order_type = mt5.ORDER_TYPE_SELL
                price = mt5.symbol_info_tick(symbol).bid
                sl = price + (self.stop_loss_pips * symbol_info.point)
                tp = price - (self.take_profit_pips * symbol_info.point)
                
            # إعداد طلب التداول
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": lot_size,
                "type": order_type,
                "price": price,
                "sl": sl,
                "tp": tp,
                "deviation": 20,
                "magic": 234567,
                "comment": f"Crypto AI {analysis['decision']} - Conf: {analysis['confidence']:.2%}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_FOK,
            }
            
            self.logger.info(f"🔥 تنفيذ صفقة {analysis['decision']} - {symbol}")
            self.logger.info(f"   💰 السعر: ${price:.4f}")
            self.logger.info(f"   📊 الحجم: {lot_size}")
            self.logger.info(f"   🛡️ وقف الخسارة: ${sl:.4f}")
            self.logger.info(f"   🎯 جني الربح: ${tp:.4f}")
            self.logger.info(f"   📈 الثقة: {analysis['confidence']:.2%}")
            
            # تنفيذ الطلب
            result = mt5.order_send(request)
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                self.logger.error(f"❌ فشل في تنفيذ الصفقة: {result.retcode} - {result.comment}")
                return False
                
            self.logger.info("🎉 تم تنفيذ الصفقة بنجاح!")
            self.logger.info(f"   📋 رقم الأمر: {result.order}")
            self.logger.info(f"   🎫 رقم الصفقة: {result.deal}")
            self.logger.info(f"   📊 الحجم المنفذ: {result.volume}")
            self.logger.info(f"   💰 السعر المنفذ: ${result.price:.4f}")
            
            # حفظ معلومات الصفقة للتعلم
            trade_info = {
                'symbol': symbol,
                'type': analysis['decision'],
                'entry_price': result.price,
                'volume': result.volume,
                'sl': sl,
                'tp': tp,
                'confidence': analysis['confidence'],
                'analysis': analysis,
                'timestamp': datetime.now(),
                'order_id': result.order,
                'deal_id': result.deal
            }
            
            self.positions[result.order] = trade_info
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في تنفيذ الصفقة: {e}")
            return False

    def prepare_ml_features(self, df: pd.DataFrame) -> np.ndarray:
        """إعداد ميزات التعلم الآلي"""
        try:
            features = []

            # المؤشرات الفنية
            feature_columns = [
                'sma_20', 'sma_50', 'ema_12', 'ema_26', 'macd', 'macd_signal',
                'rsi', 'bb_upper', 'bb_lower', 'stoch_k', 'stoch_d',
                'atr', 'volume_sma', 'price_change', 'price_change_5',
                'volatility', 'momentum'
            ]

            for col in feature_columns:
                if col in df.columns:
                    value = df[col].iloc[-1]
                    features.append(value if not pd.isna(value) else 0.0)
                else:
                    features.append(0.0)

            return np.array(features).reshape(1, -1)

        except Exception as e:
            self.logger.error(f"❌ خطأ في إعداد ميزات ML: {e}")
            return np.array([]).reshape(1, -1)

    def predict_price_movement(self, features: np.ndarray) -> Optional[float]:
        """التنبؤ بحركة السعر"""
        try:
            if not self.model_trained or self.price_model is None:
                return None

            features_scaled = self.scaler.transform(features)
            prediction = self.price_model.predict(features_scaled)[0]

            return prediction

        except Exception as e:
            self.logger.error(f"❌ خطأ في التنبؤ: {e}")
            return None

    def train_ml_model(self):
        """تدريب نموذج التعلم الآلي"""
        try:
            if len(self.learning_data) < 30:
                self.logger.info("📊 بيانات غير كافية لتدريب النموذج")
                return False

            # إعداد البيانات
            X = []
            y = []

            for data_point in self.learning_data:
                if 'features' in data_point and 'target' in data_point:
                    X.append(data_point['features'])
                    y.append(data_point['target'])

            if len(X) < 20:
                return False

            X = np.array(X)
            y = np.array(y)

            # تطبيع البيانات
            X_scaled = self.scaler.fit_transform(X)

            # تدريب النموذج
            self.price_model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            )

            self.price_model.fit(X_scaled, y)
            self.model_trained = True

            # حفظ النموذج
            self.save_model()

            self.logger.info(f"✅ تم تدريب النموذج بنجاح - {len(X)} نقطة بيانات")
            return True

        except Exception as e:
            self.logger.error(f"❌ خطأ في تدريب النموذج: {e}")
            return False

    def check_open_positions(self):
        """فحص الصفقات المفتوحة وتحديث بيانات التعلم"""
        try:
            if not self.connected:
                return

            # الحصول على الصفقات المفتوحة من MT5
            positions = mt5.positions_get()
            if positions is None:
                positions = []

            # الحصول على تاريخ الصفقات المغلقة
            deals = mt5.history_deals_get(datetime.now() - timedelta(hours=1), datetime.now())
            if deals is None:
                deals = []

            # معالجة الصفقات المغلقة للتعلم
            for deal in deals:
                if deal.symbol in self.crypto_symbols:
                    self.process_closed_deal(deal)

            # تحديث قائمة الصفقات المفتوحة
            current_positions = {}
            for pos in positions:
                if pos.symbol in self.crypto_symbols:
                    current_positions[pos.ticket] = {
                        'symbol': pos.symbol,
                        'type': 'buy' if pos.type == mt5.POSITION_TYPE_BUY else 'sell',
                        'volume': pos.volume,
                        'price_open': pos.price_open,
                        'price_current': pos.price_current,
                        'profit': pos.profit,
                        'sl': pos.sl,
                        'tp': pos.tp,
                        'time': datetime.fromtimestamp(pos.time)
                    }

            self.positions = current_positions

        except Exception as e:
            self.logger.error(f"❌ خطأ في فحص الصفقات: {e}")

    def process_closed_deal(self, deal):
        """معالجة صفقة مغلقة لإضافة بيانات التعلم"""
        try:
            if deal.type not in [mt5.DEAL_TYPE_BUY, mt5.DEAL_TYPE_SELL]:
                return

            # الحصول على البيانات التاريخية وقت الصفقة
            df = self.get_crypto_data(deal.symbol, mt5.TIMEFRAME_H1, 50)
            if df is None:
                return

            df = self.calculate_crypto_indicators(df)
            features = self.prepare_ml_features(df)

            if features.size > 0:
                # حساب نتيجة الصفقة (نسبة الربح/الخسارة)
                if deal.profit != 0 and deal.volume > 0:
                    # تقدير نسبة الربح/الخسارة
                    price_change = deal.profit / (deal.volume * deal.price * 100)  # تقريبي

                    learning_point = {
                        'features': features.flatten(),
                        'target': price_change,
                        'timestamp': datetime.fromtimestamp(deal.time),
                        'symbol': deal.symbol,
                        'profit': deal.profit,
                        'volume': deal.volume
                    }

                    self.learning_data.append(learning_point)

                    # الاحتفاظ بآخر 1000 نقطة فقط
                    if len(self.learning_data) > 1000:
                        self.learning_data = self.learning_data[-1000:]

                    # حفظ بيانات التعلم
                    self.save_learning_data()

        except Exception as e:
            self.logger.error(f"❌ خطأ في معالجة الصفقة المغلقة: {e}")

    def save_learning_data(self):
        """حفظ بيانات التعلم"""
        try:
            os.makedirs('models', exist_ok=True)
            with open('models/mt5_crypto_learning_data.pkl', 'wb') as f:
                pickle.dump(self.learning_data, f)
        except Exception as e:
            self.logger.error(f"❌ خطأ في حفظ بيانات التعلم: {e}")

    def load_learning_data(self):
        """تحميل بيانات التعلم"""
        try:
            if os.path.exists('models/mt5_crypto_learning_data.pkl'):
                with open('models/mt5_crypto_learning_data.pkl', 'rb') as f:
                    self.learning_data = pickle.load(f)
                self.logger.info(f"✅ تم تحميل {len(self.learning_data)} نقطة تعلم")
        except Exception as e:
            self.logger.error(f"❌ خطأ في تحميل بيانات التعلم: {e}")
            self.learning_data = []

    def save_model(self):
        """حفظ النموذج"""
        try:
            os.makedirs('models', exist_ok=True)
            with open('models/mt5_crypto_price_model.pkl', 'wb') as f:
                pickle.dump(self.price_model, f)
            with open('models/mt5_crypto_scaler.pkl', 'wb') as f:
                pickle.dump(self.scaler, f)
        except Exception as e:
            self.logger.error(f"❌ خطأ في حفظ النموذج: {e}")

    def load_model(self):
        """تحميل النموذج"""
        try:
            if os.path.exists('models/mt5_crypto_price_model.pkl'):
                with open('models/mt5_crypto_price_model.pkl', 'rb') as f:
                    self.price_model = pickle.load(f)
                with open('models/mt5_crypto_scaler.pkl', 'rb') as f:
                    self.scaler = pickle.load(f)
                self.model_trained = True
                self.logger.info("✅ تم تحميل النموذج المدرب")
                return True
        except Exception as e:
            self.logger.error(f"❌ خطأ في تحميل النموذج: {e}")
        return False

    def run_crypto_trading_session(self, duration_minutes: int = 60):
        """تشغيل جلسة تداول العملات الرقمية"""
        try:
            if not self.connect_mt5():
                return False

            self.logger.info(f"🚀 بدء جلسة تداول العملات الرقمية - {duration_minutes} دقيقة")
            self.logger.info(f"💰 الرصيد: ${self.account_info.balance:.2f}")
            self.logger.info(f"🎯 العملات المتوفرة: {len(self.crypto_symbols)}")
            self.logger.info(f"🔄 وضع التداول: {'تجريبي' if self.demo_mode else 'حقيقي'}")

            # تحميل النموذج المدرب
            self.load_model()

            start_time = datetime.now()
            end_time = start_time + timedelta(minutes=duration_minutes)

            trade_count = 0
            analysis_count = 0

            while datetime.now() < end_time:
                try:
                    # فحص الصفقات المفتوحة
                    self.check_open_positions()

                    # تحليل العملات المختلفة
                    for symbol in self.crypto_symbols[:5]:  # تحليل أول 5 عملات
                        if len(self.positions) >= self.max_positions:
                            break

                        analysis = self.analyze_crypto_market(symbol)
                        analysis_count += 1

                        self.logger.info(f"📊 {symbol}: {analysis['decision']} - ثقة: {analysis['confidence']:.2%}")

                        # تنفيذ التداول
                        if analysis['decision'] in ['buy', 'sell'] and analysis['confidence'] >= self.min_confidence:
                            success = self.execute_crypto_trade(analysis)
                            if success:
                                trade_count += 1

                        time.sleep(10)  # انتظار بين تحليل العملات

                    # إعادة تدريب النموذج كل 10 دورات
                    if analysis_count % 50 == 0 and len(self.learning_data) > 30:
                        self.logger.info("🧠 إعادة تدريب النموذج...")
                        self.train_ml_model()

                    # انتظار قبل الدورة التالية
                    self.logger.info(f"⏰ انتظار 5 دقائق... (صفقات: {len(self.positions)}/{self.max_positions})")
                    time.sleep(300)  # 5 دقائق

                except KeyboardInterrupt:
                    self.logger.info("⏹️ تم إيقاف الجلسة بواسطة المستخدم")
                    break
                except Exception as e:
                    self.logger.error(f"❌ خطأ في الجلسة: {e}")
                    time.sleep(60)

            # ملخص الجلسة
            self.print_session_summary(start_time, trade_count, analysis_count)

        except Exception as e:
            self.logger.error(f"❌ خطأ في تشغيل الجلسة: {e}")
        finally:
            mt5.shutdown()

    def print_session_summary(self, start_time: datetime, trade_count: int, analysis_count: int):
        """طباعة ملخص الجلسة"""
        try:
            duration = datetime.now() - start_time

            print("\n" + "="*60)
            print("📊 ملخص جلسة تداول العملات الرقمية")
            print("="*60)
            print(f"⏰ مدة الجلسة: {duration}")
            print(f"🔍 عدد التحليلات: {analysis_count}")
            print(f"💼 عدد الصفقات المنفذة: {trade_count}")
            print(f"📈 الصفقات المفتوحة: {len(self.positions)}")

            # معلومات الحساب النهائية
            if self.connected:
                account_info = mt5.account_info()
                if account_info:
                    print(f"💰 الرصيد النهائي: ${account_info.balance:.2f}")
                    print(f"📊 الأسهم: ${account_info.equity:.2f}")
                    print(f"📈 الهامش المستخدم: ${account_info.margin:.2f}")

            print(f"🧠 نقاط التعلم المجمعة: {len(self.learning_data)}")
            print(f"🤖 النموذج مدرب: {'نعم' if self.model_trained else 'لا'}")

            # عرض الصفقات المفتوحة
            if self.positions:
                print(f"\n📈 الصفقات المفتوحة:")
                for ticket, pos in self.positions.items():
                    profit_status = "🟢" if pos['profit'] > 0 else "🔴" if pos['profit'] < 0 else "⚪"
                    print(f"   {profit_status} {pos['symbol']}: {pos['type']} - ربح: ${pos['profit']:.2f}")

            print("="*60)

        except Exception as e:
            self.logger.error(f"❌ خطأ في طباعة الملخص: {e}")

    def get_account_summary(self) -> Dict:
        """الحصول على ملخص الحساب"""
        try:
            if not self.connected:
                return {'error': 'غير متصل'}

            account_info = mt5.account_info()
            if not account_info:
                return {'error': 'لا يمكن الحصول على معلومات الحساب'}

            return {
                'balance': account_info.balance,
                'equity': account_info.equity,
                'margin': account_info.margin,
                'free_margin': account_info.margin_free,
                'margin_level': account_info.margin_level,
                'open_positions': len(self.positions),
                'learning_points': len(self.learning_data),
                'model_trained': self.model_trained,
                'available_cryptos': len(self.crypto_symbols),
                'current_symbol': self.current_symbol,
                'connected': self.connected
            }

        except Exception as e:
            return {'error': f'خطأ: {str(e)}'}

    def switch_symbol(self, symbol: str) -> bool:
        """تبديل العملة الرقمية"""
        if symbol in self.crypto_symbols:
            self.current_symbol = symbol
            self.logger.info(f"🔄 تم التبديل إلى: {symbol}")
            return True
        else:
            self.logger.error(f"❌ العملة {symbol} غير متوفرة")
            return False
