-- ========================================
-- قاعدة بيانات متجر الأدوية بين الصيدليات
-- Pharmacy Store Database for Inter-Pharmacy Medicine Trading
-- ========================================

USE PharmacyNetworkOnline;
GO

-- ========================================
-- 1. جدول الصيدليات المسجلة
-- Registered Pharmacies Table
-- ========================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacies')
BEGIN
    CREATE TABLE pharmacies (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacy_name NVARCHAR(255) NOT NULL,
        owner_name NVARCHAR(255) NOT NULL,
        phone NVARCHAR(50) NOT NULL,
        email NVARCHAR(255),
        address NVARCHAR(500) NOT NULL,
        city NVARCHAR(100) NOT NULL,
        license_number NVARCHAR(100) UNIQUE NOT NULL,
        registration_date DATETIME DEFAULT GETDATE(),
        is_active BIT DEFAULT 1,
        subscription_end_date DATETIME,
        created_at DATETIME DEFAULT GETDATE(),
        updated_at DATETIME DEFAULT GETDATE()
    );
    
    PRINT '✅ تم إنشاء جدول الصيدليات (pharmacies)';
END
ELSE
BEGIN
    PRINT '⚠️ جدول الصيدليات موجود مسبقاً';
END
GO

-- ========================================
-- 2. جدول الأدوية المنشورة للبيع
-- Published Medicines for Sale Table
-- ========================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'published_medicines')
BEGIN
    CREATE TABLE published_medicines (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacy_id INT NOT NULL,
        medicine_name NVARCHAR(255) NOT NULL,
        medicine_number NVARCHAR(100),
        dosage NVARCHAR(100),
        quantity_available INT NOT NULL,
        price_per_unit DECIMAL(10,2) NOT NULL,
        manufacturing_date DATE,
        expiry_date DATE NOT NULL,
        description NVARCHAR(1000),
        is_available BIT DEFAULT 1,
        published_date DATETIME DEFAULT GETDATE(),
        updated_date DATETIME DEFAULT GETDATE(),
        
        FOREIGN KEY (pharmacy_id) REFERENCES pharmacies(id) ON DELETE CASCADE,
        
        -- فهرس للبحث السريع
        INDEX IX_published_medicines_name (medicine_name),
        INDEX IX_published_medicines_pharmacy (pharmacy_id),
        INDEX IX_published_medicines_expiry (expiry_date),
        INDEX IX_published_medicines_available (is_available)
    );
    
    PRINT '✅ تم إنشاء جدول الأدوية المنشورة (published_medicines)';
END
ELSE
BEGIN
    PRINT '⚠️ جدول الأدوية المنشورة موجود مسبقاً';
END
GO

-- ========================================
-- 3. جدول طلبات الشراء
-- Purchase Requests Table
-- ========================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'purchase_requests')
BEGIN
    CREATE TABLE purchase_requests (
        id INT IDENTITY(1,1) PRIMARY KEY,
        buyer_pharmacy_id INT NOT NULL,
        seller_pharmacy_id INT NOT NULL,
        medicine_id INT NOT NULL,
        requested_quantity INT NOT NULL,
        offered_price DECIMAL(10,2),
        request_message NVARCHAR(1000),
        status NVARCHAR(50) DEFAULT 'pending', -- pending, accepted, rejected, completed
        request_date DATETIME DEFAULT GETDATE(),
        response_date DATETIME NULL,
        response_message NVARCHAR(1000),
        
        FOREIGN KEY (buyer_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (seller_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (medicine_id) REFERENCES published_medicines(id) ON DELETE CASCADE,
        
        -- فهرس للبحث السريع
        INDEX IX_purchase_requests_buyer (buyer_pharmacy_id),
        INDEX IX_purchase_requests_seller (seller_pharmacy_id),
        INDEX IX_purchase_requests_status (status),
        INDEX IX_purchase_requests_date (request_date)
    );
    
    PRINT '✅ تم إنشاء جدول طلبات الشراء (purchase_requests)';
END
ELSE
BEGIN
    PRINT '⚠️ جدول طلبات الشراء موجود مسبقاً';
END
GO

-- ========================================
-- 4. جدول الرسائل بين الصيدليات
-- Messages Between Pharmacies Table
-- ========================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacy_messages')
BEGIN
    CREATE TABLE pharmacy_messages (
        id INT IDENTITY(1,1) PRIMARY KEY,
        sender_pharmacy_id INT NOT NULL,
        receiver_pharmacy_id INT NOT NULL,
        subject NVARCHAR(255),
        message_content NVARCHAR(2000) NOT NULL,
        is_read BIT DEFAULT 0,
        sent_date DATETIME DEFAULT GETDATE(),
        read_date DATETIME NULL,
        related_request_id INT NULL, -- ربط بطلب شراء إذا كان متعلق به
        
        FOREIGN KEY (sender_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (receiver_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (related_request_id) REFERENCES purchase_requests(id),
        
        -- فهرس للبحث السريع
        INDEX IX_pharmacy_messages_receiver (receiver_pharmacy_id),
        INDEX IX_pharmacy_messages_sender (sender_pharmacy_id),
        INDEX IX_pharmacy_messages_read (is_read),
        INDEX IX_pharmacy_messages_date (sent_date)
    );
    
    PRINT '✅ تم إنشاء جدول الرسائل (pharmacy_messages)';
END
ELSE
BEGIN
    PRINT '⚠️ جدول الرسائل موجود مسبقاً';
END
GO

-- ========================================
-- 5. جدول الإشعارات
-- Notifications Table
-- ========================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'notifications')
BEGIN
    CREATE TABLE notifications (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacy_id INT NOT NULL,
        notification_type NVARCHAR(50) NOT NULL, -- purchase_request, message, system
        title NVARCHAR(255) NOT NULL,
        content NVARCHAR(1000) NOT NULL,
        is_read BIT DEFAULT 0,
        created_date DATETIME DEFAULT GETDATE(),
        read_date DATETIME NULL,
        related_id INT NULL, -- ID of related record (request, message, etc.)
        
        FOREIGN KEY (pharmacy_id) REFERENCES pharmacies(id) ON DELETE CASCADE,
        
        -- فهرس للبحث السريع
        INDEX IX_notifications_pharmacy (pharmacy_id),
        INDEX IX_notifications_read (is_read),
        INDEX IX_notifications_type (notification_type),
        INDEX IX_notifications_date (created_date)
    );
    
    PRINT '✅ تم إنشاء جدول الإشعارات (notifications)';
END
ELSE
BEGIN
    PRINT '⚠️ جدول الإشعارات موجود مسبقاً';
END
GO

-- ========================================
-- 6. جدول تقييمات الصيدليات
-- Pharmacy Ratings Table
-- ========================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacy_ratings')
BEGIN
    CREATE TABLE pharmacy_ratings (
        id INT IDENTITY(1,1) PRIMARY KEY,
        rated_pharmacy_id INT NOT NULL,
        rater_pharmacy_id INT NOT NULL,
        rating INT CHECK (rating >= 1 AND rating <= 5),
        review_comment NVARCHAR(1000),
        transaction_id INT, -- ربط بمعاملة محددة
        rating_date DATETIME DEFAULT GETDATE(),
        
        FOREIGN KEY (rated_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (rater_pharmacy_id) REFERENCES pharmacies(id),
        
        -- منع تقييم نفس الصيدلية أكثر من مرة لنفس المعاملة
        UNIQUE (rated_pharmacy_id, rater_pharmacy_id, transaction_id),
        
        -- فهرس للبحث السريع
        INDEX IX_pharmacy_ratings_rated (rated_pharmacy_id),
        INDEX IX_pharmacy_ratings_rater (rater_pharmacy_id)
    );
    
    PRINT '✅ تم إنشاء جدول التقييمات (pharmacy_ratings)';
END
ELSE
BEGIN
    PRINT '⚠️ جدول التقييمات موجود مسبقاً';
END
GO

PRINT '';
PRINT '========================================';
PRINT '✅ تم إنشاء جميع جداول متجر الأدوية بنجاح!';
PRINT '========================================';
PRINT '';
PRINT 'الجداول المنشأة:';
PRINT '1. pharmacies - الصيدليات المسجلة';
PRINT '2. published_medicines - الأدوية المنشورة للبيع';
PRINT '3. purchase_requests - طلبات الشراء';
PRINT '4. pharmacy_messages - الرسائل بين الصيدليات';
PRINT '5. notifications - الإشعارات';
PRINT '6. pharmacy_ratings - تقييمات الصيدليات';
PRINT '';
