@echo off
echo ========================================
echo        اختبار أخطاء البناء
echo ========================================
echo.

echo تحديد مسار Visual Studio...
set "VSPATH=C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
if not exist "%VSPATH%" (
    set "VSPATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
)
if not exist "%VSPATH%" (
    set "VSPATH=C:\Program Files (x86)\MSBuild\14.0\Bin\MSBuild.exe"
)

echo محاولة البناء...
cd /d "c:\Users\<USER>\source\repos\Pharmacy Management System\Pharmacy Management System"
if exist "%VSPATH%" (
    "%VSPATH%" "Pharmacy Management System.csproj" /p:Configuration=Debug /verbosity:normal
) else (
    echo لم يتم العثور على MSBuild
    echo محاولة استخدام dotnet...
    dotnet build "Pharmacy Management System.csproj" --verbosity normal
)

echo.
echo انتهى الاختبار
pause
