-- اختبار سريع لقاعدة البيانات
USE UnifiedPharmacy;

PRINT '=== اختبار سريع لقاعدة البيانات ===';

-- فحص وجود الجداول
PRINT '1. فحص وجود الجداول:';
SELECT 
    TABLE_NAME as 'اسم الجدول',
    CASE WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = t.TABLE_NAME) 
         THEN 'موجود' ELSE 'غير موجود' END as 'الحالة'
FROM (
    SELECT 'pharmacies' as TABLE_NAME
    UNION SELECT 'published_medicines'
    UNION SELECT 'purchase_requests'
    UNION SELECT 'users'
) t;

-- فحص عدد السجلات في كل جدول
PRINT '2. عدد السجلات في كل جدول:';

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacies')
    SELECT 'pharmacies' as 'الجدول', COUNT(*) as 'العدد' FROM pharmacies
ELSE
    SELECT 'pharmacies' as 'الجدول', 0 as 'العدد';

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'published_medicines')
    SELECT 'published_medicines' as 'الجدول', COUNT(*) as 'العدد' FROM published_medicines
ELSE
    SELECT 'published_medicines' as 'الجدول', 0 as 'العدد';

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'purchase_requests')
    SELECT 'purchase_requests' as 'الجدول', COUNT(*) as 'العدد' FROM purchase_requests
ELSE
    SELECT 'purchase_requests' as 'الجدول', 0 as 'العدد';

-- اختبار الاستعلام الأساسي
PRINT '3. اختبار استعلام طلبات الأدوية:';

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'purchase_requests')
BEGIN
    -- اختبار الاستعلام بدون JOIN
    SELECT 
        'بدون JOIN' as 'نوع الاستعلام',
        COUNT(*) as 'العدد'
    FROM purchase_requests;
    
    -- اختبار الاستعلام مع JOIN
    IF EXISTS (SELECT * FROM sys.tables WHERE name = 'published_medicines') 
       AND EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacies')
    BEGIN
        SELECT 
            'مع JOIN' as 'نوع الاستعلام',
            COUNT(*) as 'العدد'
        FROM purchase_requests pr
        INNER JOIN published_medicines pm ON pr.published_medicine_id = pm.id
        INNER JOIN pharmacies p_buyer ON pr.buyer_pharmacy_id = p_buyer.id;
    END
    ELSE
    BEGIN
        SELECT 'مع JOIN' as 'نوع الاستعلام', 'جداول مفقودة' as 'العدد';
    END
END
ELSE
BEGIN
    SELECT 'جدول purchase_requests غير موجود' as 'رسالة';
END

-- اختبار الاستعلام للصيدلية رقم 1
PRINT '4. اختبار للصيدلية رقم 1:';

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'purchase_requests')
BEGIN
    SELECT 
        seller_pharmacy_id as 'معرف الصيدلية البائعة',
        COUNT(*) as 'عدد الطلبات'
    FROM purchase_requests 
    GROUP BY seller_pharmacy_id
    ORDER BY seller_pharmacy_id;
    
    -- طلبات للصيدلية رقم 1
    SELECT COUNT(*) as 'طلبات للصيدلية رقم 1' 
    FROM purchase_requests 
    WHERE seller_pharmacy_id = 1;
END

PRINT '=== انتهى الاختبار ===';
