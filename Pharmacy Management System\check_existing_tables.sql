-- فحص بنية الجداول الموجودة
USE UnifiedPharmacy;

PRINT '=== فحص بنية الجداول الموجودة ===';

-- فحص جدول pharmacies
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacies')
BEGIN
    PRINT '--- بنية جدول pharmacies ---';
    SELECT 
        COLUMN_NAME as 'اسم العمود',
        DATA_TYPE as 'نوع البيانات',
        IS_NULLABLE as 'يقبل NULL'
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'pharmacies'
    ORDER BY ORDINAL_POSITION;
END
ELSE
    PRINT 'جدول pharmacies غير موجود';

-- فحص جدول published_medicines
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'published_medicines')
BEGIN
    PRINT '--- بنية جدول published_medicines ---';
    SELECT 
        COLUMN_NAME as 'اسم العمود',
        DATA_TYPE as 'نوع البيانات',
        IS_NULLABLE as 'يقبل NULL'
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'published_medicines'
    ORDER BY ORDINAL_POSITION;
END
ELSE
    PRINT 'جدول published_medicines غير موجود';

-- فحص جدول purchase_requests
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'purchase_requests')
BEGIN
    PRINT '--- بنية جدول purchase_requests ---';
    SELECT 
        COLUMN_NAME as 'اسم العمود',
        DATA_TYPE as 'نوع البيانات',
        IS_NULLABLE as 'يقبل NULL'
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'purchase_requests'
    ORDER BY ORDINAL_POSITION;
END
ELSE
    PRINT 'جدول purchase_requests غير موجود';

-- فحص جميع الجداول في قاعدة البيانات
PRINT '--- جميع الجداول في قاعدة البيانات ---';
SELECT 
    TABLE_NAME as 'اسم الجدول',
    TABLE_TYPE as 'نوع الجدول'
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE'
ORDER BY TABLE_NAME;

PRINT '=== انتهى الفحص ===';
