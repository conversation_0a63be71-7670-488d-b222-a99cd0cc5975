USE pharmacy;
GO

-- Add test medicines with English names to avoid encoding issues
INSERT INTO medic (mid, mname, mnumber, mDate, eDate, quantity, perUnit, lu, br)
VALUES 
('TEST001', 'Paracetamol 500mg', '500mg', '2024-01-01', '2025-12-31', 100, 5, 'Box', 'Pfizer'),
('TEST002', 'Amoxicillin 250mg', '250mg', '2024-02-01', '2025-10-15', 75, 15, 'Box', 'GSK'),
('TEST003', 'Vitamin C 1000mg', '1000mg', '2024-03-01', '2026-03-20', 60, 12, 'Bottle', 'Bayer'),
('TEST004', 'Ibuprofen 400mg', '400mg', '2024-04-01', '2025-08-10', 80, 8, 'Box', 'Novartis'),
('TEST005', 'Cough Syrup', '120ml', '2024-05-01', '2025-11-25', 30, 18, '<PERSON><PERSON>', 'Sanofi');

-- Add test user
INSERT INTO users (userRole, name, dob, mobile, email, username, pass)
VALUES ('Employee', 'Test Employee', '1990-01-01', 1234567890, '<EMAIL>', 'testuser', 'test123');

-- Check results
SELECT COUNT(*) as TotalMedicines FROM medic;
SELECT COUNT(*) as AvailableMedicines FROM medic WHERE quantity > 0;
SELECT COUNT(*) as TotalUsers FROM users;
