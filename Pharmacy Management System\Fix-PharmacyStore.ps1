# Fix Pharmacy Store Build Errors
# إصلاح أخطاء بناء متجر الصيدلية

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    إصلاح أخطاء بناء متجر الصيدلية" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# التحقق من وجود الملفات
Write-Host "التحقق من وجود الملفات الجديدة..." -ForegroundColor Green

$requiredFiles = @(
    "PharmacistUC\UC_P_PharmacyStore.cs",
    "PharmacistUC\UC_P_PharmacyStore.Designer.cs",
    "PharmacistUC\UC_P_PharmacyStore.resx",
    "PublishMedicineForm.cs",
    "PublishMedicineForm.Designer.cs",
    "RequestMedicineForm.cs",
    "RequestMedicineForm.Designer.cs"
)

$missingFiles = @()
foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        $missingFiles += $file
        Write-Host "❌ ملف مفقود: $file" -ForegroundColor Red
    } else {
        Write-Host "✅ موجود: $file" -ForegroundColor Green
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host ""
    Write-Host "❌ بعض الملفات مفقودة!" -ForegroundColor Red
    Write-Host "يرجى إعادة إنشاء الملفات المفقودة أولاً." -ForegroundColor Yellow
    Read-Host "اضغط Enter للمتابعة"
    exit 1
}

Write-Host ""
Write-Host "✅ جميع الملفات موجودة!" -ForegroundColor Green

# تنظيف المشروع
Write-Host ""
Write-Host "تنظيف مجلدات البناء..." -ForegroundColor Yellow

if (Test-Path "bin") {
    Remove-Item "bin" -Recurse -Force
    Write-Host "✅ تم حذف مجلد bin" -ForegroundColor Green
}

if (Test-Path "obj") {
    Remove-Item "obj" -Recurse -Force
    Write-Host "✅ تم حذف مجلد obj" -ForegroundColor Green
}

# البحث عن MSBuild
Write-Host ""
Write-Host "البحث عن MSBuild..." -ForegroundColor Yellow

$msbuildPaths = @(
    "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
    "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe",
    "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe",
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe",
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
)

$msbuildPath = $null
foreach ($path in $msbuildPaths) {
    if (Test-Path $path) {
        $msbuildPath = $path
        Write-Host "✅ تم العثور على MSBuild: $path" -ForegroundColor Green
        break
    }
}

if (-not $msbuildPath) {
    Write-Host "❌ لم يتم العثور على MSBuild" -ForegroundColor Red
    Write-Host ""
    Write-Host "الحل اليدوي:" -ForegroundColor Yellow
    Write-Host "1. فتح Visual Studio" -ForegroundColor White
    Write-Host "2. File → Open → Project/Solution" -ForegroundColor White
    Write-Host "3. اختيار Pharmacy Management System.sln" -ForegroundColor White
    Write-Host "4. Build → Rebuild Solution" -ForegroundColor White
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# محاولة البناء
Write-Host ""
Write-Host "محاولة بناء المشروع..." -ForegroundColor Yellow

$projectFile = "Pharmacy Management System.csproj"
if (-not (Test-Path $projectFile)) {
    Write-Host "❌ ملف المشروع غير موجود: $projectFile" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

try {
    $buildResult = & $msbuildPath $projectFile /p:Configuration=Debug /t:Rebuild /verbosity:minimal
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "✅ تم بناء المشروع بنجاح!" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host ""
        Write-Host "صفحة متجر الصيدلية جاهزة للاستخدام!" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "الخطوات التالية:" -ForegroundColor Cyan
        Write-Host "1. فتح Visual Studio" -ForegroundColor White
        Write-Host "2. تشغيل البرنامج (F5)" -ForegroundColor White
        Write-Host "3. تسجيل الدخول كموظف صيدلية" -ForegroundColor White
        Write-Host "4. الضغط على زر 'متجر الأدوية'" -ForegroundColor White
        Write-Host ""
        Write-Host "🎉 استمتع بالمميزات الجديدة!" -ForegroundColor Green
    } else {
        Write-Host ""
        Write-Host "❌ فشل في بناء المشروع" -ForegroundColor Red
        Write-Host ""
        Write-Host "الحلول المقترحة:" -ForegroundColor Yellow
        Write-Host "1. فتح المشروع في Visual Studio" -ForegroundColor White
        Write-Host "2. مراجعة نافذة Error List" -ForegroundColor White
        Write-Host "3. إصلاح الأخطاء الظاهرة" -ForegroundColor White
        Write-Host "4. Build → Rebuild Solution" -ForegroundColor White
        Write-Host ""
        Write-Host "أخطاء شائعة وحلولها:" -ForegroundColor Cyan
        Write-Host "• Missing Guna.UI2: Install-Package Guna.UI2.WinForms" -ForegroundColor White
        Write-Host "• Type not found: إضافة الملفات للمشروع يدوياً" -ForegroundColor White
        Write-Host "• Namespace error: تصحيح using statements" -ForegroundColor White
    }
} catch {
    Write-Host "❌ خطأ في تشغيل MSBuild: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Read-Host "اضغط Enter للخروج"
