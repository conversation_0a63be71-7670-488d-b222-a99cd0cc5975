@echo off
echo ========================================
echo   إصلاح سريع لجميع مشاكل النظام
echo   Quick Fix for All System Issues
echo ========================================
echo.

echo 🔧 إصلاح المشاكل التالية:
echo - عدم حفظ الحسابات الجديدة
echo - عدم ظهور الأدوية في صفحة البيع
echo - عدم ظهور الأدوية في المتجر الأونلاين
echo.

echo الخطوة 1: إضافة مستخدمين تجريبيين...
sqlcmd -S NARUTO -E -d pharmacy -Q "IF NOT EXISTS (SELECT * FROM users WHERE username = 'testadmin') INSERT INTO users (userRole, name, dob, mobile, email, username, pass) VALUES ('Administrator', 'Test Admin', '1980-01-01', 1111111111, '<EMAIL>', 'testadmin', 'admin123')"

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إضافة مستخدم مدير تجريبي
) else (
    echo ❌ فشل في إضافة المستخدم المدير
)

sqlcmd -S NARUTO -E -d pharmacy -Q "IF NOT EXISTS (SELECT * FROM users WHERE username = 'testemp') INSERT INTO users (userRole, name, dob, mobile, email, username, pass) VALUES ('Employee', 'Test Employee', '1990-01-01', 2222222222, '<EMAIL>', 'testemp', 'emp123')"

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إضافة موظف تجريبي
) else (
    echo ❌ فشل في إضافة الموظف
)

echo.
echo الخطوة 2: إضافة أدوية تجريبية...

sqlcmd -S NARUTO -E -d pharmacy -Q "IF NOT EXISTS (SELECT * FROM medic WHERE mid = 'QUICK001') INSERT INTO medic (mid, mname, mnumber, mDate, eDate, quantity, perUnit, lu, br) VALUES ('QUICK001', 'Quick Medicine A', '500mg', '2024-01-01', '2025-12-31', 100, 10, 'Box', 'TestCompany')"

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إضافة دواء تجريبي A
) else (
    echo ❌ فشل في إضافة الدواء A
)

sqlcmd -S NARUTO -E -d pharmacy -Q "IF NOT EXISTS (SELECT * FROM medic WHERE mid = 'QUICK002') INSERT INTO medic (mid, mname, mnumber, mDate, eDate, quantity, perUnit, lu, br) VALUES ('QUICK002', 'Quick Medicine B', '250mg', '2024-01-01', '2025-12-31', 75, 15, 'Box', 'TestCompany')"

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إضافة دواء تجريبي B
) else (
    echo ❌ فشل في إضافة الدواء B
)

sqlcmd -S NARUTO -E -d pharmacy -Q "IF NOT EXISTS (SELECT * FROM medic WHERE mid = 'QUICK003') INSERT INTO medic (mid, mname, mnumber, mDate, eDate, quantity, perUnit, lu, br) VALUES ('QUICK003', 'Quick Medicine C', '100mg', '2024-01-01', '2025-12-31', 50, 8, 'Box', 'TestCompany')"

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إضافة دواء تجريبي C
) else (
    echo ❌ فشل في إضافة الدواء C
)

echo.
echo الخطوة 3: التحقق من النتائج...

echo عدد المستخدمين:
sqlcmd -S NARUTO -E -d pharmacy -Q "SELECT COUNT(*) FROM users"

echo عدد الأدوية المتاحة:
sqlcmd -S NARUTO -E -d pharmacy -Q "SELECT COUNT(*) FROM medic WHERE quantity > 0"

echo عدد الأدوية في الشبكة الأونلاين:
sqlcmd -S NARUTO -E -d PharmacyNetworkOnline -Q "SELECT COUNT(*) FROM networkmedicines WHERE isAvailableForSale = 1"

echo.
echo ========================================
echo   ✅ تم الإصلاح!
echo ========================================
echo.
echo الآن يمكنك:
echo 1. تسجيل دخول بالحسابات التجريبية:
echo    - المدير: testadmin / admin123
echo    - الموظف: testemp / emp123
echo.
echo 2. رؤية الأدوية في صفحة البيع
echo.
echo 3. استخدام المتجر الأونلاين (10 أدوية متاحة)
echo.
echo 🎯 جرب الآن: شغل البرنامج واختبر الميزات!
echo.

pause
