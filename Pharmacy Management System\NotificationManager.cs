using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;

namespace Pharmacy_Management_System
{
    /// <summary>
    /// مدير الإشعارات - لإدارة جميع الإشعارات والرسائل في النظام
    /// Notification Manager - Manages all notifications and messages in the system
    /// </summary>
    public class NotificationManager
    {
        private UnifiedPharmacyFunction unifiedDb;
        private int currentPharmacyId;

        public NotificationManager()
        {
            unifiedDb = new UnifiedPharmacyFunction();
            currentPharmacyId = SessionManager.CurrentPharmacyId > 0 ? SessionManager.CurrentPharmacyId : 1;
        }

        /// <summary>
        /// الحصول على جميع الإشعارات للصيدلية الحالية
        /// </summary>
        public DataSet GetNotifications(bool unreadOnly = false)
        {
            try
            {
                string query = @"
                    SELECT 
                        n.id,
                        n.notification_type as notificationType,
                        n.title,
                        n.content,
                        n.is_read as isRead,
                        n.created_date as createdDate,
                        n.related_id as relatedId,
                        CASE 
                            WHEN n.notification_type = 'purchase_request' THEN 'طلب شراء'
                            WHEN n.notification_type = 'message' THEN 'رسالة'
                            WHEN n.notification_type = 'system' THEN 'نظام'
                            ELSE 'عام'
                        END as typeDisplay
                    FROM notifications n
                    WHERE n.pharmacy_id = @pharmacyId";

                if (unreadOnly)
                {
                    query += " AND n.is_read = 0";
                }

                query += " ORDER BY n.created_date DESC";

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", currentPharmacyId}
                };

                return unifiedDb.ExecuteQuery(query, parameters);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في الحصول على الإشعارات: {ex.Message}");
                return new DataSet();
            }
        }

        /// <summary>
        /// عدد الإشعارات غير المقروءة
        /// </summary>
        public int GetUnreadNotificationCount()
        {
            try
            {
                string query = @"
                    SELECT COUNT(*) as unreadCount
                    FROM notifications 
                    WHERE pharmacy_id = @pharmacyId AND is_read = 0";

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", currentPharmacyId}
                };

                DataSet result = unifiedDb.ExecuteQuery(query, parameters);
                if (result.Tables.Count > 0 && result.Tables[0].Rows.Count > 0)
                {
                    return Convert.ToInt32(result.Tables[0].Rows[0]["unreadCount"]);
                }
                return 0;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في عدد الإشعارات غير المقروءة: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// تحديد إشعار كمقروء
        /// </summary>
        public bool MarkNotificationAsRead(int notificationId)
        {
            try
            {
                string query = @"
                    UPDATE notifications 
                    SET is_read = 1, read_date = GETDATE()
                    WHERE id = @notificationId AND pharmacy_id = @pharmacyId";

                var parameters = new Dictionary<string, object>
                {
                    {"@notificationId", notificationId},
                    {"@pharmacyId", currentPharmacyId}
                };

                unifiedDb.ExecuteQuery(query, parameters);
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في تحديد الإشعار كمقروء: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحديد جميع الإشعارات كمقروءة
        /// </summary>
        public bool MarkAllNotificationsAsRead()
        {
            try
            {
                string query = @"
                    UPDATE notifications 
                    SET is_read = 1, read_date = GETDATE()
                    WHERE pharmacy_id = @pharmacyId AND is_read = 0";

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", currentPharmacyId}
                };

                unifiedDb.ExecuteQuery(query, parameters);
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في تحديد جميع الإشعارات كمقروءة: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إنشاء إشعار جديد
        /// </summary>
        public bool CreateNotification(int targetPharmacyId, string notificationType, string title, string content, int? relatedId = null)
        {
            try
            {
                string query = @"
                    INSERT INTO notifications (pharmacy_id, notification_type, title, content, related_id, created_date, is_read)
                    VALUES (@pharmacyId, @notificationType, @title, @content, @relatedId, GETDATE(), 0)";

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", targetPharmacyId},
                    {"@notificationType", notificationType},
                    {"@title", title},
                    {"@content", content},
                    {"@relatedId", relatedId}
                };

                unifiedDb.ExecuteQuery(query, parameters);
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في إنشاء الإشعار: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على معلومات الصيدلية من الإشعار
        /// </summary>
        public DataRow GetPharmacyFromNotification(int notificationId)
        {
            try
            {
                string query = @"
                    SELECT DISTINCT
                        p.id as pharmacyId,
                        p.pharmacyName,
                        p.pharmacyCode,
                        p.phone,
                        p.address
                    FROM notifications n
                    LEFT JOIN purchase_requests pr ON n.related_id = pr.id AND n.notification_type = 'purchase_request'
                    LEFT JOIN published_medicines pm ON pr.published_medicine_id = pm.id
                    LEFT JOIN pharmacies p ON pm.pharmacy_id = p.id
                    LEFT JOIN pharmacy_messages msg ON n.related_id = msg.id AND n.notification_type = 'message'
                    LEFT JOIN pharmacies p2 ON msg.sender_pharmacy_id = p2.id
                    WHERE n.id = @notificationId
                    AND (p.id IS NOT NULL OR p2.id IS NOT NULL)";

                var parameters = new Dictionary<string, object>
                {
                    {"@notificationId", notificationId}
                };

                DataSet result = unifiedDb.ExecuteQuery(query, parameters);
                if (result.Tables.Count > 0 && result.Tables[0].Rows.Count > 0)
                {
                    return result.Tables[0].Rows[0];
                }
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في الحصول على معلومات الصيدلية: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// حذف إشعار
        /// </summary>
        public bool DeleteNotification(int notificationId)
        {
            try
            {
                string query = @"
                    DELETE FROM notifications 
                    WHERE id = @notificationId AND pharmacy_id = @pharmacyId";

                var parameters = new Dictionary<string, object>
                {
                    {"@notificationId", notificationId},
                    {"@pharmacyId", currentPharmacyId}
                };

                unifiedDb.ExecuteQuery(query, parameters);
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في حذف الإشعار: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على تفاصيل الإشعار
        /// </summary>
        public DataRow GetNotificationDetails(int notificationId)
        {
            try
            {
                string query = @"
                    SELECT 
                        n.*,
                        CASE 
                            WHEN n.notification_type = 'purchase_request' THEN 
                                (SELECT pm.medicine_name FROM purchase_requests pr 
                                 JOIN published_medicines pm ON pr.published_medicine_id = pm.id 
                                 WHERE pr.id = n.related_id)
                            WHEN n.notification_type = 'message' THEN 
                                (SELECT msg.subject FROM pharmacy_messages msg WHERE msg.id = n.related_id)
                            ELSE ''
                        END as relatedInfo
                    FROM notifications n
                    WHERE n.id = @notificationId AND n.pharmacy_id = @pharmacyId";

                var parameters = new Dictionary<string, object>
                {
                    {"@notificationId", notificationId},
                    {"@pharmacyId", currentPharmacyId}
                };

                DataSet result = unifiedDb.ExecuteQuery(query, parameters);
                if (result.Tables.Count > 0 && result.Tables[0].Rows.Count > 0)
                {
                    return result.Tables[0].Rows[0];
                }
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في الحصول على تفاصيل الإشعار: {ex.Message}");
                return null;
            }
        }
    }
}
