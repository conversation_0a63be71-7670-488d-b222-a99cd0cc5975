using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Pharmacy_Management_System
{
    /// <summary>
    /// إدارة جلسة المستخدم الحالي في النظام الموحد
    /// </summary>
    public static class SessionManager
    {
        // معلومات المستخدم الحالي
        public static int CurrentUserId { get; set; } = 0;
        public static string CurrentUsername { get; set; } = "";
        public static string CurrentUserName { get; set; } = "";
        public static string CurrentUserRole { get; set; } = "";
        
        // معلومات الصيدلية الحالية
        public static int CurrentPharmacyId { get; set; } = 0;
        public static string CurrentPharmacyName { get; set; } = "";
        public static string CurrentPharmacyCode { get; set; } = "";
        
        // معلومات الجلسة
        public static DateTime LoginTime { get; set; } = DateTime.Now;
        public static bool IsLoggedIn { get; set; } = false;
        
        /// <summary>
        /// تسجيل دخول المستخدم
        /// </summary>
        public static void Login(int userId, string username, string userName, string userRole, 
                               int pharmacyId, string pharmacyName, string pharmacyCode = "")
        {
            CurrentUserId = userId;
            CurrentUsername = username;
            CurrentUserName = userName;
            CurrentUserRole = userRole;
            CurrentPharmacyId = pharmacyId;
            CurrentPharmacyName = pharmacyName;
            CurrentPharmacyCode = pharmacyCode;
            LoginTime = DateTime.Now;
            IsLoggedIn = true;
        }
        
        /// <summary>
        /// تسجيل خروج المستخدم
        /// </summary>
        public static void Logout()
        {
            CurrentUserId = 0;
            CurrentUsername = "";
            CurrentUserName = "";
            CurrentUserRole = "";
            CurrentPharmacyId = 0;
            CurrentPharmacyName = "";
            CurrentPharmacyCode = "";
            IsLoggedIn = false;
        }
        
        /// <summary>
        /// التحقق من صلاحيات المستخدم
        /// </summary>
        public static bool HasPermission(string permission)
        {
            if (!IsLoggedIn) return false;
            
            switch (CurrentUserRole.ToLower())
            {
                case "administrator":
                    return true; // المدير له جميع الصلاحيات
                    
                case "pharmacist":
                case "employee":
                    // الموظف والصيدلي لهم صلاحيات محدودة
                    switch (permission.ToLower())
                    {
                        case "sell_medicines":
                        case "view_medicines":
                        case "view_sales":
                        case "online_network":
                            return true;
                        case "add_user":
                        case "edit_user":
                        case "delete_user":
                        case "add_medicine":
                        case "edit_medicine":
                        case "delete_medicine":
                            return false;
                        default:
                            return false;
                    }
                    
                default:
                    return false;
            }
        }
        
        /// <summary>
        /// الحصول على معلومات المستخدم الحالي
        /// </summary>
        public static string GetCurrentUserInfo()
        {
            if (!IsLoggedIn) return "غير مسجل دخول";
            
            return CurrentUserName + " (" + CurrentUserRole + ") - " + CurrentPharmacyName;
        }
        
        /// <summary>
        /// الحصول على مدة الجلسة
        /// </summary>
        public static TimeSpan GetSessionDuration()
        {
            if (!IsLoggedIn) return TimeSpan.Zero;
            
            return DateTime.Now - LoginTime;
        }
    }
}
