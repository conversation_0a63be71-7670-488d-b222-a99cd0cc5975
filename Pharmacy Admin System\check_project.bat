@echo off
chcp 65001 >nul
echo ========================================
echo 🔍 فحص اكتمال مشروع إدارة الصيدليات المركزي
echo 🔍 Checking Central Pharmacy Admin System Project
echo ========================================
echo.

set MISSING_FILES=0

echo 📁 فحص الملفات الأساسية...
echo 📁 Checking core files...

if exist "Program.cs" (
    echo ✅ Program.cs
) else (
    echo ❌ Program.cs مفقود
    set /a MISSING_FILES+=1
)

if exist "App.config" (
    echo ✅ App.config
) else (
    echo ❌ App.config مفقود
    set /a MISSING_FILES+=1
)

if exist "Pharmacy Admin System.csproj" (
    echo ✅ Pharmacy Admin System.csproj
) else (
    echo ❌ Pharmacy Admin System.csproj مفقود
    set /a MISSING_FILES+=1
)

if exist "Pharmacy Admin System.sln" (
    echo ✅ Pharmacy Admin System.sln
) else (
    echo ❌ Pharmacy Admin System.sln مفقود
    set /a MISSING_FILES+=1
)

echo.
echo 🖥️ فحص ملفات الواجهات...
echo 🖥️ Checking form files...

if exist "AdminLoginForm.cs" (
    echo ✅ AdminLoginForm.cs
) else (
    echo ❌ AdminLoginForm.cs مفقود
    set /a MISSING_FILES+=1
)

if exist "AdminLoginForm.Designer.cs" (
    echo ✅ AdminLoginForm.Designer.cs
) else (
    echo ❌ AdminLoginForm.Designer.cs مفقود
    set /a MISSING_FILES+=1
)

if exist "AdminLoginForm.resx" (
    echo ✅ AdminLoginForm.resx
) else (
    echo ❌ AdminLoginForm.resx مفقود
    set /a MISSING_FILES+=1
)

if exist "MainAdminForm.cs" (
    echo ✅ MainAdminForm.cs
) else (
    echo ❌ MainAdminForm.cs مفقود
    set /a MISSING_FILES+=1
)

if exist "MainAdminForm.Designer.cs" (
    echo ✅ MainAdminForm.Designer.cs
) else (
    echo ❌ MainAdminForm.Designer.cs مفقود
    set /a MISSING_FILES+=1
)

if exist "MainAdminForm.resx" (
    echo ✅ MainAdminForm.resx
) else (
    echo ❌ MainAdminForm.resx مفقود
    set /a MISSING_FILES+=1
)

echo.
echo 🔧 فحص ملفات الإدارة...
echo 🔧 Checking management files...

if exist "DatabaseManager.cs" (
    echo ✅ DatabaseManager.cs
) else (
    echo ❌ DatabaseManager.cs مفقود
    set /a MISSING_FILES+=1
)

if exist "PharmacyManager.cs" (
    echo ✅ PharmacyManager.cs
) else (
    echo ❌ PharmacyManager.cs مفقود
    set /a MISSING_FILES+=1
)

if exist "SubscriptionManager.cs" (
    echo ✅ SubscriptionManager.cs
) else (
    echo ❌ SubscriptionManager.cs مفقود
    set /a MISSING_FILES+=1
)

if exist "BackupManager.cs" (
    echo ✅ BackupManager.cs
) else (
    echo ❌ BackupManager.cs مفقود
    set /a MISSING_FILES+=1
)

if exist "ReportManager.cs" (
    echo ✅ ReportManager.cs
) else (
    echo ❌ ReportManager.cs مفقود
    set /a MISSING_FILES+=1
)

echo.
echo 📂 فحص مجلد Properties...
echo 📂 Checking Properties folder...

if exist "Properties\AssemblyInfo.cs" (
    echo ✅ Properties\AssemblyInfo.cs
) else (
    echo ❌ Properties\AssemblyInfo.cs مفقود
    set /a MISSING_FILES+=1
)

if exist "Properties\Resources.resx" (
    echo ✅ Properties\Resources.resx
) else (
    echo ❌ Properties\Resources.resx مفقود
    set /a MISSING_FILES+=1
)

if exist "Properties\Resources.Designer.cs" (
    echo ✅ Properties\Resources.Designer.cs
) else (
    echo ❌ Properties\Resources.Designer.cs مفقود
    set /a MISSING_FILES+=1
)

if exist "Properties\Settings.settings" (
    echo ✅ Properties\Settings.settings
) else (
    echo ❌ Properties\Settings.settings مفقود
    set /a MISSING_FILES+=1
)

if exist "Properties\Settings.Designer.cs" (
    echo ✅ Properties\Settings.Designer.cs
) else (
    echo ❌ Properties\Settings.Designer.cs مفقود
    set /a MISSING_FILES+=1
)

echo.
echo 🗄️ فحص ملفات قاعدة البيانات...
echo 🗄️ Checking database files...

if exist "create_admin_database.sql" (
    echo ✅ create_admin_database.sql
) else (
    echo ❌ create_admin_database.sql مفقود
    set /a MISSING_FILES+=1
)

if exist "quick_setup.sql" (
    echo ✅ quick_setup.sql
) else (
    echo ❌ quick_setup.sql مفقود
    set /a MISSING_FILES+=1
)

echo.
echo ========================================
if %MISSING_FILES% EQU 0 (
    echo ✅ جميع الملفات موجودة! المشروع مكتمل.
    echo ✅ All files present! Project is complete.
    echo.
    echo 🚀 يمكنك الآن تشغيل البرنامج:
    echo 🚀 You can now run the application:
    echo 1. شغل build_and_run.bat
    echo 2. أو افتح المشروع في Visual Studio
    echo.
) else (
    echo ❌ يوجد %MISSING_FILES% ملف مفقود
    echo ❌ %MISSING_FILES% files are missing
    echo.
    echo 🔧 يرجى إعادة إنشاء الملفات المفقودة
    echo 🔧 Please recreate the missing files
)
echo ========================================

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
