-- إنشاء قاعدة بيانات الإدارة المركزية للصيدليات
-- Central Pharmacy Administration Database

USE master;
GO

-- إنشاء قاعدة البيانات المنفصلة للإدارة
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'PharmacyAdminSystem')
BEGIN
    CREATE DATABASE PharmacyAdminSystem;
    PRINT '✅ تم إنشاء قاعدة بيانات الإدارة PharmacyAdminSystem';
END
ELSE
BEGIN
    PRINT '✅ قاعدة بيانات الإدارة PharmacyAdminSystem موجودة بالفعل';
END
GO

USE PharmacyAdminSystem;
GO

PRINT '========================================';
PRINT '   نظام إدارة الصيدليات المركزي';
PRINT '   Central Pharmacy Management System';
PRINT '========================================';

-- 1. جدول المديرين العامين
PRINT '';
PRINT '1. إنشاء جدول المديرين العامين...';
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='admin_users' AND xtype='U')
BEGIN
    CREATE TABLE admin_users (
        id int IDENTITY(1,1) PRIMARY KEY,
        username nvarchar(100) UNIQUE NOT NULL,
        password nvarchar(255) NOT NULL,
        fullName nvarchar(255) NOT NULL,
        email nvarchar(255) NULL,
        phone nvarchar(20) NULL,
        role nvarchar(50) DEFAULT 'SuperAdmin',
        isActive bit DEFAULT 1,
        lastLogin datetime NULL,
        createdDate datetime DEFAULT GETDATE(),
        createdBy int NULL
    );
    PRINT '✅ تم إنشاء جدول admin_users';
END

-- إضافة مدير عام افتراضي
IF NOT EXISTS (SELECT * FROM admin_users WHERE username = 'superadmin')
BEGIN
    INSERT INTO admin_users (username, password, fullName, email, phone, role, isActive)
    VALUES ('superadmin', 'admin2025', N'المدير العام للنظام', '<EMAIL>', '0501234567', 'SuperAdmin', 1);
    PRINT '✅ تم إضافة المدير العام الافتراضي (superadmin/admin2025)';
END

-- 2. جدول الصيدليات المسجلة في النظام
PRINT '';
PRINT '2. إنشاء جدول الصيدليات المسجلة...';
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='registered_pharmacies' AND xtype='U')
BEGIN
    CREATE TABLE registered_pharmacies (
        id int IDENTITY(1,1) PRIMARY KEY,
        pharmacyName nvarchar(255) NOT NULL,
        pharmacyCode nvarchar(50) UNIQUE NOT NULL,
        ownerName nvarchar(255) NOT NULL,
        ownerPhone nvarchar(20) NOT NULL,
        ownerEmail nvarchar(255) NULL,
        address nvarchar(500) NOT NULL,
        city nvarchar(100) NOT NULL,
        region nvarchar(100) NULL,
        licenseNumber nvarchar(100) NOT NULL,
        taxNumber nvarchar(100) NULL,
        subscriptionType nvarchar(50) DEFAULT 'Basic',
        subscriptionStartDate datetime DEFAULT GETDATE(),
        subscriptionEndDate datetime NULL,
        monthlyFee decimal(10,2) DEFAULT 0.00,
        status nvarchar(50) DEFAULT 'Pending', -- Pending, Approved, Rejected, Suspended
        isActive bit DEFAULT 0,
        registrationDate datetime DEFAULT GETDATE(),
        approvedDate datetime NULL,
        approvedBy int NULL,
        rejectionReason nvarchar(500) NULL,
        notes nvarchar(1000) NULL,
        databaseName nvarchar(100) NULL, -- اسم قاعدة البيانات الخاصة بالصيدلية
        lastBackupDate datetime NULL,
        FOREIGN KEY (approvedBy) REFERENCES admin_users(id)
    );
    PRINT '✅ تم إنشاء جدول registered_pharmacies';
END

-- 3. جدول أنواع الاشتراكات
PRINT '';
PRINT '3. إنشاء جدول أنواع الاشتراكات...';
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='subscription_plans' AND xtype='U')
BEGIN
    CREATE TABLE subscription_plans (
        id int IDENTITY(1,1) PRIMARY KEY,
        planName nvarchar(100) NOT NULL,
        planNameAr nvarchar(100) NOT NULL,
        description nvarchar(500) NULL,
        monthlyPrice decimal(10,2) NOT NULL,
        maxUsers int DEFAULT 5,
        maxMedicines int DEFAULT 1000,
        hasNetworkAccess bit DEFAULT 1,
        hasReports bit DEFAULT 1,
        hasBackup bit DEFAULT 1,
        supportLevel nvarchar(50) DEFAULT 'Basic',
        isActive bit DEFAULT 1,
        createdDate datetime DEFAULT GETDATE()
    );
    PRINT '✅ تم إنشاء جدول subscription_plans';
    
    -- إضافة خطط الاشتراك الافتراضية
    INSERT INTO subscription_plans (planName, planNameAr, description, monthlyPrice, maxUsers, maxMedicines, hasNetworkAccess, hasReports, hasBackup, supportLevel)
    VALUES 
    ('Basic', N'أساسي', N'خطة أساسية للصيدليات الصغيرة', 99.00, 3, 500, 1, 1, 0, 'Basic'),
    ('Standard', N'قياسي', N'خطة قياسية للصيدليات المتوسطة', 199.00, 5, 1000, 1, 1, 1, 'Standard'),
    ('Premium', N'مميز', N'خطة مميزة للصيدليات الكبيرة', 299.00, 10, 5000, 1, 1, 1, 'Premium'),
    ('Enterprise', N'مؤسسي', N'خطة مؤسسية للسلاسل الكبيرة', 499.00, 50, 50000, 1, 1, 1, 'Enterprise');
    
    PRINT '✅ تم إضافة خطط الاشتراك الافتراضية';
END

-- 4. جدول مدفوعات الاشتراكات
PRINT '';
PRINT '4. إنشاء جدول مدفوعات الاشتراكات...';
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='subscription_payments' AND xtype='U')
BEGIN
    CREATE TABLE subscription_payments (
        id int IDENTITY(1,1) PRIMARY KEY,
        pharmacyId int NOT NULL,
        planId int NOT NULL,
        amount decimal(10,2) NOT NULL,
        paymentDate datetime DEFAULT GETDATE(),
        paymentMethod nvarchar(50) NULL,
        transactionId nvarchar(100) NULL,
        status nvarchar(50) DEFAULT 'Completed',
        validFrom datetime NOT NULL,
        validTo datetime NOT NULL,
        notes nvarchar(500) NULL,
        createdBy int NULL,
        FOREIGN KEY (pharmacyId) REFERENCES registered_pharmacies(id),
        FOREIGN KEY (planId) REFERENCES subscription_plans(id),
        FOREIGN KEY (createdBy) REFERENCES admin_users(id)
    );
    PRINT '✅ تم إنشاء جدول subscription_payments';
END

-- 5. جدول النسخ الاحتياطية
PRINT '';
PRINT '5. إنشاء جدول النسخ الاحتياطية...';
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='backup_history' AND xtype='U')
BEGIN
    CREATE TABLE backup_history (
        id int IDENTITY(1,1) PRIMARY KEY,
        pharmacyId int NOT NULL,
        backupType nvarchar(50) NOT NULL, -- Full, Incremental, Differential
        backupPath nvarchar(500) NOT NULL,
        backupSize bigint NULL,
        backupDate datetime DEFAULT GETDATE(),
        status nvarchar(50) DEFAULT 'Completed',
        errorMessage nvarchar(1000) NULL,
        createdBy int NULL,
        FOREIGN KEY (pharmacyId) REFERENCES registered_pharmacies(id),
        FOREIGN KEY (createdBy) REFERENCES admin_users(id)
    );
    PRINT '✅ تم إنشاء جدول backup_history';
END

-- 6. جدول سجل النشاطات
PRINT '';
PRINT '6. إنشاء جدول سجل النشاطات...';
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='activity_log' AND xtype='U')
BEGIN
    CREATE TABLE activity_log (
        id int IDENTITY(1,1) PRIMARY KEY,
        adminUserId int NULL,
        pharmacyId int NULL,
        activityType nvarchar(100) NOT NULL,
        description nvarchar(500) NOT NULL,
        details nvarchar(2000) NULL,
        ipAddress nvarchar(50) NULL,
        userAgent nvarchar(500) NULL,
        activityDate datetime DEFAULT GETDATE(),
        FOREIGN KEY (adminUserId) REFERENCES admin_users(id),
        FOREIGN KEY (pharmacyId) REFERENCES registered_pharmacies(id)
    );
    PRINT '✅ تم إنشاء جدول activity_log';
END

-- 7. جدول إعدادات النظام
PRINT '';
PRINT '7. إنشاء جدول إعدادات النظام...';
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='system_settings' AND xtype='U')
BEGIN
    CREATE TABLE system_settings (
        id int IDENTITY(1,1) PRIMARY KEY,
        settingKey nvarchar(100) UNIQUE NOT NULL,
        settingValue nvarchar(1000) NULL,
        description nvarchar(500) NULL,
        category nvarchar(100) NULL,
        isEditable bit DEFAULT 1,
        lastModified datetime DEFAULT GETDATE(),
        modifiedBy int NULL,
        FOREIGN KEY (modifiedBy) REFERENCES admin_users(id)
    );
    PRINT '✅ تم إنشاء جدول system_settings';
    
    -- إضافة إعدادات افتراضية
    INSERT INTO system_settings (settingKey, settingValue, description, category, isEditable)
    VALUES 
    ('SystemName', N'نظام إدارة الصيدليات المركزي', N'اسم النظام', 'General', 1),
    ('SystemVersion', '1.0.0', N'إصدار النظام', 'General', 0),
    ('MaxPharmacies', '1000', N'الحد الأقصى للصيدليات المسجلة', 'Limits', 1),
    ('BackupRetentionDays', '90', N'عدد أيام الاحتفاظ بالنسخ الاحتياطية', 'Backup', 1),
    ('DefaultSubscriptionDays', '30', N'مدة الاشتراك الافتراضية بالأيام', 'Subscription', 1),
    ('AutoApprovePharmacies', '0', N'الموافقة التلقائية على الصيدليات الجديدة', 'Approval', 1),
    ('MaintenanceMode', '0', N'وضع الصيانة', 'System', 1),
    ('SupportEmail', '<EMAIL>', N'بريد الدعم الفني', 'Contact', 1),
    ('SupportPhone', '0112345678', N'هاتف الدعم الفني', 'Contact', 1);
    
    PRINT '✅ تم إضافة الإعدادات الافتراضية';
END

-- 8. إنشاء الفهارس لتحسين الأداء
PRINT '';
PRINT '8. إنشاء الفهارس...';

-- فهارس جدول الصيدليات
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_registered_pharmacies_status')
    CREATE INDEX IX_registered_pharmacies_status ON registered_pharmacies(status);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_registered_pharmacies_city')
    CREATE INDEX IX_registered_pharmacies_city ON registered_pharmacies(city);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_registered_pharmacies_subscription')
    CREATE INDEX IX_registered_pharmacies_subscription ON registered_pharmacies(subscriptionType, subscriptionEndDate);

-- فهارس جدول المدفوعات
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_subscription_payments_pharmacy')
    CREATE INDEX IX_subscription_payments_pharmacy ON subscription_payments(pharmacyId, paymentDate);

-- فهارس جدول النشاطات
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_activity_log_date')
    CREATE INDEX IX_activity_log_date ON activity_log(activityDate);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_activity_log_pharmacy')
    CREATE INDEX IX_activity_log_pharmacy ON activity_log(pharmacyId, activityDate);

PRINT '✅ تم إنشاء الفهارس';

PRINT '';
PRINT '========================================';
PRINT '✅ تم إنشاء قاعدة بيانات الإدارة بنجاح';
PRINT '✅ Admin database created successfully';
PRINT '========================================';
PRINT '';
PRINT 'الجداول المنشأة:';
PRINT '- admin_users: المديرين العامين';
PRINT '- registered_pharmacies: الصيدليات المسجلة';
PRINT '- subscription_plans: خطط الاشتراك';
PRINT '- subscription_payments: مدفوعات الاشتراكات';
PRINT '- backup_history: سجل النسخ الاحتياطية';
PRINT '- activity_log: سجل النشاطات';
PRINT '- system_settings: إعدادات النظام';
PRINT '';
PRINT 'بيانات تسجيل الدخول للمدير العام:';
PRINT 'اسم المستخدم: superadmin';
PRINT 'كلمة المرور: admin2025';
PRINT '';
PRINT 'خطط الاشتراك المتاحة:';
PRINT '- أساسي: 99 ريال/شهر (3 مستخدمين، 500 دواء)';
PRINT '- قياسي: 199 ريال/شهر (5 مستخدمين، 1000 دواء)';
PRINT '- مميز: 299 ريال/شهر (10 مستخدمين، 5000 دواء)';
PRINT '- مؤسسي: 499 ريال/شهر (50 مستخدم، 50000 دواء)';
PRINT '';
PRINT '🚀 نظام الإدارة المركزي جاهز للاستخدام!';
