-- إضافة أكواد الصيدليات - سكريبت مبسط
-- Add Pharmacy Codes - Simple Script

USE UnifiedPharmacy;
GO

PRINT 'بدء إضافة أكواد الصيدليات...';

-- 1. إض<PERSON><PERSON>ة عمود pharmacy_code إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('pharmacies') AND name = 'pharmacy_code')
BEGIN
    ALTER TABLE pharmacies ADD pharmacy_code NVARCHAR(50);
    PRINT 'تم إضافة عمود pharmacy_code';
END
ELSE
BEGIN
    PRINT 'عمود pharmacy_code موجود بالفعل';
END

-- 2. تحديث الصيدليات الموجودة بأكواد فريدة
UPDATE pharmacies 
SET pharmacy_code = 'PHARM' + RIGHT('000' + CAST(id AS VARCHAR(3)), 3) 
WHERE pharmacy_code IS NULL;

PRINT 'تم تحديث الصيدليات الموجودة بأكواد فريدة';

-- 3. إضافة قيد الفرادة
IF NOT EXISTS (SELECT * FROM sys.key_constraints WHERE name = 'UQ_pharmacies_code')
BEGIN
    ALTER TABLE pharmacies ADD CONSTRAINT UQ_pharmacies_code UNIQUE (pharmacy_code);
    PRINT 'تم إضافة قيد الفرادة';
END

-- 4. إضافة صيدليات تجريبية
IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacy_code = 'MAIN001')
BEGIN
    INSERT INTO pharmacies (pharmacy_code, pharmacy_name, owner_name, phone, address, city, email, is_active)
    VALUES ('MAIN001', N'الصيدلية الرئيسية', N'المدير العام', '0123456789', N'الرياض - حي الملك فهد', N'الرياض', '<EMAIL>', 1);
    PRINT 'تم إنشاء الصيدلية الرئيسية';
END

IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacy_code = 'BRANCH01')
BEGIN
    INSERT INTO pharmacies (pharmacy_code, pharmacy_name, owner_name, phone, address, city, email, is_active)
    VALUES ('BRANCH01', N'صيدلية الفرع الأول', N'مدير الفرع', '0123456790', N'جدة - حي الصفا', N'جدة', '<EMAIL>', 1);
    PRINT 'تم إنشاء صيدلية الفرع الأول';
END

-- 5. تحديث المستخدمين
UPDATE users 
SET pharmacy_id = (SELECT TOP 1 id FROM pharmacies WHERE pharmacy_code = 'MAIN001')
WHERE pharmacy_id IS NULL OR pharmacy_id = 0;

PRINT 'تم ربط المستخدمين بالصيدلية الرئيسية';

-- 6. عرض النتائج
SELECT 
    id,
    pharmacy_code AS 'كود الصيدلية',
    pharmacy_name AS 'اسم الصيدلية',
    owner_name AS 'اسم المالك'
FROM pharmacies
ORDER BY id;

PRINT 'تم الانتهاء من إضافة أكواد الصيدليات بنجاح!';
