# دليل المستخدم الشامل - نظام إدارة الصيدلية

## 🏥 مرحباً بك في نظام إدارة الصيدلية المحدث

### 📋 المحتويات:
1. [بدء التشغيل](#بدء-التشغيل)
2. [واجهة الصيدلي](#واجهة-الصيدلي)
3. [واجهة المسؤول](#واجهة-المسؤول)
4. [الميزات الجديدة](#الميزات-الجديدة)
5. [حل المشاكل](#حل-المشاكل)

---

## 🚀 بدء التشغيل

### متطلبات النظام:
- Windows 10 أو أحدث
- .NET Framework 4.7.2 أو أحدث
- SQL Server 2016 أو أحدث
- 4 GB RAM كحد أدنى

### خطوات التشغيل:
1. **تشغيل قاعدة البيانات**: تأكد من تشغيل SQL Server
2. **فتح البرنامج**: شغل ملف `Pharmacy Management System.exe`
3. **تسجيل الدخول**: أدخل اسم المستخدم وكلمة المرور

---

## 👨‍⚕️ واجهة الصيدلي

### 🏠 لوحة التحكم (Dashboard)
- عرض إحصائيات سريعة
- الأدوية منتهية الصلاحية
- المخزون المنخفض

### ➕ إضافة دواء جديد
**الخطوات:**
1. اضغط على "Add Medicine"
2. أدخل البيانات الأساسية:
   - الرقم التعريفي (مطلوب)
   - اسم الدواء (مطلوب)
   - تاريخ الإنتاج والانتهاء
   - السعر (مطلوب)
3. أدخل الجرعات المختلفة:
   - الجرعة الأساسية (مطلوبة)
   - الجرعات الإضافية (اختيارية)
4. اضغط "Add" لحفظ البيانات

**نصائح:**
- ✅ تأكد من عدم تكرار الرقم التعريفي
- ✅ أدخل جميع الجرعات المتاحة
- ✅ تحقق من صحة التواريخ

### 🔄 تحديث دواء موجود
**الخطوات:**
1. اضغط على "Modify Medicine"
2. أدخل الرقم التعريفي أو اسم الدواء
3. ستظهر البيانات الحالية تلقائياً
4. عدل البيانات المطلوبة
5. اضغط "Update" لحفظ التغييرات

**ميزات التحديث:**
- 🔍 بحث تلقائي أثناء الكتابة
- 📝 تحديث جميع الجرعات
- ➕ إضافة كميات جديدة
- 📅 تحديث تواريخ الانتهاء

### 👁️ عرض الأدوية
**الوظائف:**
- عرض جميع الأدوية في جدول منظم
- البحث السريع
- حذف الأدوية (بتأكيد)
- تحديث البيانات

### 🛒 بيع الأدوية
**خطوات البيع:**
1. اضغط على "Sell Medicine"
2. اختر الدواء من القائمة
3. ستظهر معلومات الجرعات تلقائياً
4. أدخل الكمية المطلوبة
5. اضغط "Add to Cart"
6. كرر للأدوية الأخرى
7. اضغط "Sell and Print" لإتمام البيع

**الميزات الجديدة:**
- 💊 عرض تفصيلي للجرعات المختلفة
- 📊 عرض الكمية المتبقية
- 🧾 طباعة فاتورة احترافية
- 💾 حفظ تلقائي للمبيعات

### ⏰ فحص صلاحية الأدوية
**الوظائف:**
- عرض الأدوية منتهية الصلاحية
- تصفية حسب التاريخ
- طباعة تقرير مفصل
- تنبيهات للأدوية قريبة الانتهاء

---

## 👨‍💼 واجهة المسؤول

### 📊 تقرير المبيعات
**الميزات:**
- عرض جميع المبيعات مع تفاصيل الموظفين
- البحث حسب اسم الدواء أو الموظف
- تصفية حسب التاريخ
- تصفية حسب الموظف المحدد
- حساب إجمالي المبيعات

**كيفية الاستخدام:**
1. اضغط على "Sales Report"
2. استخدم مربع البحث للبحث السريع
3. اختر فترة زمنية من التقويم
4. اختر موظف محدد من القائمة
5. اضغط "عرض جلسات الموظفين" لرؤية أوقات العمل

### 👥 جلسات عمل الموظفين
**المعلومات المعروضة:**
- اسم المستخدم والموظف
- وقت الدخول والخروج
- مدة الجلسة
- حالة الجلسة (نشطة/منتهية)

---

## 🆕 الميزات الجديدة

### 1. نظام الجرعات المتعددة
- إمكانية إضافة 4 جرعات مختلفة لكل دواء
- عرض تفصيلي للجرعات في صفحة البيع
- حفظ كميات منفصلة لكل جرعة

### 2. تتبع المبيعات
- حفظ تلقائي لجميع عمليات البيع
- ربط المبيعات بالموظف المسؤول
- تقارير مفصلة للمبيعات

### 3. نظام جلسات العمل
- تسجيل تلقائي لدخول وخروج الموظفين
- تتبع ساعات العمل
- تقارير حضور الموظفين

### 4. تحسينات الطباعة
- تخطيط محسن للتقارير
- عرض جميع الأعمدة بوضوح
- معلومات الصفحة والتاريخ

### 5. واجهة محسنة
- رسائل باللغة العربية
- إزالة الرسائل الفارغة المزعجة
- تحسين تجربة المستخدم

---

## 🔧 حل المشاكل

### مشاكل شائعة وحلولها:

#### 1. لا تظهر المبيعات في التقرير
**الحل:**
- تأكد من إتمام عمليات بيع فعلية
- تحقق من اتصال قاعدة البيانات
- تأكد من إنشاء جدول sales

#### 2. لا تظهر معلومات الجرعات
**الحل:**
- تأكد من إدخال الجرعات عند إضافة الدواء
- تحقق من وجود بيانات في أعمدة dos2, dos3, dos4
- أعد تحميل بيانات الدواء

#### 3. مشاكل في الطباعة
**الحل:**
- تأكد من تثبيت طابعة
- تحقق من إعدادات الطباعة
- جرب طباعة تقرير بسيط أولاً

#### 4. رسائل خطأ في قاعدة البيانات
**الحل:**
- تحقق من اتصال SQL Server
- تأكد من صحة اسم قاعدة البيانات
- تحقق من صلاحيات المستخدم

### 📞 الدعم التقني
إذا واجهت مشاكل أخرى:
1. تحقق من ملف تعليمات_التشغيل.md
2. راجع ملف تحسينات_التصميم.md
3. تأكد من تطبيق جميع تحديثات قاعدة البيانات

---

## 🎯 نصائح للاستخدام الأمثل

### للصيادلة:
- 📝 أدخل جميع الجرعات عند إضافة دواء جديد
- 🔍 استخدم البحث السريع لتوفير الوقت
- 📊 راجع الكميات المتبقية بانتظام
- 🧾 اطبع الفواتير لجميع المبيعات

### للمسؤولين:
- 📈 راجع تقارير المبيعات يومياً
- 👥 تابع جلسات عمل الموظفين
- 💾 اعمل نسخ احتياطية دورية
- 🔄 حدث بيانات الأدوية بانتظام

---

**🎉 مبروك! أنت الآن جاهز لاستخدام نظام إدارة الصيدلية المحدث بكفاءة عالية.**
