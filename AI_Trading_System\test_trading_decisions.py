#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار قرارات التداول
Test Trading Decisions
"""

import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_trading_decisions():
    """اختبار قرارات التداول"""
    print("🧠 اختبار قرارات التداول...")
    print("=" * 50)
    
    try:
        from intelligent_trading_system_v2 import IntelligentTradingSystemV2
        
        # إنشاء النظام في الوضع الحقيقي
        print("1️⃣ إنشاء النظام...")
        system = IntelligentTradingSystemV2(demo_mode=False)
        
        print(f"✅ تم إنشاء النظام - وضع حقيقي: {not system.demo_mode}")
        
        # محاولة الاتصال
        print("\n2️⃣ الاتصال بـ MT5...")
        connected = system.connect_to_mt5()
        
        if not connected:
            print("❌ فشل في الاتصال بـ MT5")
            return False
        
        print("✅ تم الاتصال بنجاح")
        
        # اختبار التحليل الذكي
        print("\n3️⃣ اختبار التحليل الذكي...")
        
        for i in range(5):
            print(f"\n🔄 دورة تحليل {i+1}:")
            
            # تشغيل دورة تداول ذكية
            decision = system.run_intelligent_cycle()
            
            if decision:
                print(f"✅ قرار: {decision.get('decision', 'غير محدد')}")
                print(f"🎯 ثقة: {decision.get('confidence', 0):.2%}")
                print(f"💡 سبب: {decision.get('reason', 'غير محدد')}")
                
                if decision.get('decision') != 'hold':
                    print("🎉 النظام اتخذ قرار تداول!")
                    print(f"📊 حجم الصفقة: {decision.get('position_size', 0)}")
                    print(f"🛡️ وقف الخسارة: {decision.get('stop_loss', 0)}")
                    print(f"🎯 جني الربح: {decision.get('take_profit', 0)}")
                    return True
                else:
                    print("⏸️ قرار انتظار")
            else:
                print("❌ فشل في الحصول على قرار")
        
        print("\n⚠️ جميع القرارات كانت انتظار")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_confidence_threshold():
    """اختبار حد الثقة"""
    print("\n🎯 اختبار حد الثقة...")
    print("=" * 50)
    
    try:
        from advanced_analysis_engine import AdvancedAnalysisEngine
        
        # إنشاء محرك التحليل
        engine = AdvancedAnalysisEngine('EURUSD')
        
        print(f"✅ حد الثقة الحالي: {engine.analysis_config['min_confidence']:.2%}")
        
        if engine.analysis_config['min_confidence'] <= 0.5:
            print("✅ حد الثقة مناسب للتداول")
            return True
        else:
            print("⚠️ حد الثقة مرتفع قد يمنع التداول")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار حد الثقة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار قرارات التداول والثقة")
    print("=" * 60)
    
    # اختبار حد الثقة
    confidence_result = test_confidence_threshold()
    
    # اختبار قرارات التداول
    decision_result = test_trading_decisions()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"   🎯 حد الثقة: {'✅ مناسب' if confidence_result else '❌ مرتفع'}")
    print(f"   🧠 قرارات التداول: {'✅ يتخذ قرارات' if decision_result else '❌ انتظار فقط'}")
    
    if confidence_result and decision_result:
        print("\n🎉 النظام جاهز للتداول!")
        print("✅ سيتخذ قرارات تداول حقيقية")
        print("🔥 سيفتح صفقات في MetaTrader 5")
    elif confidence_result:
        print("\n⚠️ النظام محافظ جداً")
        print("💡 قد يحتاج وقت أطول لاتخاذ قرارات")
        print("🔄 جرب تشغيله لفترة أطول")
    else:
        print("\n❌ النظام لن يتداول")
        print("💡 حد الثقة مرتفع جداً")
        
    print("\n💡 للتشغيل الفعلي:")
    print("   🚀 run_intelligent_gui_v2.bat")
    print("   ⏰ اتركه يعمل لفترة أطول")
    print("   📊 راقب السجلات للقرارات")

if __name__ == "__main__":
    main()
