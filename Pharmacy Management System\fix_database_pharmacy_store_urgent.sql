-- إصلاح عاجل لقاعدة بيانات متجر الصيدلية
-- Fix Urgent Pharmacy Store Database Issues

USE UnifiedPharmacy;
GO

PRINT 'بدء إصلاح قاعدة بيانات متجر الصيدلية...';
PRINT 'Starting Pharmacy Store Database Fix...';

-- ===================================
-- 1. التحقق من وجود الجداول المطلوبة
-- ===================================

-- التحقق من جدول الصيدليات
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacies')
BEGIN
    PRINT 'إنشاء جدول الصيدليات...';
    CREATE TABLE pharmacies (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacy_name NVARCHAR(255) NOT NULL,
        owner_name NVARCHAR(255) NOT NULL,
        phone NVARCHAR(50),
        address NVARCHAR(500),
        email NVARCHAR(255),
        registration_date DATETIME DEFAULT GETDATE(),
        is_active BIT DEFAULT 1
    );
    PRINT '✅ تم إنشاء جدول pharmacies';
END
ELSE
    PRINT '✅ جدول pharmacies موجود';

-- التحقق من جدول الأدوية المنشورة
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'published_medicines')
BEGIN
    PRINT 'إنشاء جدول الأدوية المنشورة...';
    CREATE TABLE published_medicines (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacy_id INT NOT NULL,
        medicine_name NVARCHAR(255) NOT NULL,
        quantity INT NOT NULL DEFAULT 0,
        price_per_unit DECIMAL(10,2) NOT NULL DEFAULT 0,
        description NVARCHAR(1000),
        publish_date DATETIME DEFAULT GETDATE(),
        is_active BIT DEFAULT 1,
        dosage_info NVARCHAR(MAX), -- JSON للجرعات المتعددة
        expiry_date DATE,
        medicine_number NVARCHAR(100),
        FOREIGN KEY (pharmacy_id) REFERENCES pharmacies(id)
    );
    PRINT '✅ تم إنشاء جدول published_medicines';
END
ELSE
    PRINT '✅ جدول published_medicines موجود';

-- التحقق من جدول طلبات الشراء
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'purchase_requests')
BEGIN
    PRINT 'إنشاء جدول طلبات الشراء...';
    CREATE TABLE purchase_requests (
        id INT IDENTITY(1,1) PRIMARY KEY,
        buyer_pharmacy_id INT NOT NULL,
        seller_pharmacy_id INT NOT NULL,
        published_medicine_id INT NOT NULL,
        requested_quantity INT NOT NULL,
        offered_price DECIMAL(10,2),
        status NVARCHAR(50) DEFAULT 'pending', -- pending, accepted, rejected, completed
        request_date DATETIME DEFAULT GETDATE(),
        response_date DATETIME,
        response_message NVARCHAR(1000),
        FOREIGN KEY (buyer_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (seller_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (published_medicine_id) REFERENCES published_medicines(id)
    );
    PRINT '✅ تم إنشاء جدول purchase_requests';
END
ELSE
    PRINT '✅ جدول purchase_requests موجود';

-- التحقق من جدول الرسائل
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacy_messages')
BEGIN
    PRINT 'إنشاء جدول الرسائل...';
    CREATE TABLE pharmacy_messages (
        id INT IDENTITY(1,1) PRIMARY KEY,
        sender_pharmacy_id INT NOT NULL,
        receiver_pharmacy_id INT NOT NULL,
        message_content NVARCHAR(MAX) NOT NULL,
        send_date DATETIME DEFAULT GETDATE(),
        is_read BIT DEFAULT 0,
        related_request_id INT,
        FOREIGN KEY (sender_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (receiver_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (related_request_id) REFERENCES purchase_requests(id)
    );
    PRINT '✅ تم إنشاء جدول pharmacy_messages';
END
ELSE
    PRINT '✅ جدول pharmacy_messages موجود';

-- ===================================
-- 2. إضافة بيانات افتراضية للصيدليات
-- ===================================

-- التحقق من وجود صيدليات
IF NOT EXISTS (SELECT * FROM pharmacies)
BEGIN
    PRINT 'إضافة صيدليات افتراضية...';
    
    INSERT INTO pharmacies (pharmacy_name, owner_name, phone, address, email)
    VALUES 
    (N'صيدلية الشفاء', N'أحمد محمد', '01234567890', N'شارع الجامعة، المدينة', '<EMAIL>'),
    (N'صيدلية النور', N'فاطمة علي', '01234567891', N'شارع السلام، المدينة', '<EMAIL>'),
    (N'صيدلية الصحة', N'محمد حسن', '01234567892', N'شارع الملك، المدينة', '<EMAIL>');
    
    PRINT '✅ تم إضافة صيدليات افتراضية';
END
ELSE
    PRINT '✅ الصيدليات موجودة';

-- ===================================
-- 3. إنشاء/تحديث الإجراءات المخزنة
-- ===================================

-- إجراء نشر دواء
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_PublishMedicine')
    DROP PROCEDURE sp_PublishMedicine;
GO

CREATE PROCEDURE sp_PublishMedicine
    @pharmacy_id INT,
    @medicine_name NVARCHAR(255),
    @quantity INT,
    @price_per_unit DECIMAL(10,2),
    @description NVARCHAR(1000) = NULL,
    @dosage_info NVARCHAR(MAX) = NULL,
    @expiry_date DATE = NULL,
    @medicine_number NVARCHAR(100) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- التحقق من وجود الصيدلية
        IF NOT EXISTS (SELECT 1 FROM pharmacies WHERE id = @pharmacy_id)
        BEGIN
            RAISERROR(N'الصيدلية غير موجودة', 16, 1);
            RETURN;
        END
        
        -- التحقق من صحة البيانات
        IF @quantity <= 0
        BEGIN
            RAISERROR(N'الكمية يجب أن تكون أكبر من صفر', 16, 1);
            RETURN;
        END
        
        IF @price_per_unit <= 0
        BEGIN
            RAISERROR(N'السعر يجب أن يكون أكبر من صفر', 16, 1);
            RETURN;
        END
        
        -- نشر الدواء
        INSERT INTO published_medicines 
        (pharmacy_id, medicine_name, quantity, price_per_unit, description, dosage_info, expiry_date, medicine_number)
        VALUES 
        (@pharmacy_id, @medicine_name, @quantity, @price_per_unit, @description, @dosage_info, @expiry_date, @medicine_number);
        
        SELECT SCOPE_IDENTITY() as published_id;
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
GO

-- إجراء الحصول على الأدوية المنشورة
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetPublishedMedicines')
    DROP PROCEDURE sp_GetPublishedMedicines;
GO

CREATE PROCEDURE sp_GetPublishedMedicines
    @pharmacy_id INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        pm.id,
        pm.pharmacy_id,
        p.pharmacy_name,
        pm.medicine_name,
        pm.quantity,
        pm.price_per_unit,
        pm.description,
        pm.dosage_info,
        pm.expiry_date,
        pm.medicine_number,
        pm.publish_date,
        pm.is_active
    FROM published_medicines pm
    INNER JOIN pharmacies p ON pm.pharmacy_id = p.id
    WHERE (@pharmacy_id IS NULL OR pm.pharmacy_id = @pharmacy_id)
      AND pm.is_active = 1
    ORDER BY pm.publish_date DESC;
END
GO

-- إجراء تحديث دواء منشور
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_UpdatePublishedMedicine')
    DROP PROCEDURE sp_UpdatePublishedMedicine;
GO

CREATE PROCEDURE sp_UpdatePublishedMedicine
    @published_medicine_id INT,
    @quantity INT,
    @price_per_unit DECIMAL(10,2),
    @description NVARCHAR(1000) = NULL,
    @dosage_info NVARCHAR(MAX) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- التحقق من وجود الدواء المنشور
        IF NOT EXISTS (SELECT 1 FROM published_medicines WHERE id = @published_medicine_id)
        BEGIN
            RAISERROR(N'الدواء المنشور غير موجود', 16, 1);
            RETURN;
        END
        
        -- التحقق من صحة البيانات
        IF @quantity <= 0
        BEGIN
            RAISERROR(N'الكمية يجب أن تكون أكبر من صفر', 16, 1);
            RETURN;
        END
        
        IF @price_per_unit <= 0
        BEGIN
            RAISERROR(N'السعر يجب أن يكون أكبر من صفر', 16, 1);
            RETURN;
        END
        
        -- تحديث الدواء
        UPDATE published_medicines 
        SET 
            quantity = @quantity,
            price_per_unit = @price_per_unit,
            description = @description,
            dosage_info = @dosage_info
        WHERE id = @published_medicine_id;
        
        SELECT 1 as success;
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
GO

-- إجراء إنشاء طلب شراء
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_CreatePurchaseRequest')
    DROP PROCEDURE sp_CreatePurchaseRequest;
GO

CREATE PROCEDURE sp_CreatePurchaseRequest
    @buyer_pharmacy_id INT,
    @seller_pharmacy_id INT,
    @published_medicine_id INT,
    @requested_quantity INT,
    @offered_price DECIMAL(10,2) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- التحقق من صحة البيانات
        IF NOT EXISTS (SELECT 1 FROM pharmacies WHERE id = @buyer_pharmacy_id)
        BEGIN
            RAISERROR(N'صيدلية المشتري غير موجودة', 16, 1);
            RETURN;
        END
        
        IF NOT EXISTS (SELECT 1 FROM published_medicines WHERE id = @published_medicine_id AND pharmacy_id = @seller_pharmacy_id)
        BEGIN
            RAISERROR(N'الدواء المطلوب غير متاح', 16, 1);
            RETURN;
        END
        
        -- إنشاء طلب الشراء
        INSERT INTO purchase_requests 
        (buyer_pharmacy_id, seller_pharmacy_id, published_medicine_id, requested_quantity, offered_price)
        VALUES 
        (@buyer_pharmacy_id, @seller_pharmacy_id, @published_medicine_id, @requested_quantity, @offered_price);
        
        SELECT SCOPE_IDENTITY() as request_id;
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
GO

PRINT '✅ تم إنشاء جميع الإجراءات المخزنة بنجاح';

-- ===================================
-- 4. إضافة بيانات تجريبية للأدوية
-- ===================================

-- إضافة أدوية تجريبية إذا لم تكن موجودة
IF NOT EXISTS (SELECT * FROM published_medicines)
BEGIN
    PRINT 'إضافة أدوية تجريبية...';
    
    INSERT INTO published_medicines (pharmacy_id, medicine_name, quantity, price_per_unit, description, medicine_number)
    VALUES 
    (1, N'باراسيتامول 500mg', 100, 15.50, N'مسكن للألم وخافض للحرارة', 'PAR500'),
    (1, N'أموكسيسيلين 250mg', 50, 25.75, N'مضاد حيوي واسع المجال', 'AMX250'),
    (2, N'إيبوبروفين 400mg', 75, 18.25, N'مضاد للالتهاب ومسكن للألم', 'IBU400'),
    (2, N'أوميبرازول 20mg', 60, 32.00, N'لعلاج حموضة المعدة', 'OME20'),
    (3, N'لوراتادين 10mg', 80, 12.50, N'مضاد للحساسية', 'LOR10');
    
    PRINT '✅ تم إضافة أدوية تجريبية';
END
ELSE
    PRINT '✅ الأدوية المنشورة موجودة';

-- ===================================
-- 5. التحقق النهائي
-- ===================================

PRINT '';
PRINT '=== تقرير حالة قاعدة البيانات ===';
PRINT 'Database Status Report';
PRINT '';

-- عدد الصيدليات
DECLARE @PharmacyCount INT = (SELECT COUNT(*) FROM pharmacies);
PRINT 'عدد الصيدليات: ' + CAST(@PharmacyCount AS NVARCHAR(10));

-- عدد الأدوية المنشورة
DECLARE @PublishedCount INT = (SELECT COUNT(*) FROM published_medicines WHERE is_active = 1);
PRINT 'عدد الأدوية المنشورة: ' + CAST(@PublishedCount AS NVARCHAR(10));

-- عدد طلبات الشراء
DECLARE @RequestCount INT = (SELECT COUNT(*) FROM purchase_requests);
PRINT 'عدد طلبات الشراء: ' + CAST(@RequestCount AS NVARCHAR(10));

PRINT '';
PRINT '✅ تم إصلاح قاعدة البيانات بنجاح!';
PRINT '✅ Database fixed successfully!';
PRINT '';
PRINT 'يمكنك الآن اختبار نشر الأدوية وتعديلها';
PRINT 'You can now test publishing and editing medicines';
