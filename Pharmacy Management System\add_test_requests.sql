USE UnifiedPharmacy;

-- حذف الطلبات القديمة
DELETE FROM purchase_requests;

-- إضافة دواء جديد للاختبار
INSERT INTO medic (pharmacyId, mname, mnumber, quantity, eDate, perUnit, mDate, originalQuantity)
VALUES (1, N'باراسيتامول 500mg', 'PAR500', 100, '2025-12-31', 15.50, GETDATE(), 100);

-- الحصول على معرف الدواء
DECLARE @medicineId INT;
SELECT TOP 1 @medicineId = id FROM medic WHERE pharmacyId = 1 AND mname = N'باراسيتامول 500mg';

-- إضافة طلبات اختبار
INSERT INTO purchase_requests (medicine_id, buyer_pharmacy_id, seller_pharmacy_id, requested_quantity, offered_price, request_message, status, request_date)
VALUES 
(@medicineId, 2, 1, 20, 15.00, N'نحتاج هذا الدواء بشكل عاجل', 'pending', GETDATE()),
(@medicineId, 3, 1, 10, 15.50, N'طلب عادي', 'pending', DATEADD(HOUR, -1, GETDATE())),
(@medicineId, 2, 1, 5, 14.00, N'طلب صغير', 'pending', DATEADD(HOUR, -2, GETDATE()));

-- التحقق من النتيجة
SELECT 
    pr.id,
    m.mname as medicine_name,
    pr.requested_quantity,
    pr.status,
    p.pharmacyName as buyer_pharmacy
FROM purchase_requests pr
INNER JOIN medic m ON pr.medicine_id = m.id
INNER JOIN pharmacies p ON pr.buyer_pharmacy_id = p.id
WHERE pr.seller_pharmacy_id = 1;
