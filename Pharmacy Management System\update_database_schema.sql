-- تحديث مخطط قاعدة البيانات لنظام إدارة الصيدلية
-- Database Schema Update for Pharmacy Management System

-- إنشاء قاعدة البيانات إذا لم تكن موجودة
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'pharmacy')
BEGIN
    CREATE DATABASE pharmacy;
    PRINT 'تم إنشاء قاعدة البيانات pharmacy';
END
GO

USE pharmacy;
GO

-- ===================================
-- 1. جدول المستخدمين (users)
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
BEGIN
    CREATE TABLE users(
        id int identity (1,1) primary key,
        userRole varchar(50) not null,
        name varchar (250) not null,
        dob varchar (250) not null,
        mobile bigint not null,
        email varchar (250) not null,
        username varchar (250) unique not null,
        pass varchar(250) not null
    );
    PRINT 'تم إنشاء جدول users';
END
GO

-- ===================================
-- 2. جدول الأدوية (medic)
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='medic' AND xtype='U')
BEGIN
    CREATE TABLE medic(
        id int identity(1,1) primary key,
        mid varchar(250) not null,
        mname varchar (250) not null,
        mnumber varchar (250) not null,
        mDate varchar (250) not null,
        eDate varchar(250) not null,
        quantity bigint not null,
        perUnit bigint not null,
        lu varchar(250) not null,
        br varchar(250) not null
    );
    PRINT 'تم إنشاء جدول medic';
END
GO

-- إضافة الأعمدة الإضافية لجدول medic إذا لم تكن موجودة
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'newEDate')
BEGIN
    ALTER TABLE medic ADD newEDate VARCHAR(250) NULL;
    PRINT 'تم إضافة عمود newEDate';
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'newQuantity')
BEGIN
    ALTER TABLE medic ADD newQuantity BIGINT NULL;
    PRINT 'تم إضافة عمود newQuantity';
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'allqun')
BEGIN
    ALTER TABLE medic ADD allqun BIGINT NULL;
    PRINT 'تم إضافة عمود allqun';
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'mnumber_qty')
BEGIN
    ALTER TABLE medic ADD mnumber_qty INT NULL;
    PRINT 'تم إضافة عمود mnumber_qty';
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'newMDate')
BEGIN
    ALTER TABLE medic ADD newMDate VARCHAR(250) NULL;
    PRINT 'تم إضافة عمود newMDate';
END

-- إضافة أعمدة الجرعات
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'dos2')
BEGIN
    ALTER TABLE medic ADD dos2 VARCHAR(100) NULL;
    PRINT 'تم إضافة عمود dos2';
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'dos2_qty')
BEGIN
    ALTER TABLE medic ADD dos2_qty INT NULL;
    PRINT 'تم إضافة عمود dos2_qty';
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'dos3')
BEGIN
    ALTER TABLE medic ADD dos3 VARCHAR(100) NULL;
    PRINT 'تم إضافة عمود dos3';
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'dos3_qty')
BEGIN
    ALTER TABLE medic ADD dos3_qty INT NULL;
    PRINT 'تم إضافة عمود dos3_qty';
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'dos4')
BEGIN
    ALTER TABLE medic ADD dos4 VARCHAR(100) NULL;
    PRINT 'تم إضافة عمود dos4';
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'dos4_qty')
BEGIN
    ALTER TABLE medic ADD dos4_qty INT NULL;
    PRINT 'تم إضافة عمود dos4_qty';
END

-- إضافة أعمدة الكميات الأصلية
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'originalQuantity')
BEGIN
    ALTER TABLE medic ADD originalQuantity BIGINT DEFAULT 0;
    PRINT 'تم إضافة عمود originalQuantity';
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'originalNewQuantity')
BEGIN
    ALTER TABLE medic ADD originalNewQuantity BIGINT DEFAULT 0;
    PRINT 'تم إضافة عمود originalNewQuantity';
END

-- ===================================
-- 3. جدول المبيعات (sales)
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='sales' AND xtype='U')
BEGIN
    CREATE TABLE sales (
        id INT IDENTITY(1,1) PRIMARY KEY,
        mid VARCHAR(250) NOT NULL,
        medicineName VARCHAR(250) NOT NULL,
        dosage VARCHAR(100) NOT NULL,
        quantity INT NOT NULL,
        pricePerUnit BIGINT NOT NULL,
        totalPrice BIGINT NOT NULL,
        employeeUsername VARCHAR(250) NOT NULL,
        employeeName VARCHAR(250) NOT NULL,
        saleDate DATETIME DEFAULT GETDATE()
    );
    PRINT 'تم إنشاء جدول sales';
END
GO

-- ===================================
-- 4. جدول جلسات الموظفين (employee_sessions)
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='employee_sessions' AND xtype='U')
BEGIN
    CREATE TABLE employee_sessions (
        id INT IDENTITY(1,1) PRIMARY KEY,
        username VARCHAR(250),
        employeeName VARCHAR(250),
        loginTime DATETIME,
        logoutTime DATETIME NULL,
        sessionDate DATE
    );
    PRINT 'تم إنشاء جدول employee_sessions';
END
GO

-- ===================================
-- 5. تحديث البيانات الموجودة
-- ===================================

-- تحديث الكميات الأصلية للأدوية الموجودة
UPDATE medic 
SET originalQuantity = quantity 
WHERE originalQuantity = 0 OR originalQuantity IS NULL;

UPDATE medic 
SET originalNewQuantity = ISNULL(newQuantity, 0) 
WHERE originalNewQuantity = 0 OR originalNewQuantity IS NULL;

-- تنظيف البيانات غير الصالحة للتواريخ
UPDATE medic
SET newEDate = NULL
WHERE TRY_CONVERT(datetime, newEDate, 103) IS NULL AND newEDate IS NOT NULL;

UPDATE medic
SET eDate = '01/01/1900'
WHERE TRY_CONVERT(datetime, eDate, 103) IS NULL;

PRINT 'تم تحديث قاعدة البيانات بنجاح!';
PRINT 'يمكنك الآن استخدام نظام إدارة الصيدلية.';

-- عرض معلومات الجداول
PRINT '===================================';
PRINT 'معلومات الجداول:';
PRINT '===================================';

SELECT 'users' as TableName, COUNT(*) as RecordCount FROM users
UNION ALL
SELECT 'medic' as TableName, COUNT(*) as RecordCount FROM medic
UNION ALL
SELECT 'sales' as TableName, COUNT(*) as RecordCount FROM sales
UNION ALL
SELECT 'employee_sessions' as TableName, COUNT(*) as RecordCount FROM employee_sessions;
