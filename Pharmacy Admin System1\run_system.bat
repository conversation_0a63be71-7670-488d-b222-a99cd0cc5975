@echo off
chcp 65001 >nul
echo ========================================
echo 🚀 نظام إدارة الصيدليات المركزي
echo 🚀 Central Pharmacy Management System
echo ========================================
echo.

echo الخطوة 1: إعداد قاعدة البيانات...
sqlcmd -S NARUTO -E -i quick_setup.sql

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إعداد قاعدة البيانات بنجاح
    echo ✅ Database setup completed successfully
) else (
    echo ❌ خطأ في إعداد قاعدة البيانات
    echo ❌ Database setup failed
)

echo.
echo الخطوة 2: فتح المشروع في Visual Studio...
echo Step 2: Opening project in Visual Studio...

start "" "Pharmacy Admin System1.sln"

echo.
echo ========================================
echo 🔐 بيانات تسجيل الدخول:
echo 🔐 Login credentials:
echo اسم المستخدم | Username: superadmin
echo كلمة المرور | Password: admin2025
echo ========================================
echo.
echo 📋 الخطوات التالية:
echo 📋 Next steps:
echo 1. انتظر حتى يتم تحميل المشروع في Visual Studio
echo 2. اضغط F5 لتشغيل البرنامج
echo 3. سجل دخول بالبيانات أعلاه
echo.

pause
