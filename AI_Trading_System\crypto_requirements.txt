# متطلبات نظام تداول العملات الرقمية
# Cryptocurrency Trading System Requirements

# مكتبات التحليل والبيانات
pandas>=1.5.0
numpy>=1.21.0
requests>=2.28.0

# مكتبات التحليل الفني
ta>=0.10.0

# مكتبات التعلم الآلي
scikit-learn>=1.1.0

# مكتبات الواجهة الرسومية
tkinter  # مدمجة مع Python

# مكتبات إضافية
matplotlib>=3.5.0  # للرسوم البيانية (اختياري)
plotly>=5.0.0      # للرسوم التفاعلية (اختياري)

# مكتبة Binance API (للتداول الحقيقي)
python-binance>=1.0.0  # اختياري للتداول الحقيقي
