-- إصلاح الأعمدة المفقودة في قاعدة البيانات UnifiedPharmacy
-- Fix missing columns in UnifiedPharmacy database

USE UnifiedPharmacy;
GO

PRINT '========================================';
PRINT '   إصلاح الأعمدة المفقودة';
PRINT '   Fixing Missing Columns';
PRINT '========================================';

-- 1. التحقق من وجود جدول users وإضافة الأعمدة المفقودة
PRINT '';
PRINT '1. فحص جدول users...';

-- إضافة عمود pharmacyId إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'pharmacyId')
BEGIN
    ALTER TABLE users ADD pharmacyId int NULL;
    PRINT '✅ تم إضافة عمود pharmacyId إلى جدول users';
END
ELSE
BEGIN
    PRINT '✅ عمود pharmacyId موجود في جدول users';
END

-- إضافة عمود isActive إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'isActive')
BEGIN
    ALTER TABLE users ADD isActive bit DEFAULT 1;
    PRINT '✅ تم إضافة عمود isActive إلى جدول users';
END
ELSE
BEGIN
    PRINT '✅ عمود isActive موجود في جدول users';
END

-- إضافة عمود createdDate إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'createdDate')
BEGIN
    ALTER TABLE users ADD createdDate datetime DEFAULT GETDATE();
    PRINT '✅ تم إضافة عمود createdDate إلى جدول users';
END
ELSE
BEGIN
    PRINT '✅ عمود createdDate موجود في جدول users';
END

-- 2. التحقق من وجود جدول pharmacies
PRINT '';
PRINT '2. فحص جدول pharmacies...';

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies' AND xtype='U')
BEGIN
    PRINT 'إنشاء جدول pharmacies...';
    CREATE TABLE pharmacies (
        id int IDENTITY(1,1) PRIMARY KEY,
        pharmacyName nvarchar(255) NOT NULL,
        pharmacyCode nvarchar(50) UNIQUE NOT NULL,
        ownerName nvarchar(255) NULL,
        address nvarchar(500) NULL,
        city nvarchar(100) NULL,
        phone nvarchar(20) NULL,
        email nvarchar(255) NULL,
        isActive bit DEFAULT 1,
        subscriptionType nvarchar(50) DEFAULT 'Basic',
        registrationDate datetime DEFAULT GETDATE()
    );
    PRINT '✅ تم إنشاء جدول pharmacies';
END
ELSE
BEGIN
    PRINT '✅ جدول pharmacies موجود';
END

-- 3. إضافة صيدلية افتراضية إذا لم تكن موجودة
PRINT '';
PRINT '3. فحص الصيدلية الافتراضية...';

IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacyCode = 'MAIN001')
BEGIN
    INSERT INTO pharmacies (pharmacyName, pharmacyCode, ownerName, address, city, phone, email, isActive, subscriptionType)
    VALUES (N'الصيدلية الرئيسية', 'MAIN001', N'مدير النظام', N'الرياض', N'الرياض', '0112345678', '<EMAIL>', 1, 'Premium');
    PRINT '✅ تم إضافة الصيدلية الافتراضية';
END
ELSE
BEGIN
    PRINT '✅ الصيدلية الافتراضية موجودة';
END

-- 4. إضافة Foreign Key إذا لم يكن موجوداً
PRINT '';
PRINT '4. فحص Foreign Key...';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME = 'FK_users_pharmacies')
BEGIN
    -- التأكد من أن جميع المستخدمين لديهم pharmacyId صحيح
    DECLARE @defaultPharmacyId INT;
    SELECT @defaultPharmacyId = id FROM pharmacies WHERE pharmacyCode = 'MAIN001';
    
    -- تحديث المستخدمين الذين لا يملكون pharmacyId صحيح
    UPDATE users 
    SET pharmacyId = @defaultPharmacyId 
    WHERE pharmacyId IS NULL OR pharmacyId = 0 OR pharmacyId NOT IN (SELECT id FROM pharmacies);
    
    -- إضافة Foreign Key
    ALTER TABLE users 
    ADD CONSTRAINT FK_users_pharmacies 
    FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id);
    
    PRINT '✅ تم إضافة Foreign Key بين users و pharmacies';
END
ELSE
BEGIN
    PRINT '✅ Foreign Key موجود بين users و pharmacies';
END

-- 5. تحديث المستخدمين الموجودين
PRINT '';
PRINT '5. تحديث المستخدمين الموجودين...';

DECLARE @defaultPharmacyId2 INT;
SELECT @defaultPharmacyId2 = id FROM pharmacies WHERE pharmacyCode = 'MAIN001';

-- تحديث المستخدمين الذين لا يملكون pharmacyId صحيح
UPDATE users 
SET pharmacyId = @defaultPharmacyId2, isActive = 1 
WHERE pharmacyId IS NULL OR pharmacyId = 0;

DECLARE @updatedCount INT = @@ROWCOUNT;
PRINT '✅ تم تحديث ' + CAST(@updatedCount AS VARCHAR(10)) + ' مستخدم';

-- 6. إضافة مستخدمين افتراضيين إذا لم يكونوا موجودين
PRINT '';
PRINT '6. فحص المستخدمين الافتراضيين...';

-- إضافة المدير الافتراضي
IF NOT EXISTS (SELECT * FROM users WHERE username = 'admin')
BEGIN
    INSERT INTO users (userRole, name, dob, mobile, email, username, pass, pharmacyId, isActive)
    VALUES ('Administrator', N'مدير النظام', '1990-01-01', '0501234567', '<EMAIL>', 'admin', 'admin123', @defaultPharmacyId2, 1);
    PRINT '✅ تم إضافة المدير الافتراضي (admin/admin123)';
END
ELSE
BEGIN
    -- تحديث المدير الموجود
    UPDATE users 
    SET pharmacyId = @defaultPharmacyId2, isActive = 1 
    WHERE username = 'admin';
    PRINT '✅ تم تحديث المدير الموجود';
END

-- إضافة الصيدلي الافتراضي
IF NOT EXISTS (SELECT * FROM users WHERE username = 'pharmacist')
BEGIN
    INSERT INTO users (userRole, name, dob, mobile, email, username, pass, pharmacyId, isActive)
    VALUES ('Pharmacist', N'صيدلي النظام', '1992-01-01', '0507654321', '<EMAIL>', 'pharmacist', 'pharm123', @defaultPharmacyId2, 1);
    PRINT '✅ تم إضافة الصيدلي الافتراضي (pharmacist/pharm123)';
END
ELSE
BEGIN
    -- تحديث الصيدلي الموجود
    UPDATE users 
    SET pharmacyId = @defaultPharmacyId2, isActive = 1 
    WHERE username = 'pharmacist';
    PRINT '✅ تم تحديث الصيدلي الموجود';
END

-- 7. فحص جدول networkmedicines
PRINT '';
PRINT '7. فحص جدول networkmedicines...';

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='networkmedicines' AND xtype='U')
BEGIN
    PRINT 'إنشاء جدول networkmedicines...';
    CREATE TABLE networkmedicines (
        id int IDENTITY(1,1) PRIMARY KEY,
        pharmacyId int NOT NULL,
        medicineName nvarchar(255) NOT NULL,
        manufacturer nvarchar(255) NULL,
        category nvarchar(100) NULL,
        pricePerUnit decimal(10,2) NOT NULL DEFAULT 0.0,
        availableQuantity int NOT NULL,
        expiryDate datetime NOT NULL,
        description nvarchar(500) NULL DEFAULT '',
        dateAdded datetime DEFAULT GETDATE(),
        isActive bit DEFAULT 1,
        isAvailableForSale bit DEFAULT 1,
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
    );
    PRINT '✅ تم إنشاء جدول networkmedicines';
    
    -- إضافة أدوية تجريبية
    INSERT INTO networkmedicines (pharmacyId, medicineName, manufacturer, category, pricePerUnit, availableQuantity, expiryDate, description, isActive, isAvailableForSale)
    VALUES
    (@defaultPharmacyId2, N'باراسيتامول 500 مجم', N'شركة الدواء السعودية', N'مسكنات', 15.50, 100, DATEADD(YEAR, 2, GETDATE()), N'مسكن للألم وخافض للحرارة', 1, 1),
    (@defaultPharmacyId2, N'أموكسيسيلين 250 مجم', N'شركة المضادات الحيوية', N'مضادات حيوية', 45.00, 50, DATEADD(YEAR, 1, GETDATE()), N'مضاد حيوي واسع المجال', 1, 1),
    (@defaultPharmacyId2, N'إيبوبروفين 400 مجم', N'شركة الأدوية المتقدمة', N'مضادات الالتهاب', 25.75, 75, DATEADD(MONTH, 18, GETDATE()), N'مضاد للالتهاب ومسكن', 1, 1),
    (@defaultPharmacyId2, N'أوميبرازول 20 مجم', N'شركة الجهاز الهضمي', N'أدوية الجهاز الهضمي', 35.25, 60, DATEADD(YEAR, 2, GETDATE()), N'لعلاج قرحة المعدة', 1, 1),
    (@defaultPharmacyId2, N'لوراتادين 10 مجم', N'شركة الحساسية', N'مضادات الحساسية', 18.90, 80, DATEADD(MONTH, 15, GETDATE()), N'لعلاج الحساسية', 1, 1);
    
    PRINT '✅ تم إضافة 5 أدوية تجريبية';
END
ELSE
BEGIN
    PRINT '✅ جدول networkmedicines موجود';
    
    -- التحقق من وجود أدوية تجريبية
    IF NOT EXISTS (SELECT * FROM networkmedicines)
    BEGIN
        INSERT INTO networkmedicines (pharmacyId, medicineName, manufacturer, category, pricePerUnit, availableQuantity, expiryDate, description, isActive, isAvailableForSale)
        VALUES
        (@defaultPharmacyId2, N'باراسيتامول 500 مجم', N'شركة الدواء السعودية', N'مسكنات', 15.50, 100, DATEADD(YEAR, 2, GETDATE()), N'مسكن للألم وخافض للحرارة', 1, 1),
        (@defaultPharmacyId2, N'أموكسيسيلين 250 مجم', N'شركة المضادات الحيوية', N'مضادات حيوية', 45.00, 50, DATEADD(YEAR, 1, GETDATE()), N'مضاد حيوي واسع المجال', 1, 1),
        (@defaultPharmacyId2, N'إيبوبروفين 400 مجم', N'شركة الأدوية المتقدمة', N'مضادات الالتهاب', 25.75, 75, DATEADD(MONTH, 18, GETDATE()), N'مضاد للالتهاب ومسكن', 1, 1),
        (@defaultPharmacyId2, N'أوميبرازول 20 مجم', N'شركة الجهاز الهضمي', N'أدوية الجهاز الهضمي', 35.25, 60, DATEADD(YEAR, 2, GETDATE()), N'لعلاج قرحة المعدة', 1, 1),
        (@defaultPharmacyId2, N'لوراتادين 10 مجم', N'شركة الحساسية', N'مضادات الحساسية', 18.90, 80, DATEADD(MONTH, 15, GETDATE()), N'لعلاج الحساسية', 1, 1);
        
        PRINT '✅ تم إضافة 5 أدوية تجريبية';
    END
    ELSE
    BEGIN
        PRINT '✅ الأدوية التجريبية موجودة';
    END
END

PRINT '';
PRINT '========================================';
PRINT '✅ تم إصلاح جميع الأعمدة المفقودة';
PRINT '✅ All missing columns fixed';
PRINT '========================================';
PRINT '';
PRINT 'الجداول والأعمدة المتوفرة الآن:';
PRINT '- users: id, userRole, name, dob, mobile, email, username, pass, pharmacyId, isActive, createdDate';
PRINT '- pharmacies: id, pharmacyName, pharmacyCode, ownerName, address, city, phone, email, isActive, subscriptionType, registrationDate';
PRINT '- networkmedicines: id, pharmacyId, medicineName, manufacturer, category, pricePerUnit, availableQuantity, expiryDate, description, dateAdded, isActive, isAvailableForSale';
PRINT '';
PRINT 'البيانات الافتراضية:';
PRINT '- صيدلية رئيسية: الصيدلية الرئيسية (MAIN001)';
PRINT '- مدير: admin / admin123';
PRINT '- صيدلي: pharmacist / pharm123';
PRINT '- 5 أدوية تجريبية في الشبكة';
PRINT '';
PRINT '🚀 النظام جاهز للاستخدام الآن!';
PRINT 'جرب تسجيل الدخول والضغط على زر "Connect" في صفحة المتجر';
