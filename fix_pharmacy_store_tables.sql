-- إصلاح جداول متجر الصيدلية
USE UnifiedPharmacy;

-- حذف الجداول القديمة إذا كانت موجودة
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'medicine_requests')
    DROP TABLE medicine_requests;

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'published_medicines')
    DROP TABLE published_medicines;

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacies')
    DROP TABLE pharmacies;

-- إنشاء جدول الصيدليات
CREATE TABLE pharmacies (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacy_name NVARCHAR(255) NOT NULL,
    address NVARCHAR(500),
    city NVARCHAR(100),
    phone NVARCHAR(50),
    email NVARCHAR(255),
    created_date DATETIME DEFAULT GETDATE(),
    is_active BIT DEFAULT 1
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول الأدوية المنشورة
CREATE TABLE published_medicines (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacy_id INT NOT NULL,
    medicine_name NVARCHAR(255) NOT NULL,
    medicine_number NVARCHAR(100),
    quantity INT NOT NULL,
    expiry_date DATE NOT NULL,
    price_per_unit DECIMAL(10,2) NOT NULL,
    description NVARCHAR(1000),
    publish_date DATETIME DEFAULT GETDATE(),
    is_active BIT DEFAULT 1,
    FOREIGN KEY (pharmacy_id) REFERENCES pharmacies(id)
);

-- إنشاء جدول طلبات الأدوية
CREATE TABLE medicine_requests (
    id INT IDENTITY(1,1) PRIMARY KEY,
    published_medicine_id INT NOT NULL,
    requesting_pharmacy_id INT NOT NULL,
    request_quantity INT NOT NULL,
    request_message NVARCHAR(1000),
    request_date DATETIME DEFAULT GETDATE(),
    status NVARCHAR(50) DEFAULT 'Pending',
    response_message NVARCHAR(1000),
    response_date DATETIME,
    FOREIGN KEY (published_medicine_id) REFERENCES published_medicines(id),
    FOREIGN KEY (requesting_pharmacy_id) REFERENCES pharmacies(id)
);

-- إدراج صيدلية افتراضية
INSERT INTO pharmacies (pharmacy_name, address, city, phone, email)
VALUES ('الصيدلية الرئيسية', 'العنوان الافتراضي', 'المدينة', '123456789', '<EMAIL>');

-- إدراج بعض الأدوية المنشورة للاختبار
INSERT INTO published_medicines (pharmacy_id, medicine_name, medicine_number, quantity, expiry_date, price_per_unit, description)
VALUES 
(1, 'باراسيتامول', '001', 100, '2025-12-31', 5.50, 'مسكن للألم وخافض للحرارة'),
(1, 'أسبرين', '002', 50, '2025-06-30', 3.25, 'مسكن ومضاد للالتهاب'),
(1, 'فيتامين سي', '003', 200, '2026-01-15', 12.00, 'مكمل غذائي لتقوية المناعة');

PRINT 'تم إنشاء جداول متجر الصيدلية بنجاح!';
