@echo off
chcp 65001 > nul
title 🚀 Advanced Trading System with Backtesting

echo.
echo ===============================================
echo 🚀 Advanced Trading System with Backtesting
echo ===============================================
echo.
echo ✨ New Features:
echo    📈 Live Trading with Real-time Analysis
echo    🔄 Historical Backtesting Simulation
echo    📊 LIVE Performance Metrics During Backtest
echo    🧠 REAL-TIME Learning System
echo    🎯 Advanced Confidence Display
echo    💰 Multi-Symbol Support (50+ pairs)
echo    🔍 MT5 Symbol Search Integration
echo    📋 Detailed Trade Logging
echo    💡 AI-Powered Decision Making
echo.
echo 🔄 Backtesting Features:
echo    📅 Multiple Time Periods:
echo       • 1 Month
echo       • 3 Months  
echo       • 6 Months
echo       • 1 Year
echo       • 2 Years
echo.
echo    📊 Performance Metrics:
echo       • Total Trades & Win Rate
echo       • Profit/Loss Analysis
echo       • Maximum Drawdown
echo       • Sharpe Ratio
echo       • Average Trade Performance
echo.
echo    🎯 Trading Simulation:
echo       • Historical Price Data
echo       • Real Trading Conditions
echo       • Risk Management Rules
echo       • Entry/Exit Signals
echo       • LIVE Performance Tracking
echo       • Real-time Statistics Updates
echo       • Advanced Learning System
echo       • Mistake Pattern Recognition
echo       • Strategy Auto-Optimization
echo.
echo 🎛️ How to Use:
echo    1. Connect to MetaTrader 5
echo    2. Choose your symbol (BTCUSD, EURUSD, etc.)
echo    3. Select mode:
echo       📈 Live Trading - Real market execution
echo       🔄 Backtesting - Historical simulation
echo    4. Set confidence threshold (30-100%)
echo    5. For Backtesting: Choose time period
echo    6. Click Start/Run to begin
echo.
echo 🛡️ Safety Features:
echo    • Demo Mode for safe testing
echo    • Real-time risk monitoring
echo    • Stop-loss protection
echo    • Position size management
echo.
echo 🧠 NEW: Advanced Learning System:
echo    • Remembers successful patterns
echo    • Avoids repeated mistakes
echo    • Auto-adjusts confidence levels
echo    • Generates strategy recommendations
echo    • Learns from every trade
echo    • Click "🧠 Learning Stats" to view insights
echo.
echo 📋 Requirements:
echo    ✓ MetaTrader 5 installed and running
echo    ✓ Valid MT5 account (demo or live)
echo    ✓ Stable internet connection
echo    ✓ Python libraries installed
echo.
echo 🚀 Starting Advanced Trading System...
echo.

cd /d "%~dp0"
python advanced_trading_gui.py

echo.
echo ⏹️ System closed
pause
