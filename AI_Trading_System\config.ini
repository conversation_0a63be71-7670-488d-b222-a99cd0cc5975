[MT5_CONNECTION]
server = MetaQuotes-Demo
login = 96406085
password = D!2qKdJy
timeout = 10

[MULTI_CURRENCY_TRADING]
# Major currency pairs for simultaneous analysis
major_pairs = EURUSD,GBPUSD,USDJPY,AUDUSD,USDCAD,NZDUSD
# Cross currency pairs
cross_pairs = EURGBP,EURJPY,GBPJPY,AUDJPY,EURCHF,GBPCHF
# Exotic pairs (optional)
exotic_pairs = USDTRY,USDZAR,USDMXN
# All active symbols
active_symbols = EURUSD,GBPUSD,USDJPY,AUDUSD,USDCAD,NZDUSD,EURGBP,EURJPY,GBPJPY,AUDJPY,USDCHF,EURCHF
lot_size = 0.01
sl_pips = 50
tp_pips = 100
max_positions_total = 15
max_positions_per_symbol = 2
risk_per_trade = 0.02
max_concurrent_analysis = 12

[DATA_SETTINGS]
data_source = alpha_vantage
years_of_data = 5
update_frequency = daily
yahoo_symbols = XAUUSD=GC-F,USDCAD=USDCAD-X,EURUSD=EURUSD-X,GBPUSD=GBPUSD-X
alpha_vantage_api_key = 2VEPVRWAMNOOMX3Q
alpha_vantage_symbols = XAUUSD=XAUUSD,USDCAD=USDCAD,EURUSD=EURUSD,GBPUSD=GBPUSD

[INTELLIGENT_TRADING]
max_positions = 5
training_days = 365
update_interval = 300
training_interval = 3600
min_confidence = 0.3
max_risk = 0.02
position_sizing_method = confidence_based
max_position_multiplier = 3.0
continuous_trading = true

[MODEL_SETTINGS]
model_type = lightgbm
model_path = models/trading_model.joblib
retrain_frequency = daily
test_size = 0.2
cv_folds = 5
use_technical_indicators = true
lookback_period = 20
prediction_horizon = 1

[LOGGING]
log_level = INFO
log_file = logs/trading.log
trade_log_file = logs/trades.log
max_log_size = 10MB
backup_count = 5

[SAFETY]
demo_mode = true
min_balance = 10.0
max_daily_trades = 5
max_daily_loss = 50
emergency_stop = false

[MULTI_STRATEGY_ANALYSIS]
# Multiple strategies for each currency
strategies = trend_following,mean_reversion,breakout,momentum,scalping,swing_trading
# Multiple timeframes for comprehensive analysis
timeframes = M1,M5,M15,M30,H1,H4,D1,W1,MN1
# Primary timeframes for main analysis
primary_timeframes = M15,H1,H4,D1
# Quick scalping timeframes
scalping_timeframes = M1,M5,M15
# Long-term analysis timeframes
longterm_timeframes = H4,D1,W1,MN1
min_confidence = 0.65
max_risk_per_trade = 0.02
analysis_interval = 180
learning_enabled = true
use_advanced_indicators = true
multi_timeframe_analysis = true
multi_strategy_analysis = true
strategy_weight_learning = true
adaptive_strategy_selection = true

[MONEY_MANAGEMENT]
small_account_threshold = 100.0
medium_account_threshold = 1000.0
max_margin_usage = 0.30
max_spread_pips = 3.0
small_account_max_risk = 0.05
medium_account_max_risk = 0.03
large_account_max_risk = 0.02

[RISK_MANAGEMENT]
max_daily_risk = 0.05
max_weekly_risk = 0.15
max_monthly_risk = 0.30
stop_loss_atr_multiplier = 2.0
take_profit_atr_multiplier = 3.0
min_risk_reward_ratio = 1.5

[SELF_LEARNING_SYSTEM]
# Continuous learning and adaptation
continuous_learning = true
learning_update_interval = 1800
experience_memory_size = 10000
strategy_performance_tracking = true
adaptive_parameter_optimization = true
cross_currency_learning = true
pattern_recognition_learning = true
market_condition_adaptation = true
# Learning from successful and failed trades
success_pattern_weight = 1.5
failure_pattern_weight = 2.0
# Strategy evolution parameters
strategy_mutation_rate = 0.1
strategy_crossover_rate = 0.3
population_size = 50
generations = 100

[ADVANCED_BACKTESTING]
default_test_days = 30
initial_balance = 1000.0
commission_per_lot = 7.0
enable_learning_from_backtest = true
multi_currency_backtest = true
multi_strategy_backtest = true
walk_forward_analysis = true
monte_carlo_simulation = true
stress_testing = true

