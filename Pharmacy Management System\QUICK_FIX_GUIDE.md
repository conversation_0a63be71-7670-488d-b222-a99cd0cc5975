# دليل الإصلاح السريع - Quick Fix Guide

## 🚨 **إذا ظهرت أخطاء في البناء:**

### **الخطوة 1: إعادة بناء المشروع**
1. افتح **Visual Studio**
2. اضغط **Build** → **Rebuild Solution**
3. أو اضغط **Ctrl+Shift+B**

### **الخطوة 2: إذا استمرت الأخطاء**
1. اضغط **Tools** → **NuGet Package Manager** → **Package Manager Console**
2. شغل: `Update-Package -reinstall`

### **الخطوة 3: إضافة المراجع المفقودة**
إذا ظهرت أخطاء "UnifiedFunction not found":

1. **في Solution Explorer**
2. **Right-click على المشروع**
3. **Add** → **Existing Item**
4. **اختر الملفات:**
   - `UnifiedFunction.cs`
   - `SessionManager.cs`
   - `PharmacySelectionForm.cs`
   - `PharmacySelectionForm.Designer.cs`

### **الخطوة 4: إصلاح مراجع قاعدة البيانات**
إذا ظهرت أخطاء اتصال قاعدة البيانات:

1. **تأكد من تشغيل SQL Server**
2. **تأكد من وجود قاعدة البيانات UnifiedPharmacy**
3. **شغل السكريپت:**
```sql
CREATE DATABASE UnifiedPharmacy;
```

---

## 🎯 **إصلاح سريع للأخطاء الشائعة:**

### **خطأ: "UnifiedFunction could not be found"**
**الحل:**
```csharp
// في أعلى ملف function.cs أضف:
using System.Data;
using System.Data.SqlClient;
```

### **خطأ: "SessionManager could not be found"**
**الحل:**
```csharp
// في أعلى ملف Form1.cs أضف:
using static Pharmacy_Management_System.SessionManager;
```

### **خطأ: "PharmacySelectionForm could not be found"**
**الحل:**
1. تأكد من وجود الملف `PharmacySelectionForm.cs`
2. تأكد من إضافته للمشروع
3. أعد بناء المشروع

---

## 🔧 **إصلاح مشاكل قاعدة البيانات:**

### **إنشاء قاعدة البيانات الموحدة يدوياً:**
```sql
-- 1. إنشاء قاعدة البيانات
CREATE DATABASE UnifiedPharmacy;
GO

USE UnifiedPharmacy;
GO

-- 2. إنشاء جدول الصيدليات
CREATE TABLE pharmacies (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacyCode VARCHAR(20) UNIQUE NOT NULL,
    pharmacyName NVARCHAR(250) NOT NULL,
    ownerName NVARCHAR(250) NOT NULL,
    licenseNumber VARCHAR(100) NOT NULL,
    address NVARCHAR(500) NOT NULL,
    city NVARCHAR(100) NOT NULL,
    region NVARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(250) NOT NULL,
    isActive BIT DEFAULT 1,
    registrationDate DATETIME DEFAULT GETDATE(),
    lastOnline DATETIME DEFAULT GETDATE(),
    subscriptionType VARCHAR(50) DEFAULT 'Basic',
    createdAt DATETIME DEFAULT GETDATE(),
    updatedAt DATETIME DEFAULT GETDATE()
);

-- 3. إنشاء جدول المستخدمين
CREATE TABLE users (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacyId INT NOT NULL,
    userRole VARCHAR(50) NOT NULL,
    name NVARCHAR(250) NOT NULL,
    dob VARCHAR(250) NOT NULL,
    mobile BIGINT NOT NULL,
    email VARCHAR(250) NOT NULL,
    username VARCHAR(250) UNIQUE NOT NULL,
    pass VARCHAR(250) NOT NULL,
    isActive BIT DEFAULT 1,
    lastLogin DATETIME NULL,
    createdAt DATETIME DEFAULT GETDATE(),
    updatedAt DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
);

-- 4. إضافة بيانات تجريبية
INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, licenseNumber, address, city, region, phone, email)
VALUES ('MAIN001', 'الصيدلية الرئيسية', 'مدير النظام', 'LIC001', 'العنوان الرئيسي', 'المدينة', 'المنطقة', '**********', '<EMAIL>');

INSERT INTO users (pharmacyId, userRole, name, dob, mobile, email, username, pass)
VALUES 
(1, 'Administrator', 'مدير النظام', '1980-01-01', **********, '<EMAIL>', 'admin', 'admin123'),
(1, 'Employee', 'موظف الصيدلية', '1990-01-01', **********, '<EMAIL>', 'employee', 'emp123');
```

---

## 🎉 **اختبار النظام:**

### **الخطوة 1: تشغيل البرنامج**
1. اضغط **F5** في Visual Studio
2. أو شغل الملف `.exe` من مجلد `bin\Debug`

### **الخطوة 2: اختبار تسجيل الدخول**
1. **اضغط "اختيار الصيدلية"**
2. **اختر "الصيدلية الرئيسية"**
3. **سجل دخول بـ:**
   - المدير: `admin` / `admin123`
   - الموظف: `employee` / `emp123`

### **الخطوة 3: التحقق من الميزات**
- ✅ اختيار الصيدلية يعمل
- ✅ تسجيل الدخول يعمل
- ✅ الواجهة تظهر بشكل صحيح
- ✅ قاعدة البيانات متصلة

---

## 📞 **إذا استمرت المشاكل:**

### **تحقق من:**
1. **SQL Server يعمل**
2. **قاعدة البيانات UnifiedPharmacy موجودة**
3. **جميع الملفات موجودة في المشروع**
4. **لا توجد أخطاء في Error List**

### **إعادة تشغيل كاملة:**
1. **أغلق Visual Studio**
2. **أعد تشغيل SQL Server**
3. **افتح Visual Studio مرة أخرى**
4. **اضغط Rebuild Solution**
5. **شغل البرنامج**

---

## ✅ **النتيجة المتوقعة:**

**عند تشغيل البرنامج بنجاح:**
- ✅ صفحة تسجيل دخول مع زر "اختيار الصيدلية"
- ✅ نافذة اختيار الصيدلية تعمل
- ✅ تسجيل دخول موحد يعمل
- ✅ جميع الميزات متاحة

**🎯 النظام الموحد جاهز للاستخدام!**
