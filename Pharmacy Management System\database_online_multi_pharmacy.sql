-- قاعدة البيانات المركزية الأونلاين لربط جميع الصيدليات
-- Online Multi-Pharmacy Central Database

-- إنشاء قاعدة البيانات المركزية
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'PharmacyNetworkOnline')
BEGIN
    CREATE DATABASE PharmacyNetworkOnline;
    PRINT 'تم إنشاء قاعدة البيانات المركزية PharmacyNetworkOnline';
END
GO

USE PharmacyNetworkOnline;
GO

-- ===================================
-- 1. جدول الصيدليات المسجلة في الشبكة
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies' AND xtype='U')
BEGIN
    CREATE TABLE pharmacies (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyCode VARCHAR(20) UNIQUE NOT NULL, -- كود فريد لكل صيدلية
        pharmacyName NVARCHAR(250) NOT NULL,
        ownerName NVARCHAR(250) NOT NULL,
        licenseNumber VARCHAR(100) UNIQUE NOT NULL,
        address NVARCHAR(500) NOT NULL,
        city NVARCHAR(100) NOT NULL,
        region NVARCHAR(100) NOT NULL,
        phone VARCHAR(20) NOT NULL,
        email VARCHAR(250) NOT NULL,
        latitude DECIMAL(10, 8) NULL, -- للخريطة
        longitude DECIMAL(11, 8) NULL, -- للخريطة
        isActive BIT DEFAULT 1,
        registrationDate DATETIME DEFAULT GETDATE(),
        lastOnline DATETIME DEFAULT GETDATE(),
        subscriptionType VARCHAR(50) DEFAULT 'Basic', -- Basic, Premium, Enterprise
        subscriptionExpiry DATETIME NULL,
        connectionString NVARCHAR(500) NULL, -- للاتصال بقاعدة البيانات المحلية
        apiKey VARCHAR(100) UNIQUE NULL, -- مفتاح API للأمان
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE()
    );
    PRINT 'تم إنشاء جدول pharmacies';
END
GO

-- ===================================
-- 2. جدول المستخدمين عبر الشبكة
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='network_users' AND xtype='U')
BEGIN
    CREATE TABLE network_users (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyId INT NOT NULL,
        userRole VARCHAR(50) NOT NULL, -- Admin, Pharmacist, Employee
        name NVARCHAR(250) NOT NULL,
        username VARCHAR(250) UNIQUE NOT NULL,
        passwordHash VARCHAR(500) NOT NULL, -- مشفرة
        email VARCHAR(250) NOT NULL,
        phone VARCHAR(20) NULL,
        isActive BIT DEFAULT 1,
        lastLogin DATETIME NULL,
        permissions TEXT NULL, -- JSON للصلاحيات المخصصة
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
    );
    PRINT 'تم إنشاء جدول network_users';
END
GO

-- ===================================
-- 3. جدول الأدوية المشتركة في الشبكة
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='networkmedicines' AND xtype='U')
BEGIN
    CREATE TABLE networkmedicines (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyId INT NOT NULL,
        localMedicineId VARCHAR(250) NOT NULL, -- الرقم في قاعدة البيانات المحلية
        medicineName NVARCHAR(250) NOT NULL,
        genericName NVARCHAR(250) NULL,
        brandName NVARCHAR(250) NULL,
        manufacturer NVARCHAR(250) NOT NULL,
        category NVARCHAR(100) NULL,
        dosageForm VARCHAR(100) NULL, -- أقراص، شراب، حقن، إلخ
        strength VARCHAR(100) NULL, -- التركيز
        availableQuantity INT NOT NULL,
        unitPrice DECIMAL(10, 2) NOT NULL,
        wholesalePrice DECIMAL(10, 2) NULL, -- سعر الجملة للصيدليات
        manufacturingDate DATE NULL,
        expiryDate DATE NOT NULL,
        batchNumber VARCHAR(100) NULL,
        location NVARCHAR(100) NULL, -- موقع الدواء في الصيدلية
        isAvailableForSale BIT DEFAULT 1, -- متاح للبيع للصيدليات الأخرى
        minOrderQuantity INT DEFAULT 1,
        maxOrderQuantity INT NULL,
        description NVARCHAR(1000) NULL,
        sideEffects NVARCHAR(1000) NULL,
        contraindications NVARCHAR(1000) NULL,
        storageConditions NVARCHAR(500) NULL,
        imageUrl VARCHAR(500) NULL,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
    );
    PRINT 'تم إنشاء جدول networkmedicines';
END
GO

-- ===================================
-- 4. جدول الطلبات بين الصيدليات
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='inter_pharmacy_orders' AND xtype='U')
BEGIN
    CREATE TABLE inter_pharmacy_orders (
        id INT IDENTITY(1,1) PRIMARY KEY,
        orderNumber VARCHAR(50) UNIQUE NOT NULL,
        buyerPharmacyId INT NOT NULL, -- الصيدلية المشترية
        sellerPharmacyId INT NOT NULL, -- الصيدلية البائعة
        buyerUserId INT NOT NULL, -- الموظف الذي قام بالطلب
        sellerUserId INT NULL, -- الموظف الذي وافق على الطلب
        orderStatus VARCHAR(50) DEFAULT 'Pending', -- Pending, Approved, Rejected, Shipped, Delivered, Cancelled
        totalAmount DECIMAL(12, 2) NOT NULL,
        shippingCost DECIMAL(10, 2) DEFAULT 0,
        taxAmount DECIMAL(10, 2) DEFAULT 0,
        discountAmount DECIMAL(10, 2) DEFAULT 0,
        finalAmount DECIMAL(12, 2) NOT NULL,
        paymentMethod VARCHAR(50) NULL, -- Cash, Transfer, Credit
        paymentStatus VARCHAR(50) DEFAULT 'Pending', -- Pending, Paid, Failed
        notes NVARCHAR(1000) NULL,
        shippingAddress NVARCHAR(500) NULL,
        expectedDeliveryDate DATETIME NULL,
        actualDeliveryDate DATETIME NULL,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (buyerPharmacyId) REFERENCES pharmacies(id),
        FOREIGN KEY (sellerPharmacyId) REFERENCES pharmacies(id),
        FOREIGN KEY (buyerUserId) REFERENCES network_users(id)
    );
    PRINT 'تم إنشاء جدول inter_pharmacy_orders';
END
GO

-- ===================================
-- 5. جدول تفاصيل الطلبات
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='order_details' AND xtype='U')
BEGIN
    CREATE TABLE order_details (
        id INT IDENTITY(1,1) PRIMARY KEY,
        orderId INT NOT NULL,
        medicineId INT NOT NULL,
        requestedQuantity INT NOT NULL,
        approvedQuantity INT NULL,
        unitPrice DECIMAL(10, 2) NOT NULL,
        totalPrice DECIMAL(12, 2) NOT NULL,
        notes NVARCHAR(500) NULL,
        FOREIGN KEY (orderId) REFERENCES inter_pharmacy_orders(id),
        FOREIGN KEY (medicineId) REFERENCES networkmedicines(id)
    );
    PRINT 'تم إنشاء جدول order_details';
END
GO

-- ===================================
-- 6. جدول المحادثات بين الصيدليات
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacy_chats' AND xtype='U')
BEGIN
    CREATE TABLE pharmacy_chats (
        id INT IDENTITY(1,1) PRIMARY KEY,
        chatId VARCHAR(100) UNIQUE NOT NULL, -- معرف فريد للمحادثة
        pharmacy1Id INT NOT NULL,
        pharmacy2Id INT NOT NULL,
        chatType VARCHAR(50) DEFAULT 'Direct', -- Direct, Group, Support
        chatTitle NVARCHAR(250) NULL,
        isActive BIT DEFAULT 1,
        createdAt DATETIME DEFAULT GETDATE(),
        lastMessageAt DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (pharmacy1Id) REFERENCES pharmacies(id),
        FOREIGN KEY (pharmacy2Id) REFERENCES pharmacies(id)
    );
    PRINT 'تم إنشاء جدول pharmacy_chats';
END
GO

-- ===================================
-- 7. جدول رسائل المحادثات
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='chat_messages' AND xtype='U')
BEGIN
    CREATE TABLE chat_messages (
        id INT IDENTITY(1,1) PRIMARY KEY,
        chatId INT NOT NULL,
        senderId INT NOT NULL, -- المستخدم المرسل
        messageType VARCHAR(50) DEFAULT 'Text', -- Text, Image, File, Order, Medicine
        messageContent NVARCHAR(2000) NOT NULL,
        attachmentUrl VARCHAR(500) NULL,
        relatedOrderId INT NULL, -- إذا كانت الرسالة متعلقة بطلب
        relatedMedicineId INT NULL, -- إذا كانت الرسالة متعلقة بدواء
        isRead BIT DEFAULT 0,
        readAt DATETIME NULL,
        isEdited BIT DEFAULT 0,
        editedAt DATETIME NULL,
        isDeleted BIT DEFAULT 0,
        deletedAt DATETIME NULL,
        createdAt DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (chatId) REFERENCES pharmacy_chats(id),
        FOREIGN KEY (senderId) REFERENCES network_users(id),
        FOREIGN KEY (relatedOrderId) REFERENCES inter_pharmacy_orders(id),
        FOREIGN KEY (relatedMedicineId) REFERENCES networkmedicines(id)
    );
    PRINT 'تم إنشاء جدول chat_messages';
END
GO

-- ===================================
-- 8. جدول تقييمات الصيدليات
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacy_ratings' AND xtype='U')
BEGIN
    CREATE TABLE pharmacy_ratings (
        id INT IDENTITY(1,1) PRIMARY KEY,
        ratedPharmacyId INT NOT NULL, -- الصيدلية المُقيَّمة
        raterPharmacyId INT NOT NULL, -- الصيدلية المُقيِّمة
        orderId INT NULL, -- الطلب المرتبط بالتقييم
        rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
        reviewText NVARCHAR(1000) NULL,
        serviceQuality INT NULL CHECK (serviceQuality >= 1 AND serviceQuality <= 5),
        deliverySpeed INT NULL CHECK (deliverySpeed >= 1 AND deliverySpeed <= 5),
        productQuality INT NULL CHECK (productQuality >= 1 AND productQuality <= 5),
        communication INT NULL CHECK (communication >= 1 AND communication <= 5),
        wouldRecommend BIT DEFAULT 1,
        createdAt DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (ratedPharmacyId) REFERENCES pharmacies(id),
        FOREIGN KEY (raterPharmacyId) REFERENCES pharmacies(id),
        FOREIGN KEY (orderId) REFERENCES inter_pharmacy_orders(id)
    );
    PRINT 'تم إنشاء جدول pharmacy_ratings';
END
GO

-- ===================================
-- 9. جدول الإشعارات
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='notifications' AND xtype='U')
BEGIN
    CREATE TABLE notifications (
        id INT IDENTITY(1,1) PRIMARY KEY,
        recipientPharmacyId INT NOT NULL,
        recipientUserId INT NULL, -- إذا كان للمستخدم المحدد
        notificationType VARCHAR(50) NOT NULL, -- Order, Message, System, Medicine, Payment
        title NVARCHAR(250) NOT NULL,
        content NVARCHAR(1000) NOT NULL,
        relatedOrderId INT NULL,
        relatedMedicineId INT NULL,
        relatedChatId INT NULL,
        isRead BIT DEFAULT 0,
        readAt DATETIME NULL,
        priority VARCHAR(20) DEFAULT 'Normal', -- Low, Normal, High, Urgent
        actionUrl VARCHAR(500) NULL, -- رابط للإجراء المطلوب
        expiresAt DATETIME NULL,
        createdAt DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (recipientPharmacyId) REFERENCES pharmacies(id),
        FOREIGN KEY (recipientUserId) REFERENCES network_users(id),
        FOREIGN KEY (relatedOrderId) REFERENCES inter_pharmacy_orders(id),
        FOREIGN KEY (relatedMedicineId) REFERENCES networkmedicines(id),
        FOREIGN KEY (relatedChatId) REFERENCES pharmacy_chats(id)
    );
    PRINT 'تم إنشاء جدول notifications';
END
GO

-- ===================================
-- 10. جدول سجل النشاطات
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='activity_logs' AND xtype='U')
BEGIN
    CREATE TABLE activity_logs (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyId INT NOT NULL,
        userId INT NOT NULL,
        activityType VARCHAR(50) NOT NULL, -- Login, Logout, Order, Message, Medicine, System
        activityDescription NVARCHAR(500) NOT NULL,
        relatedEntityType VARCHAR(50) NULL, -- Order, Medicine, User, Chat
        relatedEntityId INT NULL,
        ipAddress VARCHAR(45) NULL,
        userAgent NVARCHAR(500) NULL,
        createdAt DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id),
        FOREIGN KEY (userId) REFERENCES network_users(id)
    );
    PRINT 'تم إنشاء جدول activity_logs';
END
GO

-- ===================================
-- 11. جدول إعدادات النظام
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='system_settings' AND xtype='U')
BEGIN
    CREATE TABLE system_settings (
        id INT IDENTITY(1,1) PRIMARY KEY,
        settingKey VARCHAR(100) UNIQUE NOT NULL,
        settingValue NVARCHAR(1000) NOT NULL,
        settingType VARCHAR(50) DEFAULT 'String', -- String, Number, Boolean, JSON
        description NVARCHAR(500) NULL,
        isPublic BIT DEFAULT 0, -- هل يمكن للصيدليات رؤيتها
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE()
    );
    PRINT 'تم إنشاء جدول system_settings';
END
GO

-- ===================================
-- إنشاء الفهارس لتحسين الأداء
-- ===================================

-- فهارس جدول الصيدليات
CREATE INDEX IX_pharmacies_pharmacyCode ON pharmacies(pharmacyCode);
CREATE INDEX IX_pharmacies_isActive ON pharmacies(isActive);
CREATE INDEX IX_pharmacies_city ON pharmacies(city);
CREATE INDEX IX_pharmacies_lastOnline ON pharmacies(lastOnline);

-- فهارس جدول المستخدمين
CREATE INDEX IX_network_users_pharmacyId ON network_users(pharmacyId);
CREATE INDEX IX_network_users_username ON network_users(username);
CREATE INDEX IX_network_users_isActive ON network_users(isActive);

-- فهارس جدول الأدوية
CREATE INDEX IX_networkmedicines_pharmacyId ON networkmedicines(pharmacyId);
CREATE INDEX IX_networkmedicines_medicineName ON networkmedicines(medicineName);
CREATE INDEX IX_networkmedicines_isAvailableForSale ON networkmedicines(isAvailableForSale);
CREATE INDEX IX_networkmedicines_expiryDate ON networkmedicines(expiryDate);
CREATE INDEX IX_networkmedicines_category ON networkmedicines(category);

-- فهارس جدول الطلبات
CREATE INDEX IX_inter_pharmacy_orders_buyerPharmacyId ON inter_pharmacy_orders(buyerPharmacyId);
CREATE INDEX IX_inter_pharmacy_orders_sellerPharmacyId ON inter_pharmacy_orders(sellerPharmacyId);
CREATE INDEX IX_inter_pharmacy_orders_orderStatus ON inter_pharmacy_orders(orderStatus);
CREATE INDEX IX_inter_pharmacy_orders_createdAt ON inter_pharmacy_orders(createdAt);

-- فهارس جدول المحادثات
CREATE INDEX IX_pharmacy_chats_pharmacy1Id ON pharmacy_chats(pharmacy1Id);
CREATE INDEX IX_pharmacy_chats_pharmacy2Id ON pharmacy_chats(pharmacy2Id);
CREATE INDEX IX_pharmacy_chats_isActive ON pharmacy_chats(isActive);

-- فهارس جدول الرسائل
CREATE INDEX IX_chat_messages_chatId ON chat_messages(chatId);
CREATE INDEX IX_chat_messages_senderId ON chat_messages(senderId);
CREATE INDEX IX_chat_messages_createdAt ON chat_messages(createdAt);
CREATE INDEX IX_chat_messages_isRead ON chat_messages(isRead);

-- فهارس جدول الإشعارات
CREATE INDEX IX_notifications_recipientPharmacyId ON notifications(recipientPharmacyId);
CREATE INDEX IX_notifications_isRead ON notifications(isRead);
CREATE INDEX IX_notifications_createdAt ON notifications(createdAt);

-- فهارس جدول النشاطات
CREATE INDEX IX_activity_logs_pharmacyId ON activity_logs(pharmacyId);
CREATE INDEX IX_activity_logs_userId ON activity_logs(userId);
CREATE INDEX IX_activity_logs_createdAt ON activity_logs(createdAt);

PRINT 'تم إنشاء جميع الفهارس بنجاح';

-- ===================================
-- إدراج البيانات الافتراضية
-- ===================================

-- إعدادات النظام الافتراضية
INSERT INTO system_settings (settingKey, settingValue, settingType, description, isPublic) VALUES
('SystemName', 'شبكة الصيدليات الأونلاين', 'String', 'اسم النظام', 1),
('SystemVersion', '1.0.0', 'String', 'إصدار النظام', 1),
('MaxOrderAmount', '50000', 'Number', 'الحد الأقصى لمبلغ الطلب', 1),
('MinOrderAmount', '100', 'Number', 'الحد الأدنى لمبلغ الطلب', 1),
('DefaultShippingCost', '50', 'Number', 'تكلفة الشحن الافتراضية', 1),
('TaxRate', '0.15', 'Number', 'معدل الضريبة', 1),
('ChatMessageMaxLength', '2000', 'Number', 'الحد الأقصى لطول الرسالة', 1),
('OrderExpiryDays', '7', 'Number', 'عدد أيام انتهاء صلاحية الطلب', 1),
('NotificationRetentionDays', '30', 'Number', 'عدد أيام الاحتفاظ بالإشعارات', 1),
('MaintenanceMode', 'false', 'Boolean', 'وضع الصيانة', 0);

-- صيدلية تجريبية للاختبار
INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, licenseNumber, address, city, region, phone, email, isActive) VALUES
('PH001', 'صيدلية النور الرئيسية', 'أحمد محمد علي', 'LIC001', 'شارع الملك فهد، حي النور', 'الرياض', 'الرياض', '+966501234567', '<EMAIL>', 1),
('PH002', 'صيدلية الشفاء', 'فاطمة أحمد', 'LIC002', 'طريق الملك عبدالعزيز، حي الملز', 'الرياض', 'الرياض', '+966502345678', '<EMAIL>', 1),
('PH003', 'صيدلية الحياة', 'محمد سالم', 'LIC003', 'شارع التحلية، حي السلامة', 'جدة', 'مكة المكرمة', '+966503456789', '<EMAIL>', 1);

-- مستخدمين تجريبيين
INSERT INTO network_users (pharmacyId, userRole, name, username, passwordHash, email, isActive) VALUES
(1, 'Admin', 'أحمد محمد علي', 'admin_alnoor', 'hashed_password_1', '<EMAIL>', 1),
(1, 'Pharmacist', 'سارة أحمد', 'pharmacist_alnoor', 'hashed_password_2', '<EMAIL>', 1),
(2, 'Admin', 'فاطمة أحمد', 'admin_alshifa', 'hashed_password_3', '<EMAIL>', 1),
(3, 'Admin', 'محمد سالم', 'admin_alhayat', 'hashed_password_4', '<EMAIL>', 1);

PRINT 'تم إدراج البيانات الافتراضية بنجاح';

-- ===================================
-- إنشاء Views مفيدة
-- ===================================

-- عرض الصيدليات النشطة مع إحصائياتها
CREATE VIEW vw_active_pharmacies AS
SELECT
    p.id,
    p.pharmacyCode,
    p.pharmacyName,
    p.ownerName,
    p.city,
    p.region,
    p.phone,
    p.email,
    p.lastOnline,
    p.subscriptionType,
    COUNT(DISTINCT u.id) as totalUsers,
    COUNT(DISTINCT m.id) as totalMedicines,
    COUNT(DISTINCT o1.id) as totalOrdersSent,
    COUNT(DISTINCT o2.id) as totalOrdersReceived,
    AVG(CAST(r.rating AS FLOAT)) as averageRating
FROM pharmacies p
LEFT JOIN network_users u ON p.id = u.pharmacyId AND u.isActive = 1
LEFT JOIN networkmedicines m ON p.id = m.pharmacyId AND m.isAvailableForSale = 1
LEFT JOIN inter_pharmacy_orders o1 ON p.id = o1.buyerPharmacyId
LEFT JOIN inter_pharmacy_orders o2 ON p.id = o2.sellerPharmacyId
LEFT JOIN pharmacy_ratings r ON p.id = r.ratedPharmacyId
WHERE p.isActive = 1
GROUP BY p.id, p.pharmacyCode, p.pharmacyName, p.ownerName, p.city, p.region, p.phone, p.email, p.lastOnline, p.subscriptionType;

-- عرض الأدوية المتاحة مع معلومات الصيدلية
CREATE VIEW vw_available_medicines AS
SELECT
    m.id,
    m.pharmacyId,
    p.pharmacyName,
    p.pharmacyCode,
    p.city,
    p.region,
    m.medicineName,
    m.genericName,
    m.brandName,
    m.manufacturer,
    m.category,
    m.dosageForm,
    m.strength,
    m.availableQuantity,
    m.unitPrice,
    m.wholesalePrice,
    m.expiryDate,
    m.minOrderQuantity,
    m.maxOrderQuantity,
    DATEDIFF(day, GETDATE(), m.expiryDate) as daysToExpiry
FROM networkmedicines m
INNER JOIN pharmacies p ON m.pharmacyId = p.id
WHERE m.isAvailableForSale = 1
    AND m.availableQuantity > 0
    AND m.expiryDate > GETDATE()
    AND p.isActive = 1;

PRINT 'تم إنشاء Views بنجاح';

-- ===================================
-- إنشاء Stored Procedures مفيدة
-- ===================================

-- إجراء لتسجيل صيدلية جديدة
CREATE PROCEDURE sp_RegisterPharmacy
    @pharmacyName NVARCHAR(250),
    @ownerName NVARCHAR(250),
    @licenseNumber VARCHAR(100),
    @address NVARCHAR(500),
    @city NVARCHAR(100),
    @region NVARCHAR(100),
    @phone VARCHAR(20),
    @email VARCHAR(250),
    @adminName NVARCHAR(250),
    @adminUsername VARCHAR(250),
    @adminPassword VARCHAR(500)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @pharmacyId INT;
    DECLARE @pharmacyCode VARCHAR(20);

    -- إنشاء كود فريد للصيدلية
    SET @pharmacyCode = 'PH' + RIGHT('000' + CAST((SELECT COUNT(*) + 1 FROM pharmacies) AS VARCHAR), 3);

    BEGIN TRANSACTION;

    TRY
        -- إدراج الصيدلية
        INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, licenseNumber, address, city, region, phone, email)
        VALUES (@pharmacyCode, @pharmacyName, @ownerName, @licenseNumber, @address, @city, @region, @phone, @email);

        SET @pharmacyId = SCOPE_IDENTITY();

        -- إدراج المدير
        INSERT INTO network_users (pharmacyId, userRole, name, username, passwordHash, email)
        VALUES (@pharmacyId, 'Admin', @adminName, @adminUsername, @adminPassword, @email);

        COMMIT TRANSACTION;

        SELECT @pharmacyId as PharmacyId, @pharmacyCode as PharmacyCode, 'تم تسجيل الصيدلية بنجاح' as Message;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END;

-- إجراء للبحث عن الأدوية
CREATE PROCEDURE sp_SearchMedicines
    @searchTerm NVARCHAR(250) = '',
    @pharmacyId INT = NULL,
    @category NVARCHAR(100) = '',
    @minPrice DECIMAL(10,2) = 0,
    @maxPrice DECIMAL(10,2) = 999999,
    @pageNumber INT = 1,
    @pageSize INT = 20
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @offset INT = (@pageNumber - 1) * @pageSize;

    SELECT
        m.*,
        p.pharmacyName,
        p.pharmacyCode,
        p.city,
        p.region,
        p.phone,
        DATEDIFF(day, GETDATE(), m.expiryDate) as daysToExpiry
    FROM networkmedicines m
    INNER JOIN pharmacies p ON m.pharmacyId = p.id
    WHERE m.isAvailableForSale = 1
        AND m.availableQuantity > 0
        AND m.expiryDate > GETDATE()
        AND p.isActive = 1
        AND (@pharmacyId IS NULL OR m.pharmacyId != @pharmacyId) -- استبعاد صيدلية المستخدم
        AND (@searchTerm = '' OR m.medicineName LIKE '%' + @searchTerm + '%'
             OR m.genericName LIKE '%' + @searchTerm + '%'
             OR m.brandName LIKE '%' + @searchTerm + '%'
             OR m.manufacturer LIKE '%' + @searchTerm + '%')
        AND (@category = '' OR m.category = @category)
        AND m.unitPrice BETWEEN @minPrice AND @maxPrice
    ORDER BY m.medicineName
    OFFSET @offset ROWS
    FETCH NEXT @pageSize ROWS ONLY;
END;

PRINT 'تم إنشاء Stored Procedures بنجاح';

PRINT '=== تم إنشاء قاعدة البيانات المركزية الأونلاين بنجاح ===';
PRINT 'قاعدة البيانات: PharmacyNetworkOnline';
PRINT 'الجداول: 11 جدول رئيسي';
PRINT 'الفهارس: 20+ فهرس لتحسين الأداء';
PRINT 'البيانات الافتراضية: 3 صيدليات و 4 مستخدمين تجريبيين';
PRINT 'Views: 2 عرض مفيد';
PRINT 'Stored Procedures: 2 إجراء مخزن';
GO
