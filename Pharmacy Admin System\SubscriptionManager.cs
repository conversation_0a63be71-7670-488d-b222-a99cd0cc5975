using System;
using System.Collections.Generic;
using System.Data;

namespace Pharmacy_Admin_System
{
    /// <summary>
    /// مدير الاشتراكات - إدارة اشتراكات الصيدليات وخطط الدفع
    /// Subscription Manager - Manages pharmacy subscriptions and payment plans
    /// </summary>
    public class SubscriptionManager
    {
        private DatabaseManager dbManager;

        public SubscriptionManager()
        {
            dbManager = new DatabaseManager();
        }

        #region Subscription Plans

        /// <summary>
        /// الحصول على جميع خطط الاشتراك
        /// </summary>
        public DataTable GetSubscriptionPlans()
        {
            try
            {
                string query = @"
                    SELECT 
                        id, planName, planNameAr, description, monthlyPrice, 
                        maxUsers, maxMedicines, hasNetworkAccess, hasReports, 
                        hasBackup, supportLevel, isActive, createdDate
                    FROM subscription_plans 
                    WHERE isActive = 1
                    ORDER BY monthlyPrice ASC";

                return dbManager.ExecuteAdminQuery(query);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب خطط الاشتراك: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إضافة خطة اشتراك جديدة
        /// </summary>
        public int AddSubscriptionPlan(SubscriptionPlanData planData)
        {
            try
            {
                string command = @"
                    INSERT INTO subscription_plans 
                    (planName, planNameAr, description, monthlyPrice, maxUsers, maxMedicines, 
                     hasNetworkAccess, hasReports, hasBackup, supportLevel, isActive)
                    VALUES 
                    (@planName, @planNameAr, @description, @monthlyPrice, @maxUsers, @maxMedicines,
                     @hasNetworkAccess, @hasReports, @hasBackup, @supportLevel, 1);
                    SELECT SCOPE_IDENTITY();";

                var parameters = new Dictionary<string, object>
                {
                    {"@planName", planData.PlanName},
                    {"@planNameAr", planData.PlanNameAr},
                    {"@description", planData.Description},
                    {"@monthlyPrice", planData.MonthlyPrice},
                    {"@maxUsers", planData.MaxUsers},
                    {"@maxMedicines", planData.MaxMedicines},
                    {"@hasNetworkAccess", planData.HasNetworkAccess},
                    {"@hasReports", planData.HasReports},
                    {"@hasBackup", planData.HasBackup},
                    {"@supportLevel", planData.SupportLevel}
                };

                var result = dbManager.ExecuteAdminScalar(command, parameters);
                return Convert.ToInt32(result);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة خطة الاشتراك: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث خطة اشتراك
        /// </summary>
        public void UpdateSubscriptionPlan(int planId, SubscriptionPlanData planData)
        {
            try
            {
                string command = @"
                    UPDATE subscription_plans 
                    SET planName = @planName, planNameAr = @planNameAr, description = @description,
                        monthlyPrice = @monthlyPrice, maxUsers = @maxUsers, maxMedicines = @maxMedicines,
                        hasNetworkAccess = @hasNetworkAccess, hasReports = @hasReports, 
                        hasBackup = @hasBackup, supportLevel = @supportLevel
                    WHERE id = @planId";

                var parameters = new Dictionary<string, object>
                {
                    {"@planId", planId},
                    {"@planName", planData.PlanName},
                    {"@planNameAr", planData.PlanNameAr},
                    {"@description", planData.Description},
                    {"@monthlyPrice", planData.MonthlyPrice},
                    {"@maxUsers", planData.MaxUsers},
                    {"@maxMedicines", planData.MaxMedicines},
                    {"@hasNetworkAccess", planData.HasNetworkAccess},
                    {"@hasReports", planData.HasReports},
                    {"@hasBackup", planData.HasBackup},
                    {"@supportLevel", planData.SupportLevel}
                };

                dbManager.ExecuteAdminCommand(command, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث خطة الاشتراك: {ex.Message}", ex);
            }
        }

        #endregion

        #region Pharmacy Subscriptions

        /// <summary>
        /// الحصول على اشتراكات الصيدليات
        /// </summary>
        public DataTable GetPharmacySubscriptions()
        {
            try
            {
                string query = @"
                    SELECT 
                        rp.id as pharmacyId,
                        rp.pharmacyName,
                        rp.pharmacyCode,
                        rp.ownerName,
                        rp.city,
                        rp.subscriptionType,
                        rp.subscriptionStartDate,
                        rp.subscriptionEndDate,
                        rp.monthlyFee,
                        rp.status,
                        rp.isActive,
                        sp.planNameAr,
                        sp.monthlyPrice,
                        CASE 
                            WHEN rp.subscriptionEndDate < GETDATE() THEN 'منتهي'
                            WHEN rp.subscriptionEndDate < DATEADD(day, 7, GETDATE()) THEN 'ينتهي قريباً'
                            ELSE 'نشط'
                        END as subscriptionStatus,
                        DATEDIFF(day, GETDATE(), rp.subscriptionEndDate) as daysRemaining
                    FROM registered_pharmacies rp
                    LEFT JOIN subscription_plans sp ON rp.subscriptionType = sp.planName
                    WHERE rp.status = 'Approved'
                    ORDER BY rp.subscriptionEndDate ASC";

                return dbManager.ExecuteAdminQuery(query);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب اشتراكات الصيدليات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث اشتراك صيدلية
        /// </summary>
        public void UpdatePharmacySubscription(int pharmacyId, string subscriptionType, 
            DateTime startDate, DateTime endDate, decimal monthlyFee, int updatedBy)
        {
            try
            {
                string command = @"
                    UPDATE registered_pharmacies 
                    SET subscriptionType = @subscriptionType, 
                        subscriptionStartDate = @startDate,
                        subscriptionEndDate = @endDate,
                        monthlyFee = @monthlyFee
                    WHERE id = @pharmacyId";

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", pharmacyId},
                    {"@subscriptionType", subscriptionType},
                    {"@startDate", startDate},
                    {"@endDate", endDate},
                    {"@monthlyFee", monthlyFee}
                };

                dbManager.ExecuteAdminCommand(command, parameters);

                // تسجيل النشاط
                dbManager.LogActivity(updatedBy, pharmacyId, "Subscription Updated", 
                    $"تم تحديث اشتراك الصيدلية إلى {subscriptionType}");
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث اشتراك الصيدلية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تجديد اشتراك صيدلية
        /// </summary>
        public void RenewPharmacySubscription(int pharmacyId, int months, int renewedBy)
        {
            try
            {
                string command = @"
                    UPDATE registered_pharmacies 
                    SET subscriptionEndDate = CASE 
                        WHEN subscriptionEndDate > GETDATE() 
                        THEN DATEADD(month, @months, subscriptionEndDate)
                        ELSE DATEADD(month, @months, GETDATE())
                    END
                    WHERE id = @pharmacyId";

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", pharmacyId},
                    {"@months", months}
                };

                dbManager.ExecuteAdminCommand(command, parameters);

                // تسجيل النشاط
                dbManager.LogActivity(renewedBy, pharmacyId, "Subscription Renewed", 
                    $"تم تجديد اشتراك الصيدلية لمدة {months} شهر");
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تجديد اشتراك الصيدلية: {ex.Message}", ex);
            }
        }

        #endregion

        #region Payments

        /// <summary>
        /// تسجيل دفعة اشتراك
        /// </summary>
        public int RecordSubscriptionPayment(SubscriptionPaymentData paymentData, int createdBy)
        {
            try
            {
                string command = @"
                    INSERT INTO subscription_payments 
                    (pharmacyId, planId, amount, paymentDate, paymentMethod, transactionId, 
                     status, validFrom, validTo, notes, createdBy)
                    VALUES 
                    (@pharmacyId, @planId, @amount, @paymentDate, @paymentMethod, @transactionId,
                     @status, @validFrom, @validTo, @notes, @createdBy);
                    SELECT SCOPE_IDENTITY();";

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", paymentData.PharmacyId},
                    {"@planId", paymentData.PlanId},
                    {"@amount", paymentData.Amount},
                    {"@paymentDate", paymentData.PaymentDate},
                    {"@paymentMethod", paymentData.PaymentMethod},
                    {"@transactionId", paymentData.TransactionId},
                    {"@status", paymentData.Status},
                    {"@validFrom", paymentData.ValidFrom},
                    {"@validTo", paymentData.ValidTo},
                    {"@notes", paymentData.Notes},
                    {"@createdBy", createdBy}
                };

                var result = dbManager.ExecuteAdminScalar(command, parameters);
                int paymentId = Convert.ToInt32(result);

                // تحديث تاريخ انتهاء الاشتراك
                if (paymentData.Status == "Completed")
                {
                    UpdatePharmacySubscriptionEndDate(paymentData.PharmacyId, paymentData.ValidTo);
                }

                // تسجيل النشاط
                dbManager.LogActivity(createdBy, paymentData.PharmacyId, "Payment Recorded", 
                    $"تم تسجيل دفعة بقيمة {paymentData.Amount} ريال");

                return paymentId;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تسجيل دفعة الاشتراك: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على تاريخ المدفوعات لصيدلية
        /// </summary>
        public DataTable GetPharmacyPaymentHistory(int pharmacyId)
        {
            try
            {
                string query = @"
                    SELECT 
                        sp.id,
                        sp.amount,
                        sp.paymentDate,
                        sp.paymentMethod,
                        sp.transactionId,
                        sp.status,
                        sp.validFrom,
                        sp.validTo,
                        sp.notes,
                        spl.planNameAr,
                        au.fullName as createdByName
                    FROM subscription_payments sp
                    LEFT JOIN subscription_plans spl ON sp.planId = spl.id
                    LEFT JOIN admin_users au ON sp.createdBy = au.id
                    WHERE sp.pharmacyId = @pharmacyId
                    ORDER BY sp.paymentDate DESC";

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", pharmacyId}
                };

                return dbManager.ExecuteAdminQuery(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب تاريخ المدفوعات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث تاريخ انتهاء اشتراك الصيدلية
        /// </summary>
        private void UpdatePharmacySubscriptionEndDate(int pharmacyId, DateTime endDate)
        {
            try
            {
                string command = @"
                    UPDATE registered_pharmacies 
                    SET subscriptionEndDate = @endDate
                    WHERE id = @pharmacyId";

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", pharmacyId},
                    {"@endDate", endDate}
                };

                dbManager.ExecuteAdminCommand(command, parameters);
            }
            catch
            {
                // تجاهل الأخطاء في التحديث
            }
        }

        #endregion

        #region Statistics

        /// <summary>
        /// الحصول على إحصائيات الاشتراكات
        /// </summary>
        public SubscriptionStatistics GetSubscriptionStatistics()
        {
            try
            {
                var stats = new SubscriptionStatistics();

                // إجمالي الإيرادات الشهرية
                var monthlyRevenueQuery = @"
                    SELECT ISNULL(SUM(amount), 0) 
                    FROM subscription_payments 
                    WHERE MONTH(paymentDate) = MONTH(GETDATE()) 
                      AND YEAR(paymentDate) = YEAR(GETDATE())
                      AND status = 'Completed'";
                stats.MonthlyRevenue = Convert.ToDecimal(dbManager.ExecuteAdminScalar(monthlyRevenueQuery));

                // إجمالي الإيرادات السنوية
                var yearlyRevenueQuery = @"
                    SELECT ISNULL(SUM(amount), 0) 
                    FROM subscription_payments 
                    WHERE YEAR(paymentDate) = YEAR(GETDATE())
                      AND status = 'Completed'";
                stats.YearlyRevenue = Convert.ToDecimal(dbManager.ExecuteAdminScalar(yearlyRevenueQuery));

                // الاشتراكات المنتهية
                var expiredQuery = @"
                    SELECT COUNT(*) 
                    FROM registered_pharmacies 
                    WHERE subscriptionEndDate < GETDATE() 
                      AND status = 'Approved'";
                stats.ExpiredSubscriptions = Convert.ToInt32(dbManager.ExecuteAdminScalar(expiredQuery));

                // الاشتراكات التي تنتهي قريباً (خلال 7 أيام)
                var expiringSoonQuery = @"
                    SELECT COUNT(*) 
                    FROM registered_pharmacies 
                    WHERE subscriptionEndDate BETWEEN GETDATE() AND DATEADD(day, 7, GETDATE())
                      AND status = 'Approved'";
                stats.ExpiringSoonSubscriptions = Convert.ToInt32(dbManager.ExecuteAdminScalar(expiringSoonQuery));

                return stats;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب إحصائيات الاشتراكات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على الصيدليات التي تنتهي اشتراكاتها قريباً
        /// </summary>
        public DataTable GetExpiringSoonPharmacies(int days = 7)
        {
            try
            {
                string query = @"
                    SELECT 
                        id, pharmacyName, pharmacyCode, ownerName, ownerPhone,
                        subscriptionType, subscriptionEndDate,
                        DATEDIFF(day, GETDATE(), subscriptionEndDate) as daysRemaining
                    FROM registered_pharmacies 
                    WHERE subscriptionEndDate BETWEEN GETDATE() AND DATEADD(day, @days, GETDATE())
                      AND status = 'Approved'
                      AND isActive = 1
                    ORDER BY subscriptionEndDate ASC";

                var parameters = new Dictionary<string, object>
                {
                    {"@days", days}
                };

                return dbManager.ExecuteAdminQuery(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب الاشتراكات المنتهية قريباً: {ex.Message}", ex);
            }
        }

        #endregion
    }

    #region Data Classes

    /// <summary>
    /// بيانات خطة اشتراك
    /// </summary>
    public class SubscriptionPlanData
    {
        public string PlanName { get; set; }
        public string PlanNameAr { get; set; }
        public string Description { get; set; }
        public decimal MonthlyPrice { get; set; }
        public int MaxUsers { get; set; }
        public int MaxMedicines { get; set; }
        public bool HasNetworkAccess { get; set; }
        public bool HasReports { get; set; }
        public bool HasBackup { get; set; }
        public string SupportLevel { get; set; }
    }

    /// <summary>
    /// بيانات دفعة اشتراك
    /// </summary>
    public class SubscriptionPaymentData
    {
        public int PharmacyId { get; set; }
        public int PlanId { get; set; }
        public decimal Amount { get; set; }
        public DateTime PaymentDate { get; set; }
        public string PaymentMethod { get; set; }
        public string TransactionId { get; set; }
        public string Status { get; set; }
        public DateTime ValidFrom { get; set; }
        public DateTime ValidTo { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// إحصائيات الاشتراكات
    /// </summary>
    public class SubscriptionStatistics
    {
        public decimal MonthlyRevenue { get; set; }
        public decimal YearlyRevenue { get; set; }
        public int ExpiredSubscriptions { get; set; }
        public int ExpiringSoonSubscriptions { get; set; }
    }

    #endregion
}
