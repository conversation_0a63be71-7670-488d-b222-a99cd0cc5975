# 🏥 نظام إدارة الصيدليات المركزي
## Central Pharmacy Management System

### 📋 نظرة عامة | Overview

نظام إدارة الصيدليات المركزي هو برنامج منفصل ومستقل مصمم خصيصاً لإدارة ومراقبة جميع الصيدليات المسجلة في الشبكة. يوفر النظام واجهة شاملة للمديرين العامين لإدارة الاشتراكات، الموافقات، النسخ الاحتياطية، والتقارير.

The Central Pharmacy Management System is a separate and independent application designed specifically to manage and monitor all registered pharmacies in the network. The system provides a comprehensive interface for super administrators to manage subscriptions, approvals, backups, and reports.

---

## 🚀 الميزات الرئيسية | Key Features

### 🔐 إدارة المديرين العامين
- تسجيل دخول آمن للمديرين العامين
- إدارة صلاحيات المديرين
- سجل نشاطات شامل

### 🏪 إدارة الصيدليات
- عرض جميع الصيدليات المسجلة
- الموافقة على طلبات التسجيل الجديدة
- تعليق وإعادة تفعيل الصيدليات
- البحث والفلترة المتقدمة

### 💰 إدارة الاشتراكات
- خطط اشتراك متعددة (أساسي، قياسي، مميز، مؤسسي)
- تتبع مدة الاشتراكات وتواريخ الانتهاء
- إدارة المدفوعات والفواتير
- تجديد الاشتراكات التلقائي

### 💾 النسخ الاحتياطية
- نسخ احتياطية تلقائية لقواعد البيانات
- أنواع متعددة من النسخ (كاملة، تفاضلية، سجل المعاملات)
- استعادة البيانات الآمنة
- تنظيف النسخ القديمة تلقائياً

### 📊 التقارير والإحصائيات
- تقارير الإيرادات الشهرية والسنوية
- إحصائيات الصيدليات حسب المدينة ونوع الاشتراك
- تقارير نشاطات المديرين
- تقارير الاشتراكات المنتهية والمنتهية قريباً

### 👥 إدارة المستخدمين
- إدارة مستخدمي جميع الصيدليات
- تتبع جلسات تسجيل الدخول
- إدارة الصلاحيات والأدوار

---

## 🛠️ المتطلبات التقنية | Technical Requirements

### البرمجيات المطلوبة | Required Software
- **Windows 10/11** أو أحدث
- **SQL Server 2016** أو أحدث
- **.NET Framework 4.7.2** أو أحدث
- **Visual Studio 2019/2022** (للتطوير)

### قواعد البيانات | Databases
- **PharmacyAdminSystem**: قاعدة البيانات الإدارية المنفصلة
- **UnifiedPharmacy**: قاعدة بيانات الصيدليات (للقراءة فقط)

---

## 📦 التثبيت والإعداد | Installation & Setup

### 1. إعداد قاعدة البيانات | Database Setup
```bash
# شغل سكريپت الإعداد
setup_admin_database.bat

# أو يدوياً في SQL Server Management Studio
# افتح ملف create_admin_database.sql واضغط F5
```

### 2. تكوين الاتصال | Connection Configuration
قم بتحديث ملف `App.config` بمعلومات الخادم الصحيحة:
```xml
<connectionStrings>
    <add name="AdminDatabase" connectionString="data source=YOUR_SERVER;database=PharmacyAdminSystem;integrated security=True" />
    <add name="PharmacyDatabase" connectionString="data source=YOUR_SERVER;database=UnifiedPharmacy;integrated security=True" />
</connectionStrings>
```

### 3. تشغيل البرنامج | Running the Application
```bash
# من Visual Studio
F5 أو Ctrl+F5

# من الملف التنفيذي
Pharmacy Admin System.exe
```

---

## 🔑 بيانات تسجيل الدخول الافتراضية | Default Login Credentials

```
اسم المستخدم | Username: superadmin
كلمة المرور | Password: admin2025
```

---

## 💰 خطط الاشتراك | Subscription Plans

| الخطة | السعر الشهري | المستخدمين | الأدوية | الميزات |
|-------|-------------|-----------|---------|---------|
| أساسي | 99 ريال | 3 | 500 | أساسية |
| قياسي | 199 ريال | 5 | 1,000 | + تقارير |
| مميز | 299 ريال | 10 | 5,000 | + نسخ احتياطية |
| مؤسسي | 499 ريال | 50 | 50,000 | جميع الميزات |

---

## 📁 هيكل المشروع | Project Structure

```
Pharmacy Admin System/
├── 📄 Program.cs                 # نقطة الدخول الرئيسية
├── 🔐 AdminLoginForm.cs          # واجهة تسجيل الدخول
├── 🏠 MainAdminForm.cs           # الواجهة الرئيسية
├── 🗄️ DatabaseManager.cs        # إدارة قاعدة البيانات
├── 🏪 PharmacyManager.cs         # إدارة الصيدليات
├── 💰 SubscriptionManager.cs     # إدارة الاشتراكات
├── 💾 BackupManager.cs           # إدارة النسخ الاحتياطية
├── 📊 ReportManager.cs           # إدارة التقارير
├── ⚙️ App.config                 # إعدادات التطبيق
├── 🗄️ create_admin_database.sql # سكريپت إنشاء قاعدة البيانات
├── 🚀 setup_admin_database.bat  # ملف الإعداد التلقائي
└── 📖 README.md                  # هذا الملف
```

---

## 🎯 الواجهات الرئيسية | Main Interfaces

### 1. لوحة التحكم | Dashboard
- إحصائيات سريعة للصيدليات والإيرادات
- النشاطات الأخيرة
- تنبيهات الاشتراكات المنتهية

### 2. إدارة الصيدليات | Pharmacy Management
- قائمة شاملة بجميع الصيدليات
- تفاصيل كل صيدلية
- أدوات البحث والفلترة

### 3. طلبات التسجيل | Registration Requests
- طلبات التسجيل المعلقة
- أدوات الموافقة والرفض
- تتبع حالة الطلبات

### 4. إدارة الاشتراكات | Subscription Management
- عرض جميع الاشتراكات
- تجديد وتحديث الاشتراكات
- تتبع المدفوعات

### 5. النسخ الاحتياطية | Backup Management
- إنشاء نسخ احتياطية يدوية
- جدولة النسخ التلقائية
- استعادة البيانات

### 6. التقارير | Reports
- تقارير مالية مفصلة
- إحصائيات الاستخدام
- تقارير النشاطات

---

## 🔒 الأمان | Security

### حماية البيانات | Data Protection
- تشفير كلمات المرور
- جلسات آمنة
- سجل نشاطات شامل

### صلاحيات الوصول | Access Control
- مستويات صلاحيات متعددة
- تتبع جلسات المستخدمين
- حماية من الوصول غير المصرح

### النسخ الاحتياطية | Backup Security
- تشفير النسخ الاحتياطية
- تخزين آمن
- استعادة محمية

---

## 🐛 استكشاف الأخطاء | Troubleshooting

### مشاكل الاتصال بقاعدة البيانات
```
خطأ: Cannot connect to database
الحل: تحقق من تشغيل SQL Server وصحة اسم الخادم
```

### مشاكل تسجيل الدخول
```
خطأ: Invalid username or password
الحل: استخدم البيانات الافتراضية أو أعد تشغيل سكريپت الإعداد
```

### مشاكل النسخ الاحتياطية
```
خطأ: Backup failed
الحل: تحقق من صلاحيات الكتابة في مجلد النسخ الاحتياطية
```

---

## 📞 الدعم الفني | Technical Support

### معلومات الاتصال | Contact Information
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: 0112345678
- **ساعات العمل**: 8:00 ص - 6:00 م (السبت - الخميس)

### الموارد المفيدة | Useful Resources
- دليل المستخدم الكامل
- فيديوهات تعليمية
- قاعدة المعرفة
- منتدى المجتمع

---

## 🔄 التحديثات | Updates

### الإصدار الحالي | Current Version
**v1.0.0** - الإصدار الأولي

### التحديثات القادمة | Upcoming Updates
- واجهات محسنة
- تقارير إضافية
- ميزات أمان متقدمة
- دعم قواعد بيانات متعددة

---

## 📄 الترخيص | License

هذا البرنامج مطور خصيصاً لإدارة الصيدليات ومحمي بحقوق الطبع والنشر.

This software is specifically developed for pharmacy management and is protected by copyright.

---

## 🎉 شكر وتقدير | Acknowledgments

تم تطوير هذا النظام بعناية فائقة لتوفير حل شامل ومتكامل لإدارة الصيدليات. نشكر جميع من ساهم في تطوير وتحسين هذا النظام.

---

**🚀 نظام إدارة الصيدليات المركزي - الحل الأمثل لإدارة شبكة الصيدليات**
