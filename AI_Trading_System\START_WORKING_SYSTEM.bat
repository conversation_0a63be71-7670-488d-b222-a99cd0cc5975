@echo off
chcp 65001 >nul
title 🎯 نظام التداول الذكي - يعمل 100%
color 0A

echo.
echo ================================================
echo    🎯 نظام التداول الذكي - يعمل 100%% 🎯
echo ================================================
echo.
echo ✅ تم حل جميع المشاكل:
echo    🔧 النظام يدخل صفقات حقيقية الآن
echo    🧠 التعلم محسن ومتوازن
echo    🛡️ نظام تجريبي متكامل كبديل
echo    📊 تحليل ذكي للسوق
echo.
echo 🚀 كيف يعمل النظام:
echo    1️⃣ يحاول الاتصال بـ MT5 أولاً
echo    2️⃣ إذا فشل يتبدل للنظام التجريبي
echo    3️⃣ يحلل السوق ويدخل صفقات
echo    4️⃣ يتعلم من كل صفقة ويحسن الأداء
echo.
echo 🎛️ الأزرار المتوفرة:
echo    🔗 Connect - للاتصال بالنظام
echo    ▶️ Start Trading - لبدء التداول
echo    🧠 Learning Stats - لعرض إحصائيات التعلم
echo    🔄 Reset Memory - لإعادة تعيين الذاكرة
echo.

cd /d "%~dp0"

echo 📦 فحص المتطلبات...
python -c "import tkinter; print('✅ tkinter متوفر')" 2>nul || (echo ❌ tkinter غير متوفر && pause && exit)
python -c "import pandas; print('✅ pandas متوفر')" 2>nul || (echo ❌ pandas غير متوفر && pause && exit)
python -c "import numpy; print('✅ numpy متوفر')" 2>nul || (echo ❌ numpy غير متوفر && pause && exit)

echo.
echo 🚀 تشغيل النظام...
echo 💡 النظام سيعمل حتى لو لم يكن MT5 متوفراً!
echo.

python advanced_trading_gui.py

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ حدث خطأ في تشغيل النظام
    echo 🔍 تحقق من الأخطاء أعلاه
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق النظام بنجاح
    echo 🎯 شكراً لاستخدام النظام!
)

pause
