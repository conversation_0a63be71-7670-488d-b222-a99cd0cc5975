# تعليمات التشغيل السريع لنظام إدارة الصيدلية
# Quick Start Guide for Pharmacy Management System

## 🚀 البدء السريع | Quick Start

### 1. إعداد قاعدة البيانات | Database Setup
```bash
# تشغيل إعداد قاعدة البيانات الشامل
# Run complete database setup
setup_pharmacy_store_database.bat
```

### 2. بناء المشروع | Build Project
```bash
# إصلاح مشاكل البناء
# Fix build issues
fix_build_issues.bat
```

### 3. التشغيل الكامل | Complete Setup
```bash
# إعداد النظام بالكامل
# Complete system setup
complete_system_setup.bat
```

## 📋 معلومات تسجيل الدخول | Login Information

### المدير الرئيسي | Main Administrator
- **اس<PERSON> المستخدم | Username:** `admin`
- **كلمة المرور | Password:** `admin123`
- **الصلاحيات | Permissions:** جميع الصلاحيات | Full Access

### الموظفين | Employees
- **اسم المستخدم | Username:** `employee1`
- **كلمة المرور | Password:** `emp123`
- **الصلاحيات | Permissions:** صلاحيات الموظف | Employee Access

## 🗄️ قاعدة البيانات | Database

### الاسم | Name
`UnifiedPharmacy`

### المسار | Path
`C:\Program Files\Microsoft SQL Server\MSSQL16.MSSQLSERVER\MSSQL\DATA\`

### الجداول الرئيسية | Main Tables
- `users` - المستخدمون
- `pharmacies` - الصيدليات
- `medic` - الأدوية
- `sales` - المبيعات
- `employee_sessions` - جلسات الموظفين
- `published_medicines` - الأدوية المنشورة
- `purchase_requests` - طلبات الشراء
- `pharmacy_messages` - رسائل الصيدليات
- `print_settings` - إعدادات الطباعة

## 🔧 استكشاف الأخطاء | Troubleshooting

### مشكلة الاتصال بقاعدة البيانات
```
Cannot connect to SQL Server
```
**الحل:**
1. تأكد من تشغيل SQL Server
2. تحقق من اسم الخادم
3. تأكد من الصلاحيات

### مشكلة البناء
```
Build failed
```
**الحل:**
1. تشغيل `fix_build_issues.bat`
2. إعادة تشغيل Visual Studio
3. تنظيف وإعادة بناء المشروع

## 📞 الدعم | Support

للحصول على المساعدة:
For assistance:

1. تحقق من ملفات التعليمات
2. تشغيل ملفات الإصلاح
3. مراجعة رسائل الخطأ

---
**تم التحديث:** 2025-06-30  
**الإصدار:** 2.0
