#!/usr/bin/env python3
"""
Smart Money Manager - مدير رأس المال الذكي
نظام متقدم لإدارة رأس المال والمخاطر للحسابات الصغيرة والكبيرة
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

try:
    import MetaTrader5 as mt5
except ImportError:
    mt5 = None

class SmartMoneyManager:
    """
    مدير رأس المال الذكي للتداول الآمن والمربح
    """
    
    def __init__(self, symbol: str = "EURUSD", min_balance: float = 10.0):
        """
        تهيئة مدير رأس المال الذكي
        """
        self.symbol = symbol
        self.min_balance = min_balance
        self.logger = self._setup_logger()
        
        # إعدادات إدارة رأس المال
        self.money_management_config = {
            # إعدادات المخاطر
            'max_risk_per_trade': 0.02,      # 2% مخاطر لكل صفقة
            'max_daily_risk': 0.05,          # 5% مخاطر يومية
            'max_weekly_risk': 0.15,         # 15% مخاطر أسبوعية
            'max_monthly_risk': 0.30,        # 30% مخاطر شهرية
            
            # إعدادات الحسابات الصغيرة (أقل من 100$)
            'small_account_threshold': 100.0,
            'small_account_max_risk': 0.05,  # 5% للحسابات الصغيرة
            'small_account_min_volume': 0.01,
            
            # إعدادات الحسابات المتوسطة (100$ - 1000$)
            'medium_account_threshold': 1000.0,
            'medium_account_max_risk': 0.03, # 3% للحسابات المتوسطة
            
            # إعدادات الحسابات الكبيرة (أكثر من 1000$)
            'large_account_max_risk': 0.02,  # 2% للحسابات الكبيرة
            
            # إعدادات الهامش والرافعة
            'max_margin_usage': 0.30,        # لا تستخدم أكثر من 30% من الرصيد كهامش
            'safety_margin_multiplier': 3.0, # احتفظ بـ 3x الهامش المطلوب
            
            # إعدادات السبريد
            'max_spread_pips': 3.0,          # الحد الأقصى للسبريد المقبول
            'spread_cost_factor': 1.5,       # عامل تكلفة السبريد
        }
        
        # إحصائيات المخاطر
        self.risk_stats = {
            'daily_risk_used': 0.0,
            'weekly_risk_used': 0.0,
            'monthly_risk_used': 0.0,
            'current_exposure': 0.0,
            'max_drawdown': 0.0,
            'risk_adjusted_return': 0.0,
            'last_reset_date': datetime.now().date()
        }
        
        # سجل الصفقات لتتبع المخاطر
        self.trades_log = []
    
    def _setup_logger(self) -> logging.Logger:
        """
        إعداد نظام السجلات
        """
        logger = logging.getLogger(f'SmartMoneyManager_{self.symbol}')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def get_account_type(self, balance: float) -> str:
        """
        تحديد نوع الحساب بناءً على الرصيد
        """
        if balance < self.money_management_config['small_account_threshold']:
            return 'small'
        elif balance < self.money_management_config['medium_account_threshold']:
            return 'medium'
        else:
            return 'large'
    
    def calculate_optimal_position_size(self, balance: float, confidence: float,
                                      stop_loss_pips: float, current_spread: float = None) -> Dict:
        """
        حساب حجم الصفقة الأمثل بناءً على إدارة رأس المال الذكية
        """
        try:
            # التحقق من الحد الأدنى للرصيد
            if balance < self.min_balance:
                return {
                    'volume': 0.0,
                    'reason': f'الرصيد أقل من الحد الأدنى (${balance:.2f} < ${self.min_balance})',
                    'risk_amount': 0.0,
                    'margin_required': 0.0,
                    'account_type': self.get_account_type(balance)
                }
            
            # تحديد نوع الحساب
            account_type = self.get_account_type(balance)
            
            # تحديد الحد الأقصى للمخاطر حسب نوع الحساب
            if account_type == 'small':
                max_risk_per_trade = self.money_management_config['small_account_max_risk']
            elif account_type == 'medium':
                max_risk_per_trade = self.money_management_config['medium_account_max_risk']
            else:
                max_risk_per_trade = self.money_management_config['large_account_max_risk']
            
            # تعديل المخاطر بناءً على الثقة
            adjusted_risk = max_risk_per_trade * confidence
            
            # التحقق من المخاطر اليومية المتراكمة
            self._update_risk_stats()
            if self.risk_stats['daily_risk_used'] + adjusted_risk > self.money_management_config['max_daily_risk']:
                remaining_daily_risk = self.money_management_config['max_daily_risk'] - self.risk_stats['daily_risk_used']
                if remaining_daily_risk <= 0:
                    return {
                        'volume': 0.0,
                        'reason': 'تم استنفاد المخاطر اليومية المسموحة',
                        'risk_amount': 0.0,
                        'margin_required': 0.0,
                        'account_type': account_type
                    }
                adjusted_risk = min(adjusted_risk, remaining_daily_risk)
            
            # الحصول على معلومات الرمز
            if not mt5:
                return self._calculate_fallback_position_size(balance, adjusted_risk, stop_loss_pips, account_type)
            
            symbol_info = mt5.symbol_info(self.symbol)
            if not symbol_info:
                return self._calculate_fallback_position_size(balance, adjusted_risk, stop_loss_pips, account_type)
            
            # الحصول على السعر الحالي
            tick_info = mt5.symbol_info_tick(self.symbol)
            if not tick_info:
                return self._calculate_fallback_position_size(balance, adjusted_risk, stop_loss_pips, account_type)
            
            # حساب السبريد الحالي
            if current_spread is None:
                current_spread = (tick_info.ask - tick_info.bid) / symbol_info.point
            
            # التحقق من السبريد
            if current_spread > self.money_management_config['max_spread_pips']:
                return {
                    'volume': 0.0,
                    'reason': f'السبريد مرتفع جداً ({current_spread:.1f} نقطة)',
                    'risk_amount': 0.0,
                    'margin_required': 0.0,
                    'account_type': account_type,
                    'current_spread': current_spread
                }
            
            # حساب قيمة النقطة
            pip_value = self._calculate_pip_value(symbol_info, tick_info)
            
            # حساب المبلغ المعرض للمخاطر
            risk_amount = balance * adjusted_risk
            
            # تعديل المخاطر لتشمل تكلفة السبريد
            spread_cost = current_spread * pip_value * self.money_management_config['spread_cost_factor']
            effective_stop_loss_pips = stop_loss_pips + (spread_cost / pip_value)
            
            # حساب حجم الصفقة
            if pip_value > 0 and effective_stop_loss_pips > 0:
                calculated_volume = risk_amount / (effective_stop_loss_pips * pip_value)
            else:
                calculated_volume = symbol_info.volume_min
            
            # تطبيق حدود الحجم
            min_volume = symbol_info.volume_min
            max_volume = symbol_info.volume_max
            
            # حد أقصى آمن للحجم بناءً على الرصيد
            safe_max_volume = balance / (tick_info.ask * 1000)  # تقدير محافظ
            max_volume = min(max_volume, safe_max_volume)
            
            # تقريب الحجم
            volume_step = symbol_info.volume_step
            final_volume = max(min_volume, min(calculated_volume, max_volume))
            final_volume = round(final_volume / volume_step) * volume_step
            
            # حساب الهامش المطلوب
            margin_required = self._calculate_margin_required(final_volume, tick_info.ask, symbol_info)
            
            # التحقق من استخدام الهامش
            margin_usage_percentage = (margin_required / balance) * 100
            if margin_usage_percentage > self.money_management_config['max_margin_usage'] * 100:
                # تقليل حجم الصفقة لتناسب حدود الهامش
                max_affordable_volume = (balance * self.money_management_config['max_margin_usage']) / (tick_info.ask * symbol_info.margin_initial / 100)
                final_volume = min(final_volume, max_affordable_volume)
                final_volume = round(final_volume / volume_step) * volume_step
                margin_required = self._calculate_margin_required(final_volume, tick_info.ask, symbol_info)
            
            # حساب المخاطر الفعلية
            actual_risk_amount = final_volume * effective_stop_loss_pips * pip_value
            actual_risk_percentage = (actual_risk_amount / balance) * 100
            
            # حساب العائد المتوقع
            expected_return = final_volume * (effective_stop_loss_pips * 2) * pip_value  # افتراض نسبة 1:2
            
            result = {
                'volume': final_volume,
                'risk_amount': actual_risk_amount,
                'risk_percentage': actual_risk_percentage,
                'margin_required': margin_required,
                'margin_usage_percentage': margin_usage_percentage,
                'expected_return': expected_return,
                'pip_value': pip_value,
                'effective_stop_loss_pips': effective_stop_loss_pips,
                'spread_cost': spread_cost,
                'current_spread': current_spread,
                'account_type': account_type,
                'confidence_factor': confidence,
                'reason': 'حجم صفقة محسوب بذكاء',
                'safety_score': self._calculate_safety_score(balance, actual_risk_amount, margin_required)
            }
            
            self.logger.info(f"💰 حجم الصفقة المحسوب: {final_volume}")
            self.logger.info(f"💰 نوع الحساب: {account_type}")
            self.logger.info(f"💰 المخاطر: ${actual_risk_amount:.2f} ({actual_risk_percentage:.2f}%)")
            self.logger.info(f"💰 الهامش: ${margin_required:.2f} ({margin_usage_percentage:.1f}%)")
            self.logger.info(f"💰 السبريد: {current_spread:.1f} نقطة")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في حساب حجم الصفقة: {e}")
            return {
                'volume': 0.0,
                'reason': f'خطأ في الحساب: {str(e)}',
                'risk_amount': 0.0,
                'margin_required': 0.0,
                'account_type': self.get_account_type(balance)
            }
    
    def _calculate_pip_value(self, symbol_info, tick_info) -> float:
        """
        حساب قيمة النقطة
        """
        try:
            if self.symbol.endswith('USD'):
                # للأزواج المقومة بالدولار
                pip_value = symbol_info.trade_tick_value * 10
            elif self.symbol.startswith('USD'):
                # للأزواج التي تبدأ بالدولار
                pip_value = symbol_info.trade_tick_value * 10 / tick_info.bid
            else:
                # للأزواج الأخرى
                pip_value = symbol_info.trade_tick_value * 10
            
            return max(pip_value, 0.1)  # حد أدنى لقيمة النقطة
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في حساب قيمة النقطة: {e}")
            return 1.0  # قيمة افتراضية
    
    def _calculate_margin_required(self, volume: float, price: float, symbol_info) -> float:
        """
        حساب الهامش المطلوب
        """
        try:
            # حساب الهامش بناءً على معلومات الرمز
            contract_size = symbol_info.trade_contract_size
            margin_rate = symbol_info.margin_initial / 100
            
            margin_required = volume * contract_size * price * margin_rate
            
            return max(margin_required, volume * 100)  # حد أدنى للهامش
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في حساب الهامش: {e}")
            return volume * price * 100  # تقدير تقريبي
    
    def _calculate_safety_score(self, balance: float, risk_amount: float, margin_required: float) -> float:
        """
        حساب نقاط الأمان للصفقة
        """
        try:
            # عوامل الأمان
            risk_factor = 1 - (risk_amount / balance)  # كلما قلت المخاطر، زاد الأمان
            margin_factor = 1 - (margin_required / balance)  # كلما قل الهامش، زاد الأمان
            balance_factor = min(balance / 1000, 1.0)  # الحسابات الأكبر أكثر أماناً
            
            # حساب النقاط الإجمالية (من 0 إلى 100)
            safety_score = (risk_factor * 40 + margin_factor * 40 + balance_factor * 20) * 100
            
            return max(0, min(100, safety_score))
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في حساب نقاط الأمان: {e}")
            return 50.0  # نقاط متوسطة
    
    def _calculate_fallback_position_size(self, balance: float, risk_percentage: float, 
                                        stop_loss_pips: float, account_type: str) -> Dict:
        """
        حساب حجم الصفقة الاحتياطي بدون MT5
        """
        try:
            # قيم افتراضية للحساب
            pip_value = 1.0  # قيمة افتراضية للنقطة
            risk_amount = balance * risk_percentage
            
            # حساب الحجم
            if stop_loss_pips > 0:
                volume = risk_amount / (stop_loss_pips * pip_value)
            else:
                volume = 0.01
            
            # تطبيق حدود الحجم
            min_volume = 0.01
            max_volume = balance / 1000  # حد أقصى آمن
            
            final_volume = max(min_volume, min(volume, max_volume))
            final_volume = round(final_volume, 2)  # تقريب لخانتين عشريتين
            
            return {
                'volume': final_volume,
                'risk_amount': final_volume * stop_loss_pips * pip_value,
                'risk_percentage': (final_volume * stop_loss_pips * pip_value / balance) * 100,
                'margin_required': final_volume * 1000,  # تقدير تقريبي
                'account_type': account_type,
                'reason': 'حساب احتياطي بدون MT5',
                'safety_score': 70.0
            }
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في الحساب الاحتياطي: {e}")
            return {
                'volume': 0.01,
                'risk_amount': balance * 0.02,
                'risk_percentage': 2.0,
                'margin_required': 100.0,
                'account_type': account_type,
                'reason': 'حساب افتراضي',
                'safety_score': 50.0
            }

    def _update_risk_stats(self):
        """
        تحديث إحصائيات المخاطر
        """
        try:
            current_date = datetime.now().date()

            # إعادة تعيين المخاطر اليومية إذا تغير اليوم
            if current_date != self.risk_stats['last_reset_date']:
                self.risk_stats['daily_risk_used'] = 0.0
                self.risk_stats['last_reset_date'] = current_date

            # حساب المخاطر الأسبوعية والشهرية من سجل الصفقات
            week_ago = current_date - timedelta(days=7)
            month_ago = current_date - timedelta(days=30)

            weekly_risk = 0.0
            monthly_risk = 0.0

            for trade in self.trades_log:
                trade_date = trade.get('date', current_date)
                if isinstance(trade_date, str):
                    trade_date = datetime.strptime(trade_date, '%Y-%m-%d').date()

                risk_amount = trade.get('risk_amount', 0.0)

                if trade_date >= week_ago:
                    weekly_risk += risk_amount

                if trade_date >= month_ago:
                    monthly_risk += risk_amount

            # تحديث الإحصائيات
            self.risk_stats['weekly_risk_used'] = weekly_risk
            self.risk_stats['monthly_risk_used'] = monthly_risk

        except Exception as e:
            self.logger.error(f"❌ خطأ في تحديث إحصائيات المخاطر: {e}")

    def can_open_position(self, balance: float, risk_amount: float) -> Dict:
        """
        التحقق من إمكانية فتح صفقة جديدة
        """
        try:
            self._update_risk_stats()

            # حساب النسب المئوية للمخاطر
            daily_risk_percentage = (self.risk_stats['daily_risk_used'] + risk_amount) / balance
            weekly_risk_percentage = (self.risk_stats['weekly_risk_used'] + risk_amount) / balance
            monthly_risk_percentage = (self.risk_stats['monthly_risk_used'] + risk_amount) / balance

            # التحقق من الحدود
            checks = {
                'daily_risk_ok': daily_risk_percentage <= self.money_management_config['max_daily_risk'],
                'weekly_risk_ok': weekly_risk_percentage <= self.money_management_config['max_weekly_risk'],
                'monthly_risk_ok': monthly_risk_percentage <= self.money_management_config['max_monthly_risk'],
                'balance_sufficient': balance >= self.min_balance
            }

            # تحديد السبب في حالة الرفض
            reasons = []
            if not checks['daily_risk_ok']:
                reasons.append(f'تجاوز المخاطر اليومية ({daily_risk_percentage:.1%})')
            if not checks['weekly_risk_ok']:
                reasons.append(f'تجاوز المخاطر الأسبوعية ({weekly_risk_percentage:.1%})')
            if not checks['monthly_risk_ok']:
                reasons.append(f'تجاوز المخاطر الشهرية ({monthly_risk_percentage:.1%})')
            if not checks['balance_sufficient']:
                reasons.append(f'الرصيد غير كافي (${balance:.2f})')

            can_open = all(checks.values())

            result = {
                'can_open': can_open,
                'reasons': reasons,
                'daily_risk_used': daily_risk_percentage,
                'weekly_risk_used': weekly_risk_percentage,
                'monthly_risk_used': monthly_risk_percentage,
                'remaining_daily_risk': max(0, self.money_management_config['max_daily_risk'] - daily_risk_percentage),
                'remaining_weekly_risk': max(0, self.money_management_config['max_weekly_risk'] - weekly_risk_percentage),
                'remaining_monthly_risk': max(0, self.money_management_config['max_monthly_risk'] - monthly_risk_percentage)
            }

            if can_open:
                self.logger.info("✅ يمكن فتح صفقة جديدة")
            else:
                self.logger.warning(f"❌ لا يمكن فتح صفقة: {', '.join(reasons)}")

            return result

        except Exception as e:
            self.logger.error(f"❌ خطأ في التحقق من إمكانية فتح الصفقة: {e}")
            return {
                'can_open': False,
                'reasons': [f'خطأ في التحقق: {str(e)}'],
                'daily_risk_used': 0.0,
                'weekly_risk_used': 0.0,
                'monthly_risk_used': 0.0
            }

    def log_trade(self, trade_info: Dict):
        """
        تسجيل صفقة في السجل لتتبع المخاطر
        """
        try:
            trade_record = {
                'date': datetime.now().date(),
                'timestamp': datetime.now(),
                'symbol': trade_info.get('symbol', self.symbol),
                'type': trade_info.get('type', 'unknown'),
                'volume': trade_info.get('volume', 0.0),
                'risk_amount': trade_info.get('risk_amount', 0.0),
                'margin_used': trade_info.get('margin_required', 0.0),
                'confidence': trade_info.get('confidence', 0.0),
                'balance_at_time': trade_info.get('balance', 0.0)
            }

            self.trades_log.append(trade_record)

            # الاحتفاظ بآخر 1000 صفقة فقط
            if len(self.trades_log) > 1000:
                self.trades_log = self.trades_log[-1000:]

            # تحديث المخاطر اليومية
            self.risk_stats['daily_risk_used'] += trade_record['risk_amount']

            self.logger.info(f"📝 تم تسجيل الصفقة: {trade_record['type']} {trade_record['volume']}")

        except Exception as e:
            self.logger.error(f"❌ خطأ في تسجيل الصفقة: {e}")

    def get_risk_report(self, balance: float) -> Dict:
        """
        الحصول على تقرير المخاطر الحالي
        """
        try:
            self._update_risk_stats()

            account_type = self.get_account_type(balance)

            # حساب النسب المئوية
            daily_risk_pct = (self.risk_stats['daily_risk_used'] / balance) * 100 if balance > 0 else 0
            weekly_risk_pct = (self.risk_stats['weekly_risk_used'] / balance) * 100 if balance > 0 else 0
            monthly_risk_pct = (self.risk_stats['monthly_risk_used'] / balance) * 100 if balance > 0 else 0

            # حساب المخاطر المتبقية
            max_daily = self.money_management_config['max_daily_risk'] * 100
            max_weekly = self.money_management_config['max_weekly_risk'] * 100
            max_monthly = self.money_management_config['max_monthly_risk'] * 100

            remaining_daily = max(0, max_daily - daily_risk_pct)
            remaining_weekly = max(0, max_weekly - weekly_risk_pct)
            remaining_monthly = max(0, max_monthly - monthly_risk_pct)

            # تقييم حالة المخاطر
            risk_status = 'آمن'
            if daily_risk_pct > 80 or weekly_risk_pct > 80 or monthly_risk_pct > 80:
                risk_status = 'خطر عالي'
            elif daily_risk_pct > 60 or weekly_risk_pct > 60 or monthly_risk_pct > 60:
                risk_status = 'خطر متوسط'
            elif daily_risk_pct > 40 or weekly_risk_pct > 40 or monthly_risk_pct > 40:
                risk_status = 'تحذير'

            report = {
                'account_type': account_type,
                'balance': balance,
                'risk_status': risk_status,
                'daily_risk': {
                    'used': daily_risk_pct,
                    'max': max_daily,
                    'remaining': remaining_daily,
                    'amount_used': self.risk_stats['daily_risk_used'],
                    'amount_remaining': balance * (remaining_daily / 100)
                },
                'weekly_risk': {
                    'used': weekly_risk_pct,
                    'max': max_weekly,
                    'remaining': remaining_weekly,
                    'amount_used': self.risk_stats['weekly_risk_used'],
                    'amount_remaining': balance * (remaining_weekly / 100)
                },
                'monthly_risk': {
                    'used': monthly_risk_pct,
                    'max': max_monthly,
                    'remaining': remaining_monthly,
                    'amount_used': self.risk_stats['monthly_risk_used'],
                    'amount_remaining': balance * (remaining_monthly / 100)
                },
                'total_trades_today': len([t for t in self.trades_log if t['date'] == datetime.now().date()]),
                'total_trades_week': len([t for t in self.trades_log if t['date'] >= datetime.now().date() - timedelta(days=7)]),
                'total_trades_month': len([t for t in self.trades_log if t['date'] >= datetime.now().date() - timedelta(days=30)]),
                'recommendations': self._get_risk_recommendations(risk_status, daily_risk_pct, weekly_risk_pct, monthly_risk_pct)
            }

            return report

        except Exception as e:
            self.logger.error(f"❌ خطأ في إنشاء تقرير المخاطر: {e}")
            return {
                'account_type': 'unknown',
                'balance': balance,
                'risk_status': 'خطأ',
                'error': str(e)
            }

    def _get_risk_recommendations(self, risk_status: str, daily: float, weekly: float, monthly: float) -> List[str]:
        """
        الحصول على توصيات إدارة المخاطر
        """
        recommendations = []

        if risk_status == 'خطر عالي':
            recommendations.append('🚨 توقف عن التداول فوراً حتى تنخفض المخاطر')
            recommendations.append('📊 راجع استراتيجية إدارة المخاطر')
            recommendations.append('💡 فكر في تقليل أحجام الصفقات')

        elif risk_status == 'خطر متوسط':
            recommendations.append('⚠️ كن حذراً في الصفقات القادمة')
            recommendations.append('📉 قلل من أحجام الصفقات')
            recommendations.append('🎯 ركز على الصفقات عالية الجودة فقط')

        elif risk_status == 'تحذير':
            recommendations.append('📋 راقب المخاطر عن كثب')
            recommendations.append('🔍 تأكد من جودة الإشارات قبل الدخول')

        else:  # آمن
            recommendations.append('✅ مستوى المخاطر آمن')
            recommendations.append('📈 يمكن مواصلة التداول بحذر')

        # توصيات إضافية بناءً على النسب
        if daily > 70:
            recommendations.append('⏰ انتظر حتى الغد لفتح صفقات جديدة')

        if weekly > 80:
            recommendations.append('📅 تجنب التداول لبقية الأسبوع')

        if monthly > 90:
            recommendations.append('🗓️ توقف عن التداول لبقية الشهر')

        return recommendations
