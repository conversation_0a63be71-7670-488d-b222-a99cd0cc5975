-- اختبار إصلاحات صفحة بيع الأدوية
-- Test Medicine Selling Page Fixes

USE pharmacy;
GO

PRINT '========================================';
PRINT 'اختبار إصلاحات صفحة بيع الأدوية';
PRINT '========================================';

-- ===================================
-- 1. عرض الأدوية المتاحة
-- ===================================

PRINT 'الأدوية المتاحة (كمية > 0):';
SELECT 
    mname as 'اسم الدواء',
    quantity as 'الكمية',
    eDate as 'تاريخ الانتهاء',
    CASE 
        WHEN TRY_CONVERT(date, eDate, 103) < GETDATE() THEN 'منتهي الصلاحية'
        WHEN TRY_CONVERT(date, eDate, 103) BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE()) THEN 'قريب الانتهاء'
        ELSE 'صالح'
    END as 'حالة الصلاحية'
FROM medic 
WHERE quantity > 0
ORDER BY 
    CASE 
        WHEN TRY_CONVERT(date, eDate, 103) BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE()) THEN 1
        WHEN TRY_CONVERT(date, eDate, 103) < GETDATE() THEN 2
        ELSE 3
    END, mname;

-- ===================================
-- 2. اختبار الاستعلام الجديد للفلترة
-- ===================================

PRINT '';
PRINT 'اختبار فلتر الأدوية قريبة الانتهاء (خلال 30 يوم):';
SELECT 
    mname as 'اسم الدواء',
    eDate as 'تاريخ الانتهاء',
    DATEDIFF(day, GETDATE(), TRY_CONVERT(date, eDate, 103)) as 'أيام متبقية'
FROM medic 
WHERE quantity > 0 
    AND TRY_CONVERT(date, eDate, 103) BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE())
ORDER BY TRY_CONVERT(date, eDate, 103);

-- ===================================
-- 3. اختبار الأدوية منتهية الصلاحية
-- ===================================

PRINT '';
PRINT 'الأدوية منتهية الصلاحية:';
SELECT 
    mname as 'اسم الدواء',
    quantity as 'الكمية',
    eDate as 'تاريخ الانتهاء',
    DATEDIFF(day, TRY_CONVERT(date, eDate, 103), GETDATE()) as 'أيام منذ الانتهاء'
FROM medic 
WHERE quantity > 0 
    AND TRY_CONVERT(date, eDate, 103) < GETDATE()
ORDER BY TRY_CONVERT(date, eDate, 103) DESC;

-- ===================================
-- 4. إضافة أدوية تجريبية بتواريخ مختلفة
-- ===================================

PRINT '';
PRINT 'إضافة أدوية تجريبية بتواريخ انتهاء مختلفة...';

-- دواء قريب الانتهاء (خلال 15 يوم)
INSERT INTO medic (mid, mname, mnumber, mDate, eDate, quantity, perUnit, lu, br)
VALUES ('EXPIRING001', 'Medicine Expiring Soon', '500mg', '2024-01-01', 
        CONVERT(varchar, DATEADD(day, 15, GETDATE()), 23), 25, 12, 'Box', 'TestCompany');

-- دواء قريب الانتهاء (خلال 25 يوم)
INSERT INTO medic (mid, mname, mnumber, mDate, eDate, quantity, perUnit, lu, br)
VALUES ('EXPIRING002', 'Another Expiring Medicine', '250mg', '2024-01-01', 
        CONVERT(varchar, DATEADD(day, 25, GETDATE()), 23), 30, 8, 'Box', 'TestCompany');

-- دواء منتهي الصلاحية (منذ 5 أيام)
INSERT INTO medic (mid, mname, mnumber, mDate, eDate, quantity, perUnit, lu, br)
VALUES ('EXPIRED001', 'Expired Medicine', '100mg', '2024-01-01', 
        CONVERT(varchar, DATEADD(day, -5, GETDATE()), 23), 15, 5, 'Box', 'TestCompany');

-- دواء صالح لفترة طويلة
INSERT INTO medic (mid, mname, mnumber, mDate, eDate, quantity, perUnit, lu, br)
VALUES ('VALID001', 'Long Valid Medicine', '1000mg', '2024-01-01', 
        CONVERT(varchar, DATEADD(day, 365, GETDATE()), 23), 50, 20, 'Box', 'TestCompany');

PRINT 'تم إضافة 4 أدوية تجريبية بتواريخ انتهاء مختلفة';

-- ===================================
-- 5. اختبار الاستعلامات الجديدة
-- ===================================

PRINT '';
PRINT 'اختبار الاستعلام الجديد - جميع الأدوية المتاحة مع حالة الصلاحية:';
SELECT 
    mname as 'اسم الدواء',
    quantity as 'الكمية',
    eDate as 'تاريخ الانتهاء',
    CASE 
        WHEN TRY_CONVERT(date, eDate, 103) BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE()) 
             OR TRY_CONVERT(date, newEDate, 103) BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE())
        THEN mname + ' (قريب الانتهاء)'
        WHEN TRY_CONVERT(date, eDate, 103) < GETDATE() 
             OR TRY_CONVERT(date, newEDate, 103) < GETDATE()
        THEN mname + ' (منتهي الصلاحية)'
        ELSE mname
    END as 'اسم العرض'
FROM medic 
WHERE (quantity > 0 AND (TRY_CONVERT(date, eDate, 103) >= GETDATE() OR eDate >= CONVERT(varchar, GETDATE(), 23))) 
      OR (newQuantity > 0 AND (TRY_CONVERT(date, newEDate, 103) >= GETDATE() OR newEDate >= CONVERT(varchar, GETDATE(), 23)))
ORDER BY 
    CASE 
        WHEN TRY_CONVERT(date, eDate, 103) BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE()) 
             OR TRY_CONVERT(date, newEDate, 103) BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE())
        THEN 1
        ELSE 2
    END, mname;

-- ===================================
-- 6. إحصائيات نهائية
-- ===================================

PRINT '';
PRINT 'إحصائيات الأدوية:';
SELECT 
    'إجمالي الأدوية' as النوع,
    COUNT(*) as العدد
FROM medic

UNION ALL

SELECT 
    'الأدوية المتاحة' as النوع,
    COUNT(*) as العدد
FROM medic 
WHERE quantity > 0

UNION ALL

SELECT 
    'الأدوية قريبة الانتهاء' as النوع,
    COUNT(*) as العدد
FROM medic 
WHERE quantity > 0 
    AND TRY_CONVERT(date, eDate, 103) BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE())

UNION ALL

SELECT 
    'الأدوية منتهية الصلاحية' as النوع,
    COUNT(*) as العدد
FROM medic 
WHERE quantity > 0 
    AND TRY_CONVERT(date, eDate, 103) < GETDATE();

PRINT '';
PRINT '========================================';
PRINT 'تم الانتهاء من اختبار الإصلاحات!';
PRINT '';
PRINT 'الآن يمكنك:';
PRINT '1. تشغيل البرنامج';
PRINT '2. الذهاب لصفحة بيع الأدوية';
PRINT '3. رؤية الأدوية مع حالة الصلاحية';
PRINT '4. استخدام أزرار الفلترة الجديدة';
PRINT '========================================';
