# 🚀 دليل نظام تداول العملات الرقمية على MetaTrader 5

## 📋 نظرة عامة

نظام تداول العملات الرقمية المتطور الذي يعمل على منصة MetaTrader 5 مع ميزات الذكاء الاصطناعي والتعلم الآلي.

### ✨ المميزات الرئيسية

- 🎯 **20+ عملة رقمية مدعومة**: Bitcoin, Ethereum, Litecoin, Ripple وأكثر
- 🧠 **ذكاء اصطناعي متقدم**: نموذج Random Forest للتنبؤ بحركة الأسعار
- 📊 **تحليل فني شامل**: 15+ مؤشر فني متخصص للعملات الرقمية
- 🔄 **تعلم ذاتي**: النظام يتعلم من كل صفقة ويحسن أداءه
- 🛡️ **إدارة مخاطر متقدمة**: وقف خسارة وجني ربح تلقائي
- 🖥️ **واجهة رسومية احترافية**: تصميم داكن عصري مع تحديثات مباشرة
- 🔄 **وضعين للتداول**: تجريبي وحقيقي

---

## 🛠️ متطلبات التشغيل

### البرامج المطلوبة
- **MetaTrader 5**: أحدث إصدار
- **Python 3.8+**: مع المكتبات المطلوبة
- **حساب تداول**: يدعم العملات الرقمية

### المكتبات المطلوبة
```bash
pip install MetaTrader5 pandas numpy scikit-learn ta tkinter
```

### متطلبات الوسيط
- دعم العملات الرقمية (BTCUSD, ETHUSD, إلخ)
- تفعيل التداول الآلي (AutoTrading)
- API متاح للاتصال الخارجي

---

## 🚀 التثبيت والإعداد

### 1. إعداد MetaTrader 5
1. قم بتثبيت وتشغيل MetaTrader 5
2. سجل الدخول لحسابك
3. فعّل التداول الآلي: Tools → Options → Expert Advisors → Allow automated trading
4. تأكد من إضافة العملات الرقمية للمراقبة

### 2. إعداد النظام
1. انسخ جميع الملفات لمجلد واحد
2. عدّل ملف `mt5_crypto_config.ini`:
```ini
[MT5_CONNECTION]
login = رقم_حسابك
password = كلمة_المرور
server = اسم_الخادم
```

### 3. اختبار النظام
```bash
# تشغيل الاختبار الشامل
TEST_MT5_CRYPTO_SYSTEM.bat
```

---

## 🎮 كيفية الاستخدام

### التشغيل السريع
```bash
# تشغيل النظام مع الواجهة الرسومية
RUN_MT5_CRYPTO_TRADING.bat
```

### خطوات التشغيل
1. **الاتصال**: اضغط "🔌 اتصال" للاتصال بـ MT5
2. **اختيار العملة**: اختر العملة الرقمية من القائمة
3. **تحديد الوضع**: اختر "تجريبي" أو "حقيقي"
4. **بدء التداول**: اضغط "🚀 بدء التداول"

### الواجهة الرسومية

#### 📊 معلومات الحساب
- **الرصيد**: الرصيد الحالي للحساب
- **الأسهم**: قيمة الأسهم الحالية
- **الصفقات**: عدد الصفقات المفتوحة
- **نقاط التعلم**: عدد نقاط البيانات المجمعة للتعلم

#### 🎮 لوحة التحكم
- **العملة الرقمية**: اختيار العملة للتداول
- **الوضع**: تبديل بين التجريبي والحقيقي
- **بدء/إيقاف التداول**: التحكم في التداول
- **تدريب النموذج**: تدريب نموذج الذكاء الاصطناعي
- **تحليل فوري**: تحليل فوري للعملة المختارة

#### 📈 تحليل السوق
عرض مفصل للتحليل الفني يشمل:
- القرار النهائي ومستوى الثقة
- تحليل الاتجاه والزخم
- المؤشرات الفنية (RSI, MACD, إلخ)
- توقعات الذكاء الاصطناعي

#### 💼 الصفقات المفتوحة
جدول يعرض جميع الصفقات المفتوحة مع:
- الرمز ونوع الصفقة
- حجم الصفقة وأسعار الدخول
- الربح/الخسارة الحالية

---

## 🧠 نظام التعلم الآلي

### كيف يعمل
1. **جمع البيانات**: النظام يجمع بيانات من كل صفقة
2. **تحليل النتائج**: يحلل نتائج الصفقات (ربح/خسارة)
3. **تدريب النموذج**: يدرب نموذج Random Forest
4. **تحسين القرارات**: يستخدم التوقعات لتحسين قرارات التداول

### ميزات التعلم
- **تعلم مستمر**: من كل صفقة جديدة
- **تحسين تلقائي**: للمؤشرات والإعدادات
- **توقعات دقيقة**: بناءً على البيانات التاريخية
- **تكيف مع السوق**: يتكيف مع تغيرات السوق

---

## 📊 المؤشرات الفنية المدعومة

### مؤشرات الاتجاه
- **SMA**: المتوسط المتحرك البسيط (20, 50)
- **EMA**: المتوسط المتحرك الأسي (12, 26)
- **MACD**: مؤشر تقارب وتباعد المتوسطات المتحركة
- **Bollinger Bands**: نطاقات بولينجر

### مؤشرات الزخم
- **RSI**: مؤشر القوة النسبية
- **Stochastic**: مذبذب ستوكاستيك
- **Momentum**: مؤشر الزخم

### مؤشرات التقلبات
- **ATR**: متوسط المدى الحقيقي
- **Volatility**: مؤشر التقلبات المخصص

### مؤشرات الحجم
- **Volume SMA**: متوسط حجم التداول
- **Volume Ratio**: نسبة الحجم الحالي للمتوسط

---

## 🛡️ إدارة المخاطر

### الإعدادات الافتراضية
- **حجم الصفقة**: 0.01 لوت
- **وقف الخسارة**: 500 نقطة
- **جني الربح**: 1000 نقطة
- **المخاطر لكل صفقة**: 2% من رأس المال
- **الحد الأقصى للصفقات**: 5 صفقات مفتوحة

### ميزات الحماية
- **وقف خسارة تلقائي**: لكل صفقة
- **جني ربح تلقائي**: عند الوصول للهدف
- **حد أقصى للصفقات**: منع الإفراط في التداول
- **إدارة رأس المال**: حساب حجم الصفقة بناءً على المخاطر

---

## ⚙️ الإعدادات المتقدمة

### ملف الإعدادات `mt5_crypto_config.ini`

```ini
[MT5_CONNECTION]
login = 96406085                # رقم الحساب
password = كلمة_المرور          # كلمة مرور الحساب
server = MetaQuotes-Demo        # خادم الوسيط

[TRADING]
lot_size = 0.01                 # حجم الصفقة
min_confidence = 0.6            # الحد الأدنى للثقة
max_positions = 5               # الحد الأقصى للصفقات
stop_loss_pips = 500           # وقف الخسارة بالنقاط
take_profit_pips = 1000        # جني الربح بالنقاط
risk_per_trade = 0.02          # المخاطر لكل صفقة (2%)

[LEARNING]
learning_enabled = true         # تفعيل التعلم
retrain_interval = 24          # إعادة التدريب كل 24 ساعة
min_data_points = 50           # الحد الأدنى لنقاط البيانات
```

---

## 💎 العملات الرقمية المدعومة

| الرمز | العملة | الوصف |
|-------|---------|--------|
| BTCUSD | Bitcoin | العملة الرقمية الأولى |
| ETHUSD | Ethereum | منصة العقود الذكية |
| LTCUSD | Litecoin | الفضة الرقمية |
| XRPUSD | Ripple | شبكة المدفوعات |
| BCHUSD | Bitcoin Cash | فرع من البيتكوين |
| EOSUSD | EOS | منصة التطبيقات اللامركزية |
| XLMUSD | Stellar | شبكة المدفوعات العالمية |
| ADAUSD | Cardano | بلوك تشين الجيل الثالث |
| TRXUSD | Tron | منصة الترفيه اللامركزية |
| BNBUSD | Binance Coin | عملة منصة بينانس |
| DOTUSD | Polkadot | شبكة البلوك تشين المتعددة |
| UNIUSD | Uniswap | بروتوكول التداول اللامركزي |
| LINKUSD | Chainlink | شبكة أوراكل البيانات |
| SOLUSD | Solana | بلوك تشين عالي الأداء |
| AVAXUSD | Avalanche | منصة التطبيقات اللامركزية |
| MATICUSD | Polygon | حل التوسع لإيثريوم |
| DOGEUSD | Dogecoin | العملة الميمية |
| SHIBUSD | Shiba Inu | عملة المجتمع |
| APEUSD | ApeCoin | عملة النادي |
| SANDUSD | Sandbox | عالم الألعاب الافتراضي |

---

## 🔧 استكشاف الأخطاء

### مشاكل الاتصال
**المشكلة**: فشل الاتصال بـ MT5
**الحلول**:
- تأكد من تشغيل MetaTrader 5
- تحقق من بيانات الحساب في ملف الإعدادات
- تأكد من تفعيل التداول الآلي
- تحقق من الاتصال بالإنترنت

### مشاكل البيانات
**المشكلة**: لا يمكن الحصول على بيانات العملات
**الحلول**:
- تأكد من دعم الوسيط للعملات الرقمية
- أضف العملات للمراقبة في MT5
- تحقق من أوقات السوق
- تأكد من صلاحيات الحساب

### مشاكل التداول
**المشكلة**: فشل في تنفيذ الصفقات
**الحلول**:
- تحقق من الرصيد الكافي
- تأكد من تفعيل التداول
- تحقق من أوقات السوق
- راجع إعدادات المخاطر

---

## 📈 نصائح للنجاح

### للمبتدئين
1. **ابدأ بالوضع التجريبي**: تعلم كيف يعمل النظام
2. **استخدم مبالغ صغيرة**: في البداية
3. **راقب النظام**: لا تتركه يعمل بدون مراقبة
4. **تعلم من النتائج**: راجع الصفقات وتعلم منها

### للمتقدمين
1. **عدّل الإعدادات**: حسب استراتيجيتك
2. **استخدم التعلم الآلي**: دع النظام يتعلم من بياناتك
3. **نوّع العملات**: لا تركز على عملة واحدة
4. **راقب الأداء**: استخدم المقاييس لتحسين النتائج

### إدارة المخاطر
1. **لا تستثمر أكثر مما تستطيع خسارته**
2. **استخدم وقف الخسارة دائماً**
3. **نوّع محفظتك**
4. **راقب السوق والأخبار**

---

## 📞 الدعم والمساعدة

### الملفات المهمة
- `mt5_crypto_trading_system.py`: النظام الأساسي
- `mt5_crypto_gui.py`: الواجهة الرسومية
- `mt5_crypto_config.ini`: ملف الإعدادات
- `test_mt5_crypto_system.py`: نظام الاختبار

### ملفات السجلات
- `logs/mt5_crypto_trading_YYYYMMDD.log`: سجل التداول اليومي
- `models/`: مجلد نماذج التعلم الآلي

### نصائح الصيانة
1. **نسخ احتياطية منتظمة**: للإعدادات والنماذج
2. **تحديث البيانات**: راجع الإعدادات دورياً
3. **مراقبة الأداء**: تابع النتائج والإحصائيات
4. **تحديث النظام**: عند توفر إصدارات جديدة

---

## ⚠️ تحذيرات مهمة

### مخاطر التداول
- **العملات الرقمية عالية التقلب**: قد تخسر كامل استثمارك
- **التداول الآلي له مخاطر**: راقب النظام باستمرار
- **الأداء السابق لا يضمن النتائج المستقبلية**
- **استخدم أموال يمكنك تحمل خسارتها فقط**

### المسؤولية
- **أنت مسؤول عن قرارات التداول**
- **النظام أداة مساعدة وليس ضماناً للربح**
- **راجع وافهم جميع الإعدادات قبل التداول الحقيقي**
- **استشر خبير مالي إذا لزم الأمر**

---

## 🎯 الخلاصة

نظام تداول العملات الرقمية على MetaTrader 5 هو أداة قوية تجمع بين التحليل الفني المتقدم والذكاء الاصطناعي لتوفير تجربة تداول متطورة. استخدمه بحكمة وحذر، وابدأ دائماً بالوضع التجريبي لتتعلم كيف يعمل قبل الانتقال للتداول الحقيقي.

**🚀 استمتع بالتداول الذكي والآمن!**
