@echo off
chcp 65001 >nul
echo ═══════════════════════════════════════════════════════════════
echo                    إصلاح أخطاء البناء السريع
echo                    Quick Build Errors Fix
echo ═══════════════════════════════════════════════════════════════
echo.

echo 🔧 إصلاح الأخطاء المحددة:
echo 🔧 Fixing identified errors:
echo ───────────────────────────────────────────────────────────────
echo ✅ 1. SimpleLoginForm - تم إنشاء ملف Designer
echo ✅ 1. SimpleLoginForm - Designer file created
echo ✅ 2. UC_AddUser - تم إزالة المتغيرات غير المستخدمة
echo ✅ 2. UC_AddUser - Removed unused variables
echo ⚠️ 3. UC_P_PharmacyStore - تحذيرات async/await (غير حرجة)
echo ⚠️ 3. UC_P_PharmacyStore - async/await warnings (non-critical)
echo.

echo الخطوة 1: تنظيف المشروع
echo Step 1: Cleaning project
echo ───────────────────────────────────────────────────────────────

if exist "bin" (
    echo 🗑️ حذف مجلد bin...
    echo 🗑️ Deleting bin folder...
    rmdir /s /q "bin" >nul 2>&1
)

if exist "obj" (
    echo 🗑️ حذف مجلد obj...
    echo 🗑️ Deleting obj folder...
    rmdir /s /q "obj" >nul 2>&1
)

echo ✅ تم تنظيف المشروع
echo ✅ Project cleaned

echo.
echo الخطوة 2: بناء المشروع
echo Step 2: Building project
echo ───────────────────────────────────────────────────────────────

REM البحث عن MSBuild
set MSBUILD_PATH=""
if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
) else (
    echo ⚠️ MSBuild غير موجود، محاولة استخدام dotnet build
    echo ⚠️ MSBuild not found, trying dotnet build
    dotnet build "Pharmacy Management System.csproj" --configuration Debug --verbosity quiet
    goto CHECK_BUILD
)

echo 🔨 بناء المشروع باستخدام MSBuild...
echo 🔨 Building project using MSBuild...
%MSBUILD_PATH% "Pharmacy Management System.csproj" /p:Configuration=Debug /p:Platform="Any CPU" /nologo /verbosity:minimal

:CHECK_BUILD
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم بناء المشروع بنجاح!
    echo ✅ Project built successfully!
    echo.
    echo 📊 ملخص الإصلاحات:
    echo 📊 Fix summary:
    echo ───────────────────────────────────────────────────────────────
    echo ✅ SimpleLoginForm.Designer.cs - تم إنشاؤه
    echo ✅ SimpleLoginForm.Designer.cs - Created
    echo ✅ UC_AddUser.cs - تم إزالة المتغيرات غير المستخدمة
    echo ✅ UC_AddUser.cs - Removed unused variables
    echo ⚠️ UC_P_PharmacyStore.cs - تحذيرات async (لا تمنع التشغيل)
    echo ⚠️ UC_P_PharmacyStore.cs - async warnings (don't prevent execution)
    echo.
    echo 🚀 النظام جاهز للتشغيل!
    echo 🚀 System ready to run!
    echo.
    echo للتشغيل الآن:
    echo To run now:
    echo start "" "bin\Debug\Pharmacy Management System.exe"
    echo.
    
    REM تشغيل النظام تلقائياً
    if exist "bin\Debug\Pharmacy Management System.exe" (
        echo 🎯 تشغيل النظام تلقائياً...
        echo 🎯 Starting system automatically...
        start "" "bin\Debug\Pharmacy Management System.exe"
        echo.
        echo ✅ تم تشغيل النظام!
        echo ✅ System started!
        echo.
        echo 📋 معلومات تسجيل الدخول:
        echo 📋 Login credentials:
        echo • المدير: admin / admin123
        echo • Administrator: admin / admin123
        echo • الموظف: employee1 / emp123
        echo • Employee: employee1 / emp123
    )
) else (
    echo.
    echo ❌ فشل في بناء المشروع
    echo ❌ Project build failed
    echo.
    echo 🔍 الأخطاء المحتملة:
    echo 🔍 Possible errors:
    echo ───────────────────────────────────────────────────────────────
    echo 1. تأكد من وجود Visual Studio أو .NET Framework
    echo 1. Make sure Visual Studio or .NET Framework is installed
    echo 2. تحقق من ملفات المشروع
    echo 2. Check project files
    echo 3. أعد تشغيل Visual Studio كمدير
    echo 3. Restart Visual Studio as administrator
    echo.
    echo للمساعدة، افتح Visual Studio وتحقق من:
    echo For help, open Visual Studio and check:
    echo • Error List (قائمة الأخطاء)
    echo • Output Window (نافذة الإخراج)
    echo • Build Output (إخراج البناء)
)

echo.
echo ═══════════════════════════════════════════════════════════════
echo                        🎉 انتهى الإصلاح!
echo                     🎉 Fix Completed!
echo ═══════════════════════════════════════════════════════════════
echo.
echo ملاحظات مهمة:
echo Important notes:
echo • تحذيرات async/await لا تمنع تشغيل البرنامج
echo • async/await warnings don't prevent program execution
echo • النظام سيعمل بشكل طبيعي مع هذه التحذيرات
echo • System will work normally with these warnings
echo • يمكن إصلاحها لاحقاً إذا لزم الأمر
echo • Can be fixed later if needed
echo.
pause
