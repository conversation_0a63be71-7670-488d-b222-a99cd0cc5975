using DGVPrinterHelper;
using Guna.UI2.WinForms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy_Management_System.PharmacistUC
{


    public partial class UC__P_SellMedicine : UserControl
    {
        Function fn = new Function();
        String query;
        DataSet ds;

        // متغيرات لمعلومات الموظف الحالي
        public string CurrentUsername { get; set; }
        public string CurrentEmployeeName { get; set; }

        public UC__P_SellMedicine()
        {
            InitializeComponent();

            // تطبيق التصميم العصري
            ApplyModernDesign();

            // الاشتراك في أحداث التغيير
            LanguageManager.LanguageChanged += OnLanguageChanged;
            ModernTheme.ThemeChanged += OnThemeChanged;
        }

        private void label4_Click(object sender, EventArgs e)
        {

        }

        private void UC__P_SellMedicine_Load(object sender, EventArgs e)
        {
            // تطبيق اللغة الحالية
            ApplyLanguage();

            // إضافة أزرار الفلترة
            AddFilterButtons();

            // استخدام الدالة الجديدة للفلترة
            LoadMedicinesWithFilter("available");
        }

        private void btnSync_Click(object sender, EventArgs e)
        {
            LoadMedicinesWithFilter("available");
        }

        // تم حذف أزرار الفلترة الثلاثة كما طُلب
        private void AddFilterButtons()
        {
            // لا حاجة لإضافة أزرار فلترة إضافية
            // سيتم استخدام btnSync فقط لعرض الأدوية الصالحة للبيع
        }

        private void LoadMedicinesWithFilter(string filterType = "all")
        {
            try
            {
                listBoxMedicines.Items.Clear();

                string whereClause = "";
                switch (filterType.ToLower())
                {
                    case "expiring":
                        // الأدوية التي تنتهي خلال 30 يوم
                        whereClause = @"AND (
                            (TRY_CONVERT(date, eDate, 103) BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE()))
                            OR
                            (TRY_CONVERT(date, newEDate, 103) BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE()))
                        )";
                        break;
                    case "expired":
                        // الأدوية منتهية الصلاحية
                        whereClause = @"AND (
                            (TRY_CONVERT(date, eDate, 103) < GETDATE())
                            OR
                            (TRY_CONVERT(date, newEDate, 103) < GETDATE())
                        )";
                        break;
                    case "available":
                        // الأدوية المتاحة فقط
                        whereClause = @"AND (
                            (quantity > 0 AND (TRY_CONVERT(date, eDate, 103) >= GETDATE() OR eDate >= CONVERT(varchar, GETDATE(), 23)))
                            OR
                            (newQuantity > 0 AND (TRY_CONVERT(date, newEDate, 103) >= GETDATE() OR newEDate >= CONVERT(varchar, GETDATE(), 23)))
                        )";
                        break;
                    default:
                        // جميع الأدوية المتاحة
                        whereClause = @"AND (
                            (quantity > 0 AND (TRY_CONVERT(date, eDate, 103) >= GETDATE() OR eDate >= CONVERT(varchar, GETDATE(), 23)))
                            OR
                            (newQuantity > 0 AND (TRY_CONVERT(date, newEDate, 103) >= GETDATE() OR newEDate >= CONVERT(varchar, GETDATE(), 23)))
                        )";
                        break;
                }

                // عرض الأدوية الصالحة للبيع فقط (بدون منتهية الصلاحية)
                query = $@"
                    SELECT mname,
                           CASE
                               WHEN TRY_CONVERT(date, eDate, 103) BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE())
                                    OR TRY_CONVERT(date, newEDate, 103) BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE())
                               THEN mname + ' (قريب الانتهاء)'
                               ELSE mname
                           END as DisplayName
                    FROM medic
                    WHERE (
                        (quantity > 0 AND (TRY_CONVERT(date, eDate, 103) >= GETDATE() OR eDate >= CONVERT(varchar, GETDATE(), 23)))
                        OR
                        (newQuantity > 0 AND (TRY_CONVERT(date, newEDate, 103) >= GETDATE() OR newEDate >= CONVERT(varchar, GETDATE(), 23)))
                    )
                    AND pharmacy_id = {SessionManager.CurrentPharmacyId}
                    ORDER BY
                        CASE
                            WHEN TRY_CONVERT(date, eDate, 103) BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE())
                                 OR TRY_CONVERT(date, newEDate, 103) BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE())
                            THEN 1
                            ELSE 2
                        END, mname";

                ds = fn.getData(query);

                for (int i = 0; i < ds.Tables[0].Rows.Count; i++)
                {
                    listBoxMedicines.Items.Add(ds.Tables[0].Rows[i]["DisplayName"].ToString());
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Error loading medicine list") + ": " + ex.Message,
                    LanguageManager.GetText("Data loading error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            try
            {
                listBoxMedicines.Items.Clear();

                if (string.IsNullOrEmpty(txtSearch.Text))
                {
                    LoadMedicinesWithFilter("available");
                    return;
                }

                query = @"
                SELECT mname,
                       CASE
                           WHEN TRY_CONVERT(date, eDate, 103) BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE())
                                OR TRY_CONVERT(date, newEDate, 103) BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE())
                           THEN mname + ' (قريب الانتهاء)'
                           WHEN TRY_CONVERT(date, eDate, 103) < GETDATE()
                                OR TRY_CONVERT(date, newEDate, 103) < GETDATE()
                           THEN mname + ' (منتهي الصلاحية)'
                           ELSE mname
                       END as DisplayName
                FROM medic
                WHERE
                    mname LIKE '" + txtSearch.Text + @"%' AND
                    pharmacy_id = " + SessionManager.CurrentPharmacyId + @" AND
                    (
                        (quantity > 0 AND (TRY_CONVERT(date, eDate, 103) >= GETDATE() OR eDate >= CONVERT(varchar, GETDATE(), 23)))
                        OR
                        (newQuantity > 0 AND (TRY_CONVERT(date, newEDate, 103) >= GETDATE() OR newEDate >= CONVERT(varchar, GETDATE(), 23)))
                    )
                ORDER BY
                    CASE
                        WHEN TRY_CONVERT(date, eDate, 103) BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE())
                             OR TRY_CONVERT(date, newEDate, 103) BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE())
                        THEN 1
                        ELSE 2
                    END, mname";

                ds = fn.getData(query);

                for (int i = 0; i < ds.Tables[0].Rows.Count; i++)
                {
                    listBoxMedicines.Items.Add(ds.Tables[0].Rows[i]["DisplayName"].ToString());
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Error loading medicine list") + ": " + ex.Message,
                    LanguageManager.GetText("Data loading error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void listBoxMedicines_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                txtNoOfUnits.Clear();

                if (listBoxMedicines.SelectedItem != null)
                {
                    String name = listBoxMedicines.GetItemText(listBoxMedicines.SelectedItem);

                    // إزالة النصوص الإضافية من اسم الدواء
                    if (name.Contains(" (قريب الانتهاء)"))
                        name = name.Replace(" (قريب الانتهاء)", "");
                    if (name.Contains(" (منتهي الصلاحية)"))
                        name = name.Replace(" (منتهي الصلاحية)", "");

                    txtMediName.Text = name;

                    query = "SELECT mid, eDate, newEDate, perUnit, lu, br, quantity, newQuantity FROM medic WHERE mname = @mname AND pharmacy_id = @pharmacyId";
                    using (var con = fn.getConnection())
                    {
                        var cmd = new System.Data.SqlClient.SqlCommand(query, con);
                        cmd.Parameters.AddWithValue("@mname", name);
                        cmd.Parameters.AddWithValue("@pharmacyId", SessionManager.CurrentPharmacyId);
                        var da = new System.Data.SqlClient.SqlDataAdapter(cmd);
                        ds = new DataSet();
                        da.Fill(ds);
                    }

                    if (ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        txtMedild.Text = ds.Tables[0].Rows[0]["mid"].ToString();
                        DateTime eDate = Convert.ToDateTime(ds.Tables[0].Rows[0]["eDate"]);
                        DateTime newEDate = ds.Tables[0].Rows[0]["newEDate"] != DBNull.Value ? Convert.ToDateTime(ds.Tables[0].Rows[0]["newEDate"]) : DateTime.MinValue;

                        if (eDate >= DateTime.Now)
                            txtExpireDate.Value = eDate;
                        else if (newEDate >= DateTime.Now)
                            txtExpireDate.Value = newEDate;
                        else
                            txtExpireDate.Value = DateTime.Now; // تعيين التاريخ الحالي إذا كان منتهي الصلاحية

                        txtPricePerUnit.Text = ds.Tables[0].Rows[0]["perUnit"].ToString();
                        txtlu.Text = ds.Tables[0].Rows[0]["lu"].ToString();
                        txtbr.Text = ds.Tables[0].Rows[0]["br"].ToString();

                        // عرض الكمية المتبقية
                        long quantity = Convert.ToInt64(ds.Tables[0].Rows[0]["quantity"]);
                        long newQuantity = ds.Tables[0].Rows[0]["newQuantity"] != DBNull.Value ? Convert.ToInt64(ds.Tables[0].Rows[0]["newQuantity"]) : 0;
                        long totalAvailable = quantity + newQuantity;

                        // عرض الكمية المتبقية
                        lblRemainingQuantity.Text = $"{LanguageManager.GetText("Remaining Quantity")}: {totalAvailable} " + LanguageManager.GetText("Unit");

                        // تحميل الجرعات المتوفرة
                        LoadAvailableDosages(txtMedild.Text);
                    }
                    else
                    {
                        MessageBox.Show(LanguageManager.GetText("No data for this medicine"), LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Error selecting medicine") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadAvailableDosages(string medicineId)
        {
            try
            {
                cmbDosage.Items.Clear();

                // استعلام لجلب الجرعات المتوفرة
                query = @"SELECT dos2, dos2_qty, dos3, dos3_qty, dos4, dos4_qty, mnumber, mnumber_qty
                         FROM medic WHERE mid = @mid AND pharmacy_id = @pharmacyId";

                using (var con = fn.getConnection())
                {
                    var cmd = new System.Data.SqlClient.SqlCommand(query, con);
                    cmd.Parameters.AddWithValue("@mid", medicineId);
                    cmd.Parameters.AddWithValue("@pharmacyId", SessionManager.CurrentPharmacyId);
                    var da = new System.Data.SqlClient.SqlDataAdapter(cmd);
                    var dosageDs = new DataSet();
                    da.Fill(dosageDs);

                    if (dosageDs.Tables.Count > 0 && dosageDs.Tables[0].Rows.Count > 0)
                    {
                        var row = dosageDs.Tables[0].Rows[0];

                        // الجرعة الأولى (mnumber)
                        if (row["mnumber"] != DBNull.Value && row["mnumber_qty"] != DBNull.Value)
                        {
                            string dosage1 = row["mnumber"].ToString();
                            int qty1 = Convert.ToInt32(row["mnumber_qty"]);
                            if (qty1 > 0)
                            {
                                cmbDosage.Items.Add($"{LanguageManager.GetText("First Dosage")}: {dosage1} ({LanguageManager.GetText("Available")}: {qty1})");
                            }
                        }

                        // الجرعة الثانية
                        if (row["dos2"] != DBNull.Value && row["dos2_qty"] != DBNull.Value)
                        {
                            string dosage2 = row["dos2"].ToString();
                            int qty2 = Convert.ToInt32(row["dos2_qty"]);
                            if (qty2 > 0)
                            {
                                cmbDosage.Items.Add($"{LanguageManager.GetText("Second Dosage")}: {dosage2} ({LanguageManager.GetText("Available")}: {qty2})");
                            }
                        }

                        // الجرعة الثالثة
                        if (row["dos3"] != DBNull.Value && row["dos3_qty"] != DBNull.Value)
                        {
                            string dosage3 = row["dos3"].ToString();
                            int qty3 = Convert.ToInt32(row["dos3_qty"]);
                            if (qty3 > 0)
                            {
                                cmbDosage.Items.Add($"{LanguageManager.GetText("Third Dosage")}: {dosage3} ({LanguageManager.GetText("Available")}: {qty3})");
                            }
                        }

                        // الجرعة الرابعة
                        if (row["dos4"] != DBNull.Value && row["dos4_qty"] != DBNull.Value)
                        {
                            string dosage4 = row["dos4"].ToString();
                            int qty4 = Convert.ToInt32(row["dos4_qty"]);
                            if (qty4 > 0)
                            {
                                cmbDosage.Items.Add($"{LanguageManager.GetText("Fourth Dosage")}: {dosage4} ({LanguageManager.GetText("Available")}: {qty4})");
                            }
                        }
                    }

                    if (cmbDosage.Items.Count == 0)
                    {
                        cmbDosage.Items.Add(LanguageManager.GetText("No dosages available"));
                    }
                    else
                    {
                        cmbDosage.SelectedIndex = 0; // اختيار الجرعة الأولى افتراضياً
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Error loading dosages") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void cmbDosage_SelectedIndexChanged(object sender, EventArgs e)
        {
            // يمكن إضافة منطق إضافي هنا عند تغيير الجرعة المختارة
        }

        private string UpdateDosageQuantity(string selectedDosage, int requestedUnits)
        {
            try
            {
                // إذا لم تكن هناك جرعة محددة، استخدم النظام القديم
                if (selectedDosage == "غير محدد")
                {
                    return GetOldSystemUpdateQuery(requestedUnits);
                }

                // تحديد نوع الجرعة من النص المختار
                string quantityColumn = "";

                if (selectedDosage.Contains("الجرعة الأولى"))
                {
                    quantityColumn = "mnumber_qty";
                }
                else if (selectedDosage.Contains("الجرعة الثانية"))
                {
                    quantityColumn = "dos2_qty";
                }
                else if (selectedDosage.Contains("الجرعة الثالثة"))
                {
                    quantityColumn = "dos3_qty";
                }
                else if (selectedDosage.Contains("الجرعة الرابعة"))
                {
                    quantityColumn = "dos4_qty";
                }
                else
                {
                    // إذا لم نتمكن من تحديد الجرعة، استخدم النظام القديم
                    return GetOldSystemUpdateQuery(requestedUnits);
                }

                // جلب الكمية الحالية للجرعة المحددة
                query = $"SELECT {quantityColumn} FROM medic WHERE mid = '{txtMedild.Text}'";
                ds = fn.getData(query);

                if (ds.Tables[0].Rows.Count == 0)
                {
                    return "";
                }

                int currentQuantity = ds.Tables[0].Rows[0][0] != DBNull.Value ?
                                    Convert.ToInt32(ds.Tables[0].Rows[0][0]) : 0;

                // التحقق من توفر الكمية المطلوبة
                if (currentQuantity < requestedUnits)
                {
                    return "";
                }

                // إنشاء استعلام التحديث
                int newQuantity = currentQuantity - requestedUnits;
                return $"UPDATE medic SET {quantityColumn} = {newQuantity} WHERE mid = '{txtMedild.Text}'";
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Error updating dosage quantity") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
                return "";
            }
        }

        private string GetOldSystemUpdateQuery(int requestedUnits)
        {
            try
            {
                // النظام القديم - التحقق من الكمية الكلية
                query = "SELECT quantity, eDate, newQuantity, newEDate FROM medic WHERE mid = '" + txtMedild.Text + "'";
                ds = fn.getData(query);

                if (ds.Tables[0].Rows.Count == 0)
                {
                    return "";
                }

                Int64 oldQuantity = Convert.ToInt64(ds.Tables[0].Rows[0][0]);
                DateTime oldEDate = Convert.ToDateTime(ds.Tables[0].Rows[0][1]);
                Int64 newQuantity = ds.Tables[0].Rows[0][2] != DBNull.Value ? Convert.ToInt64(ds.Tables[0].Rows[0][2]) : 0;
                DateTime newEDate = ds.Tables[0].Rows[0][3] != DBNull.Value ? Convert.ToDateTime(ds.Tables[0].Rows[0][3]) : DateTime.MinValue;

                // تحديد أي كمية نستخدم
                if (oldEDate >= DateTime.Now && oldQuantity >= requestedUnits)
                {
                    return "UPDATE medic SET quantity = " + (oldQuantity - requestedUnits) + " WHERE mid = '" + txtMedild.Text + "'";
                }
                else if (newEDate >= DateTime.Now && newQuantity >= requestedUnits)
                {
                    return "UPDATE medic SET newQuantity = " + (newQuantity - requestedUnits) + " WHERE mid = '" + txtMedild.Text + "'";
                }
                else
                {
                    return "";
                }
            }
            catch
            {
                return "";
            }
        }

        private string GetTotalQuantityUpdateQuery(int requestedUnits)
        {
            try
            {
                // التحقق من الكمية الإجمالية المتاحة (بدون تحديد جرعة)
                query = "SELECT quantity, eDate, newQuantity, newEDate FROM medic WHERE mid = '" + txtMedild.Text + "'";
                ds = fn.getData(query);

                if (ds.Tables[0].Rows.Count == 0)
                {
                    return "";
                }

                Int64 oldQuantity = Convert.ToInt64(ds.Tables[0].Rows[0][0]);
                DateTime oldEDate = Convert.ToDateTime(ds.Tables[0].Rows[0][1]);
                Int64 newQuantity = ds.Tables[0].Rows[0][2] != DBNull.Value ? Convert.ToInt64(ds.Tables[0].Rows[0][2]) : 0;
                DateTime newEDate = ds.Tables[0].Rows[0][3] != DBNull.Value ? Convert.ToDateTime(ds.Tables[0].Rows[0][3]) : DateTime.MinValue;

                // حساب الكمية الإجمالية المتاحة
                Int64 totalAvailable = 0;
                if (oldEDate >= DateTime.Now) totalAvailable += oldQuantity;
                if (newEDate >= DateTime.Now) totalAvailable += newQuantity;

                if (totalAvailable >= requestedUnits)
                {
                    // استخدام الكمية القديمة أولاً
                    if (oldEDate >= DateTime.Now && oldQuantity >= requestedUnits)
                    {
                        return "UPDATE medic SET quantity = " + (oldQuantity - requestedUnits) + " WHERE mid = '" + txtMedild.Text + "'";
                    }
                    else if (oldEDate >= DateTime.Now && oldQuantity > 0)
                    {
                        // استخدام الكمية القديمة جزئياً والباقي من الجديدة
                        int remainingUnits = requestedUnits - (int)oldQuantity;
                        return "UPDATE medic SET quantity = 0, newQuantity = " + (newQuantity - remainingUnits) + " WHERE mid = '" + txtMedild.Text + "'";
                    }
                    else if (newEDate >= DateTime.Now && newQuantity >= requestedUnits)
                    {
                        return "UPDATE medic SET newQuantity = " + (newQuantity - requestedUnits) + " WHERE mid = '" + txtMedild.Text + "'";
                    }
                }

                return "";
            }
            catch
            {
                return "";
            }
        }

        private void txtNoOfUnits_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(txtNoOfUnits.Text) && !string.IsNullOrWhiteSpace(txtPricePerUnit.Text))
                {
                    if (Int64.TryParse(txtPricePerUnit.Text, out Int64 unitPrice) &&
                        Int64.TryParse(txtNoOfUnits.Text, out Int64 noofUnit))
                    {
                        Int64 totalAmount = unitPrice * noofUnit;
                        txtTotalPrice.Text = totalAmount.ToString();
                    }
                    else
                    {
                        txtTotalPrice.Clear();
                    }
                }
                else
                {
                    txtTotalPrice.Clear();
                }
            }
            catch (Exception)
            {
                txtTotalPrice.Clear();
            }
        }

        protected int n, totalAmount = 0;
        protected Int64 quantity, newQuantity;


        private void btnAddToCart_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtNoOfUnits.Text))
            {
                MessageBox.Show(LanguageManager.GetText("Please enter number of units."), LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // تبسيط الكود - إزالة نظام الجرعات المعقد
            int requestedUnits;

            if (!int.TryParse(txtNoOfUnits.Text, out requestedUnits))
            {
                MessageBox.Show(LanguageManager.GetText("Please enter a valid number for quantity."), LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // التحقق من صحة الكمية المطلوبة
            if (requestedUnits <= 0)
            {
                MessageBox.Show(LanguageManager.GetText("Please enter valid quantity greater than zero"), LanguageManager.GetText("Input error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (string.IsNullOrWhiteSpace(txtMedild.Text))
            {
                MessageBox.Show("يرجى اختيار الدواء أولاً.", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // التحقق من اختيار الجرعة (اختياري - يمكن بيع الدواء بدون جرعة محددة)
            string selectedDosage = "غير محدد";
            if (cmbDosage.SelectedIndex != -1 && !cmbDosage.Text.Contains("لا توجد جرعات"))
            {
                selectedDosage = cmbDosage.Text;
            }

            try
            {
                // تحديث الكمية بناءً على الجرعة المختارة
                string updateQuery = UpdateDosageQuantity(selectedDosage, requestedUnits);

                // إذا لم تكن هناك جرعة محددة أو كانت الكمية غير كافية، تحقق من الكمية الإجمالية
                if (string.IsNullOrEmpty(updateQuery))
                {
                    // محاولة استخدام الكمية الإجمالية للدواء
                    updateQuery = GetTotalQuantityUpdateQuery(requestedUnits);

                    if (string.IsNullOrEmpty(updateQuery))
                    {
                        MessageBox.Show("لا توجد كمية كافية من هذا الدواء.", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                }

                // تنفيذ التحديث
                fn.setData(updateQuery, "");

                // إضافة العنصر إلى السلة مع الجرعة المختارة (أو "غير محدد" إذا لم تكن متوفرة)
                guna2DataGridView1.Rows.Add(
                    txtMedild.Text,
                    txtMediName.Text,
                    txtExpireDate.Value.ToShortDateString(),
                    txtPricePerUnit.Text,
                    txtNoOfUnits.Text,
                    txtTotalPrice.Text,
                    selectedDosage
                );

                totalAmount += int.Parse(txtTotalPrice.Text);
                totallabel.Text = "dr. " + totalAmount.ToString();

                clearAll();
                UC__P_SellMedicine_Load(this, null);
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Error adding item to cart") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        int valueAmount;
        String valueld;
        protected Int64 noOfunit;

        private void guna2DataGridView1_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                valueAmount = int.Parse(guna2DataGridView1.Rows[e.RowIndex].Cells[5].Value.ToString());
                valueld = guna2DataGridView1.Rows[e.RowIndex].Cells[0].Value.ToString();
                noOfunit = Int64.Parse(guna2DataGridView1.Rows[e.RowIndex].Cells[4].Value.ToString());
            }
            catch (Exception)
            {
            }
        }

        private void btnRemove_Click(object sender, EventArgs e)
        {
            if (guna2DataGridView1.SelectedRows.Count > 0)
            {
                try
                {
                    int rowIndex = guna2DataGridView1.SelectedRows[0].Index;

                    if (guna2DataGridView1.Rows[rowIndex].Cells[5].Value != null &&
                        guna2DataGridView1.Rows[rowIndex].Cells[0].Value != null &&
                        guna2DataGridView1.Rows[rowIndex].Cells[4].Value != null)
                    {
                        valueAmount = int.Parse(guna2DataGridView1.Rows[rowIndex].Cells[5].Value.ToString());
                        valueld = guna2DataGridView1.Rows[rowIndex].Cells[0].Value.ToString();
                        noOfunit = Int64.Parse(guna2DataGridView1.Rows[rowIndex].Cells[4].Value.ToString());

                        guna2DataGridView1.Rows.RemoveAt(rowIndex);

                        using (var con = fn.getConnection())
                        {
                            query = "SELECT quantity FROM medic WHERE mid = @mid";
                            var cmd = new System.Data.SqlClient.SqlCommand(query, con);
                            cmd.Parameters.AddWithValue("@mid", valueld);
                            var da = new System.Data.SqlClient.SqlDataAdapter(cmd);
                            ds = new DataSet();
                            da.Fill(ds);
                        }

                        if (ds.Tables[0].Rows.Count > 0)
                        {
                            quantity = Int64.Parse(ds.Tables[0].Rows[0][0].ToString());
                            newQuantity = quantity + noOfunit;

                            fn.setData("UPDATE medic SET quantity = " + newQuantity + " WHERE mid ='" + valueld + "'", LanguageManager.GetText("Medicine removed from cart"));
                            totalAmount -= valueAmount;
                            totallabel.Text = "dr. " + totalAmount.ToString();
                        }
                    }
                    else
                    {
                        MessageBox.Show("The selected item has missing data.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show("Error while removing item: " + ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                finally
                {
                    UC__P_SellMedicine_Load(this, null);
                }
            }
            else
            {
                MessageBox.Show("Please select an item to remove.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void txtMediName_TextChanged(object sender, EventArgs e)
        {
        }

        private void btnSellOnly_Click(object sender, EventArgs e)
        {
            if (guna2DataGridView1.Rows.Count == 0)
            {
                MessageBox.Show(LanguageManager.GetText("Please select a medicine to sell"), LanguageManager.GetText("Warning"), MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            ProcessSale(false, true); // بيع بدون طباعة
        }

        private void btnPurchasePrint_Click(object sender, EventArgs e)
        {
            if (guna2DataGridView1.Rows.Count == 0)
            {
                MessageBox.Show(LanguageManager.GetText("Please select a medicine to sell"), LanguageManager.GetText("Warning"), MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            ProcessSale(true, true); // بيع مع طباعة
        }

        private void ProcessSale(bool printInvoice, bool saveToDatabase)
        {
            try
            {
                // حفظ المبيعات في قاعدة البيانات
                saveSalesToDatabase();

                // طباعة الفاتورة إذا كان مطلوباً
                if (printInvoice)
                {
                    PrintInvoiceOptimized();
                }

                // تنظيف الشاشة
                totalAmount = 0;
                totallabel.Text = "dr. 00";
                guna2DataGridView1.Rows.Clear();

                // رسالة النجاح
                string message = printInvoice ?
                    LanguageManager.GetText("Sales saved and invoice printed successfully") :
                    LanguageManager.GetText("Sales saved successfully");

                MessageBox.Show(message, LanguageManager.GetText("Success"), MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Error saving sales") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PrintInvoiceOptimized()
        {
            try
            {
                // التحقق من وجود بيانات للطباعة
                if (guna2DataGridView1.Rows.Count == 0)
                {
                    MessageBox.Show("لا توجد عناصر للطباعة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // إنشاء جدول مؤقت للطباعة مع جميع الأعمدة في صفحة واحدة
                DataTable printTable = CreateOptimizedPrintTable();

                // التحقق من أن الجدول المؤقت يحتوي على بيانات
                if (printTable.Rows.Count == 0)
                {
                    MessageBox.Show($"لا توجد بيانات صالحة للطباعة\nعدد صفوف الجدول الأصلي: {guna2DataGridView1.Rows.Count}", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // رسالة تشخيص
                System.Diagnostics.Debug.WriteLine($"عدد الصفوف في الجدول المؤقت: {printTable.Rows.Count}");

                // إنشاء DataGridView مؤقت للطباعة
                DataGridView tempGrid = new DataGridView();
                tempGrid.DataSource = printTable;

                // تحسين إعدادات الجدول المؤقت
                OptimizeTempGridForPrinting(tempGrid);

                // إعداد الطباعة
                DGVPrinter print = new DGVPrinter();

                // تطبيق إعدادات الطباعة المحفوظة مع التحقق
                AdministratorUC.PrintHelper.ApplyPrintSettingsWithValidation(print, "مبيعات الأدوية");

                // تخصيص العنوان الفرعي والتذييل
                print.SubTitle = String.Format(LanguageManager.GetText("Date") + ": {0} - " + LanguageManager.GetText("Employee") + ": {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm"), CurrentEmployeeName);
                print.Footer = LanguageManager.GetText("Total amount due") + ": " + totallabel.Text;

                // طباعة الجدول المحسن مع معاينة
                print.PrintPreviewDataGridView(tempGrid);

                // تنظيف الموارد
                tempGrid.Dispose();
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Print error") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private DataTable CreateOptimizedPrintTable()
        {
            DataTable table = new DataTable();

            // إضافة الأعمدة بأسماء مختصرة
            table.Columns.Add("رقم الدواء", typeof(string));
            table.Columns.Add("اسم الدواء", typeof(string));
            table.Columns.Add("تاريخ الانتهاء", typeof(string));
            table.Columns.Add("سعر الوحدة", typeof(string));
            table.Columns.Add("الكمية", typeof(string));
            table.Columns.Add("السعر الإجمالي", typeof(string));
            table.Columns.Add("الجرعة", typeof(string));

            // نسخ البيانات من الجدول الأصلي
            foreach (DataGridViewRow row in guna2DataGridView1.Rows)
            {
                if (row.Cells[0].Value != null && !row.IsNewRow)
                {
                    DataRow newRow = table.NewRow();

                    // التحقق من عدد الأعمدة المتاحة
                    int columnCount = Math.Min(row.Cells.Count, 7);

                    for (int i = 0; i < columnCount; i++)
                    {
                        newRow[i] = row.Cells[i].Value?.ToString() ?? "";
                    }

                    // إضافة الجرعة إذا لم تكن موجودة
                    if (columnCount < 7)
                    {
                        newRow[6] = "غير محدد";
                    }

                    table.Rows.Add(newRow);
                }
            }

            return table;
        }

        private void OptimizeTempGridForPrinting(DataGridView tempGrid)
        {
            // تحسين إعدادات الجدول المؤقت
            tempGrid.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
            tempGrid.AllowUserToAddRows = false;
            tempGrid.RowHeadersVisible = false;

            // تعيين عرض الأعمدة بشكل متناسب
            if (tempGrid.Columns.Count >= 7)
            {
                tempGrid.Columns[0].Width = 70;  // رقم الدواء
                tempGrid.Columns[1].Width = 110; // اسم الدواء
                tempGrid.Columns[2].Width = 85;  // تاريخ الانتهاء
                tempGrid.Columns[3].Width = 65;  // سعر الوحدة
                tempGrid.Columns[4].Width = 50;  // الكمية
                tempGrid.Columns[5].Width = 75;  // السعر الإجمالي
                tempGrid.Columns[6].Width = 85;  // الجرعة
            }

            // تحسين مظهر الجدول
            tempGrid.DefaultCellStyle.Font = new Font("Arial", 9);
            tempGrid.ColumnHeadersDefaultCellStyle.Font = new Font("Arial", 10, FontStyle.Bold);
            tempGrid.ColumnHeadersDefaultCellStyle.BackColor = Color.LightGray;
        }

        private void saveSalesToDatabase()
        {
            try
            {
                // إنشاء الجداول إذا لم تكن موجودة
                createTablesIfNotExists();

                // حفظ كل عنصر في السلة كعملية بيع منفصلة
                foreach (DataGridViewRow row in guna2DataGridView1.Rows)
                {
                    if (row.Cells[0].Value != null)
                    {
                        string mid = row.Cells[0].Value.ToString();
                        string medicineName = row.Cells[1].Value.ToString();
                        string dosage = row.Cells[6].Value?.ToString() ?? "غير محدد"; // الجرعة من العمود السابع
                        int quantity = int.Parse(row.Cells[4].Value.ToString());
                        long pricePerUnit = long.Parse(row.Cells[3].Value.ToString());
                        long totalPrice = long.Parse(row.Cells[5].Value.ToString());

                        // استخدام نفس أنواع البيانات الموجودة في قاعدة البيانات
                        using (var con = fn.getConnection())
                        {
                            query = @"INSERT INTO sales (mid, medicineName, dosage, quantity, pricePerUnit, totalPrice, employeeUsername, employeeName, saleDate, pharmacy_id)
                                     VALUES (@mid, @mname, @dosage, @qty, @price, @total, @user, @empName, GETDATE(), @pharmacy_id)";
                            var cmd = new System.Data.SqlClient.SqlCommand(query, con);
                            cmd.Parameters.AddWithValue("@mid", mid);
                            cmd.Parameters.AddWithValue("@mname", medicineName);
                            cmd.Parameters.AddWithValue("@dosage", dosage);
                            cmd.Parameters.AddWithValue("@qty", quantity);
                            cmd.Parameters.AddWithValue("@price", pricePerUnit);
                            cmd.Parameters.AddWithValue("@total", totalPrice);
                            cmd.Parameters.AddWithValue("@user", CurrentUsername ?? "غير محدد");
                            cmd.Parameters.AddWithValue("@empName", CurrentEmployeeName ?? "غير محدد");
                            cmd.Parameters.AddWithValue("@pharmacy_id", SessionManager.CurrentPharmacyId);
                            con.Open();
                            cmd.ExecuteNonQuery();
                            con.Close();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في حفظ المبيعات: " + ex.Message);
            }
        }

        private void createTablesIfNotExists()
        {
            try
            {
                // إنشاء جدول المبيعات إذا لم يكن موجوداً
                query = @"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='sales' AND xtype='U')
                         CREATE TABLE sales (
                             id INT IDENTITY(1,1) PRIMARY KEY,
                             mid VARCHAR(250),
                             medicineName VARCHAR(250),
                             dosage VARCHAR(100),
                             quantity INT,
                             pricePerUnit BIGINT,
                             totalPrice BIGINT,
                             employeeUsername VARCHAR(250),
                             employeeName VARCHAR(250),
                             saleDate DATETIME DEFAULT GETDATE(),
                             pharmacy_id INT
                         )";
                fn.setData(query, "");

                // إنشاء جدول تسجيل دخول/خروج الموظفين
                query = @"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='employee_sessions' AND xtype='U')
                         CREATE TABLE employee_sessions (
                             id INT IDENTITY(1,1) PRIMARY KEY,
                             username VARCHAR(250),
                             employeeName VARCHAR(250),
                             loginTime DATETIME,
                             logoutTime DATETIME NULL,
                             sessionDate DATE
                         )";
                fn.setData(query, "");
            }
            catch (Exception)
            {
                // تجاهل الأخطاء في إنشاء الجداول إذا كانت موجودة بالفعل
            }
        }

        private void clearAll()
        {
            txtMediName.Clear();
            txtMedild.Clear();
            txtExpireDate.Value = DateTime.Now;
            txtPricePerUnit.Clear();
            txtNoOfUnits.Text = "1";
            txtTotalPrice.Clear();
            txtlu.Clear();
            txtbr.Clear();
            lblRemainingQuantity.Text = "";
        }

        private void OnLanguageChanged(object sender, EventArgs e)
        {
            ApplyLanguage();
        }

        public void ApplyLanguage()
        {
            // تطبيق الترجمات على الأزرار
            btnSync.Text = LanguageManager.GetText("Sync");
            btnAddToCart.Text = LanguageManager.GetText("Add to Cart");
            btnRemove.Text = LanguageManager.GetText("Remove");
            btnPurchasePrint.Text = "🧾 " + LanguageManager.GetText("Purchase & Print");
            btnSellOnly.Text = "💰 " + LanguageManager.GetText("Sell Only");

            // تطبيق الترجمات على التسميات مع الأيقونات
            if (label1 != null) label1.Text = "🏥 " + LanguageManager.GetText("Sell Medicine");
            if (label2 != null) label2.Text = "🔍 " + LanguageManager.GetText("Search Medicine");
            if (label3 != null) label3.Text = "🆔 " + LanguageManager.GetText("Medicine ID");
            if (label4 != null) label4.Text = "💊 " + LanguageManager.GetText("Medicine Name");
            if (label5 != null) label5.Text = "💰 " + LanguageManager.GetText("Price Per Unit");
            if (label6 != null) label6.Text = "📦 " + LanguageManager.GetText("Number of Units");
            if (label7 != null) label7.Text = LanguageManager.GetText("Expire Date");
            if (label8 != null) label8.Text = LanguageManager.GetText("Total Price");
            if (label9 != null) label9.Text = LanguageManager.GetText("Remaining Quantity");
            if (label10 != null) label10.Text = LanguageManager.GetText("Pharmaceutical Company");

            // تطبيق الترجمات على أعمدة الجدول
            if (guna2DataGridView1.Columns.Count > 0)
            {
                if (guna2DataGridView1.Columns.Count > 0) guna2DataGridView1.Columns[0].HeaderText = LanguageManager.GetText("Medicine ID");
                if (guna2DataGridView1.Columns.Count > 1) guna2DataGridView1.Columns[1].HeaderText = LanguageManager.GetText("Medicine Name");
                if (guna2DataGridView1.Columns.Count > 2) guna2DataGridView1.Columns[2].HeaderText = LanguageManager.GetText("Expire Date");
                if (guna2DataGridView1.Columns.Count > 3) guna2DataGridView1.Columns[3].HeaderText = LanguageManager.GetText("Price Per Unit");
                if (guna2DataGridView1.Columns.Count > 4) guna2DataGridView1.Columns[4].HeaderText = LanguageManager.GetText("Number of Units");
                if (guna2DataGridView1.Columns.Count > 5) guna2DataGridView1.Columns[5].HeaderText = LanguageManager.GetText("Total Price");
                if (guna2DataGridView1.Columns.Count > 6) guna2DataGridView1.Columns[6].HeaderText = LanguageManager.GetText("Dosage");
            }

            // الحفاظ على التصميم من اليسار لليمين في جميع اللغات
            this.RightToLeft = RightToLeft.No;
        }

        private void ApplyModernDesign()
        {
            // تطبيق الوضع الحالي
            this.BackColor = ModernTheme.Colors.Background;

            // تحسين العنوان الرئيسي
            if (label1 != null)
            {
                label1.Font = new Font("Segoe UI", 18F, FontStyle.Bold);
                label1.ForeColor = ModernTheme.Colors.Primary;
            }

            // تحسين الأزرار
            ApplyModernButtonStyle(btnSync, false);
            ApplyModernButtonStyle(btnAddToCart, true);
            ApplyModernButtonStyle(btnRemove, false);
            ApplyModernButtonStyle(btnPurchasePrint, true);

            // تحسين جدول البيانات
            if (guna2DataGridView1 != null)
            {
                guna2DataGridView1.BackgroundColor = ModernTheme.Colors.Surface;
                guna2DataGridView1.BorderStyle = BorderStyle.None;
                guna2DataGridView1.DefaultCellStyle.BackColor = ModernTheme.Colors.Surface;
                guna2DataGridView1.DefaultCellStyle.ForeColor = ModernTheme.Colors.TextPrimary;
                guna2DataGridView1.DefaultCellStyle.Font = new Font("Segoe UI", 11F);
                guna2DataGridView1.ColumnHeadersDefaultCellStyle.BackColor = ModernTheme.Colors.SurfaceVariant;
                guna2DataGridView1.ColumnHeadersDefaultCellStyle.ForeColor = ModernTheme.Colors.TextPrimary;
                guna2DataGridView1.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            }

            // تحسين قائمة الأدوية
            if (listBoxMedicines != null)
            {
                listBoxMedicines.BackColor = ModernTheme.Colors.Surface;
                listBoxMedicines.ForeColor = ModernTheme.Colors.TextPrimary;
                listBoxMedicines.Font = new Font("Segoe UI", 11F);
            }

            // تحسين تسمية الإجمالي
            if (totallabel != null)
            {
                totallabel.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
                totallabel.ForeColor = ModernTheme.Colors.Success;
            }

            // تحسين تسمية الكمية المتبقية
            if (lblRemainingQuantity != null)
            {
                lblRemainingQuantity.Font = new Font("Segoe UI", 11F);
                lblRemainingQuantity.ForeColor = ModernTheme.Colors.Info;
            }

            // إضافة زر الوضع الليلي
            CreateDarkModeButton();
        }

        private void CreateDarkModeButton()
        {
            // إنشاء زر الوضع الليلي
            if (this.Controls.Find("btnDarkMode", false).Length == 0)
            {
                Guna.UI2.WinForms.Guna2Button btnDarkMode = new Guna.UI2.WinForms.Guna2Button();
                btnDarkMode.Name = "btnDarkMode";
                btnDarkMode.Text = ModernTheme.IsDarkMode ? "🌞" : "🌙";
                btnDarkMode.Size = new Size(40, 30);
                btnDarkMode.Location = new Point(this.Width - 50, 10);
                btnDarkMode.Anchor = AnchorStyles.Top | AnchorStyles.Right;
                btnDarkMode.BorderRadius = 6;
                btnDarkMode.Font = new Font("Segoe UI", 12F);
                btnDarkMode.Cursor = Cursors.Hand;
                btnDarkMode.FillColor = ModernTheme.Colors.Secondary;
                btnDarkMode.ForeColor = ModernTheme.Colors.TextOnPrimary;
                btnDarkMode.HoverState.FillColor = ModernTheme.Colors.SecondaryDark;
                btnDarkMode.Click += (s, e) => ModernTheme.ToggleDarkMode();

                this.Controls.Add(btnDarkMode);
                btnDarkMode.BringToFront();
            }
        }

        private void OnThemeChanged(object sender, EventArgs e)
        {
            ApplyModernDesign();
            ApplyLanguage();
        }

        private void ApplyModernButtonStyle(Guna.UI2.WinForms.Guna2Button button, bool isPrimary)
        {
            if (button != null)
            {
                button.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                button.Cursor = Cursors.Hand;
                button.Size = new Size(140, 40);
                button.BorderRadius = 8;

                if (isPrimary)
                {
                    button.FillColor = ModernTheme.Colors.Primary;
                    button.ForeColor = ModernTheme.Colors.TextOnPrimary;
                    button.BorderThickness = 0;
                    button.HoverState.FillColor = ModernTheme.Colors.PrimaryDark;
                }
                else
                {
                    button.FillColor = ModernTheme.Colors.Surface;
                    button.ForeColor = ModernTheme.Colors.Primary;
                    button.BorderColor = ModernTheme.Colors.Primary;
                    button.BorderThickness = 1;
                    button.HoverState.FillColor = ModernTheme.Colors.PrimaryLight;
                }
            }
        }

        protected override void OnHandleDestroyed(EventArgs e)
        {
            // إلغاء الاشتراك في حدث تغيير اللغة
            LanguageManager.LanguageChanged -= OnLanguageChanged;
            base.OnHandleDestroyed(e);
        }
    }
}