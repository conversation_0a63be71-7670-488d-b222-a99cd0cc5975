using System;
using System.Collections.Generic;
using System.Data;
using System.IO;

namespace Pharmacy_Admin_System
{
    /// <summary>
    /// مدير التقارير - إنشاء وإدارة التقارير الإحصائية للنظام
    /// Report Manager - Creates and manages statistical reports for the system
    /// </summary>
    public class ReportManager
    {
        private DatabaseManager dbManager;

        public ReportManager()
        {
            dbManager = new DatabaseManager();
        }

        #region Financial Reports

        /// <summary>
        /// تقرير الإيرادات الشهرية
        /// </summary>
        public DataTable GetMonthlyRevenueReport(int year, int month)
        {
            try
            {
                string query = @"
                    SELECT 
                        rp.pharmacyName,
                        rp.pharmacyCode,
                        rp.city,
                        sp.amount,
                        sp.paymentDate,
                        sp.paymentMethod,
                        spl.planNameAr as subscriptionPlan
                    FROM subscription_payments sp
                    INNER JOIN registered_pharmacies rp ON sp.pharmacyId = rp.id
                    INNER JOIN subscription_plans spl ON sp.planId = spl.id
                    WHERE YEAR(sp.paymentDate) = @year 
                      AND MONTH(sp.paymentDate) = @month
                      AND sp.status = 'Completed'
                    ORDER BY sp.paymentDate DESC";

                var parameters = new Dictionary<string, object>
                {
                    {"@year", year},
                    {"@month", month}
                };

                return dbManager.ExecuteAdminQuery(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء تقرير الإيرادات الشهرية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تقرير الإيرادات السنوية
        /// </summary>
        public DataTable GetYearlyRevenueReport(int year)
        {
            try
            {
                string query = @"
                    SELECT 
                        MONTH(sp.paymentDate) as Month,
                        DATENAME(month, sp.paymentDate) as MonthName,
                        COUNT(*) as PaymentCount,
                        SUM(sp.amount) as TotalRevenue,
                        AVG(sp.amount) as AveragePayment
                    FROM subscription_payments sp
                    WHERE YEAR(sp.paymentDate) = @year
                      AND sp.status = 'Completed'
                    GROUP BY MONTH(sp.paymentDate), DATENAME(month, sp.paymentDate)
                    ORDER BY MONTH(sp.paymentDate)";

                var parameters = new Dictionary<string, object>
                {
                    {"@year", year}
                };

                return dbManager.ExecuteAdminQuery(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء تقرير الإيرادات السنوية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تقرير الإيرادات حسب خطة الاشتراك
        /// </summary>
        public DataTable GetRevenueBySubscriptionPlan(DateTime fromDate, DateTime toDate)
        {
            try
            {
                string query = @"
                    SELECT 
                        spl.planNameAr as SubscriptionPlan,
                        COUNT(*) as PaymentCount,
                        SUM(sp.amount) as TotalRevenue,
                        AVG(sp.amount) as AveragePayment,
                        MIN(sp.amount) as MinPayment,
                        MAX(sp.amount) as MaxPayment
                    FROM subscription_payments sp
                    INNER JOIN subscription_plans spl ON sp.planId = spl.id
                    WHERE sp.paymentDate BETWEEN @fromDate AND @toDate
                      AND sp.status = 'Completed'
                    GROUP BY spl.planNameAr, spl.monthlyPrice
                    ORDER BY TotalRevenue DESC";

                var parameters = new Dictionary<string, object>
                {
                    {"@fromDate", fromDate},
                    {"@toDate", toDate}
                };

                return dbManager.ExecuteAdminQuery(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء تقرير الإيرادات حسب خطة الاشتراك: {ex.Message}", ex);
            }
        }

        #endregion

        #region Pharmacy Reports

        /// <summary>
        /// تقرير الصيدليات حسب المدينة
        /// </summary>
        public DataTable GetPharmaciesByCity()
        {
            try
            {
                string query = @"
                    SELECT 
                        city,
                        COUNT(*) as TotalPharmacies,
                        SUM(CASE WHEN status = 'Approved' AND isActive = 1 THEN 1 ELSE 0 END) as ActivePharmacies,
                        SUM(CASE WHEN status = 'Pending' THEN 1 ELSE 0 END) as PendingPharmacies,
                        SUM(CASE WHEN status = 'Suspended' THEN 1 ELSE 0 END) as SuspendedPharmacies
                    FROM registered_pharmacies
                    GROUP BY city
                    ORDER BY TotalPharmacies DESC";

                return dbManager.ExecuteAdminQuery(query);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء تقرير الصيدليات حسب المدينة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تقرير الصيدليات حسب نوع الاشتراك
        /// </summary>
        public DataTable GetPharmaciesBySubscriptionType()
        {
            try
            {
                string query = @"
                    SELECT 
                        rp.subscriptionType,
                        spl.planNameAr,
                        COUNT(*) as PharmacyCount,
                        SUM(CASE WHEN rp.status = 'Approved' AND rp.isActive = 1 THEN 1 ELSE 0 END) as ActiveCount,
                        AVG(spl.monthlyPrice) as AveragePrice,
                        SUM(rp.monthlyFee) as TotalMonthlyRevenue
                    FROM registered_pharmacies rp
                    LEFT JOIN subscription_plans spl ON rp.subscriptionType = spl.planName
                    GROUP BY rp.subscriptionType, spl.planNameAr
                    ORDER BY PharmacyCount DESC";

                return dbManager.ExecuteAdminQuery(query);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء تقرير الصيدليات حسب نوع الاشتراك: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تقرير نمو التسجيلات الشهرية
        /// </summary>
        public DataTable GetMonthlyRegistrationGrowth(int year)
        {
            try
            {
                string query = @"
                    SELECT 
                        MONTH(registrationDate) as Month,
                        DATENAME(month, registrationDate) as MonthName,
                        COUNT(*) as NewRegistrations,
                        SUM(COUNT(*)) OVER (ORDER BY MONTH(registrationDate)) as CumulativeRegistrations
                    FROM registered_pharmacies
                    WHERE YEAR(registrationDate) = @year
                    GROUP BY MONTH(registrationDate), DATENAME(month, registrationDate)
                    ORDER BY MONTH(registrationDate)";

                var parameters = new Dictionary<string, object>
                {
                    {"@year", year}
                };

                return dbManager.ExecuteAdminQuery(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء تقرير نمو التسجيلات: {ex.Message}", ex);
            }
        }

        #endregion

        #region Activity Reports

        /// <summary>
        /// تقرير نشاطات المديرين
        /// </summary>
        public DataTable GetAdminActivityReport(DateTime fromDate, DateTime toDate)
        {
            try
            {
                string query = @"
                    SELECT 
                        au.fullName as AdminName,
                        al.activityType,
                        COUNT(*) as ActivityCount,
                        MIN(al.activityDate) as FirstActivity,
                        MAX(al.activityDate) as LastActivity
                    FROM activity_log al
                    INNER JOIN admin_users au ON al.adminUserId = au.id
                    WHERE al.activityDate BETWEEN @fromDate AND @toDate
                    GROUP BY au.fullName, al.activityType
                    ORDER BY au.fullName, ActivityCount DESC";

                var parameters = new Dictionary<string, object>
                {
                    {"@fromDate", fromDate},
                    {"@toDate", toDate}
                };

                return dbManager.ExecuteAdminQuery(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء تقرير نشاطات المديرين: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تقرير النشاطات اليومية
        /// </summary>
        public DataTable GetDailyActivityReport(DateTime date)
        {
            try
            {
                string query = @"
                    SELECT 
                        al.activityType,
                        al.description,
                        al.activityDate,
                        au.fullName as AdminName,
                        rp.pharmacyName
                    FROM activity_log al
                    LEFT JOIN admin_users au ON al.adminUserId = au.id
                    LEFT JOIN registered_pharmacies rp ON al.pharmacyId = rp.id
                    WHERE CAST(al.activityDate AS DATE) = @date
                    ORDER BY al.activityDate DESC";

                var parameters = new Dictionary<string, object>
                {
                    {"@date", date.Date}
                };

                return dbManager.ExecuteAdminQuery(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء تقرير النشاطات اليومية: {ex.Message}", ex);
            }
        }

        #endregion

        #region Subscription Reports

        /// <summary>
        /// تقرير الاشتراكات المنتهية والمنتهية قريباً
        /// </summary>
        public DataTable GetExpiringSubscriptionsReport(int daysAhead = 30)
        {
            try
            {
                string query = @"
                    SELECT 
                        rp.pharmacyName,
                        rp.pharmacyCode,
                        rp.ownerName,
                        rp.ownerPhone,
                        rp.city,
                        rp.subscriptionType,
                        rp.subscriptionEndDate,
                        DATEDIFF(day, GETDATE(), rp.subscriptionEndDate) as DaysRemaining,
                        CASE 
                            WHEN rp.subscriptionEndDate < GETDATE() THEN 'منتهي'
                            WHEN rp.subscriptionEndDate < DATEADD(day, 7, GETDATE()) THEN 'ينتهي خلال أسبوع'
                            WHEN rp.subscriptionEndDate < DATEADD(day, 30, GETDATE()) THEN 'ينتهي خلال شهر'
                            ELSE 'نشط'
                        END as Status
                    FROM registered_pharmacies rp
                    WHERE rp.subscriptionEndDate <= DATEADD(day, @daysAhead, GETDATE())
                      AND rp.status = 'Approved'
                    ORDER BY rp.subscriptionEndDate ASC";

                var parameters = new Dictionary<string, object>
                {
                    {"@daysAhead", daysAhead}
                };

                return dbManager.ExecuteAdminQuery(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء تقرير الاشتراكات المنتهية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تقرير معدل التجديد
        /// </summary>
        public DataTable GetRenewalRateReport(int year)
        {
            try
            {
                string query = @"
                    WITH MonthlyData AS (
                        SELECT 
                            MONTH(sp.paymentDate) as Month,
                            DATENAME(month, sp.paymentDate) as MonthName,
                            COUNT(DISTINCT sp.pharmacyId) as RenewedPharmacies
                        FROM subscription_payments sp
                        WHERE YEAR(sp.paymentDate) = @year
                          AND sp.status = 'Completed'
                        GROUP BY MONTH(sp.paymentDate), DATENAME(month, sp.paymentDate)
                    ),
                    TotalPharmacies AS (
                        SELECT 
                            MONTH(registrationDate) as Month,
                            COUNT(*) as TotalRegistered
                        FROM registered_pharmacies
                        WHERE YEAR(registrationDate) <= @year
                        GROUP BY MONTH(registrationDate)
                    )
                    SELECT 
                        md.Month,
                        md.MonthName,
                        md.RenewedPharmacies,
                        ISNULL(tp.TotalRegistered, 0) as TotalRegistered,
                        CASE 
                            WHEN ISNULL(tp.TotalRegistered, 0) > 0 
                            THEN CAST(md.RenewedPharmacies AS FLOAT) / tp.TotalRegistered * 100
                            ELSE 0
                        END as RenewalRate
                    FROM MonthlyData md
                    LEFT JOIN TotalPharmacies tp ON md.Month = tp.Month
                    ORDER BY md.Month";

                var parameters = new Dictionary<string, object>
                {
                    {"@year", year}
                };

                return dbManager.ExecuteAdminQuery(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء تقرير معدل التجديد: {ex.Message}", ex);
            }
        }

        #endregion

        #region System Reports

        /// <summary>
        /// تقرير الأداء العام للنظام
        /// </summary>
        public SystemPerformanceReport GetSystemPerformanceReport()
        {
            try
            {
                var report = new SystemPerformanceReport();

                // إحصائيات الصيدليات
                var pharmacyStats = @"
                    SELECT 
                        COUNT(*) as Total,
                        SUM(CASE WHEN status = 'Approved' AND isActive = 1 THEN 1 ELSE 0 END) as Active,
                        SUM(CASE WHEN status = 'Pending' THEN 1 ELSE 0 END) as Pending,
                        SUM(CASE WHEN status = 'Suspended' THEN 1 ELSE 0 END) as Suspended
                    FROM registered_pharmacies";

                var pharmacyData = dbManager.ExecuteAdminQuery(pharmacyStats);
                if (pharmacyData.Rows.Count > 0)
                {
                    var row = pharmacyData.Rows[0];
                    report.TotalPharmacies = Convert.ToInt32(row["Total"]);
                    report.ActivePharmacies = Convert.ToInt32(row["Active"]);
                    report.PendingPharmacies = Convert.ToInt32(row["Pending"]);
                    report.SuspendedPharmacies = Convert.ToInt32(row["Suspended"]);
                }

                // إحصائيات الإيرادات
                var revenueStats = @"
                    SELECT 
                        ISNULL(SUM(CASE WHEN MONTH(paymentDate) = MONTH(GETDATE()) AND YEAR(paymentDate) = YEAR(GETDATE()) THEN amount ELSE 0 END), 0) as MonthlyRevenue,
                        ISNULL(SUM(CASE WHEN YEAR(paymentDate) = YEAR(GETDATE()) THEN amount ELSE 0 END), 0) as YearlyRevenue,
                        ISNULL(SUM(amount), 0) as TotalRevenue
                    FROM subscription_payments 
                    WHERE status = 'Completed'";

                var revenueData = dbManager.ExecuteAdminQuery(revenueStats);
                if (revenueData.Rows.Count > 0)
                {
                    var row = revenueData.Rows[0];
                    report.MonthlyRevenue = Convert.ToDecimal(row["MonthlyRevenue"]);
                    report.YearlyRevenue = Convert.ToDecimal(row["YearlyRevenue"]);
                    report.TotalRevenue = Convert.ToDecimal(row["TotalRevenue"]);
                }

                // إحصائيات النشاطات
                var activityStats = @"
                    SELECT 
                        COUNT(*) as TotalActivities,
                        COUNT(CASE WHEN CAST(activityDate AS DATE) = CAST(GETDATE() AS DATE) THEN 1 END) as TodayActivities,
                        COUNT(CASE WHEN activityDate >= DATEADD(day, -7, GETDATE()) THEN 1 END) as WeekActivities
                    FROM activity_log";

                var activityData = dbManager.ExecuteAdminQuery(activityStats);
                if (activityData.Rows.Count > 0)
                {
                    var row = activityData.Rows[0];
                    report.TotalActivities = Convert.ToInt32(row["TotalActivities"]);
                    report.TodayActivities = Convert.ToInt32(row["TodayActivities"]);
                    report.WeekActivities = Convert.ToInt32(row["WeekActivities"]);
                }

                return report;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء تقرير الأداء العام: {ex.Message}", ex);
            }
        }

        #endregion

        #region Export Functions

        /// <summary>
        /// تصدير تقرير إلى CSV
        /// </summary>
        public string ExportToCSV(DataTable data, string fileName)
        {
            try
            {
                string documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                string reportsPath = Path.Combine(documentsPath, "Pharmacy Reports");
                
                if (!Directory.Exists(reportsPath))
                {
                    Directory.CreateDirectory(reportsPath);
                }

                string filePath = Path.Combine(reportsPath, $"{fileName}_{DateTime.Now:yyyyMMdd_HHmmss}.csv");

                using (var writer = new StreamWriter(filePath, false, System.Text.Encoding.UTF8))
                {
                    // كتابة العناوين
                    var headers = new List<string>();
                    foreach (DataColumn column in data.Columns)
                    {
                        headers.Add($"\"{column.ColumnName}\"");
                    }
                    writer.WriteLine(string.Join(",", headers));

                    // كتابة البيانات
                    foreach (DataRow row in data.Rows)
                    {
                        var values = new List<string>();
                        foreach (var field in row.ItemArray)
                        {
                            values.Add($"\"{field?.ToString().Replace("\"", "\"\"")}\"");
                        }
                        writer.WriteLine(string.Join(",", values));
                    }
                }

                return filePath;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تصدير التقرير: {ex.Message}", ex);
            }
        }

        #endregion
    }

    #region Data Classes

    /// <summary>
    /// تقرير الأداء العام للنظام
    /// </summary>
    public class SystemPerformanceReport
    {
        public int TotalPharmacies { get; set; }
        public int ActivePharmacies { get; set; }
        public int PendingPharmacies { get; set; }
        public int SuspendedPharmacies { get; set; }
        public decimal MonthlyRevenue { get; set; }
        public decimal YearlyRevenue { get; set; }
        public decimal TotalRevenue { get; set; }
        public int TotalActivities { get; set; }
        public int TodayActivities { get; set; }
        public int WeekActivities { get; set; }
    }

    #endregion
}
