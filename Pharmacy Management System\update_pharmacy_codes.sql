-- تحديث أكواد الصيدليات الموجودة
-- Update Existing Pharmacy Codes

USE UnifiedPharmacy;
GO

PRINT 'بدء تحديث أكواد الصيدليات...';

-- 1. تحديث الصيدليات الموجودة بأكواد فريدة إذا لم تكن لديها أكواد
UPDATE pharmacies 
SET pharmacyCode = 'PHARM' + RIGHT('000' + CAST(id AS VARCHAR(3)), 3) 
WHERE pharmacyCode IS NULL OR pharmacyCode = '';

PRINT 'تم تحديث الصيدليات الموجودة بأكواد فريدة';

-- 2. إضافة صيدليات تجريبية إضافية
IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacyCode = 'MAIN001')
BEGIN
    INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, phone, address, city, email, isActive)
    VALUES ('MAIN001', N'الصيدلية الرئيسية', N'المدير العام', '0123456789', N'الرياض - حي الملك فهد', N'الرياض', '<EMAIL>', 1);
    PRINT 'تم إنشاء الصيدلية الرئيسية (MAIN001)';
END
ELSE
BEGIN
    PRINT 'الصيدلية الرئيسية موجودة بالفعل';
END

IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacyCode = 'BRANCH01')
BEGIN
    INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, phone, address, city, email, isActive)
    VALUES ('BRANCH01', N'صيدلية الفرع الأول', N'مدير الفرع', '0123456790', N'جدة - حي الصفا', N'جدة', '<EMAIL>', 1);
    PRINT 'تم إنشاء صيدلية الفرع الأول (BRANCH01)';
END
ELSE
BEGIN
    PRINT 'صيدلية الفرع الأول موجودة بالفعل';
END

-- 3. تحديث المستخدمين لربطهم بالصيدلية الرئيسية
UPDATE users 
SET pharmacyId = (SELECT TOP 1 id FROM pharmacies WHERE pharmacyCode = 'MAIN001')
WHERE pharmacyId IS NULL OR pharmacyId = 0;

PRINT 'تم ربط المستخدمين بالصيدلية الرئيسية';

-- 4. عرض النتائج النهائية
PRINT '';
PRINT 'الصيدليات المتاحة:';
PRINT '==================';

SELECT 
    id,
    pharmacyCode AS 'كود الصيدلية',
    pharmacyName AS 'اسم الصيدلية',
    ownerName AS 'اسم المالك',
    city AS 'المدينة',
    isActive AS 'نشطة'
FROM pharmacies
ORDER BY id;

-- 5. عرض عدد المستخدمين لكل صيدلية
PRINT '';
PRINT 'عدد المستخدمين لكل صيدلية:';
PRINT '============================';

SELECT 
    p.pharmacyCode AS 'كود الصيدلية',
    p.pharmacyName AS 'اسم الصيدلية',
    COUNT(u.id) AS 'عدد المستخدمين'
FROM pharmacies p
LEFT JOIN users u ON p.id = u.pharmacyId
GROUP BY p.pharmacyCode, p.pharmacyName
ORDER BY p.pharmacyCode;

PRINT '';
PRINT '✅ تم تحديث أكواد الصيدليات بنجاح!';
PRINT '';
PRINT '🔑 أكواد الصيدليات المتاحة للاختبار:';
PRINT '   - MAIN001: الصيدلية الرئيسية';
PRINT '   - BRANCH01: صيدلية الفرع الأول';
PRINT '   - PHARM001, PHARM002, etc.: الصيدليات الأخرى';
