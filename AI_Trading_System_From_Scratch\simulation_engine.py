import pandas as pd
import numpy as np
from analysis_engine import AnalysisEngine

class SimulationEngine:
    def __init__(self, config, analysis_engine):
        self.config = config
        self.analysis_engine = analysis_engine
        self.lot_size = float(config['TRADING']['lot_size'])
        self.sl_pips = int(config['TRADING']['sl_pips'])
        self.tp_pips = int(config['TRADING']['tp_pips'])

    def simulate_trades(self, symbol, historical_data):
        # historical_data is df from analysis_engine
        df = historical_data.copy()
        df['signal'] = None
        df['position'] = 0  # 1 for long, -1 for short
        df['entry_price'] = np.nan
        df['exit_price'] = np.nan
        df['pnl'] = 0.0

        position = 0
        entry_price = 0

        for i in range(len(df)):
            # Generate signal for current bar
            current_df = df.iloc[:i+1]
            signal = self.analysis_engine.generate_signal(current_df)

            if position == 0:
                if signal == 'BUY':
                    position = 1
                    entry_price = df.iloc[i]['close']
                    df.at[i, 'position'] = 1
                    df.at[i, 'entry_price'] = entry_price
                elif signal == 'SELL':
                    position = -1
                    entry_price = df.iloc[i]['close']
                    df.at[i, 'position'] = -1
                    df.at[i, 'entry_price'] = entry_price
            else:
                # Check SL/TP
                current_price = df.iloc[i]['close']
                if position == 1:
                    if current_price <= entry_price - self.sl_pips * 0.0001:  # Assuming 4 decimal places
                        pnl = (current_price - entry_price) * self.lot_size * 100000  # For EURUSD
                        df.at[i, 'exit_price'] = current_price
                        df.at[i, 'pnl'] = pnl
                        position = 0
                    elif current_price >= entry_price + self.tp_pips * 0.0001:
                        pnl = (current_price - entry_price) * self.lot_size * 100000
                        df.at[i, 'exit_price'] = current_price
                        df.at[i, 'pnl'] = pnl
                        position = 0
                elif position == -1:
                    if current_price >= entry_price + self.sl_pips * 0.0001:
                        pnl = (entry_price - current_price) * self.lot_size * 100000
                        df.at[i, 'exit_price'] = current_price
                        df.at[i, 'pnl'] = pnl
                        position = 0
                    elif current_price <= entry_price - self.tp_pips * 0.0001:
                        pnl = (entry_price - current_price) * self.lot_size * 100000
                        df.at[i, 'exit_price'] = current_price
                        df.at[i, 'pnl'] = pnl
                        position = 0

        total_pnl = df['pnl'].sum()
        return df, total_pnl
