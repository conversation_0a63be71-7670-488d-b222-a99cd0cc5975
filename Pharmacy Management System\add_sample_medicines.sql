-- ========================================
-- إضافة أدوية تجريبية لاختبار متجر الأدوية
-- Add Sample Medicines for Testing Pharmacy Store
-- ========================================

USE UnifiedPharmacy;
GO

PRINT '🧪 إضافة أدوية تجريبية لاختبار متجر الأدوية...';
PRINT '';

-- التحقق من وجود جدول medic
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'medic')
BEGIN
    PRINT '❌ جدول medic غير موجود! يرجى إنشاؤه أولاً.';
    RETURN;
END

-- حذف البيانات التجريبية السابقة إذا وجدت
DELETE FROM medic WHERE mname LIKE 'دواء تجريبي%' OR mname LIKE 'Test Medicine%';

PRINT '🗑️ تم حذف البيانات التجريبية السابقة';

-- إدراج أدوية تجريبية
INSERT INTO medic (mid, mname, mnumber, mDate, eDate, quantity, perUnit, lu, br, originalQuantity, originalNewQuantity, allqun, mnumber_qty, newMDate, newEDate, newQuantity, dos2, dos2_qty, dos3, dos3_qty, dos4, dos4_qty)
VALUES 
-- دواء 1: باراسيتامول
('MED001', N'باراسيتامول 500 مجم', 'PAR500', '2024-01-15', '2025-12-31', 100, 2.50, N'صندوق', N'فايزر', 100, 0, 100, 100, '2024-01-15', '2025-12-31', 0, N'أقراص', 100, NULL, 0, NULL, 0),

-- دواء 2: أموكسيسيلين
('MED002', N'أموكسيسيلين 250 مجم', 'AMX250', '2024-02-10', '2025-11-30', 75, 15.00, N'علبة', N'جلاكسو', 75, 0, 75, 75, '2024-02-10', '2025-11-30', 0, N'كبسولات', 75, NULL, 0, NULL, 0),

-- دواء 3: إيبوبروفين
('MED003', N'إيبوبروفين 400 مجم', 'IBU400', '2024-03-05', '2026-01-15', 50, 8.75, N'شريط', N'نوفارتيس', 50, 0, 50, 50, '2024-03-05', '2026-01-15', 0, N'أقراص', 50, NULL, 0, NULL, 0),

-- دواء 4: أسبرين
('MED004', N'أسبرين 100 مجم', 'ASP100', '2024-01-20', '2025-10-31', 200, 1.25, N'شريط', N'باير', 200, 0, 200, 200, '2024-01-20', '2025-10-31', 0, N'أقراص', 200, NULL, 0, NULL, 0),

-- دواء 5: أوميبرازول
('MED005', N'أوميبرازول 20 مجم', 'OME20', '2024-04-01', '2025-09-30', 30, 25.00, N'علبة', N'أسترازينيكا', 30, 0, 30, 30, '2024-04-01', '2025-09-30', 0, N'كبسولات', 30, NULL, 0, NULL, 0),

-- دواء 6: سيتريزين
('MED006', N'سيتريزين 10 مجم', 'CET10', '2024-02-15', '2025-08-31', 80, 12.50, N'علبة', N'يو سي بي', 80, 0, 80, 80, '2024-02-15', '2025-08-31', 0, N'أقراص', 80, NULL, 0, NULL, 0),

-- دواء 7: ديكلوفيناك
('MED007', N'ديكلوفيناك 50 مجم', 'DIC50', '2024-03-10', '2025-07-31', 40, 18.00, N'علبة', N'نوفارتيس', 40, 0, 40, 40, '2024-03-10', '2025-07-31', 0, N'أقراص', 40, NULL, 0, NULL, 0),

-- دواء 8: لوراتادين
('MED008', N'لوراتادين 10 مجم', 'LOR10', '2024-01-25', '2025-06-30', 60, 9.25, N'شريط', N'شيرنغ', 60, 0, 60, 60, '2024-01-25', '2025-06-30', 0, N'أقراص', 60, NULL, 0, NULL, 0),

-- دواء 9: سيمفاستاتين
('MED009', N'سيمفاستاتين 20 مجم', 'SIM20', '2024-04-05', '2025-05-31', 25, 35.00, N'علبة', N'ميرك', 25, 0, 25, 25, '2024-04-05', '2025-05-31', 0, N'أقراص', 25, NULL, 0, NULL, 0),

-- دواء 10: ميتفورمين
('MED010', N'ميتفورمين 500 مجم', 'MET500', '2024-02-20', '2025-04-30', 90, 6.50, N'علبة', N'تيفا', 90, 0, 90, 90, '2024-02-20', '2025-04-30', 0, N'أقراص', 90, NULL, 0, NULL, 0);

PRINT '';
PRINT '✅ تم إدراج 10 أدوية تجريبية بنجاح!';
PRINT '';

-- عرض الأدوية المدرجة
PRINT '📋 الأدوية المدرجة:';
SELECT 
    mname as [اسم الدواء],
    mnumber as [رقم الدواء],
    quantity as [الكمية],
    perUnit as [السعر],
    eDate as [تاريخ الانتهاء],
    DATEDIFF(day, GETDATE(), eDate) as [أيام للانتهاء]
FROM medic 
WHERE mid LIKE 'MED%'
ORDER BY mname;

PRINT '';
PRINT '🎯 الآن يمكنك اختبار متجر الأدوية:';
PRINT '1. افتح برنامج إدارة الصيدلية';
PRINT '2. اذهب لواجهة الموظف';
PRINT '3. اضغط على "متجر الصيدلية"';
PRINT '4. يجب أن ترى الأدوية في تبويب "الأدوية المحلية"';
PRINT '5. اختر دواء واضغط "نشر الدواء"';
PRINT '';

-- إحصائيات سريعة
DECLARE @totalMedicines INT, @availableMedicines INT, @expiredMedicines INT;

SELECT @totalMedicines = COUNT(*) FROM medic;
SELECT @availableMedicines = COUNT(*) FROM medic WHERE quantity > 0 AND eDate > GETDATE();
SELECT @expiredMedicines = COUNT(*) FROM medic WHERE eDate <= GETDATE();

PRINT '📊 إحصائيات الأدوية:';
PRINT '• إجمالي الأدوية: ' + CAST(@totalMedicines AS NVARCHAR(10));
PRINT '• الأدوية المتاحة: ' + CAST(@availableMedicines AS NVARCHAR(10));
PRINT '• الأدوية المنتهية الصلاحية: ' + CAST(@expiredMedicines AS NVARCHAR(10));
PRINT '';
