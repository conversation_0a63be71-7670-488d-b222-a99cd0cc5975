@echo off
chcp 65001 > nul
title 🚀 نظام تداول العملات الرقمية الذكي - Crypto Trading System

echo.
echo ================================================================
echo 🚀 نظام تداول العملات الرقمية الذكي
echo INTELLIGENT CRYPTOCURRENCY TRADING SYSTEM
echo ================================================================
echo.

echo 💡 مميزات النظام:
echo    🔥 تداول العملات الرقمية (Bitcoin, Ethereum, وأكثر)
echo    🧠 تعلم آلي ذكي وتحسين مستمر
echo    📊 تحليل فني متقدم
echo    🎯 محاكاة آمنة وتداول حقيقي
echo    📈 واجهة رسومية سهلة الاستخدام
echo.

echo 🎯 العملات المدعومة:
echo    ₿ Bitcoin (BTCUSDT)
echo    Ξ Ethereum (ETHUSDT)  
echo    🔸 Binance Coin (BNBUSDT)
echo    ♠ Cardano (ADAUSDT)
echo    💧 Ripple (XRPUSDT)
echo    ☀️ Solana (SOLUSDT)
echo    🔴 Polkadot (DOTUSDT)
echo    🐕 Dogecoin (DOGEUSDT)
echo    🔺 Avalanche (AVAXUSDT)
echo    🟣 Polygon (MATICUSDT)
echo.

echo 🔧 متطلبات التشغيل:
echo    ✅ Python 3.8+ مثبت
echo    ✅ مكتبات Python مثبتة (pip install -r crypto_requirements.txt)
echo    ✅ اتصال بالإنترنت للحصول على أسعار العملات
echo    ✅ (اختياري) مفاتيح Binance API للتداول الحقيقي
echo.

echo 🚨 تحذير مهم:
echo    💰 النظام يدعم وضعين: محاكاة (آمن) وحقيقي
echo    🛡️ ابدأ بالمحاكاة لتعلم النظام
echo    ⚠️ التداول الحقيقي يتطلب مفاتيح API ويؤثر على أموالك
echo.

echo 💡 كيفية الاستخدام:
echo    1️⃣ اختر العملة المرغوبة من القائمة
echo    2️⃣ اختر وضع التداول (محاكاة/حقيقي)
echo    3️⃣ اضغط "بدء التداول" لبدء النظام
echo    4️⃣ راقب التحليلات والصفقات في الواجهة
echo    5️⃣ استخدم "تدريب النموذج" لتحسين الأداء
echo.

pause

echo 🔄 تحقق من المتطلبات...

python -c "import pandas, numpy, requests, sklearn, ta" 2>nul
if errorlevel 1 (
    echo ❌ مكتبات مفقودة! تثبيت المكتبات...
    pip install -r crypto_requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        echo 💡 جرب: pip install pandas numpy requests scikit-learn ta
        pause
        exit /b 1
    )
)

echo ✅ المتطلبات متوفرة

echo.
echo 🚀 بدء نظام تداول العملات الرقمية...
echo.

python crypto_gui.py

echo.
echo ================================================================
echo ✅ انتهى التشغيل
echo ================================================================
echo.

echo 💡 إذا واجهت مشاكل:
echo    🔧 تأكد من تثبيت Python 3.8+
echo    📦 تأكد من تثبيت المكتبات المطلوبة
echo    🌐 تأكد من الاتصال بالإنترنت
echo    📄 راجع ملف crypto_config.ini للإعدادات
echo.

echo 🎯 للتداول الحقيقي:
echo    🔑 احصل على مفاتيح API من Binance
echo    📝 أضفها في ملف crypto_config.ini
echo    ⚠️ ابدأ بمبالغ صغيرة للاختبار
echo.

pause
