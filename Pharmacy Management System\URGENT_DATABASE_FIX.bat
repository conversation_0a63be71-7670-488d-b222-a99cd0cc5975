@echo off
chcp 65001 >nul
echo ========================================
echo 🚨 إصلاح عاجل لقاعدة البيانات
echo 🚨 URGENT Database Fix
echo ========================================
echo.

echo 🔧 إنشاء قاعدة البيانات الموحدة...
echo Creating Unified Database...
echo.

REM Create database
echo إنشاء قاعدة البيانات...
sqlcmd -S NARUTO -E -Q "IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'UnifiedPharmacy') CREATE DATABASE UnifiedPharmacy; PRINT 'Database created or already exists';"

if %errorlevel% neq 0 (
    echo ❌ خطأ في إنشاء قاعدة البيانات!
    echo جرب تشغيل SQL Server Management Studio وشغل هذا الكود:
    echo CREATE DATABASE UnifiedPharmacy;
    pause
    exit /b 1
)

echo ✅ تم إنشاء قاعدة البيانات بنجاح
echo.

REM Create tables
echo إنشاء الجداول...
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "
-- جدول الصيدليات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies' AND xtype='U')
CREATE TABLE pharmacies (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacyCode VARCHAR(20) UNIQUE NOT NULL,
    pharmacyName NVARCHAR(250) NOT NULL,
    ownerName NVARCHAR(250) NOT NULL,
    licenseNumber VARCHAR(100) NOT NULL,
    address NVARCHAR(500) NOT NULL,
    city NVARCHAR(100) NOT NULL,
    region NVARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(250) NOT NULL,
    isActive BIT DEFAULT 1,
    registrationDate DATETIME DEFAULT GETDATE()
);

-- جدول المستخدمين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
CREATE TABLE users (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacyId INT NOT NULL,
    userRole VARCHAR(50) NOT NULL,
    name NVARCHAR(250) NOT NULL,
    dob VARCHAR(250) NOT NULL,
    mobile BIGINT NOT NULL,
    email VARCHAR(250) NOT NULL,
    username VARCHAR(250) NOT NULL,
    pass VARCHAR(250) NOT NULL,
    isActive BIT DEFAULT 1,
    FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
);

-- جدول جلسات الموظفين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='employee_sessions' AND xtype='U')
CREATE TABLE employee_sessions (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacyId INT NOT NULL,
    username VARCHAR(250),
    employeeName NVARCHAR(250),
    loginTime DATETIME,
    logoutTime DATETIME NULL,
    sessionDate DATE,
    FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
);

PRINT 'Tables created successfully';
"

if %errorlevel% neq 0 (
    echo ❌ خطأ في إنشاء الجداول!
    pause
    exit /b 1
)

echo ✅ تم إنشاء الجداول بنجاح
echo.

REM Insert default data
echo إدراج البيانات الافتراضية...
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "
-- إضافة صيدلية افتراضية
IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacyCode = 'MAIN001')
INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, licenseNumber, address, city, region, phone, email)
VALUES ('MAIN001', N'الصيدلية الرئيسية', N'مدير النظام', 'LIC001', N'العنوان الرئيسي', N'المدينة', N'المنطقة', '**********', '<EMAIL>');

-- إضافة مستخدمين افتراضيين
IF NOT EXISTS (SELECT * FROM users WHERE username = 'admin')
INSERT INTO users (pharmacyId, userRole, name, dob, mobile, email, username, pass)
VALUES (1, 'Administrator', N'مدير النظام', '1980-01-01', **********, '<EMAIL>', 'admin', 'admin123');

IF NOT EXISTS (SELECT * FROM users WHERE username = 'employee')
INSERT INTO users (pharmacyId, userRole, name, dob, mobile, email, username, pass)
VALUES (1, 'Employee', N'موظف الصيدلية', '1990-01-01', **********, '<EMAIL>', 'employee', 'emp123');

PRINT 'Default data inserted successfully';
"

if %errorlevel% neq 0 (
    echo ❌ خطأ في إدراج البيانات!
    pause
    exit /b 1
)

echo ✅ تم إدراج البيانات بنجاح
echo.

echo ========================================
echo ✅ تم إعداد قاعدة البيانات بنجاح!
echo ✅ Database setup completed successfully!
echo ========================================
echo.
echo 🎯 يمكنك الآن تشغيل البرنامج:
echo 👤 حسابات تسجيل الدخول:
echo    المدير / Admin: admin / admin123
echo    الموظف / Employee: employee / emp123
echo.
echo اضغط أي مفتاح للمتابعة...
pause >nul
