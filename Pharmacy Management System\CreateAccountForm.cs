using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy_Management_System
{
    public partial class CreateAccountForm : Form
    {
        private UnifiedPharmacyFunction unifiedDb = new UnifiedPharmacyFunction();
        private int pharmacyId;
        private string pharmacyName;
        
        public string NewUsername { get; private set; } = "";

        public CreateAccountForm()
        {
            InitializeComponent();
            LoadPharmacies();
            ApplyLanguage();
        }

        public CreateAccountForm(int pharmacyId, string pharmacyName)
        {
            InitializeComponent();
            this.pharmacyId = pharmacyId;
            this.pharmacyName = pharmacyName;

            lblPharmacyName.Text = $"إنشاء حساب جديد في: {pharmacyName}";
            ApplyLanguage();
        }

        private void LoadPharmacies()
        {
            try
            {
                // تحميل الصيدليات المتاحة
                DataSet pharmaciesData = unifiedDb.GetActivePharmacies();

                if (pharmaciesData.Tables.Count > 0 && pharmaciesData.Tables[0].Rows.Count > 0)
                {
                    // استخدام أول صيدلية متاحة
                    DataRow firstPharmacy = pharmaciesData.Tables[0].Rows[0];
                    this.pharmacyId = Convert.ToInt32(firstPharmacy["id"]);
                    this.pharmacyName = firstPharmacy["pharmacyName"].ToString();

                    lblPharmacyName.Text = $"إنشاء حساب جديد في: {pharmacyName}";
                }
                else
                {
                    MessageBox.Show("لا توجد صيدليات متاحة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الصيدليات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Close();
            }
        }

        private void btnCreate_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة الإدخال
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show("يرجى إدخال الاسم", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtUsername.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المستخدم", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    MessageBox.Show("يرجى إدخال كلمة المرور", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPassword.Focus();
                    return;
                }

                if (txtPassword.Text != txtConfirmPassword.Text)
                {
                    MessageBox.Show("كلمة المرور غير متطابقة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtConfirmPassword.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtEmail.Text))
                {
                    MessageBox.Show("يرجى إدخال البريد الإلكتروني", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEmail.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtMobile.Text))
                {
                    MessageBox.Show("يرجى إدخال رقم الهاتف", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtMobile.Focus();
                    return;
                }

                // التحقق من عدم وجود اسم المستخدم مسبقاً
                if (unifiedDb.IsUsernameExists(txtUsername.Text))
                {
                    MessageBox.Show("اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return;
                }

                // إنشاء الحساب الجديد
                bool success = unifiedDb.CreateUser(
                    pharmacyId,
                    cmbRole.SelectedItem.ToString(),
                    txtName.Text.Trim(),
                    dtpBirthDate.Value,
                    Convert.ToInt64(txtMobile.Text.Trim()),
                    txtEmail.Text.Trim(),
                    txtUsername.Text.Trim(),
                    txtPassword.Text
                );

                if (success)
                {
                    NewUsername = txtUsername.Text.Trim();
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في إنشاء الحساب، يرجى المحاولة مرة أخرى", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء الحساب: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void ApplyLanguage()
        {
            try
            {
                this.Text = "إنشاء حساب جديد";
                this.RightToLeft = RightToLeft.Yes;
                this.RightToLeftLayout = true;

                // ملء قائمة الأدوار
                cmbRole.Items.Clear();
                cmbRole.Items.Add("Employee");
                cmbRole.Items.Add("Pharmacist");
                cmbRole.Items.Add("Administrator");
                cmbRole.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق اللغة: {ex.Message}");
            }
        }

        private void txtMobile_KeyPress(object sender, KeyPressEventArgs e)
        {
            // السماح بالأرقام فقط
            if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar))
            {
                e.Handled = true;
            }
        }

        private void CreateAccountForm_Load(object sender, EventArgs e)
        {
            // تركيز على أول حقل
            txtName.Focus();
        }
    }
}
