@echo off
chcp 65001 > nul
title 🔥 نظام التداول الحقيقي المبسط - SIMPLE REAL TRADER

echo.
echo ================================================================
echo 🔥 نظام التداول الحقيقي المبسط
echo SIMPLE REAL TRADER - OPENS REAL TRADES
echo ================================================================
echo.

echo 🚨 تحذير مهم:
echo    💰 هذا النظام سيفتح صفقة حقيقية واحدة!
echo    🔥 سيتم تنفيذها على حسابك الفعلي في MetaTrader 5
echo    ⚠️ تأكد من أن هذا ما تريده!
echo.

echo 📋 متطلبات التشغيل:
echo    ✅ MetaTrader 5 يعمل ومسجل دخول
echo    ✅ التداول الآلي مفعل
echo    ✅ ملف config.ini محدث ببيانات حسابك
echo    ✅ رصيد كافي في الحساب
echo.

echo 🎯 ما سيحدث:
echo    1️⃣ الاتصال بـ MetaTrader 5
echo    2️⃣ التحقق من صلاحيات التداول
echo    3️⃣ فتح صفقة شراء واحدة للاختبار
echo    4️⃣ عرض تفاصيل الصفقة
echo.

echo 💡 بعد التشغيل:
echo    📊 تحقق من تبويب "Trade" في MetaTrader 5
echo    💰 ستجد الصفقة مفتوحة على حسابك
echo    📈 ستؤثر على رصيدك الفعلي
echo.

pause

echo 🔄 بدء النظام...
echo.

python SIMPLE_REAL_TRADER.py

echo.
echo ================================================================
echo ✅ انتهى التشغيل
echo ================================================================
echo.

echo 💡 إذا نجح النظام:
echo    📊 تحقق من MetaTrader 5
echo    💰 ستجد صفقة مفتوحة في حسابك
echo    🎉 النظام يعمل ويفتح صفقات حقيقية!
echo.

echo ⚠️ إذا فشل النظام:
echo    🔧 تحقق من إعدادات MetaTrader 5
echo    📄 تحقق من ملف config.ini
echo    🔌 تأكد من الاتصال بالإنترنت
echo    ⚙️ تأكد من تفعيل التداول الآلي
echo.

pause
