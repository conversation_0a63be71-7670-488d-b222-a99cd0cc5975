import configparser
from analysis_engine import AnalysisEngine
from simulation_engine import SimulationEngine
from ml_engine import MLEngine
from mt5_handler import MT<PERSON><PERSON><PERSON><PERSON>

def main():
    # Load config
    config = configparser.ConfigParser()
    config.read('config.ini')

    # Initialize components
    analysis = AnalysisEngine(config)
    ml = MLEngine(config)
    sim = SimulationEngine(config, analysis)
    mt5 = MT5Handler(config)

    # Connect to MT5
    if not mt5.connect():
        print("Failed to connect to MT5. Exiting.")
        return

    analysis.connect_mt5()

    try:
        for symbol in analysis.symbols:
            print(f"Analyzing {symbol}")
            result = analysis.analyze_symbol(symbol)
            if result:
                # Use ML for signal
                signal = ml.predict_signal(result['data'])
                print(f"ML Signal for {symbol}: {signal}")

                if signal in ['BUY', 'SELL']:
                    # Simulate first
                    _, pnl = sim.simulate_trades(symbol, result['data'])
                    print(f"Simulated PnL for {symbol}: {pnl}")

                    # If positive, open real trade
                    if pnl > 0:
                        ticket = mt5.open_position(symbol, signal, sim.lot_size, sim.sl_pips, sim.tp_pips)
                        if ticket:
                            print(f"Opened position for {symbol}, ticket: {ticket}")
                        else:
                            print(f"Failed to open position for {symbol}")

                # Train ML with data
                ml.train_model(result['data'])

    finally:
        mt5.disconnect()
        analysis.disconnect_mt5()

if __name__ == "__main__":
    main()
