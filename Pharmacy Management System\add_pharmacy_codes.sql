-- ═══════════════════════════════════════════════════════════════
-- إضافة أكواد الصيدليات لنظام تسجيل الدخول المحدث
-- Add Pharmacy Codes for Enhanced Login System
-- التاريخ: 2025-06-30 | Date: 2025-06-30
-- ═══════════════════════════════════════════════════════════════

USE UnifiedPharmacy;
GO

PRINT '🚀 بدء تحديث قاعدة البيانات لدعم أكواد الصيدليات...';
PRINT '🚀 Starting database update to support pharmacy codes...';
PRINT '';

-- ===================================
-- 1. إضافة عمود pharmacy_code لجدول pharmacies
-- ===================================

PRINT '1. فحص وإضافة عمود pharmacy_code...';

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('pharmacies') AND name = 'pharmacy_code')
BEGIN
    ALTER TABLE pharmacies ADD pharmacy_code NVARCHAR(50);
    PRINT '✅ تم إضافة عمود pharmacy_code لجدول pharmacies';
END
ELSE
BEGIN
    PRINT '📋 عمود pharmacy_code موجود بالفعل';
END

-- ===================================
-- 2. تحديث الصيدليات الموجودة بأكواد فريدة
-- ===================================

PRINT '';
PRINT '2. تحديث الصيدليات الموجودة بأكواد فريدة...';

-- تحديث الصيدليات التي لا تحتوي على كود
UPDATE pharmacies 
SET pharmacy_code = 'PHARM' + RIGHT('000' + CAST(id AS VARCHAR(3)), 3) 
WHERE pharmacy_code IS NULL OR pharmacy_code = '';

PRINT '✅ تم تحديث الصيدليات الموجودة بأكواد فريدة';

-- ===================================
-- 3. إضافة قيد الفرادة لعمود pharmacy_code
-- ===================================

PRINT '';
PRINT '3. إضافة قيد الفرادة لعمود pharmacy_code...';

-- التحقق من وجود القيد أولاً
IF NOT EXISTS (SELECT * FROM sys.key_constraints WHERE name = 'UQ_pharmacies_code')
BEGIN
    ALTER TABLE pharmacies ADD CONSTRAINT UQ_pharmacies_code UNIQUE (pharmacy_code);
    PRINT '✅ تم إضافة قيد الفرادة لعمود pharmacy_code';
END
ELSE
BEGIN
    PRINT '📋 قيد الفرادة موجود بالفعل';
END

-- ===================================
-- 4. إنشاء صيدليات تجريبية إضافية إذا لزم الأمر
-- ===================================

PRINT '';
PRINT '4. التحقق من وجود صيدليات تجريبية...';

-- إضافة صيدلية رئيسية إذا لم تكن موجودة
IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacy_code = 'MAIN001')
BEGIN
    INSERT INTO pharmacies (pharmacy_code, pharmacy_name, owner_name, phone, address, city, email, is_active)
    VALUES ('MAIN001', N'الصيدلية الرئيسية', N'المدير العام', '0123456789', N'الرياض - حي الملك فهد', N'الرياض', '<EMAIL>', 1);
    PRINT '✅ تم إنشاء الصيدلية الرئيسية (MAIN001)';
END

-- إضافة صيدلية فرعية للاختبار
IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacy_code = 'BRANCH01')
BEGIN
    INSERT INTO pharmacies (pharmacy_code, pharmacy_name, owner_name, phone, address, city, email, is_active)
    VALUES ('BRANCH01', N'صيدلية الفرع الأول', N'مدير الفرع', '0123456790', N'جدة - حي الصفا', N'جدة', '<EMAIL>', 1);
    PRINT '✅ تم إنشاء صيدلية الفرع الأول (BRANCH01)';
END

-- ===================================
-- 5. تحديث المستخدمين لربطهم بالصيدليات
-- ===================================

PRINT '';
PRINT '5. تحديث ربط المستخدمين بالصيدليات...';

-- ربط المستخدمين الذين لا يحتوون على pharmacy_id بالصيدلية الرئيسية
UPDATE users 
SET pharmacy_id = (SELECT TOP 1 id FROM pharmacies WHERE pharmacy_code = 'MAIN001')
WHERE pharmacy_id IS NULL OR pharmacy_id = 0;

PRINT '✅ تم ربط المستخدمين بالصيدلية الرئيسية';

-- ===================================
-- 6. عرض النتائج النهائية
-- ===================================

PRINT '';
PRINT '📊 النتائج النهائية:';
PRINT '==================';

-- عرض الصيدليات مع أكوادها
SELECT 
    id,
    pharmacy_code AS 'كود الصيدلية',
    pharmacy_name AS 'اسم الصيدلية',
    owner_name AS 'اسم المالك',
    city AS 'المدينة',
    is_active AS 'نشطة'
FROM pharmacies
ORDER BY id;

-- عرض عدد المستخدمين لكل صيدلية
SELECT 
    p.pharmacy_code AS 'كود الصيدلية',
    p.pharmacy_name AS 'اسم الصيدلية',
    COUNT(u.id) AS 'عدد المستخدمين'
FROM pharmacies p
LEFT JOIN users u ON p.id = u.pharmacy_id
GROUP BY p.pharmacy_code, p.pharmacy_name
ORDER BY p.pharmacy_code;

PRINT '';
PRINT '✅ تم تحديث قاعدة البيانات بنجاح لدعم أكواد الصيدليات!';
PRINT '✅ Database successfully updated to support pharmacy codes!';
PRINT '';
PRINT '🔑 أكواد الصيدليات المتاحة للاختبار:';
PRINT '   - MAIN001: الصيدلية الرئيسية';
PRINT '   - BRANCH01: صيدلية الفرع الأول';
PRINT '   - PHARM001, PHARM002, etc.: الصيدليات الأخرى';
