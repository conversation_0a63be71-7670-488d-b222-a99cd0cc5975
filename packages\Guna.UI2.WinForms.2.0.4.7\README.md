﻿
Guna.UI2 WinForms is the suite for creating groundbreaking desktop app UI. It is for developers targeting the .NET Windows Forms platform. 

## .NET
- .NET Framework v4.0 or higher
- .NETCoreApp 3.1 or higher
- .NET 6
- .NET 7

## IDE
- Visual Studio 2012 or higger
 
## Links
- [Homepage](https://gunaui.com/)  
- [Demos](https://gunaui.com/demos/)
- [Nuget Profile](https://www.nuget.org/profiles/Sobatdata)
- [Youtube](https://www.youtube.com/@gunaui4933/)