-- التأكد من قاعدة البيانات الموحدة
-- Ensure Unified Database Structure

USE pharmacy;

PRINT '========================================';
PRINT '   التأكد من قاعدة البيانات الموحدة';
PRINT '   Ensuring Unified Database Structure';
PRINT '========================================';

-- 1. التحقق من جدول المستخدمين وإصلاحه
PRINT '';
PRINT '1. فحص وإصلاح جدول المستخدمين...';

-- التأكد من وجود الأعمدة المطلوبة
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'pharmacyId')
BEGIN
    ALTER TABLE users ADD pharmacyId INT DEFAULT 2;
    PRINT '✅ تم إضافة عمود pharmacyId';
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'isActive')
BEGIN
    ALTER TABLE users ADD isActive BIT DEFAULT 1;
    PRINT '✅ تم إضافة عمود isActive';
END

-- 2. التأكد من جدول الصيدليات
PRINT '';
PRINT '2. فحص جدول الصيدليات...';

-- عرض الصيدليات الموجودة
SELECT id, pharmacyCode, pharmacyName FROM pharmacies;

-- التأكد من وجود صيدلية افتراضية
IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacyCode = 'MAIN001')
BEGIN
    INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, licenseNumber, address, city, region, phone, email, isActive)
    VALUES ('MAIN001', N'الصيدلية الرئيسية', N'المالك الرئيسي', 'LIC001', N'العنوان الرئيسي', N'الرياض', N'الرياض', '**********', '<EMAIL>', 1);
    PRINT '✅ تم إنشاء الصيدلية الافتراضية';
END

-- 3. تحديث المستخدمين الموجودين
PRINT '';
PRINT '3. تحديث المستخدمين الموجودين...';

-- الحصول على معرف الصيدلية الافتراضية
DECLARE @defaultPharmacyId INT;
SELECT @defaultPharmacyId = id FROM pharmacies WHERE pharmacyCode = 'MAIN001';

-- تحديث المستخدمين الذين لا يملكون pharmacyId صحيح
UPDATE users 
SET pharmacyId = @defaultPharmacyId, isActive = 1 
WHERE pharmacyId IS NULL OR pharmacyId = 0 OR pharmacyId = 1;

DECLARE @updatedCount INT = @@ROWCOUNT;
PRINT '✅ تم تحديث ' + CAST(@updatedCount AS VARCHAR(10)) + ' مستخدم';

-- 4. إنشاء مستخدم تجريبي محدث
PRINT '';
PRINT '4. إنشاء مستخدم تجريبي...';

-- حذف المستخدم التجريبي القديم إن وجد
DELETE FROM users WHERE username = 'testuser';

-- إنشاء مستخدم تجريبي جديد
INSERT INTO users (pharmacyId, userRole, name, dob, mobile, email, username, pass, isActive)
VALUES (@defaultPharmacyId, 'Administrator', N'مستخدم تجريبي', '1990-01-01', 1234567890, '<EMAIL>', 'testuser', 'testpass', 1);

PRINT '✅ تم إنشاء مستخدم تجريبي جديد: testuser / testpass';

-- 5. اختبار تسجيل الدخول
PRINT '';
PRINT '5. اختبار تسجيل الدخول...';

-- اختبار الاستعلام الأساسي
SELECT 
    u.id,
    u.username,
    u.name,
    u.userRole,
    u.pharmacyId,
    p.pharmacyName,
    p.pharmacyCode,
    u.isActive
FROM users u
INNER JOIN pharmacies p ON u.pharmacyId = p.id
WHERE u.username = 'testuser' 
  AND u.pass = 'testpass' 
  AND u.pharmacyId = @defaultPharmacyId 
  AND u.isActive = 1;

-- 6. عرض ملخص النظام
PRINT '';
PRINT '6. ملخص النظام:';

DECLARE @totalUsers INT, @totalPharmacies INT;
SELECT @totalUsers = COUNT(*) FROM users WHERE isActive = 1;
SELECT @totalPharmacies = COUNT(*) FROM pharmacies WHERE isActive = 1;

PRINT 'عدد المستخدمين النشطين: ' + CAST(@totalUsers AS VARCHAR(10));
PRINT 'عدد الصيدليات النشطة: ' + CAST(@totalPharmacies AS VARCHAR(10));

-- عرض جميع المستخدمين مع صيدلياتهم
PRINT '';
PRINT 'جميع المستخدمين النشطين:';
SELECT 
    u.id,
    u.username,
    u.name,
    u.userRole,
    p.pharmacyName,
    p.pharmacyCode
FROM users u
INNER JOIN pharmacies p ON u.pharmacyId = p.id
WHERE u.isActive = 1
ORDER BY u.id;

-- 7. التحقق من جدول الجلسات
PRINT '';
PRINT '7. التحقق من جدول جلسات الموظفين...';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'employee_sessions')
BEGIN
    CREATE TABLE employee_sessions (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyId INT NOT NULL,
        username VARCHAR(255) NOT NULL,
        employeeName NVARCHAR(255) NOT NULL,
        loginTime DATETIME NOT NULL,
        logoutTime DATETIME NULL,
        sessionDate DATE NOT NULL,
        createdAt DATETIME DEFAULT GETDATE()
    );
    PRINT '✅ تم إنشاء جدول employee_sessions';
END
ELSE
BEGIN
    PRINT '✅ جدول employee_sessions موجود';
END

PRINT '';
PRINT '========================================';
PRINT '   ✅ تم التأكد من قاعدة البيانات الموحدة!';
PRINT '========================================';
PRINT '';
PRINT 'النظام جاهز للاستخدام:';
PRINT '• تسجيل الدخول: testuser / testpass';
PRINT '• إنشاء حسابات جديدة متاح';
PRINT '• جميع العمليات تتم في قاعدة البيانات الموحدة';
PRINT '• تتبع جلسات المستخدمين متاح';
PRINT '';
