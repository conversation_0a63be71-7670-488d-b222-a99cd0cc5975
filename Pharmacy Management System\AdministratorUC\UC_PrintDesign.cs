using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Drawing.Printing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.Win32;
using DGVPrinterHelper;

namespace Pharmacy_Management_System.AdministratorUC
{
    public partial class UC_PrintDesign : UserControl
    {
        private const string REGISTRY_KEY = @"SOFTWARE\PharmacyManagementSystem\PrintSettings";
        // تم التحويل من pharmacy إلى UnifiedPharmacy - إلغاء استخدام قاعدة البيانات القديمة
        string cs = @"data source = NARUTO; database=UnifiedPharmacy; integrated security =True";

        // ألوان الوضع المظلم
        private static readonly Color DarkBackColor = Color.FromArgb(45, 45, 48);
        private static readonly Color DarkForeColor = Color.White;
        private static readonly Color DarkButtonColor = Color.FromArgb(62, 62, 66);
        private static readonly Color DarkBorderColor = Color.FromArgb(85, 85, 85);

        // ألوان الوضع الفاتح
        private static readonly Color LightBackColor = Color.White;
        private static readonly Color LightForeColor = Color.Black;
        private static readonly Color LightButtonColor = Color.FromArgb(240, 240, 240);
        private static readonly Color LightBorderColor = Color.FromArgb(200, 200, 200);

        public UC_PrintDesign()
        {
            InitializeComponent();
            ApplyLanguage();

            // التحقق من وجود جدول إعدادات الطباعة وإنشاؤه إذا لم يكن موجوداً
            EnsurePrintSettingsTableExists();

            // تعيين القيمة الافتراضية لنوع التقرير
            if (cmbReportType.Items.Count > 0)
                cmbReportType.SelectedIndex = 0;

            LoadPrintSettings();

            // ربط الأحداث لتحديث المعاينة
            AttachEventHandlers();

            // الاشتراك في تغيير اللغة
            LanguageManager.LanguageChanged += OnLanguageChanged;
        }

        private void UC_PrintDesign_Load(object sender, EventArgs e)
        {
            // تحميل الإعدادات المحفوظة
            LoadPrintSettings();
        }

        private void EnsurePrintSettingsTableExists()
        {
            try
            {
                using (SqlConnection con = new SqlConnection(cs))
                {
                    con.Open();

                    // التحقق من وجود الجدول
                    string checkTableQuery = @"
                        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='print_settings' AND xtype='U')
                        BEGIN
                            SELECT 0 AS TableExists
                        END
                        ELSE
                        BEGIN
                            SELECT 1 AS TableExists
                        END";

                    using (SqlCommand checkCmd = new SqlCommand(checkTableQuery, con))
                    {
                        int tableExists = (int)checkCmd.ExecuteScalar();

                        if (tableExists == 0)
                        {
                            // إنشاء الجدول
                            string createTableQuery = @"
                                CREATE TABLE print_settings (
                                    id INT IDENTITY(1,1) PRIMARY KEY,
                                    reportType VARCHAR(100) NOT NULL,
                                    paperSize VARCHAR(50) DEFAULT 'A4',
                                    orientation VARCHAR(50) DEFAULT 'عمودي',
                                    marginTop INT DEFAULT 20,
                                    marginBottom INT DEFAULT 20,
                                    marginLeft INT DEFAULT 15,
                                    marginRight INT DEFAULT 15,
                                    titleText VARCHAR(500) DEFAULT 'تقرير الصيدلية',
                                    titleFont INT DEFAULT 18,
                                    titleAlignment VARCHAR(50) DEFAULT 'وسط',
                                    showDateTime BIT DEFAULT 1,
                                    dateFormat VARCHAR(50) DEFAULT 'dd/MM/yyyy',
                                    datePosition VARCHAR(50) DEFAULT 'أعلى يمين',
                                    tableFont INT DEFAULT 10,
                                    borderWidth INT DEFAULT 1,
                                    footerText VARCHAR(500) DEFAULT 'نظام إدارة الصيدلية',
                                    showPageNumbers BIT DEFAULT 1,
                                    titleColor INT DEFAULT -16777216,
                                    tableHeaderColor INT DEFAULT -3355444,
                                    tableTextColor INT DEFAULT -16777216,
                                    createdDate DATETIME DEFAULT GETDATE(),
                                    lastModified DATETIME DEFAULT GETDATE(),
                                    UNIQUE(reportType)
                                );";

                            using (SqlCommand createCmd = new SqlCommand(createTableQuery, con))
                            {
                                createCmd.ExecuteNonQuery();
                            }

                            // إدراج البيانات الافتراضية
                            string insertDataQuery = @"
                                INSERT INTO print_settings (reportType, titleText) VALUES
                                ('عام', 'تقرير الصيدلية'),
                                ('مبيعات الأدوية', 'تقرير مبيعات الأدوية'),
                                ('تقرير المبيعات', 'تقرير المبيعات'),
                                ('جلسات الموظفين', 'تقرير جلسات الموظفين'),
                                ('جرد الأدوية', 'تقرير جرد الأدوية'),
                                ('صلاحية الأدوية', 'تقرير صلاحية الأدوية');";

                            using (SqlCommand insertCmd = new SqlCommand(insertDataQuery, con))
                            {
                                insertCmd.ExecuteNonQuery();
                            }

                            System.Diagnostics.Debug.WriteLine("تم إنشاء جدول print_settings وإدراج البيانات الافتراضية بنجاح");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في التحقق من جدول print_settings: " + ex.Message);
                // في حالة الخطأ، سيتم استخدام الريجستري كبديل
            }
        }

        private void ApplyLanguage()
        {
            bool isArabic = LanguageManager.CurrentLanguage == "ar";

            if (isArabic)
            {
                // تطبيق الترجمة العربية
                lblTitle.Text = "تصميم صفحات الطباعة";

                // مجموعة الإعدادات العامة
                groupBoxGeneral.Text = "الإعدادات العامة";
                lblPaperSize.Text = "حجم الورق:";
                lblOrientation.Text = "اتجاه الطباعة:";

                // مجموعة الهوامش
                groupBoxMargins.Text = "الهوامش";
                lblMarginTop.Text = "الهامش العلوي:";
                lblMarginBottom.Text = "الهامش السفلي:";
                lblMarginLeft.Text = "الهامش الأيسر:";
                lblMarginRight.Text = "الهامش الأيمن:";

                // مجموعة العنوان
                groupBoxTitle.Text = "إعدادات العنوان";
                lblTitleText.Text = "نص العنوان:";
                lblTitleFont.Text = "حجم خط العنوان:";
                lblTitleColor.Text = "لون العنوان:";
                lblTitleAlignment.Text = "محاذاة العنوان:";

                // مجموعة التاريخ
                groupBoxDateTime.Text = "إعدادات التاريخ والوقت";
                chkShowDateTime.Text = "عرض التاريخ والوقت";
                lblDateFormat.Text = "تنسيق التاريخ:";
                lblDatePosition.Text = "موضع التاريخ:";

                // مجموعة الجدول
                groupBoxTable.Text = "إعدادات الجدول";
                lblTableFont.Text = "حجم خط الجدول:";
                lblHeaderColor.Text = "لون خلفية الرأس:";
                lblTextColor.Text = "لون النص:";
                lblBorderWidth.Text = "عرض الحدود:";

                // مجموعة التذييل
                groupBoxFooter.Text = "إعدادات التذييل";
                lblFooterText.Text = "نص التذييل:";
                chkShowPageNumbers.Text = "عرض أرقام الصفحات";

                // الأزرار
                btnSave.Text = "حفظ الإعدادات";
                btnReset.Text = "إعادة تعيين";
                btnPreview.Text = "معاينة";
                btnApplyToAll.Text = "تطبيق على جميع التقارير";

                // أزرار الألوان
                btnTitleColor.Text = "اختيار اللون";
                btnHeaderColor.Text = "اختيار اللون";
                btnTextColor.Text = "اختيار اللون";

                // نوع التقرير
                lblReportType.Text = "نوع التقرير:";
            }
            else
            {
                // تطبيق الترجمة الإنجليزية
                lblTitle.Text = "Print Page Design";

                // مجموعة الإعدادات العامة
                groupBoxGeneral.Text = "General Settings";
                lblPaperSize.Text = "Paper Size:";
                lblOrientation.Text = "Orientation:";

                // مجموعة الهوامش
                groupBoxMargins.Text = "Margins";
                lblMarginTop.Text = "Top Margin:";
                lblMarginBottom.Text = "Bottom Margin:";
                lblMarginLeft.Text = "Left Margin:";
                lblMarginRight.Text = "Right Margin:";

                // مجموعة العنوان
                groupBoxTitle.Text = "Title Settings";
                lblTitleText.Text = "Title Text:";
                lblTitleFont.Text = "Title Font Size:";
                lblTitleColor.Text = "Title Color:";
                lblTitleAlignment.Text = "Title Alignment:";

                // مجموعة التاريخ
                groupBoxDateTime.Text = "Date & Time Settings";
                chkShowDateTime.Text = "Show Date & Time";
                lblDateFormat.Text = "Date Format:";
                lblDatePosition.Text = "Date Position:";

                // مجموعة الجدول
                groupBoxTable.Text = "Table Settings";
                lblTableFont.Text = "Table Font Size:";
                lblHeaderColor.Text = "Header Background Color:";
                lblTextColor.Text = "Text Color:";
                lblBorderWidth.Text = "Border Width:";

                // مجموعة التذييل
                groupBoxFooter.Text = "Footer Settings";
                lblFooterText.Text = "Footer Text:";
                chkShowPageNumbers.Text = "Show Page Numbers";

                // الأزرار
                btnSave.Text = "Save Settings";
                btnReset.Text = "Reset";
                btnPreview.Text = "Preview";
                btnApplyToAll.Text = "Apply to All Reports";

                // أزرار الألوان
                btnTitleColor.Text = "Choose Color";
                btnHeaderColor.Text = "Choose Color";
                btnTextColor.Text = "Choose Color";

                // نوع التقرير
                lblReportType.Text = "Report Type:";

                // تحديث عناصر ComboBox
                UpdateComboBoxItems();
            }

            // تحديث عناصر ComboBox
            UpdateComboBoxItems();
        }

        private void UpdateComboBoxItems()
        {
            bool isArabic = LanguageManager.CurrentLanguage == "ar";

            // تحديث عناصر حجم الورق
            if (cmbPaperSize != null)
            {
                string selectedPaper = cmbPaperSize.SelectedItem?.ToString();
                cmbPaperSize.Items.Clear();
                if (isArabic)
                {
                    cmbPaperSize.Items.AddRange(new[] { "A4", "A3", "Letter", "Legal" });
                }
                else
                {
                    cmbPaperSize.Items.AddRange(new[] { "A4", "A3", "Letter", "Legal" });
                }
                if (!string.IsNullOrEmpty(selectedPaper) && cmbPaperSize.Items.Contains(selectedPaper))
                    cmbPaperSize.SelectedItem = selectedPaper;
                else if (cmbPaperSize.Items.Count > 0)
                    cmbPaperSize.SelectedIndex = 0;
            }

            // تحديث عناصر الاتجاه
            if (cmbOrientation != null)
            {
                string selectedOrientation = cmbOrientation.SelectedItem?.ToString();
                cmbOrientation.Items.Clear();
                if (isArabic)
                {
                    cmbOrientation.Items.AddRange(new[] { "عمودي", "أفقي" });
                }
                else
                {
                    cmbOrientation.Items.AddRange(new[] { "Portrait", "Landscape" });
                }
                if (!string.IsNullOrEmpty(selectedOrientation) && cmbOrientation.Items.Contains(selectedOrientation))
                    cmbOrientation.SelectedItem = selectedOrientation;
                else if (cmbOrientation.Items.Count > 0)
                    cmbOrientation.SelectedIndex = 0;
            }

            // تحديث عناصر محاذاة العنوان
            if (cmbTitleAlignment != null)
            {
                string selectedAlignment = cmbTitleAlignment.SelectedItem?.ToString();
                cmbTitleAlignment.Items.Clear();
                if (isArabic)
                {
                    cmbTitleAlignment.Items.AddRange(new[] { "يسار", "وسط", "يمين" });
                }
                else
                {
                    cmbTitleAlignment.Items.AddRange(new[] { "Left", "Center", "Right" });
                }
                if (!string.IsNullOrEmpty(selectedAlignment) && cmbTitleAlignment.Items.Contains(selectedAlignment))
                    cmbTitleAlignment.SelectedItem = selectedAlignment;
                else if (cmbTitleAlignment.Items.Count > 0)
                    cmbTitleAlignment.SelectedIndex = 1; // وسط/Center
            }

            // تحديث عناصر تنسيق التاريخ
            if (cmbDateFormat != null)
            {
                string selectedFormat = cmbDateFormat.SelectedItem?.ToString();
                cmbDateFormat.Items.Clear();
                cmbDateFormat.Items.AddRange(new[] { "dd/MM/yyyy", "MM/dd/yyyy", "yyyy-MM-dd", "dd-MM-yyyy" });
                if (!string.IsNullOrEmpty(selectedFormat) && cmbDateFormat.Items.Contains(selectedFormat))
                    cmbDateFormat.SelectedItem = selectedFormat;
                else if (cmbDateFormat.Items.Count > 0)
                    cmbDateFormat.SelectedIndex = 0;
            }

            // تحديث عناصر موضع التاريخ
            if (cmbDatePosition != null)
            {
                string selectedPosition = cmbDatePosition.SelectedItem?.ToString();
                cmbDatePosition.Items.Clear();
                if (isArabic)
                {
                    cmbDatePosition.Items.AddRange(new[] { "أعلى اليسار", "أعلى اليمين", "أسفل اليسار", "أسفل اليمين" });
                }
                else
                {
                    cmbDatePosition.Items.AddRange(new[] { "Top Left", "Top Right", "Bottom Left", "Bottom Right" });
                }
                if (!string.IsNullOrEmpty(selectedPosition) && cmbDatePosition.Items.Contains(selectedPosition))
                    cmbDatePosition.SelectedItem = selectedPosition;
                else if (cmbDatePosition.Items.Count > 0)
                    cmbDatePosition.SelectedIndex = 0;
            }

            // تحديث عناصر نوع التقرير
            if (cmbReportType != null)
            {
                string selectedReport = cmbReportType.SelectedItem?.ToString();
                cmbReportType.Items.Clear();
                if (isArabic)
                {
                    cmbReportType.Items.AddRange(new[] { "عام", "تقرير المبيعات", "جرد الأدوية", "جلسات الموظفين", "مبيعات الأدوية" });
                }
                else
                {
                    cmbReportType.Items.AddRange(new[] { "General", "Sales Report", "Medicine Inventory", "Employee Sessions", "Medicine Sales" });
                }
                if (!string.IsNullOrEmpty(selectedReport) && cmbReportType.Items.Contains(selectedReport))
                    cmbReportType.SelectedItem = selectedReport;
                else if (cmbReportType.Items.Count > 0)
                    cmbReportType.SelectedIndex = 0;
            }
        }

        private void LoadPrintSettings()
        {
            if (cmbReportType == null) return;

            string selectedReport = cmbReportType.SelectedItem?.ToString() ?? "عام";
            LoadPrintSettingsForReport(selectedReport);
        }

        private void LoadPrintSettingsForReport(string reportType)
        {
            try
            {
                var settings = GetPrintSettingsForReport(reportType);

                // تحميل الإعدادات العامة
                if (cmbPaperSize != null)
                    cmbPaperSize.SelectedItem = settings.PaperSize;
                if (cmbOrientation != null)
                    cmbOrientation.SelectedItem = settings.Orientation;

                // تحميل الهوامش
                if (numMarginTop != null)
                    numMarginTop.Value = settings.MarginTop;
                if (numMarginBottom != null)
                    numMarginBottom.Value = settings.MarginBottom;
                if (numMarginLeft != null)
                    numMarginLeft.Value = settings.MarginLeft;
                if (numMarginRight != null)
                    numMarginRight.Value = settings.MarginRight;

                // تحميل إعدادات العنوان
                if (txtTitleText != null)
                    txtTitleText.Text = settings.TitleText;
                if (numTitleFont != null)
                    numTitleFont.Value = settings.TitleFont;
                if (cmbTitleAlignment != null)
                    cmbTitleAlignment.SelectedItem = settings.TitleAlignment;

                // تحميل إعدادات التاريخ
                if (chkShowDateTime != null)
                    chkShowDateTime.Checked = settings.ShowDateTime;
                if (cmbDateFormat != null)
                    cmbDateFormat.SelectedItem = settings.DateFormat;
                if (cmbDatePosition != null)
                    cmbDatePosition.SelectedItem = settings.DatePosition;

                // تحميل إعدادات الجدول
                if (numTableFont != null)
                    numTableFont.Value = settings.TableFont;
                if (numBorderWidth != null)
                    numBorderWidth.Value = settings.BorderWidth;

                // تحميل إعدادات التذييل
                if (txtFooterText != null)
                    txtFooterText.Text = settings.FooterText;
                if (chkShowPageNumbers != null)
                    chkShowPageNumbers.Checked = settings.ShowPageNumbers;

                // تحميل الألوان
                if (btnTitleColor != null)
                {
                    btnTitleColor.BackColor = settings.TitleColor;
                    btnTitleColor.ForeColor = GetContrastColor(settings.TitleColor);
                }
                if (btnHeaderColor != null)
                {
                    btnHeaderColor.BackColor = settings.TableHeaderColor;
                    btnHeaderColor.ForeColor = GetContrastColor(settings.TableHeaderColor);
                }
                if (btnTextColor != null)
                {
                    btnTextColor.BackColor = settings.TableTextColor;
                    btnTextColor.ForeColor = GetContrastColor(settings.TableTextColor);
                }

                // تحديث المعاينة
                UpdatePreview();
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Error loading settings") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
                SetDefaultValues();
            }
        }

        private void SetDefaultValues()
        {
            // القيم الافتراضية
            cmbPaperSize.SelectedItem = "A4";
            cmbOrientation.SelectedItem = "عمودي";
            
            numMarginTop.Value = 20;
            numMarginBottom.Value = 20;
            numMarginLeft.Value = 15;
            numMarginRight.Value = 15;
            
            txtTitleText.Text = "تقرير الصيدلية";
            numTitleFont.Value = 18;
            cmbTitleAlignment.SelectedItem = "وسط";
            
            chkShowDateTime.Checked = true;
            cmbDateFormat.SelectedItem = "dd/MM/yyyy";
            cmbDatePosition.SelectedItem = "أعلى يمين";
            
            numTableFont.Value = 10;
            numBorderWidth.Value = 1;
            
            txtFooterText.Text = "نظام إدارة الصيدلية";
            chkShowPageNumbers.Checked = true;
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                SavePrintSettings();

                // إعادة تعيين الكاش لضمان تحديث البيانات
                ClearPreviewCache();

                MessageBox.Show(LanguageManager.GetText("Settings saved successfully") + "!", LanguageManager.GetText("Success"), MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Error saving settings") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // دالة لمسح الكاش
        private void ClearPreviewCache()
        {
            cachedPreviewData = null;
            cachedReportType = "";
            lastCacheTime = DateTime.MinValue;
        }

        private void SavePrintSettings()
        {
            try
            {
                string selectedReport = cmbReportType?.SelectedItem?.ToString() ?? "عام";

                // حفظ في قاعدة البيانات أولاً
                SavePrintSettingsToDatabase(selectedReport);

                // حفظ في الريجستري كنسخة احتياطية
                SavePrintSettingsToRegistry(selectedReport);

                // تحديث المعاينة
                UpdatePreview();
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Error saving settings") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SavePrintSettingsToDatabase(string reportType)
        {
            try
            {
                using (SqlConnection con = new SqlConnection(cs))
                {
                    con.Open();

                    // التحقق من وجود السجل
                    string checkQuery = "SELECT COUNT(*) FROM print_settings WHERE reportType = @reportType";
                    using (SqlCommand checkCmd = new SqlCommand(checkQuery, con))
                    {
                        checkCmd.Parameters.AddWithValue("@reportType", reportType);
                        int count = (int)checkCmd.ExecuteScalar();

                        string query;
                        if (count > 0)
                        {
                            // تحديث السجل الموجود
                            query = @"UPDATE print_settings SET
                                paperSize = @paperSize, orientation = @orientation,
                                marginTop = @marginTop, marginBottom = @marginBottom,
                                marginLeft = @marginLeft, marginRight = @marginRight,
                                titleText = @titleText, titleFont = @titleFont, titleAlignment = @titleAlignment,
                                showDateTime = @showDateTime, dateFormat = @dateFormat, datePosition = @datePosition,
                                tableFont = @tableFont, borderWidth = @borderWidth,
                                footerText = @footerText, showPageNumbers = @showPageNumbers,
                                titleColor = @titleColor, tableHeaderColor = @tableHeaderColor, tableTextColor = @tableTextColor,
                                lastModified = GETDATE()
                                WHERE reportType = @reportType";
                        }
                        else
                        {
                            // إدراج سجل جديد
                            query = @"INSERT INTO print_settings
                                (reportType, paperSize, orientation, marginTop, marginBottom, marginLeft, marginRight,
                                titleText, titleFont, titleAlignment, showDateTime, dateFormat, datePosition,
                                tableFont, borderWidth, footerText, showPageNumbers,
                                titleColor, tableHeaderColor, tableTextColor)
                                VALUES
                                (@reportType, @paperSize, @orientation, @marginTop, @marginBottom, @marginLeft, @marginRight,
                                @titleText, @titleFont, @titleAlignment, @showDateTime, @dateFormat, @datePosition,
                                @tableFont, @borderWidth, @footerText, @showPageNumbers,
                                @titleColor, @tableHeaderColor, @tableTextColor)";
                        }

                        using (SqlCommand cmd = new SqlCommand(query, con))
                        {
                            cmd.Parameters.AddWithValue("@reportType", reportType);
                            cmd.Parameters.AddWithValue("@paperSize", cmbPaperSize.SelectedItem?.ToString() ?? "A4");
                            cmd.Parameters.AddWithValue("@orientation", cmbOrientation.SelectedItem?.ToString() ?? "عمودي");
                            cmd.Parameters.AddWithValue("@marginTop", (int)numMarginTop.Value);
                            cmd.Parameters.AddWithValue("@marginBottom", (int)numMarginBottom.Value);
                            cmd.Parameters.AddWithValue("@marginLeft", (int)numMarginLeft.Value);
                            cmd.Parameters.AddWithValue("@marginRight", (int)numMarginRight.Value);
                            cmd.Parameters.AddWithValue("@titleText", txtTitleText.Text);
                            cmd.Parameters.AddWithValue("@titleFont", (int)numTitleFont.Value);
                            cmd.Parameters.AddWithValue("@titleAlignment", cmbTitleAlignment.SelectedItem?.ToString() ?? "وسط");
                            cmd.Parameters.AddWithValue("@showDateTime", chkShowDateTime.Checked);
                            cmd.Parameters.AddWithValue("@dateFormat", cmbDateFormat.SelectedItem?.ToString() ?? "dd/MM/yyyy");
                            cmd.Parameters.AddWithValue("@datePosition", cmbDatePosition.SelectedItem?.ToString() ?? "أعلى يمين");
                            cmd.Parameters.AddWithValue("@tableFont", (int)numTableFont.Value);
                            cmd.Parameters.AddWithValue("@borderWidth", (int)numBorderWidth.Value);
                            cmd.Parameters.AddWithValue("@footerText", txtFooterText.Text);
                            cmd.Parameters.AddWithValue("@showPageNumbers", chkShowPageNumbers.Checked);
                            cmd.Parameters.AddWithValue("@titleColor", btnTitleColor.BackColor.ToArgb());
                            cmd.Parameters.AddWithValue("@tableHeaderColor", btnHeaderColor.BackColor.ToArgb());
                            cmd.Parameters.AddWithValue("@tableTextColor", btnTextColor.BackColor.ToArgb());

                            cmd.ExecuteNonQuery();
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"تم حفظ إعدادات {reportType} في قاعدة البيانات بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الإعدادات في قاعدة البيانات: {ex.Message}");
                throw; // إعادة رمي الخطأ للمعالجة في المستوى الأعلى
            }
        }

        private void SavePrintSettingsToRegistry(string selectedReport)
        {
            try
            {
                string registryKey = REGISTRY_KEY + "\\" + selectedReport;

                using (RegistryKey key = Registry.CurrentUser.CreateSubKey(registryKey))
                {
                    // حفظ الإعدادات العامة
                    key.SetValue("PaperSize", cmbPaperSize.SelectedItem?.ToString() ?? "A4");
                    key.SetValue("Orientation", cmbOrientation.SelectedItem?.ToString() ?? "عمودي");

                    // حفظ الهوامش
                    key.SetValue("MarginTop", numMarginTop.Value);
                    key.SetValue("MarginBottom", numMarginBottom.Value);
                    key.SetValue("MarginLeft", numMarginLeft.Value);
                    key.SetValue("MarginRight", numMarginRight.Value);

                    // حفظ إعدادات العنوان
                    key.SetValue("TitleText", txtTitleText.Text);
                    key.SetValue("TitleFont", numTitleFont.Value);
                    key.SetValue("TitleAlignment", cmbTitleAlignment.SelectedItem?.ToString() ?? "وسط");

                    // حفظ إعدادات التاريخ
                    key.SetValue("ShowDateTime", chkShowDateTime.Checked);
                    key.SetValue("DateFormat", cmbDateFormat.SelectedItem?.ToString() ?? "dd/MM/yyyy");
                    key.SetValue("DatePosition", cmbDatePosition.SelectedItem?.ToString() ?? "أعلى يمين");

                    // حفظ إعدادات الجدول
                    key.SetValue("TableFont", numTableFont.Value);
                    key.SetValue("BorderWidth", numBorderWidth.Value);

                    // حفظ إعدادات التذييل
                    key.SetValue("FooterText", txtFooterText.Text);
                    key.SetValue("ShowPageNumbers", chkShowPageNumbers.Checked);

                    // حفظ الألوان
                    key.SetValue("TitleColor", btnTitleColor.BackColor.ToArgb());
                    key.SetValue("TableHeaderColor", btnHeaderColor.BackColor.ToArgb());
                    key.SetValue("TableTextColor", btnTextColor.BackColor.ToArgb());
                }

                System.Diagnostics.Debug.WriteLine($"تم حفظ إعدادات {selectedReport} في الريجستري كنسخة احتياطية");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الإعدادات في الريجستري: {ex.Message}");
                // لا نرمي الخطأ هنا لأن الريجستري نسخة احتياطية فقط
            }
        }

        private void btnReset_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show(LanguageManager.GetText("Reset all settings to default"), LanguageManager.GetText("Confirm"),
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                SetDefaultValues();
                MessageBox.Show(LanguageManager.GetText("Settings reset") + "!", LanguageManager.GetText("Done"), MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void btnPreview_Click(object sender, EventArgs e)
        {
            // معاينة الطباعة
            ShowPrintPreview();
        }

        private void ShowPrintPreview()
        {
            try
            {
                PrintPreviewDialog previewDialog = new PrintPreviewDialog();
                PrintDocument printDoc = new PrintDocument();
                printDoc.PrintPage += PrintDoc_PrintPage;
                
                previewDialog.Document = printDoc;
                previewDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Preview error") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PrintDoc_PrintPage(object sender, PrintPageEventArgs e)
        {
            // رسم معاينة للطباعة بالإعدادات الحالية
            Graphics g = e.Graphics;
            
            // رسم العنوان
            Font titleFont = new Font("Arial", (float)numTitleFont.Value, FontStyle.Bold);
            string titleText = txtTitleText.Text;
            SizeF titleSize = g.MeasureString(titleText, titleFont);

            float titleX = 0;
            if (cmbTitleAlignment.SelectedItem?.ToString() == "وسط")
                titleX = (e.PageBounds.Width - titleSize.Width) / 2;
            else if (cmbTitleAlignment.SelectedItem?.ToString() == "يمين")
                titleX = e.PageBounds.Width - titleSize.Width - (float)numMarginRight.Value;
            else
                titleX = (float)numMarginLeft.Value;

            // استخدام لون العنوان المحدد
            Brush titleBrush = new SolidBrush(btnTitleColor.BackColor);
            g.DrawString(titleText, titleFont, titleBrush, titleX, (float)numMarginTop.Value);
            
            // رسم التاريخ إذا كان مفعلاً
            if (chkShowDateTime.Checked)
            {
                Font dateFont = new Font("Arial", 10);
                string dateText = DateTime.Now.ToString(cmbDateFormat.SelectedItem?.ToString() ?? "dd/MM/yyyy");
                g.DrawString(dateText, dateFont, Brushes.Black, (float)numMarginLeft.Value, (float)numMarginTop.Value + 30);
            }
            
            // رسم جدول تجريبي
            Font tableFont = new Font("Arial", (float)numTableFont.Value);
            Pen borderPen = new Pen(Color.Black, (float)numBorderWidth.Value);
            
            float tableY = (float)numMarginTop.Value + 80;
            float tableX = (float)numMarginLeft.Value;
            float cellWidth = 100;
            float cellHeight = 25;
            
            // رسم رأس الجدول مع الألوان المحددة
            Brush headerBrush = new SolidBrush(btnHeaderColor.BackColor);
            Brush textBrush = new SolidBrush(btnTextColor.BackColor);

            g.FillRectangle(headerBrush, tableX, tableY, cellWidth * 3, cellHeight);
            g.DrawRectangle(borderPen, tableX, tableY, cellWidth * 3, cellHeight);
            g.DrawString("عمود 1", tableFont, textBrush, tableX + 5, tableY + 5);
            g.DrawString("عمود 2", tableFont, textBrush, tableX + cellWidth + 5, tableY + 5);
            g.DrawString("عمود 3", tableFont, textBrush, tableX + cellWidth * 2 + 5, tableY + 5);
            
            // رسم صفوف الجدول
            for (int i = 1; i <= 3; i++)
            {
                float rowY = tableY + cellHeight * i;
                g.DrawRectangle(borderPen, tableX, rowY, cellWidth * 3, cellHeight);
                g.DrawString(string.Format("بيانات {0}", i), tableFont, textBrush, tableX + 5, rowY + 5);
                g.DrawString(string.Format("قيمة {0}", i), tableFont, textBrush, tableX + cellWidth + 5, rowY + 5);
                g.DrawString(string.Format("نتيجة {0}", i), tableFont, textBrush, tableX + cellWidth * 2 + 5, rowY + 5);
            }
            
            // رسم التذييل
            Font footerFont = new Font("Arial", 9);
            string footerText = txtFooterText.Text;
            if (chkShowPageNumbers.Checked)
                footerText += " - صفحة 1";
                
            g.DrawString(footerText, footerFont, Brushes.Black, 
                (float)numMarginLeft.Value, e.PageBounds.Height - (float)numMarginBottom.Value - 20);
        }

        private void btnApplyToAll_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل تريد تطبيق هذه الإعدادات على جميع تقارير النظام؟", "تأكيد",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                // حفظ الإعدادات لجميع أنواع التقارير
                string[] reportTypes = { "عام", "مبيعات الأدوية", "تقرير المبيعات", "جلسات الموظفين", "جرد الأدوية", "صلاحية الأدوية" };

                foreach (string reportType in reportTypes)
                {
                    SavePrintSettingsForReport(reportType);
                }

                MessageBox.Show(LanguageManager.GetText("Settings applied to all reports") + "!", LanguageManager.GetText("Done"), MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void SavePrintSettingsForReport(string reportType)
        {
            try
            {
                string registryKey = REGISTRY_KEY + "\\" + reportType;

                using (RegistryKey key = Registry.CurrentUser.CreateSubKey(registryKey))
                {
                    // حفظ الإعدادات العامة
                    key.SetValue("PaperSize", cmbPaperSize.SelectedItem?.ToString() ?? "A4");
                    key.SetValue("Orientation", cmbOrientation.SelectedItem?.ToString() ?? "عمودي");

                    // حفظ الهوامش
                    key.SetValue("MarginTop", numMarginTop.Value);
                    key.SetValue("MarginBottom", numMarginBottom.Value);
                    key.SetValue("MarginLeft", numMarginLeft.Value);
                    key.SetValue("MarginRight", numMarginRight.Value);

                    // حفظ إعدادات العنوان (مع الاحتفاظ بالعنوان المناسب لكل تقرير)
                    string titleText = reportType == (cmbReportType?.SelectedItem?.ToString() ?? "عام")
                        ? txtTitleText.Text
                        : GetDefaultTitle(reportType);
                    key.SetValue("TitleText", titleText);
                    key.SetValue("TitleFont", numTitleFont.Value);
                    key.SetValue("TitleAlignment", cmbTitleAlignment.SelectedItem?.ToString() ?? "وسط");

                    // حفظ إعدادات التاريخ
                    key.SetValue("ShowDateTime", chkShowDateTime.Checked);
                    key.SetValue("DateFormat", cmbDateFormat.SelectedItem?.ToString() ?? "dd/MM/yyyy");
                    key.SetValue("DatePosition", cmbDatePosition.SelectedItem?.ToString() ?? "أعلى يمين");

                    // حفظ إعدادات الجدول
                    key.SetValue("TableFont", numTableFont.Value);
                    key.SetValue("BorderWidth", numBorderWidth.Value);

                    // حفظ إعدادات التذييل
                    key.SetValue("FooterText", txtFooterText.Text);
                    key.SetValue("ShowPageNumbers", chkShowPageNumbers.Checked);

                    // حفظ الألوان بصيغة ARGB
                    key.SetValue("TitleColor", btnTitleColor.BackColor.ToArgb());
                    key.SetValue("TableHeaderColor", btnHeaderColor.BackColor.ToArgb());
                    key.SetValue("TableTextColor", btnTextColor.BackColor.ToArgb());
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Error saving report settings") + $" {reportType}: " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdatePreview()
        {
            // منع التحديث المتعدد المتزامن
            if (isUpdatingPreview) return;

            if (previewPanel != null && previewPanel.Visible)
            {
                isUpdatingPreview = true;
                try
                {
                    // تأخير التحديث لتحسين الأداء
                    if (previewUpdateTimer != null)
                    {
                        previewUpdateTimer.Stop();
                        previewUpdateTimer.Dispose();
                    }

                    previewUpdateTimer = new System.Windows.Forms.Timer();
                    previewUpdateTimer.Interval = 300; // تأخير 300 مللي ثانية
                    previewUpdateTimer.Tick += (s, e) =>
                    {
                        previewUpdateTimer.Stop();
                        previewUpdateTimer.Dispose();
                        previewUpdateTimer = null;

                        if (previewPanel != null && !previewPanel.IsDisposed)
                        {
                            previewPanel.Invalidate(); // إعادة رسم المعاينة
                        }
                    };
                    previewUpdateTimer.Start();
                }
                finally
                {
                    isUpdatingPreview = false;
                }
            }
        }

        // متغير لحفظ البيانات المؤقتة لتحسين الأداء
        private static DataTable cachedPreviewData = null;
        private static string cachedReportType = "";
        private static DateTime lastCacheTime = DateTime.MinValue;
        private bool isUpdatingPreview = false;
        private System.Windows.Forms.Timer previewUpdateTimer = null;

        private DataTable GetPreviewDataForReport(string reportType)
        {
            try
            {
                // استخدام البيانات المحفوظة مؤقتاً إذا كانت حديثة (أقل من 5 دقائق)
                if (cachedPreviewData != null &&
                    cachedReportType == reportType &&
                    DateTime.Now.Subtract(lastCacheTime).TotalMinutes < 5)
                {
                    return cachedPreviewData;
                }

                string query = "";

                if (reportType == "تقرير المبيعات" || reportType == "Sales Report")
                {
                    query = @"SELECT TOP 3
                        u.name as 'اسم الموظف',
                        m.mname as 'اسم الدواء',
                        m.mnumber as 'رقم الدواء',
                        m.quantity as 'الكمية',
                        m.perUnit as 'سعر الوحدة',
                        (m.quantity * m.perUnit) as 'إجمالي السعر',
                        m.mDate as 'تاريخ البيع'
                    FROM medic m
                    LEFT JOIN users u ON m.lu = u.id
                    WHERE m.quantity > 0
                    ORDER BY m.mDate DESC";
                }
                else if (reportType == "جرد الأدوية" || reportType == "Medicine Inventory")
                {
                    query = @"SELECT TOP 3
                        mname as 'اسم الدواء',
                        mnumber as 'رقم الدواء',
                        quantity as 'الكمية المتاحة',
                        perUnit as 'سعر الوحدة',
                        mDate as 'تاريخ الإضافة',
                        eDate as 'تاريخ الانتهاء',
                        CASE WHEN dos2 IS NOT NULL THEN dos2 ELSE 'غير محدد' END as 'الجرعة'
                    FROM medic
                    WHERE quantity > 0
                    ORDER BY mname";
                }
                else if (reportType == "جلسات الموظفين" || reportType == "Employee Sessions")
                {
                    query = @"SELECT TOP 3
                        name as 'اسم الموظف',
                        userRole as 'الدور',
                        email as 'البريد الإلكتروني',
                        mobile as 'رقم الهاتف',
                        dob as 'تاريخ الميلاد'
                    FROM users
                    WHERE userRole != 'Administrator'
                    ORDER BY name";
                }
                else if (reportType == "مبيعات الأدوية" || reportType == "Medicine Sales")
                {
                    query = @"SELECT TOP 3
                        m.mname as 'اسم الدواء',
                        u.name as 'اسم المستخدم',
                        m.mnumber as 'رقم الدواء',
                        m.quantity as 'الكمية',
                        m.perUnit as 'سعر الوحدة',
                        (m.quantity * m.perUnit) as 'إجمالي السعر',
                        m.mDate as 'تاريخ البيع'
                    FROM medic m
                    LEFT JOIN users u ON m.lu = u.id
                    WHERE m.quantity > 0
                    ORDER BY m.mDate DESC";
                }
                else
                {
                    // تقرير عام - عرض بيانات مختلطة
                    query = @"SELECT TOP 3
                        'دواء' as 'النوع',
                        mname as 'الاسم',
                        CAST(quantity as NVARCHAR) as 'التفاصيل',
                        mDate as 'التاريخ'
                    FROM medic
                    WHERE quantity > 0
                    ORDER BY mDate DESC";
                }

                using (SqlConnection con = new SqlConnection(cs))
                {
                    SqlDataAdapter adapter = new SqlDataAdapter(query, con);
                    DataTable dt = new DataTable();
                    adapter.Fill(dt);

                    // حفظ البيانات مؤقتاً
                    cachedPreviewData = dt;
                    cachedReportType = reportType;
                    lastCacheTime = DateTime.Now;

                    return dt;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في جلب بيانات المعاينة: " + ex.Message);
                return CreateSampleData(reportType);
            }
        }

        private DataTable CreateSampleData(string reportType)
        {
            DataTable dt = new DataTable();

            if (reportType == "تقرير المبيعات" || reportType == "Sales Report")
            {
                dt.Columns.Add("اسم الموظف");
                dt.Columns.Add("اسم الدواء");
                dt.Columns.Add("رقم الدواء");
                dt.Columns.Add("الكمية");
                dt.Columns.Add("سعر الوحدة");
                dt.Columns.Add("إجمالي السعر");
                dt.Columns.Add("تاريخ البيع");

                dt.Rows.Add("أحمد محمد", "باراسيتامول", "12345", "10", "5.50", "55.00", DateTime.Now.ToString("yyyy-MM-dd"));
                dt.Rows.Add("فاطمة علي", "أسبرين", "12346", "5", "3.25", "16.25", DateTime.Now.ToString("yyyy-MM-dd"));
                dt.Rows.Add("محمد حسن", "فيتامين سي", "12347", "20", "12.00", "240.00", DateTime.Now.ToString("yyyy-MM-dd"));
            }
            else if (reportType == "جرد الأدوية" || reportType == "Medicine Inventory")
            {
                dt.Columns.Add("اسم الدواء");
                dt.Columns.Add("رقم الدواء");
                dt.Columns.Add("الكمية المتاحة");
                dt.Columns.Add("سعر الوحدة");
                dt.Columns.Add("تاريخ الإضافة");
                dt.Columns.Add("تاريخ الانتهاء");
                dt.Columns.Add("الجرعة");

                dt.Rows.Add("باراسيتامول", "12345", "100", "5.50", DateTime.Now.ToString("yyyy-MM-dd"), DateTime.Now.AddMonths(12).ToString("yyyy-MM-dd"), "500mg");
                dt.Rows.Add("أسبرين", "12346", "75", "3.25", DateTime.Now.ToString("yyyy-MM-dd"), DateTime.Now.AddMonths(18).ToString("yyyy-MM-dd"), "100mg");
                dt.Rows.Add("فيتامين سي", "12347", "200", "12.00", DateTime.Now.ToString("yyyy-MM-dd"), DateTime.Now.AddMonths(24).ToString("yyyy-MM-dd"), "1000mg");
            }
            else if (reportType == "جلسات الموظفين" || reportType == "Employee Sessions")
            {
                dt.Columns.Add("اسم الموظف");
                dt.Columns.Add("الدور");
                dt.Columns.Add("البريد الإلكتروني");
                dt.Columns.Add("رقم الهاتف");
                dt.Columns.Add("تاريخ الميلاد");

                dt.Rows.Add("أحمد محمد", "صيدلي", "<EMAIL>", "123456789", "1990-01-15");
                dt.Rows.Add("فاطمة علي", "مساعد صيدلي", "<EMAIL>", "987654321", "1992-05-20");
                dt.Rows.Add("محمد حسن", "صيدلي", "<EMAIL>", "555666777", "1988-12-10");
            }
            else if (reportType == "مبيعات الأدوية" || reportType == "Medicine Sales")
            {
                dt.Columns.Add("اسم الدواء");
                dt.Columns.Add("اسم المستخدم");
                dt.Columns.Add("رقم الدواء");
                dt.Columns.Add("الكمية");
                dt.Columns.Add("سعر الوحدة");
                dt.Columns.Add("إجمالي السعر");
                dt.Columns.Add("تاريخ البيع");

                dt.Rows.Add("باراسيتامول", "أحمد محمد", "12345", "10", "5.50", "55.00", DateTime.Now.ToString("yyyy-MM-dd"));
                dt.Rows.Add("أسبرين", "فاطمة علي", "12346", "5", "3.25", "16.25", DateTime.Now.ToString("yyyy-MM-dd"));
                dt.Rows.Add("فيتامين سي", "محمد حسن", "12347", "20", "12.00", "240.00", DateTime.Now.ToString("yyyy-MM-dd"));
            }
            else
            {
                dt.Columns.Add("النوع");
                dt.Columns.Add("الاسم");
                dt.Columns.Add("التفاصيل");
                dt.Columns.Add("التاريخ");

                dt.Rows.Add("دواء", "باراسيتامول", "100", DateTime.Now.ToString("yyyy-MM-dd"));
                dt.Rows.Add("دواء", "أسبرين", "75", DateTime.Now.ToString("yyyy-MM-dd"));
                dt.Rows.Add("دواء", "فيتامين سي", "200", DateTime.Now.ToString("yyyy-MM-dd"));
            }

            return dt;
        }

        private void DrawRealDataTable(Graphics g, Rectangle contentRect, ref float currentY)
        {
            try
            {
                if (numTableFont == null || numBorderWidth == null) return;

                // استخدام بيانات مبسطة للمعاينة لتحسين الأداء
                DataTable data = GetSimplifiedPreviewData();

                if (data == null || data.Rows.Count == 0) return;

                Font tableFont = new Font("Arial", (float)numTableFont.Value * 0.6f);
                Pen borderPen = new Pen(Color.Black, (float)numBorderWidth.Value);

                // حساب عرض الأعمدة بناءً على عدد الأعمدة الفعلي
                int columnCount = Math.Min(data.Columns.Count, 4); // تحديد عدد الأعمدة لتحسين الأداء
                float cellWidth = contentRect.Width / (float)columnCount;
                float cellHeight = 18;

                // رسم رأس الجدول مع الألوان المحددة
                Brush headerBrush = new SolidBrush(btnHeaderColor?.BackColor ?? Color.LightGray);
                Brush textBrush = new SolidBrush(btnTextColor?.BackColor ?? Color.Black);

                g.FillRectangle(headerBrush, contentRect.Left, currentY, contentRect.Width, cellHeight);
                g.DrawRectangle(borderPen, contentRect.Left, currentY, contentRect.Width, cellHeight);

                // رسم أسماء الأعمدة
                for (int i = 0; i < columnCount; i++)
                {
                    float x = contentRect.Left + i * cellWidth;
                    string headerText = data.Columns[i].ColumnName;

                    // قص النص إذا كان طويلاً
                    if (headerText.Length > 10)
                        headerText = headerText.Substring(0, 10) + "..";

                    g.DrawString(headerText, tableFont, textBrush, x + 2, currentY + 2);
                    if (i > 0) g.DrawLine(borderPen, x, currentY, x, currentY + cellHeight);
                }
                currentY += cellHeight;

                // رسم صفوف البيانات (أول 2 صفوف فقط للمعاينة لتحسين الأداء)
                int maxRows = Math.Min(data.Rows.Count, 2);
                for (int row = 0; row < maxRows; row++)
                {
                    g.DrawRectangle(borderPen, contentRect.Left, currentY, contentRect.Width, cellHeight);

                    for (int col = 0; col < columnCount; col++)
                    {
                        float x = contentRect.Left + col * cellWidth;
                        string cellText = data.Rows[row][col]?.ToString() ?? "";

                        // قص النص إذا كان طويلاً
                        if (cellText.Length > 12)
                            cellText = cellText.Substring(0, 12) + "..";

                        g.DrawString(cellText, tableFont, textBrush, x + 2, currentY + 2);
                        if (col > 0) g.DrawLine(borderPen, x, currentY, x, currentY + cellHeight);
                    }
                    currentY += cellHeight;
                }

                // إضافة نص يوضح أن هذه معاينة
                Font noteFont = new Font("Arial", 7, FontStyle.Italic);
                string noteText = "معاينة مبسطة للتصميم";
                g.DrawString(noteText, noteFont, Brushes.Gray, contentRect.Left, currentY + 5);
                currentY += 20;
                noteFont.Dispose();

                tableFont.Dispose();
                borderPen.Dispose();
                headerBrush.Dispose();
                textBrush.Dispose();
            }
            catch (Exception ex)
            {
                // في حالة الخطأ، ارسم رسالة
                g.DrawString("خطأ في عرض البيانات: " + ex.Message, SystemFonts.DefaultFont, Brushes.Red, contentRect.Left, currentY);
                currentY += 20;
            }
        }

        // دالة لإنشاء بيانات مبسطة للمعاينة لتحسين الأداء
        private DataTable GetSimplifiedPreviewData()
        {
            DataTable dt = new DataTable();

            // إضافة أعمدة مبسطة
            dt.Columns.Add("العنصر");
            dt.Columns.Add("القيمة");
            dt.Columns.Add("التاريخ");
            dt.Columns.Add("الحالة");

            // إضافة بيانات تجريبية
            dt.Rows.Add("عنصر 1", "100", DateTime.Now.ToString("dd/MM/yyyy"), "نشط");
            dt.Rows.Add("عنصر 2", "200", DateTime.Now.ToString("dd/MM/yyyy"), "مكتمل");

            return dt;
        }

        private void cmbReportType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbReportType.SelectedItem != null)
            {
                string newReportType = cmbReportType.SelectedItem.ToString();

                // مسح الكاش إذا تغير نوع التقرير
                if (cachedReportType != newReportType)
                {
                    ClearPreviewCache();
                }

                LoadPrintSettingsForReport(newReportType);
                UpdatePreview(); // تحديث المعاينة عند تغيير نوع التقرير
            }
        }

        // إضافة أحداث لتحديث المعاينة عند تغيير أي إعداد
        private void OnSettingChanged(object sender, EventArgs e)
        {
            UpdatePreview();
        }

        private void AttachEventHandlers()
        {
            // ربط جميع عناصر التحكم بحدث تحديث المعاينة مع تأخير لتحسين الأداء
            cmbReportType.SelectedIndexChanged += cmbReportType_SelectedIndexChanged;

            // استخدام Timer لتقليل عدد مرات تحديث المعاينة
            cmbPaperSize.SelectedIndexChanged += OnSettingChangedDelayed;
            cmbOrientation.SelectedIndexChanged += OnSettingChangedDelayed;
            numMarginTop.ValueChanged += OnSettingChangedDelayed;
            numMarginBottom.ValueChanged += OnSettingChangedDelayed;
            numMarginLeft.ValueChanged += OnSettingChangedDelayed;
            numMarginRight.ValueChanged += OnSettingChangedDelayed;
            txtTitleText.TextChanged += OnSettingChangedDelayed;
            numTitleFont.ValueChanged += OnSettingChangedDelayed;
            cmbTitleAlignment.SelectedIndexChanged += OnSettingChangedDelayed;
            chkShowDateTime.CheckedChanged += OnSettingChangedDelayed;
            cmbDateFormat.SelectedIndexChanged += OnSettingChangedDelayed;
            cmbDatePosition.SelectedIndexChanged += OnSettingChangedDelayed;
            numTableFont.ValueChanged += OnSettingChangedDelayed;
            numBorderWidth.ValueChanged += OnSettingChangedDelayed;
            txtFooterText.TextChanged += OnSettingChangedDelayed;
            chkShowPageNumbers.CheckedChanged += OnSettingChangedDelayed;
        }

        private System.Windows.Forms.Timer updateTimer;

        private void OnSettingChangedDelayed(object sender, EventArgs e)
        {
            // إيقاف المؤقت السابق إذا كان يعمل
            if (updateTimer != null)
            {
                updateTimer.Stop();
                updateTimer.Dispose();
            }

            // إنشاء مؤقت جديد مع تأخير أطول لتحسين الأداء
            updateTimer = new System.Windows.Forms.Timer();
            updateTimer.Interval = 1500; // زيادة التأخير إلى 1.5 ثانية
            updateTimer.Tick += (s, args) =>
            {
                updateTimer.Stop();
                updateTimer.Dispose();
                updateTimer = null;

                // تحديث المعاينة فقط إذا كانت الصفحة نشطة
                if (this.Visible && this.Parent != null)
                {
                    UpdatePreview();
                }
            };
            updateTimer.Start();
        }

        private void previewPanel_Paint(object sender, PaintEventArgs e)
        {
            DrawPreview(e.Graphics);
        }

        private void DrawPreview(Graphics g)
        {
            try
            {
                if (previewPanel == null || numMarginTop == null || numMarginBottom == null ||
                    numMarginLeft == null || numMarginRight == null) return;

                // مسح الخلفية
                g.Clear(Color.White);

                // رسم حدود الصفحة
                Rectangle pageRect = new Rectangle(10, 10, previewPanel.Width - 20, previewPanel.Height - 20);
                g.DrawRectangle(Pens.Black, pageRect);

                // حساب الهوامش
                int marginTop = (int)numMarginTop.Value * 2;
                int marginBottom = (int)numMarginBottom.Value * 2;
                int marginLeft = (int)numMarginLeft.Value * 2;
                int marginRight = (int)numMarginRight.Value * 2;

                Rectangle contentRect = new Rectangle(
                    pageRect.Left + marginLeft,
                    pageRect.Top + marginTop,
                    pageRect.Width - marginLeft - marginRight,
                    pageRect.Height - marginTop - marginBottom
                );

                // رسم منطقة المحتوى
                g.DrawRectangle(Pens.Gray, contentRect);

                float currentY = contentRect.Top;

                // رسم العنوان
                if (numTitleFont != null && txtTitleText != null)
                {
                    Font titleFont = new Font("Arial", (float)numTitleFont.Value * 0.7f, FontStyle.Bold);
                    string titleText = txtTitleText.Text;
                    SizeF titleSize = g.MeasureString(titleText, titleFont);

                    float titleX = contentRect.Left;
                    if (cmbTitleAlignment?.SelectedItem?.ToString() == "وسط" || cmbTitleAlignment?.SelectedItem?.ToString() == "Center")
                        titleX = contentRect.Left + (contentRect.Width - titleSize.Width) / 2;
                    else if (cmbTitleAlignment?.SelectedItem?.ToString() == "يمين" || cmbTitleAlignment?.SelectedItem?.ToString() == "Right")
                        titleX = contentRect.Right - titleSize.Width;

                    // استخدام لون العنوان المحدد
                    Brush titleBrush = new SolidBrush(btnTitleColor?.BackColor ?? Color.Black);
                    g.DrawString(titleText, titleFont, titleBrush, titleX, currentY);
                    titleBrush.Dispose();
                    currentY += titleSize.Height + 10;
                }

                // رسم التاريخ إذا كان مفعلاً
                if (chkShowDateTime != null && chkShowDateTime.Checked)
                {
                    Font dateFont = new Font("Arial", 8);
                    string dateText = DateTime.Now.ToString(cmbDateFormat?.SelectedItem?.ToString() ?? "dd/MM/yyyy");
                    g.DrawString(dateText, dateFont, Brushes.Black, contentRect.Left, currentY);
                    currentY += 20;
                }

                // رسم خط فاصل
                g.DrawLine(Pens.Gray, contentRect.Left, currentY, contentRect.Right, currentY);
                currentY += 10;

                // رسم الجدول الحقيقي
                DrawRealDataTable(g, contentRect, ref currentY);

                // رسم التذييل
                if (txtFooterText != null && !string.IsNullOrEmpty(txtFooterText.Text))
                {
                    Font footerFont = new Font("Arial", 7);
                    string footerText = txtFooterText.Text;
                    if (chkShowPageNumbers != null && chkShowPageNumbers.Checked)
                        footerText += " - صفحة 1";

                    float footerY = contentRect.Bottom - 20;
                    g.DrawString(footerText, footerFont, Brushes.Black, contentRect.Left, footerY);
                    footerFont.Dispose();
                }
            }
            catch (Exception ex)
            {
                // في حالة حدوث خطأ، ارسم رسالة خطأ
                g.DrawString("خطأ في المعاينة: " + ex.Message, SystemFonts.DefaultFont, Brushes.Red, 10, 10);
            }
        }

        private void btnTitleColor_Click(object sender, EventArgs e)
        {
            ColorDialog colorDialog = new ColorDialog();
            colorDialog.Color = btnTitleColor.BackColor; // تعيين اللون الحالي
            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                btnTitleColor.BackColor = colorDialog.Color;
                btnTitleColor.ForeColor = GetContrastColor(colorDialog.Color);

                // حفظ اللون فوراً
                SaveCurrentColorSettings();

                // تحديث المعاينة
                UpdatePreview();
            }
        }

        private void btnHeaderColor_Click(object sender, EventArgs e)
        {
            ColorDialog colorDialog = new ColorDialog();
            colorDialog.Color = btnHeaderColor.BackColor; // تعيين اللون الحالي
            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                btnHeaderColor.BackColor = colorDialog.Color;
                btnHeaderColor.ForeColor = GetContrastColor(colorDialog.Color);

                // حفظ اللون فوراً
                SaveCurrentColorSettings();

                // تحديث المعاينة
                UpdatePreview();
            }
        }

        private void btnTextColor_Click(object sender, EventArgs e)
        {
            ColorDialog colorDialog = new ColorDialog();
            colorDialog.Color = btnTextColor.BackColor; // تعيين اللون الحالي
            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                btnTextColor.BackColor = colorDialog.Color;
                btnTextColor.ForeColor = GetContrastColor(colorDialog.Color);

                // حفظ اللون فوراً
                SaveCurrentColorSettings();

                // تحديث المعاينة
                UpdatePreview();
            }
        }

        private Color GetContrastColor(Color color)
        {
            // حساب اللون المتباين للنص
            int brightness = (int)(color.R * 0.299 + color.G * 0.587 + color.B * 0.114);
            return brightness > 128 ? Color.Black : Color.White;
        }

        // دالة لحفظ الألوان فوراً
        private void SaveCurrentColorSettings()
        {
            try
            {
                string selectedReport = cmbReportType?.SelectedItem?.ToString() ?? "عام";

                // حفظ في قاعدة البيانات أولاً
                SaveColorsToDatabase(selectedReport);

                // حفظ في الريجستري كنسخة احتياطية
                SaveColorsToRegistry(selectedReport);

                System.Diagnostics.Debug.WriteLine($"تم حفظ الألوان للتقرير: {selectedReport}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في حفظ الألوان: " + ex.Message);
            }
        }

        private void SaveColorsToDatabase(string reportType)
        {
            try
            {
                using (SqlConnection con = new SqlConnection(cs))
                {
                    con.Open();

                    // التحقق من وجود السجل أولاً
                    string checkQuery = "SELECT COUNT(*) FROM print_settings WHERE reportType = @reportType";
                    using (SqlCommand checkCmd = new SqlCommand(checkQuery, con))
                    {
                        checkCmd.Parameters.AddWithValue("@reportType", reportType);
                        int count = (int)checkCmd.ExecuteScalar();

                        if (count > 0)
                        {
                            // تحديث الألوان فقط
                            string updateQuery = @"UPDATE print_settings SET
                                titleColor = @titleColor,
                                tableHeaderColor = @tableHeaderColor,
                                tableTextColor = @tableTextColor,
                                lastModified = GETDATE()
                                WHERE reportType = @reportType";

                            using (SqlCommand cmd = new SqlCommand(updateQuery, con))
                            {
                                cmd.Parameters.AddWithValue("@reportType", reportType);
                                cmd.Parameters.AddWithValue("@titleColor", btnTitleColor.BackColor.ToArgb());
                                cmd.Parameters.AddWithValue("@tableHeaderColor", btnHeaderColor.BackColor.ToArgb());
                                cmd.Parameters.AddWithValue("@tableTextColor", btnTextColor.BackColor.ToArgb());
                                cmd.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            // إنشاء سجل جديد بالإعدادات الافتراضية والألوان الجديدة
                            var defaultSettings = GetDefaultPrintSettings(reportType);
                            string insertQuery = @"INSERT INTO print_settings
                                (reportType, paperSize, orientation, marginTop, marginBottom, marginLeft, marginRight,
                                titleText, titleFont, titleAlignment, showDateTime, dateFormat, datePosition,
                                tableFont, borderWidth, footerText, showPageNumbers,
                                titleColor, tableHeaderColor, tableTextColor)
                                VALUES
                                (@reportType, @paperSize, @orientation, @marginTop, @marginBottom, @marginLeft, @marginRight,
                                @titleText, @titleFont, @titleAlignment, @showDateTime, @dateFormat, @datePosition,
                                @tableFont, @borderWidth, @footerText, @showPageNumbers,
                                @titleColor, @tableHeaderColor, @tableTextColor)";

                            using (SqlCommand cmd = new SqlCommand(insertQuery, con))
                            {
                                cmd.Parameters.AddWithValue("@reportType", reportType);
                                cmd.Parameters.AddWithValue("@paperSize", defaultSettings.PaperSize);
                                cmd.Parameters.AddWithValue("@orientation", defaultSettings.Orientation);
                                cmd.Parameters.AddWithValue("@marginTop", defaultSettings.MarginTop);
                                cmd.Parameters.AddWithValue("@marginBottom", defaultSettings.MarginBottom);
                                cmd.Parameters.AddWithValue("@marginLeft", defaultSettings.MarginLeft);
                                cmd.Parameters.AddWithValue("@marginRight", defaultSettings.MarginRight);
                                cmd.Parameters.AddWithValue("@titleText", defaultSettings.TitleText);
                                cmd.Parameters.AddWithValue("@titleFont", defaultSettings.TitleFont);
                                cmd.Parameters.AddWithValue("@titleAlignment", defaultSettings.TitleAlignment);
                                cmd.Parameters.AddWithValue("@showDateTime", defaultSettings.ShowDateTime);
                                cmd.Parameters.AddWithValue("@dateFormat", defaultSettings.DateFormat);
                                cmd.Parameters.AddWithValue("@datePosition", defaultSettings.DatePosition);
                                cmd.Parameters.AddWithValue("@tableFont", defaultSettings.TableFont);
                                cmd.Parameters.AddWithValue("@borderWidth", defaultSettings.BorderWidth);
                                cmd.Parameters.AddWithValue("@footerText", defaultSettings.FooterText);
                                cmd.Parameters.AddWithValue("@showPageNumbers", defaultSettings.ShowPageNumbers);
                                cmd.Parameters.AddWithValue("@titleColor", btnTitleColor.BackColor.ToArgb());
                                cmd.Parameters.AddWithValue("@tableHeaderColor", btnHeaderColor.BackColor.ToArgb());
                                cmd.Parameters.AddWithValue("@tableTextColor", btnTextColor.BackColor.ToArgb());
                                cmd.ExecuteNonQuery();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الألوان في قاعدة البيانات: {ex.Message}");
            }
        }

        private void SaveColorsToRegistry(string selectedReport)
        {
            try
            {
                string registryKey = REGISTRY_KEY + "\\" + selectedReport;

                using (RegistryKey key = Registry.CurrentUser.CreateSubKey(registryKey))
                {
                    // حفظ الألوان الحالية فقط
                    key.SetValue("TitleColor", btnTitleColor.BackColor.ToArgb());
                    key.SetValue("TableHeaderColor", btnHeaderColor.BackColor.ToArgb());
                    key.SetValue("TableTextColor", btnTextColor.BackColor.ToArgb());
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الألوان في الريجستري: {ex.Message}");
            }
        }

        // دالة للحصول على إعدادات الطباعة من الريجستري
        public static PrintSettings GetPrintSettings()
        {
            return GetPrintSettingsForReport("عام");
        }

        // دالة للحصول على العنوان الافتراضي لكل تقرير
        private static string GetDefaultTitle(string reportType)
        {
            switch (reportType)
            {
                case "مبيعات الأدوية": return "تقرير مبيعات الأدوية";
                case "تقرير المبيعات": return "تقرير المبيعات";
                case "جلسات الموظفين": return "تقرير جلسات الموظفين";
                case "جرد الأدوية": return "تقرير جرد الأدوية";
                case "صلاحية الأدوية": return "تقرير صلاحية الأدوية";
                default: return "تقرير الصيدلية";
            }
        }



        // دالة للحصول على إعدادات طباعة تقرير محدد
        public static PrintSettings GetPrintSettingsForReport(string reportType)
        {
            PrintSettings settings = new PrintSettings();

            // محاولة تحميل الإعدادات من قاعدة البيانات أولاً
            if (LoadPrintSettingsFromDatabase(reportType, ref settings))
            {
                return settings;
            }

            // إذا فشل تحميل من قاعدة البيانات، حاول من الريجستري
            if (LoadPrintSettingsFromRegistry(reportType, ref settings))
            {
                return settings;
            }

            // إذا فشل كلاهما، استخدم الإعدادات الافتراضية
            return GetDefaultPrintSettings(reportType);
        }

        private static bool LoadPrintSettingsFromDatabase(string reportType, ref PrintSettings settings)
        {
            try
            {
                // تم التحويل من pharmacy إلى UnifiedPharmacy - إلغاء استخدام قاعدة البيانات القديمة
                string cs = @"data source = NARUTO; database=UnifiedPharmacy; integrated security =True";
                using (SqlConnection con = new SqlConnection(cs))
                {
                    con.Open();
                    string query = "SELECT * FROM print_settings WHERE reportType = @reportType";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@reportType", reportType);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                settings.PaperSize = reader["paperSize"].ToString();
                                settings.Orientation = reader["orientation"].ToString();
                                settings.MarginTop = Convert.ToInt32(reader["marginTop"]);
                                settings.MarginBottom = Convert.ToInt32(reader["marginBottom"]);
                                settings.MarginLeft = Convert.ToInt32(reader["marginLeft"]);
                                settings.MarginRight = Convert.ToInt32(reader["marginRight"]);
                                settings.TitleText = reader["titleText"].ToString();
                                settings.TitleFont = Convert.ToInt32(reader["titleFont"]);
                                settings.TitleAlignment = reader["titleAlignment"].ToString();
                                settings.ShowDateTime = Convert.ToBoolean(reader["showDateTime"]);
                                settings.DateFormat = reader["dateFormat"].ToString();
                                settings.DatePosition = reader["datePosition"].ToString();
                                settings.TableFont = Convert.ToInt32(reader["tableFont"]);
                                settings.BorderWidth = Convert.ToInt32(reader["borderWidth"]);
                                settings.FooterText = reader["footerText"].ToString();
                                settings.ShowPageNumbers = Convert.ToBoolean(reader["showPageNumbers"]);
                                settings.TitleColor = Color.FromArgb(Convert.ToInt32(reader["titleColor"]));
                                settings.TableHeaderColor = Color.FromArgb(Convert.ToInt32(reader["tableHeaderColor"]));
                                settings.TableTextColor = Color.FromArgb(Convert.ToInt32(reader["tableTextColor"]));

                                System.Diagnostics.Debug.WriteLine($"تم تحميل إعدادات {reportType} من قاعدة البيانات بنجاح");
                                return true;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإعدادات من قاعدة البيانات: {ex.Message}");
            }

            return false;
        }

        private static bool LoadPrintSettingsFromRegistry(string reportType, ref PrintSettings settings)
        {
            try
            {
                string registryKey = REGISTRY_KEY + "\\" + reportType;
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(registryKey))
                {
                    if (key != null)
                    {
                        settings.PaperSize = key.GetValue("PaperSize", "A4").ToString();
                        settings.Orientation = key.GetValue("Orientation", "عمودي").ToString();
                        settings.MarginTop = Convert.ToInt32(key.GetValue("MarginTop", 20));
                        settings.MarginBottom = Convert.ToInt32(key.GetValue("MarginBottom", 20));
                        settings.MarginLeft = Convert.ToInt32(key.GetValue("MarginLeft", 15));
                        settings.MarginRight = Convert.ToInt32(key.GetValue("MarginRight", 15));
                        settings.TitleText = key.GetValue("TitleText", GetDefaultTitle(reportType)).ToString();
                        settings.TitleFont = Convert.ToInt32(key.GetValue("TitleFont", 18));
                        settings.TitleAlignment = key.GetValue("TitleAlignment", "وسط").ToString();
                        settings.ShowDateTime = Convert.ToBoolean(key.GetValue("ShowDateTime", true));
                        settings.DateFormat = key.GetValue("DateFormat", "dd/MM/yyyy").ToString();
                        settings.DatePosition = key.GetValue("DatePosition", "أعلى يمين").ToString();
                        settings.TableFont = Convert.ToInt32(key.GetValue("TableFont", 10));
                        settings.BorderWidth = Convert.ToInt32(key.GetValue("BorderWidth", 1));
                        settings.FooterText = key.GetValue("FooterText", "نظام إدارة الصيدلية").ToString();
                        settings.ShowPageNumbers = Convert.ToBoolean(key.GetValue("ShowPageNumbers", true));
                        settings.ReportType = reportType;

                        // تحميل الألوان
                        try
                        {
                            int titleColorArgb = Convert.ToInt32(key.GetValue("TitleColor", Color.Black.ToArgb()));
                            settings.TitleColor = Color.FromArgb(titleColorArgb);
                        }
                        catch
                        {
                            settings.TitleColor = Color.Black;
                        }

                        try
                        {
                            int headerColorArgb = Convert.ToInt32(key.GetValue("TableHeaderColor", Color.LightGray.ToArgb()));
                            settings.TableHeaderColor = Color.FromArgb(headerColorArgb);
                        }
                        catch
                        {
                            settings.TableHeaderColor = Color.LightGray;
                        }

                        try
                        {
                            int textColorArgb = Convert.ToInt32(key.GetValue("TableTextColor", Color.Black.ToArgb()));
                            settings.TableTextColor = Color.FromArgb(textColorArgb);
                        }
                        catch
                        {
                            settings.TableTextColor = Color.Black;
                        }

                        System.Diagnostics.Debug.WriteLine($"تم تحميل إعدادات {reportType} من الريجستري بنجاح");
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإعدادات من الريجستري: {ex.Message}");
            }

            return false;
        }

        private static PrintSettings GetDefaultPrintSettings(string reportType)
        {
            PrintSettings settings = new PrintSettings
            {
                PaperSize = "A4",
                Orientation = "عمودي",
                MarginTop = 20,
                MarginBottom = 20,
                MarginLeft = 15,
                MarginRight = 15,
                TitleText = GetDefaultTitle(reportType),
                TitleFont = 18,
                TitleAlignment = "وسط",
                ShowDateTime = true,
                DateFormat = "dd/MM/yyyy",
                DatePosition = "أعلى يمين",
                TableFont = 10,
                BorderWidth = 1,
                FooterText = "نظام إدارة الصيدلية",
                ShowPageNumbers = true,
                TitleColor = Color.Black,
                TableHeaderColor = Color.LightGray,
                TableTextColor = Color.Black,
                ReportType = reportType
            };

            System.Diagnostics.Debug.WriteLine($"تم استخدام الإعدادات الافتراضية لـ {reportType}");
            return settings;
        }



        // دالة للتحقق من الوضع المظلم
        private bool IsDarkMode()
        {
            try
            {
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"SOFTWARE\PharmacyManagement\Settings"))
                {
                    if (key != null)
                    {
                        object value = key.GetValue("DarkMode");
                        return value != null && bool.Parse(value.ToString());
                    }
                }
            }
            catch { }
            return false;
        }

        // دالة لتطبيق الوضع المظلم
        private void ApplyDarkMode()
        {
            this.BackColor = DarkBackColor;
            this.ForeColor = DarkForeColor;

            // تطبيق الألوان على جميع العناصر
            foreach (Control control in this.Controls)
            {
                ApplyDarkModeToControl(control);
            }
        }

        // دالة لتطبيق الوضع الفاتح
        private void ApplyLightMode()
        {
            this.BackColor = LightBackColor;
            this.ForeColor = LightForeColor;

            // تطبيق الألوان على جميع العناصر
            foreach (Control control in this.Controls)
            {
                ApplyLightModeToControl(control);
            }
        }

        // دالة مساعدة لتطبيق الوضع المظلم على عنصر تحكم
        private void ApplyDarkModeToControl(Control control)
        {
            if (control is Button btn)
            {
                btn.BackColor = DarkButtonColor;
                btn.ForeColor = DarkForeColor;
                btn.FlatStyle = FlatStyle.Flat;
                btn.FlatAppearance.BorderColor = DarkBorderColor;
            }
            else if (control is TextBox || control is ComboBox || control is NumericUpDown)
            {
                control.BackColor = DarkButtonColor;
                control.ForeColor = DarkForeColor;
            }
            else if (control is CheckBox || control is RadioButton)
            {
                control.BackColor = DarkBackColor;
                control.ForeColor = DarkForeColor;
            }
            else if (control is GroupBox || control is Panel)
            {
                control.BackColor = DarkBackColor;
                control.ForeColor = DarkForeColor;
                foreach (Control childControl in control.Controls)
                {
                    ApplyDarkModeToControl(childControl);
                }
            }
            else
            {
                control.BackColor = DarkBackColor;
                control.ForeColor = DarkForeColor;
            }
        }

        // دالة مساعدة لتطبيق الوضع الفاتح على عنصر تحكم
        private void ApplyLightModeToControl(Control control)
        {
            if (control is Button btn)
            {
                btn.BackColor = LightButtonColor;
                btn.ForeColor = LightForeColor;
                btn.FlatStyle = FlatStyle.Standard;
            }
            else if (control is TextBox || control is ComboBox || control is NumericUpDown)
            {
                control.BackColor = Color.White;
                control.ForeColor = LightForeColor;
            }
            else if (control is CheckBox || control is RadioButton)
            {
                control.BackColor = LightBackColor;
                control.ForeColor = LightForeColor;
            }
            else if (control is GroupBox || control is Panel)
            {
                control.BackColor = LightBackColor;
                control.ForeColor = LightForeColor;
                foreach (Control childControl in control.Controls)
                {
                    ApplyLightModeToControl(childControl);
                }
            }
            else
            {
                control.BackColor = LightBackColor;
                control.ForeColor = LightForeColor;
            }
        }

        private void OnLanguageChanged(object sender, EventArgs e)
        {
            ApplyLanguage();
            UpdatePreview();
        }

        // إصلاح مشكلة الألوان - تحديث المعاينة عند تغيير الألوان
        private void UpdatePreviewColors()
        {
            // تحديث المعاينة
            UpdatePreview();
        }

        protected override void OnHandleDestroyed(EventArgs e)
        {
            // إلغاء الاشتراك في الأحداث
            LanguageManager.LanguageChanged -= OnLanguageChanged;

            // تنظيف المؤقت
            if (updateTimer != null)
            {
                updateTimer.Stop();
                updateTimer.Dispose();
                updateTimer = null;
            }

            // تنظيف مؤقت المعاينة
            if (previewUpdateTimer != null)
            {
                previewUpdateTimer.Stop();
                previewUpdateTimer.Dispose();
                previewUpdateTimer = null;
            }

            // تنظيف البيانات المؤقتة
            cachedPreviewData = null;
            cachedReportType = "";

            base.OnHandleDestroyed(e);
        }
    }

    // فئة لحفظ إعدادات الطباعة
    public class PrintSettings
    {
        public string PaperSize { get; set; } = "A4";
        public string Orientation { get; set; } = "عمودي";
        public int MarginTop { get; set; } = 20;
        public int MarginBottom { get; set; } = 20;
        public int MarginLeft { get; set; } = 15;
        public int MarginRight { get; set; } = 15;
        public string TitleText { get; set; } = "تقرير الصيدلية";
        public int TitleFont { get; set; } = 18;
        public string TitleAlignment { get; set; } = "وسط";
        public bool ShowDateTime { get; set; } = true;
        public string DateFormat { get; set; } = "dd/MM/yyyy";
        public string DatePosition { get; set; } = "أعلى يمين";
        public int TableFont { get; set; } = 10;
        public int BorderWidth { get; set; } = 1;
        public string FooterText { get; set; } = "نظام إدارة الصيدلية";
        public bool ShowPageNumbers { get; set; } = true;
        public string ReportType { get; set; } = "عام";
        public Color TitleColor { get; set; } = Color.Black;
        public Color TableHeaderColor { get; set; } = Color.LightGray;
        public Color TableTextColor { get; set; } = Color.Black;
    }

    // فئة مساعدة لتطبيق إعدادات الطباعة على DGVPrinter
    public static class PrintHelper
    {
        public static void ApplyPrintSettings(DGVPrinter printer, string reportType = "عام")
        {
            var settings = UC_PrintDesign.GetPrintSettingsForReport(reportType);

            // تشخيص الإعدادات
            System.Diagnostics.Debug.WriteLine($"تطبيق إعدادات التقرير: {reportType}");
            System.Diagnostics.Debug.WriteLine($"العنوان: {settings.TitleText}");
            System.Diagnostics.Debug.WriteLine($"حجم الخط: {settings.TitleFont}");
            System.Diagnostics.Debug.WriteLine($"الاتجاه: {settings.Orientation}");

            // تطبيق إعدادات العنوان
            printer.Title = settings.TitleText;
            printer.TitleFont = new Font("Arial", settings.TitleFont, FontStyle.Bold);
            printer.TitleColor = settings.TitleColor;

            // تطبيق إعدادات التاريخ
            if (settings.ShowDateTime)
            {
                printer.SubTitle = $"التاريخ: {DateTime.Now.ToString(settings.DateFormat)}";
                printer.SubTitleFont = new Font("Arial", 10);
                printer.SubTitleColor = settings.TableTextColor;
            }

            // تطبيق إعدادات التذييل
            printer.Footer = settings.FooterText;
            printer.FooterFont = new Font("Arial", 9);
            printer.FooterColor = settings.TableTextColor;
            printer.PageNumbers = settings.ShowPageNumbers;

            // تطبيق إعدادات الهوامش
            printer.PageSettings.Margins.Top = settings.MarginTop;
            printer.PageSettings.Margins.Bottom = settings.MarginBottom;
            printer.PageSettings.Margins.Left = settings.MarginLeft;
            printer.PageSettings.Margins.Right = settings.MarginRight;

            // تطبيق اتجاه الطباعة
            printer.PageSettings.Landscape = (settings.Orientation == "أفقي" || settings.Orientation == "Landscape");

            // تطبيق إعدادات الجدول
            printer.PorportionalColumns = true;
            printer.HeaderCellAlignment = GetAlignment(settings.TitleAlignment);
            printer.FooterSpacing = 15;
            printer.TitleSpacing = 10;
            printer.SubTitleSpacing = 5;

            // تطبيق إعدادات الحدود والألوان للجدول
            printer.PrintColumnHeaders = true;
            printer.PrintRowHeaders = false;

            // تطبيق الخطوط المتاحة
            try
            {
                printer.TitleFont = new Font("Arial", settings.TitleFont, FontStyle.Bold);
                printer.SubTitleFont = new Font("Arial", 10);
                printer.FooterFont = new Font("Arial", 9);
            }
            catch
            {
                // استخدام الخطوط الافتراضية في حالة الخطأ
            }

            // تطبيق حجم الورق
            if (settings.PaperSize == "A3")
            {
                printer.PageSettings.PaperSize = new System.Drawing.Printing.PaperSize("A3", 1169, 1654);
            }
            else if (settings.PaperSize == "Letter")
            {
                printer.PageSettings.PaperSize = new System.Drawing.Printing.PaperSize("Letter", 850, 1100);
            }
            else if (settings.PaperSize == "Legal")
            {
                printer.PageSettings.PaperSize = new System.Drawing.Printing.PaperSize("Legal", 850, 1400);
            }
            // A4 هو الافتراضي

            // تطبيق الإعدادات النهائية
            System.Diagnostics.Debug.WriteLine("تم تطبيق إعدادات الطباعة بنجاح");
        }

        // دالة لتطبيق الإعدادات مع التحقق من التطبيق الصحيح
        public static void ApplyPrintSettingsWithValidation(DGVPrinter printer, string reportType = "عام")
        {
            try
            {
                var settings = UC_PrintDesign.GetPrintSettingsForReport(reportType);

                // تشخيص مفصل
                System.Diagnostics.Debug.WriteLine($"=== تطبيق إعدادات التقرير: {reportType} ===");
                System.Diagnostics.Debug.WriteLine($"العنوان: {settings.TitleText}");
                System.Diagnostics.Debug.WriteLine($"لون العنوان: {settings.TitleColor}");
                System.Diagnostics.Debug.WriteLine($"لون النص: {settings.TableTextColor}");
                System.Diagnostics.Debug.WriteLine($"حجم الخط: {settings.TitleFont}");

                // تطبيق الإعدادات الأساسية مع التحقق من القيم
                if (!string.IsNullOrEmpty(settings.TitleText))
                    printer.Title = settings.TitleText;

                if (!string.IsNullOrEmpty(settings.FooterText))
                    printer.Footer = settings.FooterText;

                printer.PageNumbers = settings.ShowPageNumbers;

                // تطبيق الألوان مع التحقق
                if (settings.TitleColor != Color.Empty)
                    printer.TitleColor = settings.TitleColor;

                if (settings.TableTextColor != Color.Empty)
                {
                    printer.FooterColor = settings.TableTextColor;
                    printer.SubTitleColor = settings.TableTextColor;
                }

                // تطبيق الخطوط مع التحقق من الأخطاء
                try
                {
                    if (settings.TitleFont > 0)
                        printer.TitleFont = new Font("Arial", settings.TitleFont, FontStyle.Bold);
                    else
                        printer.TitleFont = new Font("Arial", 16, FontStyle.Bold);

                    printer.SubTitleFont = new Font("Arial", 10);
                    printer.FooterFont = new Font("Arial", 9);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine("خطأ في تطبيق الخطوط: " + ex.Message);
                    // استخدام الخطوط الافتراضية
                    printer.TitleFont = new Font("Arial", 16, FontStyle.Bold);
                    printer.SubTitleFont = new Font("Arial", 10);
                    printer.FooterFont = new Font("Arial", 9);
                }

                // تطبيق التاريخ
                if (settings.ShowDateTime)
                {
                    printer.SubTitle = $"التاريخ: {DateTime.Now.ToString(settings.DateFormat)}";
                }

                // تطبيق الهوامش مع التحقق من القيم
                if (settings.MarginTop >= 0)
                    printer.PageSettings.Margins.Top = settings.MarginTop;
                if (settings.MarginBottom >= 0)
                    printer.PageSettings.Margins.Bottom = settings.MarginBottom;
                if (settings.MarginLeft >= 0)
                    printer.PageSettings.Margins.Left = settings.MarginLeft;
                if (settings.MarginRight >= 0)
                    printer.PageSettings.Margins.Right = settings.MarginRight;

            // تطبيق اتجاه الطباعة
            printer.PageSettings.Landscape = (settings.Orientation == "أفقي" || settings.Orientation == "Landscape");

            // تطبيق حجم الورق
            if (settings.PaperSize == "A3")
            {
                printer.PageSettings.PaperSize = new System.Drawing.Printing.PaperSize("A3", 1169, 1654);
            }
            else if (settings.PaperSize == "Letter")
            {
                printer.PageSettings.PaperSize = new System.Drawing.Printing.PaperSize("Letter", 850, 1100);
            }
            else if (settings.PaperSize == "Legal")
            {
                printer.PageSettings.PaperSize = new System.Drawing.Printing.PaperSize("Legal", 850, 1400);
            }

                // تطبيق إعدادات الجدول
                printer.PorportionalColumns = true;
                printer.HeaderCellAlignment = GetAlignment(settings.TitleAlignment);
                printer.FooterSpacing = 15;
                printer.TitleSpacing = 10;
                printer.SubTitleSpacing = 5;

                System.Diagnostics.Debug.WriteLine("=== تم تطبيق جميع الإعدادات بنجاح ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ عام في تطبيق إعدادات الطباعة: {ex.Message}");
                // تطبيق الإعدادات الافتراضية في حالة الخطأ
                ApplyDefaultPrintSettings(printer);
            }
        }

        // دالة لتطبيق الإعدادات الافتراضية في حالة الخطأ
        private static void ApplyDefaultPrintSettings(DGVPrinter printer)
        {
            try
            {
                printer.Title = "تقرير الصيدلية";
                printer.Footer = "نظام إدارة الصيدلية";
                printer.TitleFont = new Font("Arial", 16, FontStyle.Bold);
                printer.SubTitleFont = new Font("Arial", 10);
                printer.FooterFont = new Font("Arial", 9);
                printer.PageNumbers = true;
                printer.PorportionalColumns = true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الإعدادات الافتراضية: {ex.Message}");
            }
        }

        private static StringAlignment GetAlignment(string alignment)
        {
            switch (alignment)
            {
                case "يسار":
                case "Left": return StringAlignment.Near;
                case "يمين":
                case "Right": return StringAlignment.Far;
                case "وسط":
                case "Center":
                default: return StringAlignment.Center;
            }
        }

    }
}
