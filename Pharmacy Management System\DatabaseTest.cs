using System;
using System.Data;
using System.Data.SqlClient;
using System.Windows.Forms;

namespace Pharmacy_Management_System
{
    public partial class DatabaseTest : Form
    {
        Function fn = new Function();
        
        public DatabaseTest()
        {
            InitializeComponent();
            TestDatabaseConnection();
        }

        private void TestDatabaseConnection()
        {
            try
            {
                // اختبار الاتصال بقاعدة البيانات
                Console.WriteLine("=== اختبار الاتصال بقاعدة البيانات ===");
                
                // اختبار إنشاء جدول جلسات الموظفين
                string createTableQuery = @"IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'employee_sessions')
                         BEGIN
                             CREATE TABLE employee_sessions (
                                 id INT IDENTITY(1,1) PRIMARY KEY,
                                 username NVARCHAR(250),
                                 employeeName NVARCHAR(250),
                                 loginTime DATETIME DEFAULT GETDATE(),
                                 logoutTime DATETIME NULL,
                                 sessionDate DATE DEFAULT CONVERT(DATE, GETDATE())
                             )
                         END";
                
                fn.setData(createTableQuery, "");
                Console.WriteLine("✅ تم إنشاء جدول employee_sessions بنجاح");
                
                // اختبار إدراج بيانات عربية
                string testInsert = @"INSERT INTO employee_sessions (username, employeeName, loginTime, sessionDate)
                                     VALUES (N'اختبار', N'موظف اختبار', GETDATE(), CONVERT(DATE, GETDATE()))";
                
                fn.setData(testInsert, "");
                Console.WriteLine("✅ تم إدراج بيانات عربية بنجاح");
                
                // اختبار قراءة البيانات
                string selectQuery = "SELECT TOP 1 * FROM employee_sessions ORDER BY id DESC";
                DataSet ds = fn.getData(selectQuery);
                
                if (ds.Tables[0].Rows.Count > 0)
                {
                    string username = ds.Tables[0].Rows[0]["username"].ToString();
                    string employeeName = ds.Tables[0].Rows[0]["employeeName"].ToString();
                    Console.WriteLine($"✅ تم قراءة البيانات: {username} - {employeeName}");
                }
                
                // حذف بيانات الاختبار
                string deleteTest = "DELETE FROM employee_sessions WHERE username = N'اختبار'";
                fn.setData(deleteTest, "");
                Console.WriteLine("✅ تم حذف بيانات الاختبار");
                
                MessageBox.Show("اختبار قاعدة البيانات مكتمل بنجاح!\nتحقق من وحدة التحكم للتفاصيل.", 
                               "نجح الاختبار", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار قاعدة البيانات: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
                
                MessageBox.Show($"فشل اختبار قاعدة البيانات:\n{ex.Message}", 
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // DatabaseTest
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(400, 300);
            this.Name = "DatabaseTest";
            this.Text = "اختبار قاعدة البيانات";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.ResumeLayout(false);
        }
    }
}
