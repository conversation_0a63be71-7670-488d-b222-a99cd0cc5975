#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام تداول العملات الرقمية الذكي
Intelligent Cryptocurrency Trading System
"""

import requests
import json
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import configparser
import logging
from typing import Dict, List, Optional
import pickle
import os
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
import ta

class CryptoTradingSystem:
    def __init__(self, demo_mode: bool = True):
        self.demo_mode = demo_mode
        self.balance = 10000.0 if demo_mode else 0.0  # رصيد المحاكاة
        self.positions = {}  # الصفقات المفتوحة
        self.trade_history = []  # تاريخ الصفقات
        self.learning_data = []  # بيانات التعلم
        
        # إعدادات التداول
        self.supported_pairs = [
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'XRPUSDT',
            'SOLUSDT', 'DOTUSDT', 'DOGEUSDT', 'AVAXUSDT', 'MATICUSDT'
        ]
        self.current_pair = 'BTCUSDT'
        self.risk_per_trade = 0.02  # 2% مخاطر لكل صفقة
        
        # نماذج التعلم الآلي
        self.price_model = None
        self.scaler = StandardScaler()
        self.model_trained = False
        
        # إعداد السجلات
        self.logger = self._setup_logger()
        self.load_config()
        self.load_learning_data()
        
    def _setup_logger(self):
        """إعداد نظام السجلات"""
        logger = logging.getLogger('CryptoTrading')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.FileHandler(f'logs/crypto_trading_{datetime.now().strftime("%Y%m%d")}.log')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
        
    def load_config(self):
        """تحميل الإعدادات"""
        config = configparser.ConfigParser()
        
        if os.path.exists('crypto_config.ini'):
            config.read('crypto_config.ini')
        else:
            self.create_default_config()
            config.read('crypto_config.ini')
            
        # إعدادات API
        self.api_key = config.get('BINANCE_API', 'api_key', fallback='')
        self.api_secret = config.get('BINANCE_API', 'api_secret', fallback='')
        self.base_url = config.get('BINANCE_API', 'base_url', fallback='https://api.binance.com')
        
        # إعدادات التداول
        self.min_confidence = float(config.get('TRADING', 'min_confidence', fallback='0.6'))
        self.max_positions = int(config.get('TRADING', 'max_positions', fallback='5'))
        self.stop_loss_pct = float(config.get('TRADING', 'stop_loss_pct', fallback='0.05'))
        self.take_profit_pct = float(config.get('TRADING', 'take_profit_pct', fallback='0.10'))
        
    def create_default_config(self):
        """إنشاء ملف إعدادات افتراضي"""
        config = configparser.ConfigParser()
        
        # إعدادات API
        config.add_section('BINANCE_API')
        config.set('BINANCE_API', 'api_key', '')
        config.set('BINANCE_API', 'api_secret', '')
        config.set('BINANCE_API', 'base_url', 'https://api.binance.com')
        
        # إعدادات التداول
        config.add_section('TRADING')
        config.set('TRADING', 'min_confidence', '0.6')
        config.set('TRADING', 'max_positions', '5')
        config.set('TRADING', 'stop_loss_pct', '0.05')
        config.set('TRADING', 'take_profit_pct', '0.10')
        
        # إعدادات التعلم
        config.add_section('LEARNING')
        config.set('LEARNING', 'learning_enabled', 'true')
        config.set('LEARNING', 'retrain_interval', '24')  # ساعات
        config.set('LEARNING', 'min_data_points', '100')
        
        with open('crypto_config.ini', 'w') as f:
            config.write(f)
            
    def get_crypto_price(self, symbol: str) -> Optional[Dict]:
        """الحصول على سعر العملة الرقمية"""
        try:
            url = f"{self.base_url}/api/v3/ticker/price"
            params = {'symbol': symbol}
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            return {
                'symbol': data['symbol'],
                'price': float(data['price']),
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في الحصول على السعر: {e}")
            return None
            
    def get_historical_data(self, symbol: str, interval: str = '1h', limit: int = 100) -> Optional[pd.DataFrame]:
        """الحصول على البيانات التاريخية"""
        try:
            url = f"{self.base_url}/api/v3/klines"
            params = {
                'symbol': symbol,
                'interval': interval,
                'limit': limit
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            # تحويل البيانات إلى DataFrame
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # تحويل الأنواع
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col])
                
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            return df[numeric_columns]
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في الحصول على البيانات التاريخية: {e}")
            return None
            
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """حساب المؤشرات الفنية"""
        try:
            # المتوسطات المتحركة
            df['sma_20'] = ta.trend.sma_indicator(df['close'], window=20)
            df['sma_50'] = ta.trend.sma_indicator(df['close'], window=50)
            df['ema_12'] = ta.trend.ema_indicator(df['close'], window=12)
            df['ema_26'] = ta.trend.ema_indicator(df['close'], window=26)
            
            # MACD
            df['macd'] = ta.trend.macd_diff(df['close'])
            df['macd_signal'] = ta.trend.macd_signal(df['close'])
            
            # RSI
            df['rsi'] = ta.momentum.rsi(df['close'], window=14)
            
            # Bollinger Bands
            bb = ta.volatility.BollingerBands(df['close'])
            df['bb_upper'] = bb.bollinger_hband()
            df['bb_lower'] = bb.bollinger_lband()
            df['bb_middle'] = bb.bollinger_mavg()
            
            # Stochastic
            df['stoch_k'] = ta.momentum.stoch(df['high'], df['low'], df['close'])
            df['stoch_d'] = ta.momentum.stoch_signal(df['high'], df['low'], df['close'])
            
            # Volume indicators
            df['volume_sma'] = ta.volume.volume_sma(df['close'], df['volume'])
            
            # Price change
            df['price_change'] = df['close'].pct_change()
            df['price_change_5'] = df['close'].pct_change(periods=5)
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في حساب المؤشرات الفنية: {e}")
            return df
            
    def analyze_market_sentiment(self, df: pd.DataFrame) -> Dict:
        """تحليل معنويات السوق"""
        try:
            latest = df.iloc[-1]
            
            # تحليل الاتجاه
            trend_score = 0
            if latest['close'] > latest['sma_20']:
                trend_score += 1
            if latest['sma_20'] > latest['sma_50']:
                trend_score += 1
            if latest['macd'] > latest['macd_signal']:
                trend_score += 1
                
            # تحليل الزخم
            momentum_score = 0
            if latest['rsi'] < 30:  # oversold
                momentum_score += 1
            elif latest['rsi'] > 70:  # overbought
                momentum_score -= 1
                
            if latest['stoch_k'] < 20:
                momentum_score += 1
            elif latest['stoch_k'] > 80:
                momentum_score -= 1
                
            # تحليل التقلبات
            volatility = df['price_change'].std() * 100
            
            # حساب الثقة
            confidence = (trend_score + momentum_score + 2) / 6  # normalize to 0-1
            confidence = max(0, min(1, confidence))
            
            # تحديد القرار
            if confidence >= self.min_confidence and trend_score >= 2:
                decision = 'buy'
            elif confidence >= self.min_confidence and trend_score <= 0:
                decision = 'sell'
            else:
                decision = 'hold'
                
            return {
                'decision': decision,
                'confidence': confidence,
                'trend_score': trend_score,
                'momentum_score': momentum_score,
                'volatility': volatility,
                'rsi': latest['rsi'],
                'price': latest['close']
            }
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في تحليل معنويات السوق: {e}")
            return {'decision': 'hold', 'confidence': 0.0}
            
    def prepare_ml_features(self, df: pd.DataFrame) -> np.ndarray:
        """إعداد ميزات التعلم الآلي"""
        try:
            features = []
            
            # المؤشرات الفنية
            feature_columns = [
                'sma_20', 'sma_50', 'ema_12', 'ema_26', 'macd', 'macd_signal',
                'rsi', 'bb_upper', 'bb_lower', 'stoch_k', 'stoch_d',
                'volume_sma', 'price_change', 'price_change_5'
            ]
            
            for col in feature_columns:
                if col in df.columns:
                    features.append(df[col].iloc[-1])
                else:
                    features.append(0.0)
                    
            return np.array(features).reshape(1, -1)
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في إعداد ميزات ML: {e}")
            return np.array([]).reshape(1, -1)
            
    def train_ml_model(self):
        """تدريب نموذج التعلم الآلي"""
        try:
            if len(self.learning_data) < 50:
                self.logger.info("📊 بيانات غير كافية لتدريب النموذج")
                return False
                
            # إعداد البيانات
            X = []
            y = []
            
            for data_point in self.learning_data:
                if 'features' in data_point and 'target' in data_point:
                    X.append(data_point['features'])
                    y.append(data_point['target'])
                    
            if len(X) < 20:
                return False
                
            X = np.array(X)
            y = np.array(y)
            
            # تطبيع البيانات
            X_scaled = self.scaler.fit_transform(X)
            
            # تدريب النموذج
            self.price_model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42
            )
            
            self.price_model.fit(X_scaled, y)
            self.model_trained = True
            
            # حفظ النموذج
            self.save_model()
            
            self.logger.info(f"✅ تم تدريب النموذج بنجاح - {len(X)} نقطة بيانات")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في تدريب النموذج: {e}")
            return False

    def predict_price_movement(self, features: np.ndarray) -> Optional[float]:
        """التنبؤ بحركة السعر"""
        try:
            if not self.model_trained or self.price_model is None:
                return None

            features_scaled = self.scaler.transform(features)
            prediction = self.price_model.predict(features_scaled)[0]

            return prediction

        except Exception as e:
            self.logger.error(f"❌ خطأ في التنبؤ: {e}")
            return None

    def execute_demo_trade(self, decision: Dict) -> bool:
        """تنفيذ صفقة تجريبية"""
        try:
            symbol = self.current_pair
            current_price = decision['price']

            # حساب حجم الصفقة
            risk_amount = self.balance * self.risk_per_trade
            position_size = risk_amount / (current_price * self.stop_loss_pct)

            if decision['decision'] == 'buy':
                # فتح صفقة شراء
                trade_id = f"DEMO_{symbol}_{int(time.time())}"

                self.positions[trade_id] = {
                    'symbol': symbol,
                    'type': 'buy',
                    'size': position_size,
                    'entry_price': current_price,
                    'stop_loss': current_price * (1 - self.stop_loss_pct),
                    'take_profit': current_price * (1 + self.take_profit_pct),
                    'timestamp': datetime.now(),
                    'confidence': decision['confidence']
                }

                self.logger.info(f"🟢 فتح صفقة شراء تجريبية: {symbol}")
                self.logger.info(f"   💰 السعر: ${current_price:.4f}")
                self.logger.info(f"   📊 الحجم: {position_size:.6f}")
                self.logger.info(f"   🛡️ وقف الخسارة: ${self.positions[trade_id]['stop_loss']:.4f}")
                self.logger.info(f"   🎯 جني الربح: ${self.positions[trade_id]['take_profit']:.4f}")

                return True

            elif decision['decision'] == 'sell':
                # فتح صفقة بيع
                trade_id = f"DEMO_{symbol}_{int(time.time())}"

                self.positions[trade_id] = {
                    'symbol': symbol,
                    'type': 'sell',
                    'size': position_size,
                    'entry_price': current_price,
                    'stop_loss': current_price * (1 + self.stop_loss_pct),
                    'take_profit': current_price * (1 - self.take_profit_pct),
                    'timestamp': datetime.now(),
                    'confidence': decision['confidence']
                }

                self.logger.info(f"🔴 فتح صفقة بيع تجريبية: {symbol}")
                self.logger.info(f"   💰 السعر: ${current_price:.4f}")
                self.logger.info(f"   📊 الحجم: {position_size:.6f}")
                self.logger.info(f"   🛡️ وقف الخسارة: ${self.positions[trade_id]['stop_loss']:.4f}")
                self.logger.info(f"   🎯 جني الربح: ${self.positions[trade_id]['take_profit']:.4f}")

                return True

            return False

        except Exception as e:
            self.logger.error(f"❌ خطأ في تنفيذ الصفقة التجريبية: {e}")
            return False

    def check_positions(self):
        """فحص الصفقات المفتوحة"""
        try:
            closed_positions = []

            for trade_id, position in self.positions.items():
                # الحصول على السعر الحالي
                price_data = self.get_crypto_price(position['symbol'])
                if not price_data:
                    continue

                current_price = price_data['price']

                # فحص وقف الخسارة وجني الربح
                should_close = False
                close_reason = ""

                if position['type'] == 'buy':
                    if current_price <= position['stop_loss']:
                        should_close = True
                        close_reason = "وقف خسارة"
                    elif current_price >= position['take_profit']:
                        should_close = True
                        close_reason = "جني ربح"

                elif position['type'] == 'sell':
                    if current_price >= position['stop_loss']:
                        should_close = True
                        close_reason = "وقف خسارة"
                    elif current_price <= position['take_profit']:
                        should_close = True
                        close_reason = "جني ربح"

                if should_close:
                    # حساب الربح/الخسارة
                    if position['type'] == 'buy':
                        pnl = (current_price - position['entry_price']) * position['size']
                    else:
                        pnl = (position['entry_price'] - current_price) * position['size']

                    # تحديث الرصيد
                    self.balance += pnl

                    # إضافة إلى التاريخ
                    trade_record = {
                        'trade_id': trade_id,
                        'symbol': position['symbol'],
                        'type': position['type'],
                        'entry_price': position['entry_price'],
                        'exit_price': current_price,
                        'size': position['size'],
                        'pnl': pnl,
                        'close_reason': close_reason,
                        'entry_time': position['timestamp'],
                        'exit_time': datetime.now(),
                        'confidence': position['confidence']
                    }

                    self.trade_history.append(trade_record)
                    closed_positions.append(trade_id)

                    # إضافة بيانات التعلم
                    self.add_learning_data(position, trade_record)

                    self.logger.info(f"✅ إغلاق صفقة: {position['symbol']} - {close_reason}")
                    self.logger.info(f"   💰 الربح/الخسارة: ${pnl:.2f}")
                    self.logger.info(f"   📊 الرصيد الجديد: ${self.balance:.2f}")

            # إزالة الصفقات المغلقة
            for trade_id in closed_positions:
                del self.positions[trade_id]

        except Exception as e:
            self.logger.error(f"❌ خطأ في فحص الصفقات: {e}")

    def add_learning_data(self, position: Dict, trade_record: Dict):
        """إضافة بيانات للتعلم"""
        try:
            # الحصول على البيانات التاريخية
            df = self.get_historical_data(position['symbol'], '1h', 50)
            if df is None:
                return

            df = self.calculate_technical_indicators(df)
            features = self.prepare_ml_features(df)

            if features.size > 0:
                # الهدف هو نسبة الربح/الخسارة
                target = trade_record['pnl'] / (position['entry_price'] * position['size'])

                learning_point = {
                    'features': features.flatten(),
                    'target': target,
                    'timestamp': datetime.now(),
                    'symbol': position['symbol'],
                    'confidence': position['confidence']
                }

                self.learning_data.append(learning_point)

                # حفظ بيانات التعلم
                self.save_learning_data()

        except Exception as e:
            self.logger.error(f"❌ خطأ في إضافة بيانات التعلم: {e}")

    def save_learning_data(self):
        """حفظ بيانات التعلم"""
        try:
            os.makedirs('models', exist_ok=True)
            with open('models/crypto_learning_data.pkl', 'wb') as f:
                pickle.dump(self.learning_data, f)
        except Exception as e:
            self.logger.error(f"❌ خطأ في حفظ بيانات التعلم: {e}")

    def load_learning_data(self):
        """تحميل بيانات التعلم"""
        try:
            if os.path.exists('models/crypto_learning_data.pkl'):
                with open('models/crypto_learning_data.pkl', 'rb') as f:
                    self.learning_data = pickle.load(f)
                self.logger.info(f"✅ تم تحميل {len(self.learning_data)} نقطة تعلم")
        except Exception as e:
            self.logger.error(f"❌ خطأ في تحميل بيانات التعلم: {e}")
            self.learning_data = []

    def save_model(self):
        """حفظ النموذج"""
        try:
            os.makedirs('models', exist_ok=True)
            with open('models/crypto_price_model.pkl', 'wb') as f:
                pickle.dump(self.price_model, f)
            with open('models/crypto_scaler.pkl', 'wb') as f:
                pickle.dump(self.scaler, f)
        except Exception as e:
            self.logger.error(f"❌ خطأ في حفظ النموذج: {e}")

    def load_model(self):
        """تحميل النموذج"""
        try:
            if os.path.exists('models/crypto_price_model.pkl'):
                with open('models/crypto_price_model.pkl', 'rb') as f:
                    self.price_model = pickle.load(f)
                with open('models/crypto_scaler.pkl', 'rb') as f:
                    self.scaler = pickle.load(f)
                self.model_trained = True
                self.logger.info("✅ تم تحميل النموذج المدرب")
                return True
        except Exception as e:
            self.logger.error(f"❌ خطأ في تحميل النموذج: {e}")
        return False

    def analyze_and_trade(self) -> Dict:
        """تحليل السوق واتخاذ قرار التداول"""
        try:
            # الحصول على البيانات التاريخية
            df = self.get_historical_data(self.current_pair, '1h', 100)
            if df is None:
                return {'decision': 'hold', 'reason': 'لا يمكن الحصول على البيانات'}

            # حساب المؤشرات الفنية
            df = self.calculate_technical_indicators(df)

            # تحليل معنويات السوق
            sentiment = self.analyze_market_sentiment(df)

            # استخدام التعلم الآلي إذا كان متوفراً
            if self.model_trained:
                features = self.prepare_ml_features(df)
                if features.size > 0:
                    ml_prediction = self.predict_price_movement(features)
                    if ml_prediction is not None:
                        # دمج التحليل الفني مع التعلم الآلي
                        if ml_prediction > 0.02:  # توقع ارتفاع 2%+
                            sentiment['confidence'] = min(1.0, sentiment['confidence'] + 0.1)
                            if sentiment['decision'] == 'hold':
                                sentiment['decision'] = 'buy'
                        elif ml_prediction < -0.02:  # توقع انخفاض 2%+
                            sentiment['confidence'] = min(1.0, sentiment['confidence'] + 0.1)
                            if sentiment['decision'] == 'hold':
                                sentiment['decision'] = 'sell'

                        sentiment['ml_prediction'] = ml_prediction

            # التحقق من عدد الصفقات المفتوحة
            if len(self.positions) >= self.max_positions:
                sentiment['decision'] = 'hold'
                sentiment['reason'] = 'الحد الأقصى للصفقات المفتوحة'

            return sentiment

        except Exception as e:
            self.logger.error(f"❌ خطأ في التحليل والتداول: {e}")
            return {'decision': 'hold', 'reason': f'خطأ: {str(e)}'}

    def run_trading_session(self, duration_minutes: int = 60):
        """تشغيل جلسة تداول"""
        try:
            self.logger.info(f"🚀 بدء جلسة تداول العملات الرقمية - {duration_minutes} دقيقة")
            self.logger.info(f"💰 الرصيد الابتدائي: ${self.balance:.2f}")
            self.logger.info(f"🎯 العملة المختارة: {self.current_pair}")
            self.logger.info(f"🔄 وضع التداول: {'محاكاة' if self.demo_mode else 'حقيقي'}")

            # تحميل النموذج المدرب
            self.load_model()

            start_time = datetime.now()
            end_time = start_time + timedelta(minutes=duration_minutes)

            trade_count = 0
            analysis_count = 0

            while datetime.now() < end_time:
                try:
                    # فحص الصفقات المفتوحة
                    self.check_positions()

                    # تحليل السوق
                    analysis = self.analyze_and_trade()
                    analysis_count += 1

                    self.logger.info(f"📊 تحليل #{analysis_count}: {analysis['decision']} - ثقة: {analysis['confidence']:.2%}")

                    # تنفيذ التداول
                    if analysis['decision'] in ['buy', 'sell'] and analysis['confidence'] >= self.min_confidence:
                        if self.demo_mode:
                            success = self.execute_demo_trade(analysis)
                        else:
                            success = self.execute_real_crypto_trade(analysis)

                        if success:
                            trade_count += 1

                    # إعادة تدريب النموذج كل 10 تحليلات
                    if analysis_count % 10 == 0 and len(self.learning_data) > 20:
                        self.logger.info("🧠 إعادة تدريب النموذج...")
                        self.train_ml_model()

                    # انتظار قبل التحليل التالي
                    time.sleep(60)  # دقيقة واحدة

                except KeyboardInterrupt:
                    self.logger.info("⏹️ تم إيقاف الجلسة بواسطة المستخدم")
                    break
                except Exception as e:
                    self.logger.error(f"❌ خطأ في الجلسة: {e}")
                    time.sleep(30)

            # ملخص الجلسة
            self.print_session_summary(start_time, trade_count, analysis_count)

        except Exception as e:
            self.logger.error(f"❌ خطأ في تشغيل الجلسة: {e}")

    def execute_real_crypto_trade(self, decision: Dict) -> bool:
        """تنفيذ صفقة حقيقية (يتطلب API keys)"""
        try:
            if not self.api_key or not self.api_secret:
                self.logger.warning("⚠️ مفاتيح API غير متوفرة - التبديل للمحاكاة")
                return self.execute_demo_trade(decision)

            # هنا يمكن إضافة كود التداول الحقيقي باستخدام Binance API
            self.logger.info("🔥 تنفيذ صفقة حقيقية (يتطلب تطوير إضافي)")

            # للآن، نستخدم المحاكاة
            return self.execute_demo_trade(decision)

        except Exception as e:
            self.logger.error(f"❌ خطأ في التداول الحقيقي: {e}")
            return False

    def print_session_summary(self, start_time: datetime, trade_count: int, analysis_count: int):
        """طباعة ملخص الجلسة"""
        try:
            duration = datetime.now() - start_time

            print("\n" + "="*60)
            print("📊 ملخص جلسة التداول")
            print("="*60)
            print(f"⏰ مدة الجلسة: {duration}")
            print(f"🔍 عدد التحليلات: {analysis_count}")
            print(f"💼 عدد الصفقات: {trade_count}")
            print(f"📈 الصفقات المفتوحة: {len(self.positions)}")
            print(f"💰 الرصيد النهائي: ${self.balance:.2f}")

            # حساب الأداء
            if self.trade_history:
                total_pnl = sum(trade['pnl'] for trade in self.trade_history)
                winning_trades = len([t for t in self.trade_history if t['pnl'] > 0])
                losing_trades = len([t for t in self.trade_history if t['pnl'] < 0])
                win_rate = (winning_trades / len(self.trade_history)) * 100 if self.trade_history else 0

                print(f"📈 إجمالي الربح/الخسارة: ${total_pnl:.2f}")
                print(f"✅ الصفقات الرابحة: {winning_trades}")
                print(f"❌ الصفقات الخاسرة: {losing_trades}")
                print(f"🎯 معدل النجاح: {win_rate:.1f}%")

            print(f"🧠 نقاط التعلم المجمعة: {len(self.learning_data)}")
            print(f"🤖 النموذج مدرب: {'نعم' if self.model_trained else 'لا'}")
            print("="*60)

        except Exception as e:
            self.logger.error(f"❌ خطأ في طباعة الملخص: {e}")

    def switch_trading_mode(self, demo_mode: bool):
        """تبديل وضع التداول"""
        self.demo_mode = demo_mode
        mode_text = "محاكاة" if demo_mode else "حقيقي"
        self.logger.info(f"🔄 تم التبديل إلى وضع: {mode_text}")

        if not demo_mode and (not self.api_key or not self.api_secret):
            self.logger.warning("⚠️ مفاتيح API غير متوفرة - سيتم استخدام المحاكاة")
            self.demo_mode = True

    def get_account_summary(self) -> Dict:
        """الحصول على ملخص الحساب"""
        return {
            'balance': self.balance,
            'open_positions': len(self.positions),
            'total_trades': len(self.trade_history),
            'learning_points': len(self.learning_data),
            'model_trained': self.model_trained,
            'trading_mode': 'محاكاة' if self.demo_mode else 'حقيقي',
            'current_pair': self.current_pair
        }
