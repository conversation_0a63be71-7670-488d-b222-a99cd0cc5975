﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy_Management_System.PharmacistUC
{
    public partial class UC_P_UpdateMedicine : UserControl
    {
        Function fn = new Function();
        String query;

        public UC_P_UpdateMedicine()
        {
            InitializeComponent();

            // الاشتراك في حدث تغيير اللغة
            LanguageManager.LanguageChanged += OnLanguageChanged;
        }

        private void OnLanguageChanged(object sender, EventArgs e)
        {
            ApplyLanguage();
        }

        public void ApplyLanguage()
        {
            // تحديث النصوص حسب اللغة المختارة
            if (label1 != null) label1.Text = LanguageManager.GetText("Update Medicine");
            if (label2 != null) label2.Text = LanguageManager.GetText("Medicine ID");
            if (label3 != null) label3.Text = LanguageManager.GetText("Medicine Name");
            if (label4 != null) label4.Text = LanguageManager.GetText("Medicine Number");
            if (label5 != null) label5.Text = LanguageManager.GetText("Manufacturing Date");
            if (label6 != null) label6.Text = LanguageManager.GetText("Expire Date");
            if (label7 != null) label7.Text = LanguageManager.GetText("Available Quantity");
            if (label8 != null) label8.Text = LanguageManager.GetText("Price Per Unit");
            if (label9 != null) label9.Text = LanguageManager.GetText("Add Quantity");
            if (label10 != null) label10.Text = LanguageManager.GetText("New Manufacturing Date");
            if (label11 != null) label11.Text = LanguageManager.GetText("New Expire Date");
            if (btnUpdate != null) btnUpdate.Text = LanguageManager.GetText("Update");
            if (btnReset != null) btnReset.Text = LanguageManager.GetText("Reset");

            // الحفاظ على التصميم من اليسار لليمين في جميع اللغات
            this.RightToLeft = RightToLeft.No;
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            if (txtMediID.Text != "")
            {
                query = "SELECT * FROM medic WHERE mid = '" + txtMediID.Text + "'";
                DataSet ds = fn.getData(query);
                if (ds.Tables[0].Rows.Count != 0)
                {
                    txtMediName.Text = ds.Tables[0].Rows[0][2].ToString();
                    txtlu.Text = ds.Tables[0].Rows[0][8].ToString();
                    txtMediNumber.Text = ds.Tables[0].Rows[0][3].ToString();
                    txtMDate.Value = Convert.ToDateTime(ds.Tables[0].Rows[0][4]);
                    txtEDate.Value = Convert.ToDateTime(ds.Tables[0].Rows[0][5]);
                    txtAvailableQuantity.Text = ds.Tables[0].Rows[0][6].ToString();
                    txtPricePerUnit.Text = ds.Tables[0].Rows[0][7].ToString();
                    txtbr.Text = ds.Tables[0].Rows[0][9].ToString();
                    txtNewQuantity.Text = ds.Tables[0].Rows[0][11].ToString();
                    if (ds.Tables[0].Rows[0][10] != DBNull.Value)
                        dtpNewEDate.Value = Convert.ToDateTime(ds.Tables[0].Rows[0][10]);
                    if (ds.Tables[0].Rows[0]["newMDate"] != DBNull.Value)
                        dtpNewmDate.Value = Convert.ToDateTime(ds.Tables[0].Rows[0]["newMDate"]);
                }
                else
                {
                    MessageBox.Show(LanguageManager.GetText("No medicine found with ID") + ": " + txtMediID.Text, LanguageManager.GetText("Information"), MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            else
            {
                clearAll();
            }
        }

        private void clearAll()
        {
            txtMediID.Clear();
            txtMediName.Clear();
            txtMediNumber.Clear();
            txtAddQuan.Clear();

            txtMediNumber2.Clear();
            txtMediNumber3.Clear();
            txtMediNumber4.Clear();
            txtDosQty2.Clear();
            txtDosQty3.Clear();
            txtDosQty4.Clear();

            
        }

        private void btnReset_Click(object sender, EventArgs e)
        {
            clearAll();
        }

        // Int64 totalQuantity; // غير مستخدم

        private void btnUpdate_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtMediID.Text))
                {
                    MessageBox.Show(LanguageManager.GetText("Please enter medicine ID first"), LanguageManager.GetText("Warning"), MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Basic update fields
                String mname = txtMediName.Text;
                String mnumber = txtMediNumber.Text;
                String mdate = txtMDate.Value.ToString("yyyy-MM-dd");
                String edate = txtEDate.Value.ToString("yyyy-MM-dd");
                String lu = txtlu.Text;
                String br = txtbr.Text;
                Int64 quantity = 0;
                Int64.TryParse(txtAvailableQuantity.Text, out quantity);

                Int64 addQuantity = 0;
                Int64.TryParse(txtAddQuan.Text, out addQuantity);

                Int64 totalQuantity = quantity + addQuantity;

                Int64 unitprice = 0;
                Int64.TryParse(txtPricePerUnit.Text, out unitprice);

                Int64.TryParse(txtMediNumber2.Text, out Int64 dos2);
                Int64.TryParse(txtMediNumber3.Text, out Int64 dos3);
                Int64.TryParse(txtMediNumber4.Text, out Int64 dos4);
                Int32.TryParse(txtDosQty2.Text, out Int32 dos2q);
                Int32.TryParse(txtDosQty3.Text, out Int32 dos3q);
                Int32.TryParse(txtDosQty4.Text, out Int32 dos4q);
                Int32.TryParse(txtDosQty1.Text, out Int32 mnumber_qty);
                string query = "UPDATE medic SET " +//txtDosQty1
                       "mname = '" + mname + "', " +
                       "mnumber = '" + mnumber + "', " +
                       "mDate = '" + mdate + "', " +
                       "eDate = '" + edate + "', " +
                       "lu = '" + lu + "', " +
                       "br = '" + br + "', " +
                       "quantity = " + totalQuantity + ", " +
                       "perUnit = " + unitprice + ", " +
                       "dos2 = " + dos2 + ", " +
                       "dos3 = " + dos3 + ", " +
                       "dos4 = " + dos4 + ", " +
                       "mnumber_qty = " + mnumber_qty + ", " +
                       "dos2_qty = " + dos2q + ", " +
                       "dos3_qty = " + dos3q + ", " +
                       "dos4_qty = " + dos4q +
                       " WHERE mid = '" + txtMediID.Text + "'";

                fn.setData(query, null);  

                bool newQuantityUpdated = false;

                if (!string.IsNullOrWhiteSpace(txtNewQuantity.Text))
                {
                    Int64 newQuantity;
                    bool isValidQuantity = Int64.TryParse(txtNewQuantity.Text, out newQuantity);

                    if (isValidQuantity)
                    {
                        String newEDate = dtpNewEDate.Value.ToString("yyyy-MM-dd");
                        String newMDate = dtpNewmDate.Value.ToString("yyyy-MM-dd");

                        query = "SELECT ISNULL(quantity, 0) AS quantity, ISNULL(allqun, 0) AS allqun FROM medic WHERE mid = '" + txtMediID.Text + "'";
                        DataSet ds = fn.getData(query);

                        if (ds.Tables[0].Rows.Count > 0)
                        {
                            Int64 currentQuantity = Int64.Parse(ds.Tables[0].Rows[0]["quantity"].ToString());
                            Int64 allqun = Int64.Parse(ds.Tables[0].Rows[0]["allqun"].ToString());

                            Int64 updatedTotal = currentQuantity + newQuantity;

                            query = "UPDATE medic SET newEDate = '" + newEDate + "', newMDate = '" + newMDate +  "', newQuantity = " + newQuantity + ", allqun = " + updatedTotal + " WHERE mid = '" + txtMediID.Text + "'";
                            fn.setData(query, null); 

                            newQuantityUpdated = true;
                        }
                        else
                        {
                            MessageBox.Show("No medicine found with the entered ID.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }
                    }
                    else
                    {
                        MessageBox.Show("Invalid new quantity entered.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                }

                if (newQuantityUpdated)
                {
                    MessageBox.Show(LanguageManager.GetText("Medicine details and new quantity updated successfully"), LanguageManager.GetText("Success"), MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show(LanguageManager.GetText("Medicine details updated successfully"), LanguageManager.GetText("Success"), MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("An error occurred while updating") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        

        private void UC_P_UpdateMedicine_Load(object sender, EventArgs e)
        {
            // تطبيق اللغة الحالية
            ApplyLanguage();

            AutoCompleteStringCollection autoSource = new AutoCompleteStringCollection();

            string query = "SELECT mid, mname FROM medic WHERE pharmacy_id = " + SessionManager.CurrentPharmacyId;
            DataSet ds = fn.getData(query);

            foreach (DataRow row in ds.Tables[0].Rows)
            {
                autoSource.Add(row["mid"].ToString());
                autoSource.Add(row["mname"].ToString());
            }

            txtMediID.AutoCompleteMode = AutoCompleteMode.SuggestAppend;
            txtMediID.AutoCompleteSource = AutoCompleteSource.CustomSource;
            txtMediID.AutoCompleteCustomSource = autoSource;
        }

        // Int64 allquantity; // غير مستخدم

        private void txtMediID_TextChanged(object sender, EventArgs e)
        {
            string input = txtMediID.Text.Trim();

            if (input.Length == 0)
            {
                clearAll();
                return;
            }

            string query = "SELECT * FROM medic WHERE mid = '" + input + "' OR mname = '" + input + "'";
            DataSet ds = fn.getData(query);

            if (ds.Tables[0].Rows.Count > 0)
            {
                txtMediID.TextChanged -= txtMediID_TextChanged;  

                txtMediID.Text = ds.Tables[0].Rows[0]["mid"].ToString();  
                txtMediName.Text = ds.Tables[0].Rows[0]["mname"].ToString();
                txtMediNumber.Text = ds.Tables[0].Rows[0]["mnumber"].ToString();
                txtMDate.Value = Convert.ToDateTime(ds.Tables[0].Rows[0]["mDate"]);
                txtEDate.Value = Convert.ToDateTime(ds.Tables[0].Rows[0]["eDate"]);
                txtAvailableQuantity.Text = ds.Tables[0].Rows[0]["quantity"].ToString();
                txtPricePerUnit.Text = ds.Tables[0].Rows[0]["perUnit"].ToString();
                txtlu.Text = ds.Tables[0].Rows[0]["lu"].ToString();
                txtbr.Text = ds.Tables[0].Rows[0]["br"].ToString();
                txtNewQuantity.Text = ds.Tables[0].Rows[0]["newQuantity"].ToString();
                if (ds.Tables[0].Rows[0]["newEDate"] != DBNull.Value)
                    dtpNewEDate.Value = Convert.ToDateTime(ds.Tables[0].Rows[0]["newEDate"]);
                if (ds.Tables[0].Rows[0]["newMDate"] != DBNull.Value)
                    dtpNewmDate.Value = Convert.ToDateTime(ds.Tables[0].Rows[0]["newMDate"]);
                txtMediNumber2.Text = ds.Tables[0].Rows[0]["dos2"].ToString();
                txtMediNumber3.Text = ds.Tables[0].Rows[0]["dos3"].ToString();
                txtMediNumber4.Text = ds.Tables[0].Rows[0]["dos4"].ToString();
                txtDosQty2.Text = ds.Tables[0].Rows[0]["dos2_qty"].ToString();
                txtDosQty3.Text = ds.Tables[0].Rows[0]["dos3_qty"].ToString();
                txtDosQty4.Text = ds.Tables[0].Rows[0]["dos4_qty"].ToString();//mnumber_qty
                txtDosQty1.Text = ds.Tables[0].Rows[0]["mnumber_qty"].ToString();
                txtMediID.TextChanged += txtMediID_TextChanged;
            }
        }

        private void txtbr_TextChanged(object sender, EventArgs e)
        {

        }



        protected override void OnHandleDestroyed(EventArgs e)
        {
            // إلغاء الاشتراك في أحداث التغيير
            LanguageManager.LanguageChanged -= OnLanguageChanged;
            base.OnHandleDestroyed(e);
        }
    }
}
