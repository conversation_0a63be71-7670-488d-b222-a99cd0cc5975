-- تحديث قاعدة البيانات لإضافة جدول تسجيل دخول وخروج الموظفين
-- Employee Sessions Database Update

-- إنشاء جدول تسجيل دخول/خروج الموظفين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='employee_sessions' AND xtype='U')
BEGIN
    CREATE TABLE employee_sessions (
        id INT IDENTITY(1,1) PRIMARY KEY,
        username VARCHAR(250) NOT NULL,
        employeeName VARCHAR(250) NOT NULL,
        loginTime DATETIME NOT NULL DEFAULT GETDATE(),
        logoutTime DATETIME NULL,
        sessionDate DATE NOT NULL DEFAULT CONVERT(DATE, GETDATE())
    );
    
    PRINT 'تم إنشاء جدول employee_sessions بنجاح';
END
ELSE
BEGIN
    PRINT 'جدول employee_sessions موجود بالفعل';
END

-- إضافة فهارس لتحسين الأداء
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_employee_sessions_username')
BEGIN
    CREATE INDEX IX_employee_sessions_username ON employee_sessions(username);
    PRINT 'تم إنشاء فهرس IX_employee_sessions_username';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_employee_sessions_sessionDate')
BEGIN
    CREATE INDEX IX_employee_sessions_sessionDate ON employee_sessions(sessionDate);
    PRINT 'تم إنشاء فهرس IX_employee_sessions_sessionDate';
END

-- إضافة بيانات تجريبية (اختيارية)
-- يمكن حذف هذا القسم إذا لم تكن تريد بيانات تجريبية

/*
-- إدراج بعض البيانات التجريبية
INSERT INTO employee_sessions (username, employeeName, loginTime, logoutTime, sessionDate)
VALUES 
    ('admin', 'مدير النظام', DATEADD(hour, -2, GETDATE()), DATEADD(hour, -1, GETDATE()), CONVERT(DATE, GETDATE())),
    ('pharmacist1', 'أحمد محمد', DATEADD(hour, -3, GETDATE()), NULL, CONVERT(DATE, GETDATE())),
    ('pharmacist2', 'فاطمة علي', DATEADD(day, -1, GETDATE()), DATEADD(day, -1, DATEADD(hour, 8, GETDATE())), CONVERT(DATE, DATEADD(day, -1, GETDATE())));

PRINT 'تم إدراج البيانات التجريبية';
*/

PRINT 'تم تحديث قاعدة البيانات بنجاح - جدول جلسات الموظفين جاهز للاستخدام';
