-- ═══════════════════════════════════════════════════════════════
-- <PERSON><PERSON><PERSON><PERSON> قاعدة بيانات نظام إدارة الصيدلية الشامل والمحدث
-- Complete and Enhanced Pharmacy Management System Database Setup
-- الإصدار: 2.0 | Version: 2.0
-- التاريخ: 2025-06-30 | Date: 2025-06-30
-- ═══════════════════════════════════════════════════════════════

-- إنشاء قاعدة البيانات إذا لم تكن موجودة مع تحديد المسار
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'UnifiedPharmacy')
BEGIN
    CREATE DATABASE UnifiedPharmacy
    ON (
        NAME = 'UnifiedPharmacy',
        FILENAME = 'C:\Program Files\Microsoft SQL Server\MSSQL16.MSSQLSERVER\MSSQL\DATA\UnifiedPharmacy.mdf',
        SIZE = 100MB,
        MAXSIZE = 1GB,
        FILEGROWTH = 10MB
    )
    LOG ON (
        NAME = 'UnifiedPharmacy_Log',
        FILENAME = 'C:\Program Files\Microsoft SQL Server\MSSQL16.MSSQLSERVER\MSSQL\DATA\UnifiedPharmacy_Log.ldf',
        SIZE = 10MB,
        MAXSIZE = 100MB,
        FILEGROWTH = 5MB
    );
    PRINT '✅ تم إنشاء قاعدة البيانات UnifiedPharmacy في المسار المحدد';
END
ELSE
BEGIN
    PRINT '📋 قاعدة البيانات UnifiedPharmacy موجودة بالفعل';
END

USE UnifiedPharmacy;
GO

PRINT '🚀 بدء إعداد قاعدة بيانات نظام إدارة الصيدلية الشامل...';
PRINT '🚀 Starting Complete Pharmacy Management System Database Setup...';
PRINT '';

-- ===================================
-- 1. إنشاء جدول المستخدمين الأساسي
-- ===================================

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'users')
BEGIN
    CREATE TABLE users (
        id INT IDENTITY(1,1) PRIMARY KEY,
        userRole NVARCHAR(50) NOT NULL DEFAULT 'Employee',
        name NVARCHAR(255) NOT NULL,
        dob DATE,
        mobile NVARCHAR(50),
        email NVARCHAR(255),
        username NVARCHAR(100) NOT NULL UNIQUE,
        pass NVARCHAR(255) NOT NULL,
        pharmacy_id INT,
        created_date DATETIME DEFAULT GETDATE(),
        is_active BIT DEFAULT 1,
        last_login DATETIME,
        CONSTRAINT FK_users_pharmacy FOREIGN KEY (pharmacy_id) REFERENCES pharmacies(id)
    );
    PRINT '✅ تم إنشاء جدول users';
END
ELSE
BEGIN
    -- إضافة الأعمدة المفقودة إذا لم تكن موجودة
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'pharmacy_id')
    BEGIN
        ALTER TABLE users ADD pharmacy_id INT;
        PRINT '✅ تم إضافة عمود pharmacy_id لجدول users';
    END

    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'created_date')
    BEGIN
        ALTER TABLE users ADD created_date DATETIME DEFAULT GETDATE();
        PRINT '✅ تم إضافة عمود created_date لجدول users';
    END

    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'is_active')
    BEGIN
        ALTER TABLE users ADD is_active BIT DEFAULT 1;
        PRINT '✅ تم إضافة عمود is_active لجدول users';
    END

    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'last_login')
    BEGIN
        ALTER TABLE users ADD last_login DATETIME;
        PRINT '✅ تم إضافة عمود last_login لجدول users';
    END
END

-- ===================================
-- 2. إنشاء جدول الصيدليات إذا لم يكن موجوداً
-- ===================================

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacies')
BEGIN
    CREATE TABLE pharmacies (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacy_code NVARCHAR(50) NOT NULL UNIQUE,
        pharmacy_name NVARCHAR(255) NOT NULL,
        owner_name NVARCHAR(255) NOT NULL,
        phone NVARCHAR(50),
        address NVARCHAR(500),
        city NVARCHAR(100),
        email NVARCHAR(255),
        registration_date DATETIME DEFAULT GETDATE(),
        is_active BIT DEFAULT 1
    );
    PRINT '✅ تم إنشاء جدول pharmacies مع عمود pharmacy_code';
END
ELSE
BEGIN
    -- إضافة عمود pharmacy_code إذا لم يكن موجوداً
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('pharmacies') AND name = 'pharmacy_code')
    BEGIN
        ALTER TABLE pharmacies ADD pharmacy_code NVARCHAR(50);
        PRINT '✅ تم إضافة عمود pharmacy_code لجدول pharmacies';

        -- تحديث الصيدليات الموجودة بأكواد فريدة
        UPDATE pharmacies SET pharmacy_code = 'PHARM' + RIGHT('000' + CAST(id AS VARCHAR(3)), 3) WHERE pharmacy_code IS NULL;

        -- إضافة قيد الفرادة
        ALTER TABLE pharmacies ADD CONSTRAINT UQ_pharmacies_code UNIQUE (pharmacy_code);
        PRINT '✅ تم تحديث الصيدليات الموجودة بأكواد فريدة';
    END

    -- إضافة عمود city إذا لم يكن موجوداً
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('pharmacies') AND name = 'city')
    BEGIN
        ALTER TABLE pharmacies ADD city NVARCHAR(100);
        PRINT '✅ تم إضافة عمود city لجدول pharmacies';
    END
END

-- ===================================
-- 2. إنشاء جدول الأدوية الأساسي
-- ===================================

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'medic')
BEGIN
    CREATE TABLE medic (
        id INT IDENTITY(1,1) PRIMARY KEY,
        mid NVARCHAR(100),
        mname NVARCHAR(255) NOT NULL,
        mnumber NVARCHAR(100),
        mDate DATE,
        eDate DATE,
        quantity INT DEFAULT 0,
        perUnit DECIMAL(10,2) DEFAULT 0,
        lu NVARCHAR(100), -- Last Updated by
        br NVARCHAR(100), -- Brand
        originalQuantity INT DEFAULT 0,
        originalNewQuantity INT DEFAULT 0,
        newEDate DATE,
        newQuantity INT DEFAULT 0,
        allqun INT DEFAULT 0,
        mnumber_qty INT DEFAULT 0,
        newMDate DATE,
        -- أعمدة الجرعات المتعددة
        dos2 NVARCHAR(255),
        dos2_qty INT DEFAULT 0,
        dos3 NVARCHAR(255),
        dos3_qty INT DEFAULT 0,
        dos4 NVARCHAR(255),
        dos4_qty INT DEFAULT 0,
        pharmacy_id INT,
        created_date DATETIME DEFAULT GETDATE(),
        CONSTRAINT FK_medic_pharmacy FOREIGN KEY (pharmacy_id) REFERENCES pharmacies(id)
    );
    PRINT '✅ تم إنشاء جدول medic';
END
ELSE
BEGIN
    -- إضافة الأعمدة المفقودة إذا لم تكن موجودة
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('medic') AND name = 'pharmacy_id')
    BEGIN
        ALTER TABLE medic ADD pharmacy_id INT;
        PRINT '✅ تم إضافة عمود pharmacy_id لجدول medic';
    END

    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('medic') AND name = 'created_date')
    BEGIN
        ALTER TABLE medic ADD created_date DATETIME DEFAULT GETDATE();
        PRINT '✅ تم إضافة عمود created_date لجدول medic';
    END
END

-- ===================================
-- 4. إنشاء جدول المبيعات
-- ===================================

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'sales')
BEGIN
    CREATE TABLE sales (
        id INT IDENTITY(1,1) PRIMARY KEY,
        medicine_id INT,
        medicine_name NVARCHAR(255),
        quantity_sold INT,
        price_per_unit DECIMAL(10,2),
        total_amount DECIMAL(10,2),
        sale_date DATETIME DEFAULT GETDATE(),
        employee_name NVARCHAR(255),
        dosage_sold NVARCHAR(255),
        pharmacy_id INT,
        CONSTRAINT FK_sales_pharmacy FOREIGN KEY (pharmacy_id) REFERENCES pharmacies(id),
        CONSTRAINT FK_sales_medic FOREIGN KEY (medicine_id) REFERENCES medic(id)
    );
    PRINT '✅ تم إنشاء جدول sales';
END
ELSE
BEGIN
    -- إضافة الأعمدة المفقودة إذا لم تكن موجودة
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('sales') AND name = 'pharmacy_id')
    BEGIN
        ALTER TABLE sales ADD pharmacy_id INT;
        PRINT '✅ تم إضافة عمود pharmacy_id لجدول sales';
    END

    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('sales') AND name = 'dosage_sold')
    BEGIN
        ALTER TABLE sales ADD dosage_sold NVARCHAR(255);
        PRINT '✅ تم إضافة عمود dosage_sold لجدول sales';
    END
END

-- ===================================
-- 5. إنشاء جدول جلسات الموظفين
-- ===================================

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'employee_sessions')
BEGIN
    CREATE TABLE employee_sessions (
        id INT IDENTITY(1,1) PRIMARY KEY,
        employee_name NVARCHAR(255) NOT NULL,
        login_time DATETIME NOT NULL,
        logout_time DATETIME,
        session_date DATE NOT NULL,
        pharmacy_id INT,
        user_id INT,
        is_active BIT DEFAULT 1,
        CONSTRAINT FK_sessions_pharmacy FOREIGN KEY (pharmacy_id) REFERENCES pharmacies(id),
        CONSTRAINT FK_sessions_user FOREIGN KEY (user_id) REFERENCES users(id)
    );
    PRINT '✅ تم إنشاء جدول employee_sessions';
END
ELSE
BEGIN
    -- إضافة الأعمدة المفقودة إذا لم تكن موجودة
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('employee_sessions') AND name = 'pharmacy_id')
    BEGIN
        ALTER TABLE employee_sessions ADD pharmacy_id INT;
        PRINT '✅ تم إضافة عمود pharmacy_id لجدول employee_sessions';
    END

    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('employee_sessions') AND name = 'user_id')
    BEGIN
        ALTER TABLE employee_sessions ADD user_id INT;
        PRINT '✅ تم إضافة عمود user_id لجدول employee_sessions';
    END
END

-- ===================================
-- 6. إنشاء جدول إعدادات الطباعة
-- ===================================

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'print_settings')
BEGIN
    CREATE TABLE print_settings (
        id INT IDENTITY(1,1) PRIMARY KEY,
        report_type NVARCHAR(100) NOT NULL,
        font_name NVARCHAR(100) DEFAULT 'Arial',
        font_size INT DEFAULT 12,
        header_text NVARCHAR(500),
        footer_text NVARCHAR(500),
        show_logo BIT DEFAULT 1,
        show_date BIT DEFAULT 1,
        show_page_numbers BIT DEFAULT 1,
        margin_top INT DEFAULT 20,
        margin_bottom INT DEFAULT 20,
        margin_left INT DEFAULT 20,
        margin_right INT DEFAULT 20,
        pharmacy_id INT,
        created_date DATETIME DEFAULT GETDATE(),
        updated_date DATETIME DEFAULT GETDATE(),
        CONSTRAINT FK_print_settings_pharmacy FOREIGN KEY (pharmacy_id) REFERENCES pharmacies(id)
    );
    PRINT '✅ تم إنشاء جدول print_settings';
END

-- ===================================
-- 7. إنشاء جدول الأدوية المنشورة
-- ===================================

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'published_medicines')
BEGIN
    CREATE TABLE published_medicines (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacy_id INT NOT NULL,
        medicine_name NVARCHAR(255) NOT NULL,
        medicine_number NVARCHAR(100),
        quantity INT NOT NULL DEFAULT 0,
        expiry_date DATE,
        price_per_unit DECIMAL(10,2) NOT NULL DEFAULT 0,
        description NVARCHAR(1000),
        publish_date DATETIME DEFAULT GETDATE(),
        is_active BIT DEFAULT 1,
        dosage_info NVARCHAR(MAX), -- JSON للجرعات المتعددة
        FOREIGN KEY (pharmacy_id) REFERENCES pharmacies(id)
    );
    PRINT '✅ تم إنشاء جدول published_medicines';
END
ELSE
BEGIN
    -- إضافة الأعمدة المفقودة إذا لم تكن موجودة
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('published_medicines') AND name = 'dosage_info')
    BEGIN
        ALTER TABLE published_medicines ADD dosage_info NVARCHAR(MAX);
        PRINT '✅ تم إضافة عمود dosage_info';
    END
    
    -- تصحيح أسماء الأعمدة إذا كانت خاطئة
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('published_medicines') AND name = 'quantity_available')
    BEGIN
        EXEC sp_rename 'published_medicines.quantity_available', 'quantity', 'COLUMN';
        PRINT '✅ تم تصحيح اسم عمود quantity';
    END
    
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('published_medicines') AND name = 'published_date')
    BEGIN
        EXEC sp_rename 'published_medicines.published_date', 'publish_date', 'COLUMN';
        PRINT '✅ تم تصحيح اسم عمود publish_date';
    END
    
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('published_medicines') AND name = 'is_available')
    BEGIN
        EXEC sp_rename 'published_medicines.is_available', 'is_active', 'COLUMN';
        PRINT '✅ تم تصحيح اسم عمود is_active';
    END
END

-- ===================================
-- 3. إنشاء جدول طلبات الشراء
-- ===================================

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'purchase_requests')
BEGIN
    CREATE TABLE purchase_requests (
        id INT IDENTITY(1,1) PRIMARY KEY,
        buyer_pharmacy_id INT NOT NULL,
        seller_pharmacy_id INT NOT NULL,
        published_medicine_id INT NOT NULL,
        requested_quantity INT NOT NULL,
        offered_price DECIMAL(10,2),
        status NVARCHAR(50) DEFAULT 'pending',
        request_date DATETIME DEFAULT GETDATE(),
        response_date DATETIME,
        response_message NVARCHAR(1000),
        FOREIGN KEY (buyer_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (seller_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (published_medicine_id) REFERENCES published_medicines(id)
    );
    PRINT '✅ تم إنشاء جدول purchase_requests';
END

-- ===================================
-- 4. إنشاء جدول الرسائل
-- ===================================

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacy_messages')
BEGIN
    CREATE TABLE pharmacy_messages (
        id INT IDENTITY(1,1) PRIMARY KEY,
        sender_pharmacy_id INT NOT NULL,
        receiver_pharmacy_id INT NOT NULL,
        message_content NVARCHAR(MAX) NOT NULL,
        send_date DATETIME DEFAULT GETDATE(),
        is_read BIT DEFAULT 0,
        related_request_id INT,
        FOREIGN KEY (sender_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (receiver_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (related_request_id) REFERENCES purchase_requests(id)
    );
    PRINT '✅ تم إنشاء جدول pharmacy_messages';
END

-- ===================================
-- 5. إضافة بيانات افتراضية للصيدليات
-- ===================================

IF NOT EXISTS (SELECT * FROM pharmacies)
BEGIN
    INSERT INTO pharmacies (pharmacy_name, owner_name, phone, address, city, email)
    VALUES 
    (N'صيدلية الشفاء الرئيسية', N'أحمد محمد علي', '01234567890', N'شارع الجامعة، حي الوسط', N'الرياض', '<EMAIL>'),
    (N'صيدلية النور للأدوية', N'فاطمة علي حسن', '01234567891', N'شارع السلام، حي النهضة', N'جدة', '<EMAIL>'),
    (N'صيدلية الصحة والعافية', N'محمد حسن أحمد', '01234567892', N'شارع الملك فهد، حي العليا', N'الدمام', '<EMAIL>'),
    (N'صيدلية الحياة الطبية', N'سارة محمد', '01234567893', N'شارع التحلية، حي الروضة', N'الرياض', '<EMAIL>'),
    (N'صيدلية الأمل الحديثة', N'عبدالله أحمد', '01234567894', N'شارع الأمير سلطان، حي الفيصلية', N'جدة', '<EMAIL>');
    
    PRINT '✅ تم إضافة 5 صيدليات افتراضية';
END

-- ===================================
-- 6. إضافة أدوية تجريبية منشورة
-- ===================================

IF NOT EXISTS (SELECT * FROM published_medicines)
BEGIN
    INSERT INTO published_medicines (pharmacy_id, medicine_name, medicine_number, quantity, expiry_date, price_per_unit, description)
    VALUES 
    -- صيدلية الشفاء
    (1, N'باراسيتامول 500mg', 'PAR500', 150, '2025-12-31', 15.50, N'مسكن للألم وخافض للحرارة، آمن للاستخدام اليومي'),
    (1, N'أموكسيسيلين 250mg', 'AMX250', 80, '2025-10-15', 25.75, N'مضاد حيوي واسع المجال لعلاج الالتهابات البكتيرية'),
    (1, N'فيتامين د 1000 وحدة', 'VITD1000', 200, '2026-06-30', 32.00, N'مكمل غذائي لتقوية العظام والمناعة'),
    
    -- صيدلية النور
    (2, N'إيبوبروفين 400mg', 'IBU400', 120, '2025-11-20', 18.25, N'مضاد للالتهاب ومسكن للألم، فعال للصداع والآلام العضلية'),
    (2, N'أوميبرازول 20mg', 'OME20', 90, '2025-09-10', 28.50, N'لعلاج حموضة المعدة والقرح الهضمية'),
    (2, N'لوراتادين 10mg', 'LOR10', 160, '2026-03-15', 12.50, N'مضاد للحساسية، لا يسبب النعاس'),
    
    -- صيدلية الصحة
    (3, N'أسبرين 100mg', 'ASP100', 300, '2025-08-25', 8.75, N'مسكن ومضاد للالتهاب، يستخدم لحماية القلب'),
    (3, N'ميتفورمين 500mg', 'MET500', 100, '2025-12-01', 22.00, N'لعلاج مرض السكري من النوع الثاني'),
    (3, N'سيتريزين 10mg', 'CET10', 140, '2026-01-30', 14.25, N'مضاد للحساسية طويل المفعول'),
    
    -- صيدلية الحياة
    (4, N'كلاريثروميسين 250mg', 'CLA250', 60, '2025-07-20', 45.00, N'مضاد حيوي قوي للالتهابات الشديدة'),
    (4, N'دومبيريدون 10mg', 'DOM10', 80, '2025-11-05', 19.75, N'لعلاج الغثيان والقيء واضطرابات الهضم'),
    
    -- صيدلية الأمل
    (5, N'سيمفاستاتين 20mg', 'SIM20', 70, '2025-10-10', 35.50, N'لخفض الكوليسترول وحماية القلب'),
    (5, N'رانيتيدين 150mg', 'RAN150', 110, '2025-09-30', 16.00, N'لعلاج حموضة المعدة والحرقة');
    
    PRINT '✅ تم إضافة 13 دواء تجريبي منشور';
END

-- ===================================
-- 7. إضافة المستخدمين الافتراضيين
-- ===================================

-- إضافة المستخدمين الافتراضيين إذا لم يكونوا موجودين
IF NOT EXISTS (SELECT * FROM users WHERE username = 'admin')
BEGIN
    INSERT INTO users (userRole, name, dob, mobile, email, username, pass, pharmacy_id)
    VALUES
    ('Administrator', N'مدير النظام', '1990-01-01', '0123456789', '<EMAIL>', 'admin', 'admin123', 1),
    ('Employee', N'موظف تجريبي 1', '1995-05-15', '0123456788', '<EMAIL>', 'employee1', 'emp123', 1),
    ('Employee', N'موظف تجريبي 2', '1992-08-20', '0123456787', '<EMAIL>', 'employee2', 'emp123', 2),
    ('Administrator', N'مدير الصيدلية الثانية', '1988-12-10', '0123456786', '<EMAIL>', 'admin2', 'admin123', 2),
    ('Employee', N'موظف تجريبي 3', '1996-03-25', '0123456785', '<EMAIL>', 'employee3', 'emp123', 3);

    PRINT '✅ تم إضافة 5 مستخدمين افتراضيين';
END

-- ===================================
-- 8. إضافة إعدادات الطباعة الافتراضية
-- ===================================

IF NOT EXISTS (SELECT * FROM print_settings)
BEGIN
    INSERT INTO print_settings (report_type, font_name, font_size, header_text, footer_text, pharmacy_id)
    VALUES
    ('sales_report', 'Arial', 12, N'تقرير المبيعات - صيدلية الشفاء', N'شكراً لاستخدام نظام إدارة الصيدلية', 1),
    ('inventory_report', 'Arial', 12, N'تقرير الجرد - صيدلية الشفاء', N'تم إنشاء التقرير تلقائياً', 1),
    ('employee_sessions', 'Arial', 12, N'تقرير حضور الموظفين - صيدلية الشفاء', N'نظام إدارة الصيدلية', 1),
    ('medicine_validity', 'Arial', 12, N'تقرير صلاحية الأدوية - صيدلية الشفاء', N'يرجى مراجعة الأدوية منتهية الصلاحية', 1),
    ('sales_report', 'Arial', 12, N'تقرير المبيعات - صيدلية النور', N'شكراً لاستخدام نظام إدارة الصيدلية', 2),
    ('inventory_report', 'Arial', 12, N'تقرير الجرد - صيدلية النور', N'تم إنشاء التقرير تلقائياً', 2);

    PRINT '✅ تم إضافة إعدادات الطباعة الافتراضية';
END

-- ===================================
-- 9. إضافة أدوية تجريبية للمخزون الأساسي
-- ===================================

IF NOT EXISTS (SELECT * FROM medic)
BEGIN
    INSERT INTO medic (mid, mname, mnumber, mDate, eDate, quantity, perUnit, originalQuantity, originalNewQuantity, pharmacy_id, dos2, dos2_qty, dos3, dos3_qty)
    VALUES
    -- صيدلية الشفاء
    ('MED001', N'باراسيتامول', 'PAR500', '2024-01-15', '2025-12-31', 200, 15.50, 200, 200, 1, N'أقراص 250mg', 100, N'شراب 120ml', 50),
    ('MED002', N'أموكسيسيلين', 'AMX250', '2024-02-10', '2025-10-15', 150, 25.75, 150, 150, 1, N'كبسولات 500mg', 75, N'شراب 100ml', 25),
    ('MED003', N'إيبوبروفين', 'IBU400', '2024-01-20', '2025-11-20', 180, 18.25, 180, 180, 1, N'أقراص 200mg', 90, N'جل موضعي', 30),

    -- صيدلية النور
    ('MED004', N'أوميبرازول', 'OME20', '2024-03-05', '2025-09-10', 120, 28.50, 120, 120, 2, N'كبسولات 40mg', 60, NULL, 0),
    ('MED005', N'لوراتادين', 'LOR10', '2024-02-15', '2026-03-15', 200, 12.50, 200, 200, 2, N'شراب 60ml', 50, NULL, 0),

    -- صيدلية الصحة
    ('MED006', N'أسبرين', 'ASP100', '2024-01-10', '2025-08-25', 300, 8.75, 300, 300, 3, N'أقراص 325mg', 150, NULL, 0),
    ('MED007', N'ميتفورمين', 'MET500', '2024-04-01', '2025-12-01', 100, 22.00, 100, 100, 3, N'أقراص 850mg', 50, N'أقراص ممتدة المفعول', 25);

    PRINT '✅ تم إضافة 7 أدوية تجريبية للمخزون الأساسي';
END

-- ===================================
-- 7. إنشاء الإجراءات المخزنة المطلوبة
-- ===================================

-- إجراء نشر دواء جديد
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_PublishMedicine')
    DROP PROCEDURE sp_PublishMedicine;
GO

CREATE PROCEDURE sp_PublishMedicine
    @pharmacy_id INT,
    @medicine_name NVARCHAR(255),
    @medicine_number NVARCHAR(100) = NULL,
    @quantity INT,
    @expiry_date DATE = NULL,
    @price_per_unit DECIMAL(10,2),
    @description NVARCHAR(1000) = NULL,
    @dosage_info NVARCHAR(MAX) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- التحقق من صحة البيانات
        IF @quantity <= 0
        BEGIN
            RAISERROR(N'الكمية يجب أن تكون أكبر من صفر', 16, 1);
            RETURN;
        END
        
        IF @price_per_unit <= 0
        BEGIN
            RAISERROR(N'السعر يجب أن يكون أكبر من صفر', 16, 1);
            RETURN;
        END
        
        -- نشر الدواء
        INSERT INTO published_medicines 
        (pharmacy_id, medicine_name, medicine_number, quantity, expiry_date, price_per_unit, description, dosage_info)
        VALUES 
        (@pharmacy_id, @medicine_name, @medicine_number, @quantity, @expiry_date, @price_per_unit, @description, @dosage_info);
        
        SELECT SCOPE_IDENTITY() as published_id, 'تم نشر الدواء بنجاح' as message;
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
GO

-- إجراء الحصول على الأدوية المنشورة
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetPublishedMedicines')
    DROP PROCEDURE sp_GetPublishedMedicines;
GO

CREATE PROCEDURE sp_GetPublishedMedicines
    @pharmacy_id INT = NULL,
    @search_term NVARCHAR(255) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        pm.id,
        pm.pharmacy_id,
        p.pharmacy_name,
        p.address as pharmacy_address,
        p.city as pharmacy_city,
        pm.medicine_name,
        pm.medicine_number,
        pm.quantity,
        pm.expiry_date,
        pm.price_per_unit,
        pm.description,
        pm.dosage_info,
        pm.publish_date,
        DATEDIFF(day, GETDATE(), pm.expiry_date) as days_to_expiry
    FROM published_medicines pm
    INNER JOIN pharmacies p ON pm.pharmacy_id = p.id
    WHERE pm.is_active = 1
      AND (@pharmacy_id IS NULL OR pm.pharmacy_id = @pharmacy_id)
      AND (@search_term IS NULL OR pm.medicine_name LIKE '%' + @search_term + '%')
      AND (pm.expiry_date IS NULL OR pm.expiry_date > GETDATE())
    ORDER BY pm.publish_date DESC;
END
GO

-- إجراء تحديث دواء منشور
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_UpdatePublishedMedicine')
    DROP PROCEDURE sp_UpdatePublishedMedicine;
GO

CREATE PROCEDURE sp_UpdatePublishedMedicine
    @published_medicine_id INT,
    @quantity INT,
    @price_per_unit DECIMAL(10,2),
    @description NVARCHAR(1000) = NULL,
    @dosage_info NVARCHAR(MAX) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- التحقق من وجود الدواء
        IF NOT EXISTS (SELECT 1 FROM published_medicines WHERE id = @published_medicine_id AND is_active = 1)
        BEGIN
            RAISERROR(N'الدواء المنشور غير موجود أو غير نشط', 16, 1);
            RETURN;
        END
        
        -- التحقق من صحة البيانات
        IF @quantity <= 0
        BEGIN
            RAISERROR(N'الكمية يجب أن تكون أكبر من صفر', 16, 1);
            RETURN;
        END
        
        IF @price_per_unit <= 0
        BEGIN
            RAISERROR(N'السعر يجب أن يكون أكبر من صفر', 16, 1);
            RETURN;
        END
        
        -- تحديث الدواء
        UPDATE published_medicines 
        SET 
            quantity = @quantity,
            price_per_unit = @price_per_unit,
            description = @description,
            dosage_info = @dosage_info
        WHERE id = @published_medicine_id;
        
        SELECT 'تم تحديث الدواء بنجاح' as message;
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
GO

-- إجراء تسجيل مستخدم جديد
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_RegisterUser')
    DROP PROCEDURE sp_RegisterUser;
GO

CREATE PROCEDURE sp_RegisterUser
    @userRole NVARCHAR(50),
    @name NVARCHAR(255),
    @dob DATE = NULL,
    @mobile NVARCHAR(50) = NULL,
    @email NVARCHAR(255) = NULL,
    @username NVARCHAR(100),
    @pass NVARCHAR(255),
    @pharmacy_id INT
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        -- التحقق من عدم وجود اسم المستخدم
        IF EXISTS (SELECT 1 FROM users WHERE username = @username)
        BEGIN
            RAISERROR(N'اسم المستخدم موجود بالفعل', 16, 1);
            RETURN;
        END

        -- إضافة المستخدم الجديد
        INSERT INTO users (userRole, name, dob, mobile, email, username, pass, pharmacy_id)
        VALUES (@userRole, @name, @dob, @mobile, @email, @username, @pass, @pharmacy_id);

        SELECT SCOPE_IDENTITY() as user_id, 'تم تسجيل المستخدم بنجاح' as message;

    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
GO

PRINT '✅ تم إنشاء جميع الإجراءات المخزنة';

-- ===================================
-- 8. التحقق النهائي من البيانات
-- ===================================

PRINT '';
PRINT '=== تقرير حالة قاعدة البيانات النهائي ===';

SELECT
    'users' as table_name,
    COUNT(*) as record_count
FROM users
UNION ALL
SELECT
    'pharmacies' as table_name,
    COUNT(*) as record_count
FROM pharmacies
UNION ALL
SELECT
    'medic' as table_name,
    COUNT(*) as record_count
FROM medic
UNION ALL
SELECT
    'sales' as table_name,
    COUNT(*) as record_count
FROM sales
UNION ALL
SELECT
    'employee_sessions' as table_name,
    COUNT(*) as record_count
FROM employee_sessions
UNION ALL
SELECT
    'print_settings' as table_name,
    COUNT(*) as record_count
FROM print_settings
UNION ALL
SELECT
    'published_medicines' as table_name,
    COUNT(*) as record_count
FROM published_medicines
UNION ALL
SELECT
    'purchase_requests' as table_name,
    COUNT(*) as record_count
FROM purchase_requests
UNION ALL
SELECT
    'pharmacy_messages' as table_name,
    COUNT(*) as record_count
FROM pharmacy_messages;

PRINT '';
PRINT '═══════════════════════════════════════════════════════════════';
PRINT '✅ تم إكمال إعداد نظام إدارة الصيدلية الشامل بنجاح!';
PRINT '✅ Complete Pharmacy Management System Setup Successful!';
PRINT '═══════════════════════════════════════════════════════════════';
PRINT '';
PRINT 'تم إعداد النظام بالكامل مع:';
PRINT 'System fully configured with:';
PRINT '• قاعدة البيانات الموحدة (UnifiedPharmacy)';
PRINT '• Unified Database (UnifiedPharmacy)';
PRINT '• جميع الجداول الأساسية والمساعدة';
PRINT '• All core and supporting tables';
PRINT '• الإجراءات المخزنة المطلوبة';
PRINT '• Required stored procedures';
PRINT '• البيانات التجريبية والافتراضية';
PRINT '• Sample and default data';
PRINT '';
PRINT 'معلومات تسجيل الدخول:';
PRINT 'Login credentials:';
PRINT '• المدير: admin / admin123';
PRINT '• Administrator: admin / admin123';
PRINT '• الموظف: employee1 / emp123';
PRINT '• Employee: employee1 / emp123';
PRINT '';
PRINT 'يمكنك الآن تشغيل نظام إدارة الصيدلية!';
PRINT 'You can now run the Pharmacy Management System!';
PRINT '═══════════════════════════════════════════════════════════════';
