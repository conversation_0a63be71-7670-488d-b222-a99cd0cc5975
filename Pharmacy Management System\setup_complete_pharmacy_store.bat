@echo off
echo ========================================
echo   إعداد متجر الأدوية الكامل
echo   Complete Pharmacy Store Setup
echo ========================================
echo.

echo 🏪 إعداد متجر الأدوية بالكامل...
echo.

echo سيتم تنفيذ الخطوات التالية:
echo ✅ 1. إنشاء جداول متجر الأدوية
echo ✅ 2. إنشاء الإجراءات المخزنة
echo ✅ 3. إنشاء صيدليات افتراضية للاختبار
echo ✅ 4. إضافة أدوية تجريبية
echo ✅ 5. نشر بعض الأدوية للاختبار
echo.

pause
echo.

echo 🚀 بدء الإعداد...
echo.

echo 📊 الخطوة 1/5: إنشاء جداول متجر الأدوية...
sqlcmd -S NARUTO -E -i add_pharmacy_store_to_unified_database.sql

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في الخطوة 1!
    goto :error
)

echo.
echo 🔧 الخطوة 2/5: إنشاء الإجراءات المخزنة...
sqlcmd -S NARUTO -E -i add_pharmacy_store_procedures_unified.sql

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في الخطوة 2!
    goto :error
)

echo.
echo 🏪 الخطوة 3/5: إنشاء صيدليات افتراضية...
sqlcmd -S NARUTO -E -i create_default_pharmacy.sql

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في الخطوة 3!
    goto :error
)

echo.
echo 💊 الخطوة 4/5: إضافة أدوية تجريبية...
sqlcmd -S NARUTO -E -i add_sample_medicines.sql

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في الخطوة 4!
    goto :error
)

echo.
echo 📢 الخطوة 5/5: نشر أدوية تجريبية للاختبار...
sqlcmd -S NARUTO -E -Q "
USE UnifiedPharmacy;

-- نشر بعض الأدوية من الصيدلية الافتراضية
INSERT INTO published_medicines (pharmacy_id, medicine_name, medicine_number, quantity_available, expiry_date, price_per_unit, description, published_date, is_available)
SELECT 
    1 as pharmacy_id,
    mname as medicine_name,
    mnumber as medicine_number,
    CASE 
        WHEN quantity > 50 THEN 20
        WHEN quantity > 20 THEN 10
        ELSE 5
    END as quantity_available,
    eDate as expiry_date,
    perUnit as price_per_unit,
    N'متوفر للبيع - جودة عالية' as description,
    GETDATE() as published_date,
    1 as is_available
FROM medic 
WHERE quantity > 0 
AND eDate > GETDATE()
AND mid IN ('MED001', 'MED003', 'MED005', 'MED007', 'MED009');

-- نشر أدوية من صيدلية النور
INSERT INTO published_medicines (pharmacy_id, medicine_name, medicine_number, quantity_available, expiry_date, price_per_unit, description, published_date, is_available)
SELECT 
    2 as pharmacy_id,
    mname as medicine_name,
    mnumber as medicine_number,
    CASE 
        WHEN quantity > 30 THEN 15
        ELSE 8
    END as quantity_available,
    eDate as expiry_date,
    perUnit * 0.95 as price_per_unit,
    N'عرض خاص - خصم 5%' as description,
    GETDATE() as published_date,
    1 as is_available
FROM medic 
WHERE quantity > 0 
AND eDate > GETDATE()
AND mid IN ('MED002', 'MED004', 'MED006', 'MED008', 'MED010');

PRINT 'تم نشر أدوية تجريبية من صيدليتين مختلفتين';
"

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في الخطوة 5!
    goto :error
)

echo.
echo ========================================
echo   ✅ تم إعداد متجر الأدوية بالكامل!
echo ========================================
echo.

echo 🎯 ما تم إنجازه:
echo.
echo 📊 قاعدة البيانات:
echo   • جداول متجر الأدوية (5 جداول)
echo   • الإجراءات المخزنة (10 إجراءات)
echo   • 3 صيدليات افتراضية
echo   • 10 أدوية تجريبية
echo   • 10 أدوية منشورة للاختبار
echo.
echo 🧪 اختبر الآن:
echo 1. شغل برنامج إدارة الصيدلية
echo 2. سجل دخول كموظف
echo 3. اذهب لمتجر الأدوية
echo 4. تبويب "الأدوية المحلية": 10 أدوية متاحة للنشر
echo 5. تبويب "الأدوية المنشورة": 5 أدوية من صيدليات أخرى
echo 6. تبويب "أدويتي المعروضة": 5 أدوية منشورة من صيدليتك
echo.
echo 💡 ميزات متاحة:
echo   • نشر الأدوية للبيع
echo   • البحث والتصفية
echo   • إرسال طلبات الشراء
echo   • إدارة الأدوية المنشورة
echo   • نظام الإشعارات
echo.

goto :success

:error
echo.
echo ========================================
echo   ❌ حدث خطأ أثناء الإعداد!
echo ========================================
echo.
echo تأكد من:
echo 1. تشغيل SQL Server
echo 2. وجود قاعدة البيانات UnifiedPharmacy
echo 3. صحة اسم الخادم (NARUTO)
echo 4. وجود صلاحيات الإدارة
echo 5. وجود جميع ملفات SQL في نفس المجلد
echo.
echo الملفات المطلوبة:
echo • add_pharmacy_store_to_unified_database.sql
echo • add_pharmacy_store_procedures_unified.sql
echo • create_default_pharmacy.sql
echo • add_sample_medicines.sql
echo.
goto :end

:success
echo 🎉 تهانينا! متجر الأدوية جاهز للاستخدام
echo.
echo إذا واجهت أي مشاكل:
echo • راجع ملف "حل_مشكلة_متجر_الادوية.md"
echo • تحقق من رسائل التشخيص في Visual Studio
echo • استخدم أزرار "تحديث القائمة" في الواجهة
echo.

:end
echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
