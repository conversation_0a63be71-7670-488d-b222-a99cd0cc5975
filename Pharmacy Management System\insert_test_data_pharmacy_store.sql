-- إدراج بيانات تجريبية لاختبار متجر الصيدلية
USE UnifiedPharmacy;

PRINT '=== إدراج بيانات تجريبية لمتجر الصيدلية ===';

-- ===================================
-- 1. إدراج صيدليات تجريبية
-- ===================================
IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacy_code = 'PHARM001')
BEGIN
    INSERT INTO pharmacies (pharmacy_name, pharmacy_code, phone, city, region) VALUES
    ('الصيدلية المركزية', 'PHARM001', '0123456789', 'الرياض', 'الرياض');
    PRINT '✅ تم إدراج الصيدلية المركزية';
END

IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacy_code = 'PHARM002')
BEGIN
    INSERT INTO pharmacies (pharmacy_name, pharmacy_code, phone, city, region) VALUES
    ('صيدلية النهضة', 'PHARM002', '0123456790', 'جدة', 'مكة المكرمة');
    PRINT '✅ تم إدراج صيدلية النهضة';
END

IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacy_code = 'PHARM003')
BEGIN
    INSERT INTO pharmacies (pharmacy_name, pharmacy_code, phone, city, region) VALUES
    ('صيدلية الشفاء', 'PHARM003', '0123456791', 'الدمام', 'الشرقية');
    PRINT '✅ تم إدراج صيدلية الشفاء';
END

-- ===================================
-- 2. إدراج أدوية منشورة تجريبية
-- ===================================
DECLARE @pharmacy1_id INT = (SELECT id FROM pharmacies WHERE pharmacy_code = 'PHARM001');
DECLARE @pharmacy2_id INT = (SELECT id FROM pharmacies WHERE pharmacy_code = 'PHARM002');
DECLARE @pharmacy3_id INT = (SELECT id FROM pharmacies WHERE pharmacy_code = 'PHARM003');

-- أدوية من الصيدلية الأولى
IF NOT EXISTS (SELECT * FROM published_medicines WHERE medicine_name = 'باراسيتامول 500مج' AND pharmacy_id = @pharmacy1_id)
BEGIN
    INSERT INTO published_medicines (pharmacy_id, medicine_name, medicine_number, quantity, expiry_date, price_per_unit, description)
    VALUES (@pharmacy1_id, 'باراسيتامول 500مج', 'MED001', 100, DATEADD(MONTH, 12, GETDATE()), 5.50, 'مسكن للألم وخافض للحرارة');
    PRINT '✅ تم إدراج باراسيتامول من الصيدلية المركزية';
END

-- أدوية من الصيدلية الثانية
IF NOT EXISTS (SELECT * FROM published_medicines WHERE medicine_name = 'أموكسيسيلين 250مج' AND pharmacy_id = @pharmacy2_id)
BEGIN
    INSERT INTO published_medicines (pharmacy_id, medicine_name, medicine_number, quantity, expiry_date, price_per_unit, description)
    VALUES (@pharmacy2_id, 'أموكسيسيلين 250مج', 'MED002', 50, DATEADD(MONTH, 8, GETDATE()), 12.75, 'مضاد حيوي واسع المجال');
    PRINT '✅ تم إدراج أموكسيسيلين من صيدلية النهضة';
END

-- أدوية من الصيدلية الثالثة
IF NOT EXISTS (SELECT * FROM published_medicines WHERE medicine_name = 'فيتامين د 1000 وحدة' AND pharmacy_id = @pharmacy3_id)
BEGIN
    INSERT INTO published_medicines (pharmacy_id, medicine_name, medicine_number, quantity, expiry_date, price_per_unit, description)
    VALUES (@pharmacy3_id, 'فيتامين د 1000 وحدة', 'MED003', 75, DATEADD(MONTH, 18, GETDATE()), 25.00, 'مكمل غذائي لتقوية العظام');
    PRINT '✅ تم إدراج فيتامين د من صيدلية الشفاء';
END

-- ===================================
-- 3. إدراج طلبات شراء تجريبية
-- ===================================
DECLARE @published_med1_id INT = (SELECT TOP 1 id FROM published_medicines WHERE medicine_name = 'باراسيتامول 500مج');
DECLARE @published_med2_id INT = (SELECT TOP 1 id FROM published_medicines WHERE medicine_name = 'أموكسيسيلين 250مج');
DECLARE @published_med3_id INT = (SELECT TOP 1 id FROM published_medicines WHERE medicine_name = 'فيتامين د 1000 وحدة');

-- طلب من الصيدلية الثانية للصيدلية الأولى
IF NOT EXISTS (SELECT * FROM purchase_requests WHERE published_medicine_id = @published_med1_id AND buyer_pharmacy_id = @pharmacy2_id)
BEGIN
    INSERT INTO purchase_requests (buyer_pharmacy_id, seller_pharmacy_id, published_medicine_id, requested_quantity, offered_price, status)
    VALUES (@pharmacy2_id, @pharmacy1_id, @published_med1_id, 20, 5.25, 'pending');
    PRINT '✅ تم إدراج طلب باراسيتامول من صيدلية النهضة للصيدلية المركزية';
END

-- طلب من الصيدلية الثالثة للصيدلية الثانية
IF NOT EXISTS (SELECT * FROM purchase_requests WHERE published_medicine_id = @published_med2_id AND buyer_pharmacy_id = @pharmacy3_id)
BEGIN
    INSERT INTO purchase_requests (buyer_pharmacy_id, seller_pharmacy_id, published_medicine_id, requested_quantity, offered_price, status)
    VALUES (@pharmacy3_id, @pharmacy2_id, @published_med2_id, 15, 12.50, 'pending');
    PRINT '✅ تم إدراج طلب أموكسيسيلين من صيدلية الشفاء لصيدلية النهضة';
END

-- طلب من الصيدلية الأولى للصيدلية الثالثة
IF NOT EXISTS (SELECT * FROM purchase_requests WHERE published_medicine_id = @published_med3_id AND buyer_pharmacy_id = @pharmacy1_id)
BEGIN
    INSERT INTO purchase_requests (buyer_pharmacy_id, seller_pharmacy_id, published_medicine_id, requested_quantity, offered_price, status)
    VALUES (@pharmacy1_id, @pharmacy3_id, @published_med3_id, 10, 24.00, 'pending');
    PRINT '✅ تم إدراج طلب فيتامين د من الصيدلية المركزية لصيدلية الشفاء';
END

-- إضافة طلبات إضافية للاختبار
-- طلبات للصيدلية رقم 1 (لاختبار العرض)
INSERT INTO purchase_requests (buyer_pharmacy_id, seller_pharmacy_id, published_medicine_id, requested_quantity, offered_price, status)
VALUES (@pharmacy2_id, @pharmacy1_id, @published_med1_id, 5, 5.00, 'pending');

INSERT INTO purchase_requests (buyer_pharmacy_id, seller_pharmacy_id, published_medicine_id, requested_quantity, offered_price, status)
VALUES (@pharmacy3_id, @pharmacy1_id, @published_med1_id, 10, 5.75, 'pending');

PRINT '✅ تم إدراج طلبات إضافية للاختبار';

PRINT '=== تم إكمال إدراج البيانات التجريبية ===';

-- عرض إحصائيات
SELECT 'الصيدليات' as النوع, COUNT(*) as العدد FROM pharmacies
UNION ALL
SELECT 'الأدوية المنشورة', COUNT(*) FROM published_medicines
UNION ALL
SELECT 'طلبات الشراء', COUNT(*) FROM purchase_requests;

-- عرض الطلبات المدرجة
SELECT 
    pr.id as 'رقم الطلب',
    pm.medicine_name as 'اسم الدواء',
    p_buyer.pharmacy_name as 'الصيدلية الطالبة',
    p_seller.pharmacy_name as 'الصيدلية البائعة',
    pr.requested_quantity as 'الكمية المطلوبة',
    pr.offered_price as 'السعر المعروض',
    pr.status as 'الحالة'
FROM purchase_requests pr
INNER JOIN published_medicines pm ON pr.published_medicine_id = pm.id
INNER JOIN pharmacies p_buyer ON pr.buyer_pharmacy_id = p_buyer.id
INNER JOIN pharmacies p_seller ON pr.seller_pharmacy_id = p_seller.id
ORDER BY pr.request_date DESC;
