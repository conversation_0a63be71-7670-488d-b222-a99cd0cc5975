﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy_Management_System.AdministratorUC
{
    public partial class UC_Dashbord : UserControl
    {
        Function fn = new Function();
        String query;
        DataSet ds;
        public UC_Dashbord()
        {
            InitializeComponent();

            // الاشتراك في حدث تغيير اللغة
            LanguageManager.LanguageChanged += OnLanguageChanged;
        }

        private void UC_Dashbord_Load(object sender, EventArgs e)
        {
            // تطبيق اللغة الحالية
            ApplyLanguage();

            query = "select count(userRole) from users where userRole = 'Administrator'";
            ds = fn.getData(query);
            SetLabel(ds, AdminLabel);
            query = "select count(userRole) from users where userRole = 'Pharmacist'";
            ds = fn.getData(query);
            SetLabel(ds, PharLabel);

            UpdateTotalUsersText();
        }

        private void SetLabel(DataSet ds,Label lbl)
        {
            if(ds.Tables[0].Rows.Count!= 0)
            {
                lbl.Text = ds.Tables[0].Rows[0][0].ToString();
            }
            else
            {
                lbl.Text = "0";
            }
        }

        private void btnSync_Click(object sender, EventArgs e)
        {
            UC_Dashbord_Load(this, null);
        }

        private void PharLabel_Click(object sender, EventArgs e)
        {

        }

        private void panel2_Paint(object sender, PaintEventArgs e)
        {

        }

        private void UpdateTotalUsersText()
        {
            string totalUsersText = LanguageManager.GetText("Total Users");
            total.Text = string.Format("{0}: {1}", totalUsersText, Int32.Parse(AdminLabel.Text) + Int32.Parse(PharLabel.Text));
        }

        private void OnLanguageChanged(object sender, EventArgs e)
        {
            ApplyLanguage();
        }

        public void ApplyLanguage()
        {
            // تحديث النصوص حسب اللغة المختارة
            if (label1 != null) label1.Text = LanguageManager.GetText("Dashboard");
            if (label3 != null) label3.Text = LanguageManager.GetText("Administrators");
            if (label4 != null) label4.Text = LanguageManager.GetText("Pharmacists");
            if (btnSync != null) btnSync.Text = LanguageManager.GetText("Sync");

            // تحديث نص إجمالي المستخدمين
            UpdateTotalUsersText();

            // الحفاظ على التصميم من اليسار لليمين في جميع اللغات
            this.RightToLeft = RightToLeft.No;
        }

        protected override void OnHandleDestroyed(EventArgs e)
        {
            // إلغاء الاشتراك في أحداث التغيير
            LanguageManager.LanguageChanged -= OnLanguageChanged;
            base.OnHandleDestroyed(e);
        }
    }
}
