@echo off
echo ========================================
echo    بناء صفحة متجر الصيدلية الجديدة
echo ========================================
echo.

echo الملفات الجديدة المضافة:
echo - UC_P_PharmacyStore.cs (صفحة متجر الصيدلية)
echo - PublishMedicineForm.cs (نافذة نشر الدواء)
echo - RequestMedicineForm.cs (نافذة طلب الدواء)
echo.

echo جاري تنظيف المشروع...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"

echo جاري إعادة البناء...
echo.

REM محاولة استخدام MSBuild
set FOUND_MSBUILD=0

if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" "Pharmacy Management System.csproj" /p:Configuration=Debug /t:Rebuild
    set FOUND_MSBUILD=1
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" "Pharmacy Management System.csproj" /p:Configuration=Debug /t:Rebuild
    set FOUND_MSBUILD=1
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" "Pharmacy Management System.csproj" /p:Configuration=Debug /t:Rebuild
    set FOUND_MSBUILD=1
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" "Pharmacy Management System.csproj" /p:Configuration=Debug /t:Rebuild
    set FOUND_MSBUILD=1
)

if %FOUND_MSBUILD%==0 (
    echo لم يتم العثور على MSBuild
    echo يرجى فتح المشروع في Visual Studio وإعادة البناء يدوياً
    echo أو تثبيت Visual Studio Build Tools
    goto :end
)

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo ✅ تم بناء المشروع بنجاح!
    echo ========================================
    echo.
    echo صفحة متجر الصيدلية جاهزة للاستخدام:
    echo.
    echo المميزات المتاحة:
    echo ✓ عرض الأدوية المحلية
    echo ✓ نشر الأدوية في المتجر
    echo ✓ البحث والفلترة الذكية
    echo ✓ طلب الأدوية من الصيدليات الأخرى
    echo ✓ نظام التواصل بين الصيدليات
    echo ✓ إدارة الأدوية المنشورة
    echo.
    echo يمكنك الآن تشغيل البرنامج!
    echo.
) else (
    echo.
    echo ========================================
    echo ❌ فشل في بناء المشروع
    echo ========================================
    echo.
    echo الحلول المقترحة:
    echo 1. فتح المشروع في Visual Studio
    echo 2. الضغط على Build -> Rebuild Solution
    echo 3. إصلاح أي أخطاء في البناء
    echo 4. التأكد من تثبيت Guna.UI2 NuGet Package
    echo.
)

:end
pause
