@echo off
chcp 65001 > nul
title 🔥 نظام التداول الحقيقي - REAL TRADING SYSTEM

echo.
echo ================================================================
echo   🔥 نظام التداول الحقيقي - REAL TRADING SYSTEM
echo   يتداول بأموال حقيقية على MetaTrader 5
echo ================================================================
echo.
echo 🚨 تحذير مهم:
echo ✅ هذا النظام يتداول بأموال حقيقية!
echo ✅ سيتم فتح صفقات فعلية في MetaTrader 5
echo ✅ تأكد من إعداداتك قبل البدء
echo.
echo 📋 متطلبات التشغيل:
echo ✅ MetaTrader 5 يعمل ومسجل دخول
echo ✅ التداول الآلي مفعل
echo ✅ ملف config.ini محدث ببيانات حسابك
echo ✅ رصيد كافي في الحساب
echo.
echo 🎯 خطوات التشغيل:
echo ✅ تأكد من تشغيل MetaTrader 5
echo ✅ اضغط "🔌 اتصال" في الواجهة
echo ✅ تأكد من ظهور "✅ متصل (فعلي)"
echo ✅ اضغط "▶️ بدء التداول"
echo.
echo 🛡️ للأمان:
echo ✅ يمكنك اختبار النظام بـ "وضع تجريبي" أولاً
echo ✅ ثم التبديل إلى "وضع فعلي" عند الاستعداد
echo.

echo Installing required libraries...
pip install MetaTrader5 pandas numpy scikit-learn matplotlib tkinter --quiet

echo.
echo 🚀 Starting Advanced GUI with Real Trading Support...
echo 💡 The system will automatically detect MT5 connection
echo 💡 Use the mode toggle button to switch between real and demo trading
echo.

python intelligent_gui_v2.py

if errorlevel 1 (
    echo.
    echo ❌ GUI failed to start!
    echo 💡 Check the error messages above
    echo 💡 Make sure all dependencies are installed
    echo 💡 Make sure MetaTrader 5 is installed for real trading
    echo.
) else (
    echo.
    echo ✅ GUI closed successfully!
    echo.
)

pause
