#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from mt5_real_crypto_system import MT5RealCryptoSystem
import time

def test_mt5_connection():
    """اختبار الاتصال بـ MetaTrader 5"""
    print("🚀 بدء اختبار الاتصال بـ MetaTrader 5")
    print("=" * 60)
    
    # إنشاء النظام
    print("\n1️⃣ إنشاء النظام...")
    system = MT5RealCryptoSystem(demo_mode=True)
    print("✅ تم إنشاء النظام بنجاح")
    
    # اختبار الاتصال بـ MT5
    print("\n2️⃣ اختبار الاتصال بـ MetaTrader 5...")
    if system.connect():
        print("✅ تم الاتصال بـ MT5 بنجاح!")
        
        # عرض معلومات الحساب
        print("\n3️⃣ معلومات الحساب:")
        account_info = system.get_account_info()
        if account_info:
            print(f"   🏢 الشركة: {account_info.get('company', 'غير متوفر')}")
            print(f"   🖥️ الخادم: {account_info.get('server', 'غير متوفر')}")
            print(f"   👤 الحساب: {account_info.get('login', 'غير متوفر')}")
            print(f"   💰 الرصيد: ${account_info.get('balance', 0):,.2f}")
            print(f"   📊 الربح/الخسارة: ${account_info.get('profit', 0):,.2f}")
            print(f"   🔢 الرافعة المالية: 1:{account_info.get('leverage', 0)}")
        else:
            print("❌ فشل في الحصول على معلومات الحساب")
        
        # عرض الرموز المتوفرة
        print("\n4️⃣ الرموز المتوفرة في MT5:")
        symbols = system.get_mt5_symbols()
        if symbols:
            print(f"   📊 عدد الرموز المتوفرة: {len(symbols)}")
            for symbol in symbols[:10]:  # عرض أول 10 رموز
                print(f"   • {symbol}")
            if len(symbols) > 10:
                print(f"   ... و {len(symbols) - 10} رمز آخر")
        else:
            print("   ⚠️ لم يتم العثور على رموز عملات رقمية")
        
        # اختبار الأسعار
        print("\n5️⃣ اختبار الأسعار:")
        test_symbols = ['BTCUSD', 'ETHUSD', 'BTC', 'ETH']
        
        for symbol in test_symbols:
            print(f"\n   💰 اختبار {symbol}:")
            
            # السعر من MT5
            mt5_price = system.get_mt5_price(symbol)
            if mt5_price:
                print(f"      MT5: ${mt5_price:,.2f}")
            else:
                print(f"      MT5: غير متوفر")
            
            # السعر الخارجي
            if symbol in system.crypto_pairs:
                external_price = system.get_external_crypto_price(symbol)
                if external_price:
                    print(f"      خارجي: ${external_price:,.2f}")
                else:
                    print(f"      خارجي: غير متوفر")
            
            # أفضل سعر
            best_price = system.get_best_price(symbol)
            if best_price['final_price']:
                print(f"      🎯 أفضل سعر: ${best_price['final_price']:,.2f} (المصدر: {best_price['source']})")
            else:
                print(f"      🎯 أفضل سعر: غير متوفر")
        
        # اختبار التحليل
        print("\n6️⃣ اختبار التحليل الفني:")
        test_symbol = 'BTCUSD'
        
        print(f"   🔍 تحليل {test_symbol}...")
        analysis = system.analyze_market(test_symbol)
        
        if 'error' in analysis:
            print(f"   ❌ خطأ في التحليل: {analysis['error']}")
        else:
            print(f"   🎯 القرار: {analysis['decision']}")
            print(f"   📊 نسبة الثقة: {analysis['confidence']:.1f}%")
            print(f"   💰 السعر: ${analysis['price']:,.2f}")
            print(f"   📈 مصدر السعر: {analysis.get('price_source', 'غير محدد')}")
            print(f"   📊 RSI: {analysis.get('rsi', 0):.1f}")
            print(f"   📈 MACD: {analysis.get('macd', 0):.4f}")
        
        # اختبار إعدادات نسبة الثقة
        print("\n7️⃣ اختبار إعدادات نسبة الثقة:")
        test_confidences = [50, 70, 90, 100]
        
        for confidence in test_confidences:
            if system.set_confidence_threshold(confidence):
                print(f"   ✅ تم تحديد نسبة الثقة إلى {confidence}%")
                
                # اختبار شروط الدخول
                if 'error' not in analysis:
                    if system.should_enter_trade(analysis):
                        print(f"      🚀 يمكن الدخول بنسبة ثقة {confidence}%")
                    else:
                        print(f"      ⏳ لا يمكن الدخول بنسبة ثقة {confidence}%")
            else:
                print(f"   ❌ فشل في تحديد نسبة الثقة إلى {confidence}%")
        
        # اختبار الصفقات المفتوحة
        print("\n8️⃣ الصفقات المفتوحة:")
        positions = system.get_open_positions()
        if positions:
            print(f"   📋 عدد الصفقات المفتوحة: {len(positions)}")
            for pos in positions:
                print(f"   • {pos['symbol']}: {pos['type']} {pos['volume']:.2f} @ ${pos['price_open']:,.2f}")
        else:
            print("   📋 لا توجد صفقات مفتوحة")
        
        # قطع الاتصال
        print("\n9️⃣ قطع الاتصال...")
        system.disconnect()
        print("✅ تم قطع الاتصال بنجاح")
        
        print("\n" + "=" * 60)
        print("🎉 تم اكتمال جميع الاختبارات بنجاح!")
        print("✨ النظام جاهز للاستخدام مع MetaTrader 5!")
        
        return True
        
    else:
        print("❌ فشل في الاتصال بـ MetaTrader 5")
        print("\n🔧 تحقق من:")
        print("   • تشغيل MetaTrader 5")
        print("   • تسجيل الدخول في MT5")
        print("   • تفعيل التداول الآلي في MT5")
        print("   • عدم حجب الجدار الناري للبرنامج")
        
        return False

def test_mt5_requirements():
    """اختبار متطلبات MT5"""
    print("\n🔍 فحص متطلبات MetaTrader 5:")
    print("-" * 40)
    
    try:
        import MetaTrader5 as mt5
        print("✅ مكتبة MetaTrader5 متوفرة")
        
        # محاولة الاتصال البسيط
        if mt5.initialize():
            print("✅ يمكن الاتصال بـ MT5")
            
            # فحص معلومات الحساب
            account_info = mt5.account_info()
            if account_info:
                print("✅ يمكن الحصول على معلومات الحساب")
                print(f"   الحساب: {account_info.login}")
                print(f"   الشركة: {account_info.company}")
            else:
                print("⚠️ لا يمكن الحصول على معلومات الحساب")
            
            # فحص الرموز
            symbols = mt5.symbols_get()
            if symbols:
                print(f"✅ يمكن الحصول على الرموز ({len(symbols)} رمز)")
            else:
                print("⚠️ لا يمكن الحصول على الرموز")
            
            mt5.shutdown()
        else:
            print("❌ لا يمكن الاتصال بـ MT5")
            error = mt5.last_error()
            print(f"   كود الخطأ: {error}")
            
    except ImportError:
        print("❌ مكتبة MetaTrader5 غير متوفرة")
        print("   قم بتثبيتها: pip install MetaTrader5")
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")

if __name__ == "__main__":
    try:
        print("🚀 بدء اختبار نظام MetaTrader 5 للعملات الرقمية")
        print("=" * 60)
        
        # فحص المتطلبات
        test_mt5_requirements()
        
        print("\n" + "=" * 60)
        
        # الاختبار الأساسي
        if test_mt5_connection():
            print("\n🎉 جميع الاختبارات نجحت!")
            print("🚀 يمكنك الآن تشغيل الواجهة الرسومية:")
            print("   python mt5_crypto_gui.py")
        else:
            print("\n❌ فشل في بعض الاختبارات")
            
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {str(e)}")
    finally:
        print("\n👋 انتهى الاختبار")
        input("اضغط Enter للخروج...")
