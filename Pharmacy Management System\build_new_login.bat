@echo off
echo Building Pharmacy Management System with new pharmacy code login...

cd "Pharmacy Management System"

echo Cleaning previous build...
if exist "bin\Debug" rmdir /s /q "bin\Debug" 2>nul
if exist "obj\Debug" rmdir /s /q "obj\Debug" 2>nul

echo Building project...
"C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" "Pharmacy Management System.csproj" /p:Configuration=Debug /p:Platform="Any CPU" /verbosity:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Build successful!
    echo.
    echo Starting application...
    start "" "bin\Debug\Pharmacy Management System.exe"
) else (
    echo.
    echo ❌ Build failed!
    echo Check the error messages above.
    pause
)

cd ..
