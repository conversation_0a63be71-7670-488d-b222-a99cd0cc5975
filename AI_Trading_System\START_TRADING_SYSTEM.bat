@echo off
chcp 65001 > nul
title 🌐 Smart Trading System - Language Selection

:MENU
cls
echo.
echo ===============================================
echo 🌐 Smart Trading System - Language Selection
echo ===============================================
echo.
echo Choose your preferred language:
echo اختر لغتك المفضلة:
echo.
echo [1] 🇸🇦 Arabic Interface (الواجهة العربية)
echo [2] 🇺🇸 English Interface
echo [3] 📖 View Documentation (عرض الدليل)
echo [4] ❌ Exit (خروج)
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto ARABIC
if "%choice%"=="2" goto ENGLISH
if "%choice%"=="3" goto DOCS
if "%choice%"=="4" goto EXIT

echo Invalid choice. Please try again.
echo اختيار غير صحيح. حاول مرة أخرى.
pause
goto MENU

:ARABIC
cls
echo.
echo 🇸🇦 Starting Arabic Interface...
echo 🚀 بدء تشغيل الواجهة العربية...
echo.
call START_TRADING.bat
goto MENU

:ENGLISH
cls
echo.
echo 🇺🇸 Starting English Interface...
echo 🚀 Starting English Interface...
echo.
call START_ENGLISH_TRADING.bat
goto MENU

:DOCS
cls
echo.
echo 📖 Available Documentation:
echo 📚 الوثائق المتوفرة:
echo.
echo [1] 🇸🇦 Arabic Guide (CONFIDENCE_GUIDE.md)
echo [2] 🇺🇸 English Guide (ENGLISH_USER_GUIDE.md)
echo [3] 🌐 Language Comparison (LANGUAGE_VERSIONS.md)
echo [4] 🔙 Back to Main Menu
echo.
set /p doc_choice="Choose documentation (1-4): "

if "%doc_choice%"=="1" start notepad CONFIDENCE_GUIDE.md
if "%doc_choice%"=="2" start notepad ENGLISH_USER_GUIDE.md
if "%doc_choice%"=="3" start notepad LANGUAGE_VERSIONS.md
if "%doc_choice%"=="4" goto MENU

echo.
echo Press any key to return to menu...
echo اضغط أي مفتاح للعودة للقائمة...
pause > nul
goto MENU

:EXIT
cls
echo.
echo 👋 Thank you for using Smart Trading System!
echo 👋 شكراً لاستخدام نظام التداول الذكي!
echo.
pause
exit
