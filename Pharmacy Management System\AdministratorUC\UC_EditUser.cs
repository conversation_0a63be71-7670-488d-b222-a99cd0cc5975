using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace Pharmacy_Management_System.AdministratorUC
{
    public partial class UC_EditUser : UserControl
    {
        Function fn = new Function();
        String query;

        public UC_EditUser()
        {
            InitializeComponent();

            // الاشتراك في حدث تغيير اللغة
            LanguageManager.LanguageChanged += OnLanguageChanged;
        }

        private void UC_EditUser_Load(object sender, EventArgs e)
        {
            // تطبيق اللغة الحالية
            ApplyLanguage();
            
            loadUsers();
        }

        private void loadUsers()
        {
            try
            {
                query = "SELECT username, name, email, mobile, userRole, dob FROM users";
                DataSet ds = fn.getData(query);

                if (ds.Tables.Count > 0)
                {
                    guna2DataGridView1.DataSource = ds.Tables[0];
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Error loading data") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnUpdate_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(txtUsername.Text))
                {
                    MessageBox.Show(LanguageManager.GetText("Please enter username"), LanguageManager.GetText("Warning"), MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // بناء الاستعلام مع الحقول الجديدة
                string updateQuery = "UPDATE users SET name='" + txtName.Text + "', email='" + txtEmail.Text +
                       "', mobile='" + txtMobile.Text + "', userRole='" + cmbUserRole.Text +
                       "', dob='" + dtpDateOfBirth.Value.ToString("yyyy-MM-dd") + "'";

                // إضافة كلمة المرور فقط إذا تم إدخالها
                if (!string.IsNullOrEmpty(txtPassword.Text))
                {
                    updateQuery += ", pass='" + txtPassword.Text + "'";
                }

                updateQuery += " WHERE username='" + txtUsername.Text + "'";

                fn.setData(updateQuery, LanguageManager.GetText("User data updated successfully"));
                loadUsers();
                clearFields();
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Update error") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(txtUsername.Text))
                {
                    MessageBox.Show(LanguageManager.GetText("Please enter username"), LanguageManager.GetText("Warning"), MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                DialogResult result = MessageBox.Show(LanguageManager.GetText("Are you sure you want to delete this user?"), LanguageManager.GetText("Delete Confirmation"),
                                                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    query = "DELETE FROM users WHERE username='" + txtUsername.Text + "'";
                    fn.setData(query, LanguageManager.GetText("User deleted successfully"));
                    loadUsers();
                    clearFields();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Delete error") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            clearFields();
        }

        private void clearFields()
        {
            txtUsername.Clear();
            txtName.Clear();
            txtEmail.Clear();
            txtMobile.Clear();
            txtPassword.Clear();
            cmbUserRole.SelectedIndex = -1;
            dtpDateOfBirth.Value = DateTime.Now;
        }

        private void guna2DataGridView1_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0)
                {
                    DataGridViewRow row = guna2DataGridView1.Rows[e.RowIndex];
                    txtUsername.Text = row.Cells["username"].Value?.ToString() ?? "";
                    txtName.Text = row.Cells["name"].Value?.ToString() ?? "";
                    txtEmail.Text = row.Cells["email"].Value?.ToString() ?? "";
                    txtMobile.Text = row.Cells["mobile"].Value?.ToString() ?? "";

                    // تعيين نوع المستخدم في ComboBox
                    string userRole = row.Cells["userRole"].Value?.ToString() ?? "";
                    cmbUserRole.Text = userRole;

                    // تعيين تاريخ الولادة
                    string dobString = row.Cells["dob"].Value?.ToString() ?? "";
                    if (DateTime.TryParse(dobString, out DateTime dob))
                    {
                        dtpDateOfBirth.Value = dob;
                    }

                    // مسح حقل كلمة المرور (لأمان أكثر)
                    txtPassword.Clear();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Error selecting data") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OnLanguageChanged(object sender, EventArgs e)
        {
            ApplyLanguage();
        }

        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            try
            {
                string searchText = txtSearch.Text.Trim();

                if (string.IsNullOrEmpty(searchText))
                {
                    loadUsers(); // إذا كان مربع البحث فارغ، اعرض جميع المستخدمين
                }
                else
                {
                    // البحث في اسم المستخدم أو الاسم
                    query = "SELECT username, name, email, mobile, userRole, dob FROM users WHERE username LIKE '%" + searchText + "%' OR name LIKE '%" + searchText + "%'";
                    DataSet ds = fn.getData(query);

                    if (ds.Tables.Count > 0)
                    {
                        guna2DataGridView1.DataSource = ds.Tables[0];
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Search error") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public void ApplyLanguage()
        {
            // تحديث النصوص حسب اللغة المختارة
            if (label1 != null) label1.Text = LanguageManager.GetText("Edit User");
            if (label2 != null) label2.Text = LanguageManager.GetText("Username");
            if (label3 != null) label3.Text = LanguageManager.GetText("Name");
            if (label4 != null) label4.Text = LanguageManager.GetText("Email");
            if (label5 != null) label5.Text = LanguageManager.GetText("Mobile Number");
            if (lblPassword != null) lblPassword.Text = LanguageManager.GetText("Password");
            if (lblDateOfBirth != null) lblDateOfBirth.Text = LanguageManager.GetText("Date of Birth");
            if (lblUserRole != null) lblUserRole.Text = LanguageManager.GetText("User Role");
            if (lblSearch != null) lblSearch.Text = LanguageManager.GetText("Search");
            if (txtSearch != null) txtSearch.PlaceholderText = LanguageManager.GetText("Search by username or name...");
            if (txtPassword != null) txtPassword.PlaceholderText = LanguageManager.GetText("Enter new password...");
            if (btnUpdate != null) btnUpdate.Text = LanguageManager.GetText("Update");
            if (btnDelete != null) btnDelete.Text = LanguageManager.GetText("Delete");
            if (btnClear != null) btnClear.Text = LanguageManager.GetText("Clear");

            // الحفاظ على التصميم من اليسار لليمين في جميع اللغات
            this.RightToLeft = RightToLeft.No;
        }

        protected override void OnHandleDestroyed(EventArgs e)
        {
            // إلغاء الاشتراك في أحداث التغيير
            LanguageManager.LanguageChanged -= OnLanguageChanged;
            base.OnHandleDestroyed(e);
        }
    }
}
