-- إنشاء قاعدة البيانات الموحدة للصيدلية
-- Unified Pharmacy Database Creation

-- إنشاء قاعدة البيانات الجديدة
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'UnifiedPharmacy')
BEGIN
    CREATE DATABASE UnifiedPharmacy;
    PRINT 'تم إنشاء قاعدة البيانات الموحدة: UnifiedPharmacy';
END
GO

USE UnifiedPharmacy;
GO

PRINT '========================================';
PRINT 'إنشاء قاعدة البيانات الموحدة للصيدلية';
PRINT '========================================';

-- ===================================
-- 1. جدول الصيدليات
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies' AND xtype='U')
BEGIN
    CREATE TABLE pharmacies (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyCode VARCHAR(20) UNIQUE NOT NULL,
        pharmacyName NVARCHAR(250) NOT NULL,
        ownerName NVARCHAR(250) NOT NULL,
        licenseNumber VARCHAR(100) NOT NULL,
        address NVARCHAR(500) NOT NULL,
        city NVARCHAR(100) NOT NULL,
        region NVARCHAR(100) NOT NULL,
        phone VARCHAR(20) NOT NULL,
        email VARCHAR(250) NOT NULL,
        isActive BIT DEFAULT 1,
        registrationDate DATETIME DEFAULT GETDATE(),
        lastOnline DATETIME DEFAULT GETDATE(),
        subscriptionType VARCHAR(50) DEFAULT 'Basic',
        subscriptionExpiry DATETIME NULL,
        connectionString NVARCHAR(500) NULL,
        apiKey VARCHAR(100) UNIQUE NULL,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE()
    );
    PRINT 'تم إنشاء جدول pharmacies';
END

-- ===================================
-- 2. جدول المستخدمين الموحد
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
BEGIN
    CREATE TABLE users (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyId INT NOT NULL,
        userRole VARCHAR(50) NOT NULL, -- Administrator, Employee, Pharmacist
        name NVARCHAR(250) NOT NULL,
        dob VARCHAR(250) NOT NULL,
        mobile BIGINT NOT NULL,
        email VARCHAR(250) NOT NULL,
        username VARCHAR(250) UNIQUE NOT NULL,
        pass VARCHAR(250) NOT NULL,
        isActive BIT DEFAULT 1,
        lastLogin DATETIME NULL,
        permissions TEXT NULL,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
    );
    PRINT 'تم إنشاء جدول users الموحد';
END

-- ===================================
-- 3. جدول الأدوية الموحد
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='medicines' AND xtype='U')
BEGIN
    CREATE TABLE medicines (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyId INT NOT NULL,
        mid VARCHAR(250) NOT NULL,
        mname NVARCHAR(250) NOT NULL,
        mnumber VARCHAR(250) NOT NULL,
        mDate VARCHAR(250) NOT NULL,
        eDate VARCHAR(250) NOT NULL,
        quantity BIGINT NOT NULL DEFAULT 0,
        perUnit BIGINT NOT NULL,
        lu VARCHAR(250) NOT NULL,
        br VARCHAR(250) NOT NULL,
        
        -- الحقول الإضافية
        newEDate VARCHAR(250) NULL,
        newQuantity BIGINT NULL DEFAULT 0,
        allqun BIGINT NULL DEFAULT 0,
        mnumber_qty INT NULL DEFAULT 0,
        newMDate VARCHAR(250) NULL,
        
        -- حقول الجرعات
        dos2 VARCHAR(100) NULL,
        dos2_qty INT NULL DEFAULT 0,
        dos3 VARCHAR(100) NULL,
        dos3_qty INT NULL DEFAULT 0,
        dos4 VARCHAR(100) NULL,
        dos4_qty INT NULL DEFAULT 0,
        
        -- الكميات الأصلية
        originalQuantity BIGINT DEFAULT 0,
        originalNewQuantity BIGINT DEFAULT 0,
        
        -- للشبكة الأونلاين
        isAvailableForSale BIT DEFAULT 1,
        requiresPrescription BIT DEFAULT 0,
        manufacturer NVARCHAR(250) NULL,
        category NVARCHAR(100) NULL,
        dosageForm NVARCHAR(100) NULL,
        strength NVARCHAR(100) NULL,
        unitPrice DECIMAL(10,2) NULL,
        wholesalePrice DECIMAL(10,2) NULL,
        expiryDate DATE NULL,
        
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
    );
    PRINT 'تم إنشاء جدول medicines الموحد';
END

-- ===================================
-- 4. جدول المبيعات
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='sales' AND xtype='U')
BEGIN
    CREATE TABLE sales (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyId INT NOT NULL,
        mid VARCHAR(250) NOT NULL,
        medicineName NVARCHAR(250) NOT NULL,
        dosage VARCHAR(100) NOT NULL,
        quantity INT NOT NULL,
        pricePerUnit BIGINT NOT NULL,
        totalPrice BIGINT NOT NULL,
        employeeUsername VARCHAR(250) NOT NULL,
        employeeName NVARCHAR(250) NOT NULL,
        saleDate DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
    );
    PRINT 'تم إنشاء جدول sales';
END

-- ===================================
-- 5. جدول جلسات الموظفين
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='employee_sessions' AND xtype='U')
BEGIN
    CREATE TABLE employee_sessions (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyId INT NOT NULL,
        username VARCHAR(250),
        employeeName NVARCHAR(250),
        loginTime DATETIME,
        logoutTime DATETIME NULL,
        sessionDate DATE,
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
    );
    PRINT 'تم إنشاء جدول employee_sessions';
END

-- ===================================
-- 6. جدول طلبات الشبكة
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='network_orders' AND xtype='U')
BEGIN
    CREATE TABLE network_orders (
        id INT IDENTITY(1,1) PRIMARY KEY,
        orderNumber VARCHAR(50) UNIQUE NOT NULL,
        buyerPharmacyId INT NOT NULL,
        sellerPharmacyId INT NOT NULL,
        medicineId INT NOT NULL,
        requestedQuantity INT NOT NULL,
        unitPrice DECIMAL(10,2) NOT NULL,
        totalAmount DECIMAL(10,2) NOT NULL,
        orderStatus VARCHAR(50) DEFAULT 'Pending', -- Pending, Approved, Rejected, Completed
        orderDate DATETIME DEFAULT GETDATE(),
        responseDate DATETIME NULL,
        completionDate DATETIME NULL,
        notes NVARCHAR(500) NULL,
        FOREIGN KEY (buyerPharmacyId) REFERENCES pharmacies(id),
        FOREIGN KEY (sellerPharmacyId) REFERENCES pharmacies(id),
        FOREIGN KEY (medicineId) REFERENCES medicines(id)
    );
    PRINT 'تم إنشاء جدول network_orders';
END

-- ===================================
-- 7. جدول المحادثات
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacy_messages' AND xtype='U')
BEGIN
    CREATE TABLE pharmacy_messages (
        id INT IDENTITY(1,1) PRIMARY KEY,
        senderPharmacyId INT NOT NULL,
        receiverPharmacyId INT NOT NULL,
        messageText NVARCHAR(1000) NOT NULL,
        messageType VARCHAR(50) DEFAULT 'General', -- General, Order, Inquiry
        relatedOrderId INT NULL,
        isRead BIT DEFAULT 0,
        sentDate DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (senderPharmacyId) REFERENCES pharmacies(id),
        FOREIGN KEY (receiverPharmacyId) REFERENCES pharmacies(id),
        FOREIGN KEY (relatedOrderId) REFERENCES network_orders(id)
    );
    PRINT 'تم إنشاء جدول pharmacy_messages';
END

-- ===================================
-- 8. جدول إعدادات الطباعة
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='print_settings' AND xtype='U')
BEGIN
    CREATE TABLE print_settings (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyId INT NOT NULL,
        reportType VARCHAR(100) NOT NULL,
        fontSize INT DEFAULT 12,
        fontFamily VARCHAR(100) DEFAULT 'Arial',
        headerText NVARCHAR(500) DEFAULT '',
        footerText NVARCHAR(500) DEFAULT '',
        showLogo BIT DEFAULT 1,
        showDate BIT DEFAULT 1,
        showPageNumbers BIT DEFAULT 1,
        marginTop INT DEFAULT 20,
        marginBottom INT DEFAULT 20,
        marginLeft INT DEFAULT 20,
        marginRight INT DEFAULT 20,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
    );
    PRINT 'تم إنشاء جدول print_settings';
END

-- ===================================
-- 9. إضافة صيدلية افتراضية
-- ===================================

-- إضافة صيدلية افتراضية إذا لم تكن موجودة
IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacyCode = 'MAIN001')
BEGIN
    INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, licenseNumber, address, city, region, phone, email)
    VALUES ('MAIN001', 'الصيدلية الرئيسية', 'مدير النظام', 'LIC001', 'العنوان الرئيسي', 'المدينة', 'المنطقة', '**********', '<EMAIL>');
    PRINT 'تم إضافة الصيدلية الافتراضية';
END

-- ===================================
-- 10. نقل البيانات من قواعد البيانات القديمة
-- ===================================

PRINT '';
PRINT 'نقل البيانات من قواعد البيانات القديمة...';

-- نقل المستخدمين من قاعدة البيانات المحلية
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'pharmacy')
BEGIN
    DECLARE @pharmacyId INT = (SELECT id FROM pharmacies WHERE pharmacyCode = 'MAIN001');

    INSERT INTO users (pharmacyId, userRole, name, dob, mobile, email, username, pass)
    SELECT @pharmacyId, userRole, name, dob, mobile, email, username, pass
    FROM pharmacy.dbo.users
    WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = pharmacy.dbo.users.username);

    PRINT 'تم نقل المستخدمين من قاعدة البيانات المحلية';
END

-- نقل الأدوية من قاعدة البيانات المحلية
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'pharmacy')
BEGIN
    DECLARE @pharmacyId2 INT = (SELECT id FROM pharmacies WHERE pharmacyCode = 'MAIN001');

    INSERT INTO medicines (pharmacyId, mid, mname, mnumber, mDate, eDate, quantity, perUnit, lu, br,
                          newEDate, newQuantity, allqun, mnumber_qty, newMDate,
                          dos2, dos2_qty, dos3, dos3_qty, dos4, dos4_qty,
                          originalQuantity, originalNewQuantity)
    SELECT @pharmacyId2, mid, mname, mnumber, mDate, eDate, quantity, perUnit, lu, br,
           newEDate, newQuantity, allqun, mnumber_qty, newMDate,
           dos2, dos2_qty, dos3, dos3_qty, dos4, dos4_qty,
           originalQuantity, originalNewQuantity
    FROM pharmacy.dbo.medic
    WHERE NOT EXISTS (SELECT 1 FROM medicines WHERE mid = pharmacy.dbo.medic.mid AND pharmacyId = @pharmacyId2);

    PRINT 'تم نقل الأدوية من قاعدة البيانات المحلية';
END

-- نقل المبيعات من قاعدة البيانات المحلية
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'pharmacy')
   AND EXISTS (SELECT * FROM pharmacy.INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'sales')
BEGIN
    DECLARE @pharmacyId3 INT = (SELECT id FROM pharmacies WHERE pharmacyCode = 'MAIN001');

    INSERT INTO sales (pharmacyId, mid, medicineName, dosage, quantity, pricePerUnit, totalPrice, employeeUsername, employeeName, saleDate)
    SELECT @pharmacyId3, mid, medicineName, dosage, quantity, pricePerUnit, totalPrice, employeeUsername, employeeName, saleDate
    FROM pharmacy.dbo.sales;

    PRINT 'تم نقل المبيعات من قاعدة البيانات المحلية';
END

-- نقل جلسات الموظفين من قاعدة البيانات المحلية
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'pharmacy')
   AND EXISTS (SELECT * FROM pharmacy.INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'employee_sessions')
BEGIN
    DECLARE @pharmacyId4 INT = (SELECT id FROM pharmacies WHERE pharmacyCode = 'MAIN001');

    INSERT INTO employee_sessions (pharmacyId, username, employeeName, loginTime, logoutTime, sessionDate)
    SELECT @pharmacyId4, username, employeeName, loginTime, logoutTime, sessionDate
    FROM pharmacy.dbo.employee_sessions;

    PRINT 'تم نقل جلسات الموظفين من قاعدة البيانات المحلية';
END

PRINT '';
PRINT '========================================';
PRINT 'تم إنشاء قاعدة البيانات الموحدة بنجاح!';
PRINT '';
PRINT 'الإحصائيات:';

SELECT
    'الصيدليات' as النوع,
    COUNT(*) as العدد
FROM pharmacies
WHERE isActive = 1

UNION ALL

SELECT
    'المستخدمين' as النوع,
    COUNT(*) as العدد
FROM users
WHERE isActive = 1

UNION ALL

SELECT
    'الأدوية' as النوع,
    COUNT(*) as العدد
FROM medicines

UNION ALL

SELECT
    'المبيعات' as النوع,
    COUNT(*) as العدد
FROM sales

UNION ALL

SELECT
    'جلسات الموظفين' as النوع,
    COUNT(*) as العدد
FROM employee_sessions;

PRINT '========================================';
