using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DGVPrinterHelper;

namespace Pharmacy_Management_System.AdministratorUC
{
    public partial class UC_EmployeeSessions : UserControl
    {
        Function fn = new Function();
        String query;
        DataSet ds;

        public UC_EmployeeSessions()
        {
            InitializeComponent();

            // الاشتراك في حدث تغيير اللغة
            LanguageManager.LanguageChanged += OnLanguageChanged;
        }

        private void UC_EmployeeSessions_Load(object sender, EventArgs e)
        {
            // تطبيق اللغة الحالية
            ApplyLanguage();

            // تحميل بيانات جلسات الموظفين
            LoadEmployeeSessions();
        }

        private void LoadEmployeeSessions()
        {
            try
            {
                // إنشاء جدول جلسات الموظفين إذا لم يكن موجوداً
                createTablesIfNotExists();

                // استعلام لجلب جميع جلسات الموظفين مرتبة حسب التاريخ والوقت
                string currentlyOnlineText = LanguageManager.CurrentLanguage == "ar" ? "متصل حالياً" : "Currently Online";
                string minutesText = LanguageManager.CurrentLanguage == "ar" ? "دقيقة" : "minutes";

                query = @"SELECT
                            id as 'ID',
                            employeeName as 'EmployeeName',
                            username as 'Username',
                            loginTime as 'LoginTime',
                            logoutTime as 'LogoutTime',
                            sessionDate as 'SessionDate',
                            CASE
                                WHEN logoutTime IS NULL THEN '" + currentlyOnlineText + @"'
                                ELSE CAST(DATEDIFF(MINUTE, loginTime, logoutTime) AS VARCHAR) + ' " + minutesText + @"'
                            END as 'SessionDuration'
                          FROM employee_sessions
                          WHERE pharmacy_id = " + SessionManager.CurrentPharmacyId + @"
                          ORDER BY sessionDate DESC, loginTime DESC";

                ds = fn.getData(query);
                guna2DataGridView1.DataSource = ds.Tables[0];

                // تنسيق الجدول وتطبيق الترجمة
                FormatDataGridView();
                ApplyColumnTranslations();
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Error loading employee sessions data") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void FormatDataGridView()
        {
            if (guna2DataGridView1.Columns.Count > 0)
            {
                // تنسيق عرض الأعمدة
                guna2DataGridView1.Columns[0].Width = 80;  // الرقم
                guna2DataGridView1.Columns[1].Width = 150; // اسم الموظف
                guna2DataGridView1.Columns[2].Width = 120; // اسم المستخدم
                guna2DataGridView1.Columns[3].Width = 150; // وقت الدخول
                guna2DataGridView1.Columns[4].Width = 150; // وقت الخروج
                guna2DataGridView1.Columns[5].Width = 120; // تاريخ الجلسة
                guna2DataGridView1.Columns[6].Width = 120; // مدة الجلسة

                // تنسيق الألوان
                guna2DataGridView1.DefaultCellStyle.BackColor = Color.White;
                guna2DataGridView1.DefaultCellStyle.ForeColor = Color.Black;
                guna2DataGridView1.DefaultCellStyle.SelectionBackColor = Color.LightBlue;
                guna2DataGridView1.DefaultCellStyle.SelectionForeColor = Color.Black;

                // تنسيق رأس الجدول
                guna2DataGridView1.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(64, 64, 64);
                guna2DataGridView1.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
                guna2DataGridView1.ColumnHeadersDefaultCellStyle.Font = new Font("Arial", 10, FontStyle.Bold);

                // تنسيق الخطوط
                guna2DataGridView1.DefaultCellStyle.Font = new Font("Arial", 9);
                guna2DataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
                guna2DataGridView1.AllowUserToAddRows = false;
                guna2DataGridView1.ReadOnly = true;
            }
        }

        private void ApplyColumnTranslations()
        {
            if (guna2DataGridView1.Columns.Count > 0)
            {
                if (guna2DataGridView1.Columns.Count > 0) guna2DataGridView1.Columns[0].HeaderText = LanguageManager.GetText("ID");
                if (guna2DataGridView1.Columns.Count > 1) guna2DataGridView1.Columns[1].HeaderText = LanguageManager.GetText("Employee Name");
                if (guna2DataGridView1.Columns.Count > 2) guna2DataGridView1.Columns[2].HeaderText = LanguageManager.GetText("Username");
                if (guna2DataGridView1.Columns.Count > 3) guna2DataGridView1.Columns[3].HeaderText = LanguageManager.GetText("Login Time");
                if (guna2DataGridView1.Columns.Count > 4) guna2DataGridView1.Columns[4].HeaderText = LanguageManager.GetText("Logout Time");
                if (guna2DataGridView1.Columns.Count > 5) guna2DataGridView1.Columns[5].HeaderText = LanguageManager.GetText("Session Date");
                if (guna2DataGridView1.Columns.Count > 6) guna2DataGridView1.Columns[6].HeaderText = LanguageManager.GetText("Session Duration");
            }
        }

        private void createTablesIfNotExists()
        {
            try
            {
                // إنشاء جدول تسجيل دخول/خروج الموظفين
                query = @"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='employee_sessions' AND xtype='U')
                         CREATE TABLE employee_sessions (
                             id INT IDENTITY(1,1) PRIMARY KEY,
                             username VARCHAR(250),
                             employeeName VARCHAR(250),
                             loginTime DATETIME,
                             logoutTime DATETIME NULL,
                             sessionDate DATE,
                             pharmacy_id INT
                         )";
                fn.setData(query, "");
            }
            catch (Exception)
            {
                // تجاهل الأخطاء في إنشاء الجداول إذا كانت موجودة بالفعل
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadEmployeeSessions();
        }

        private void btnFilterByDate_Click(object sender, EventArgs e)
        {
            try
            {
                DateTime selectedDate = dateTimePicker1.Value.Date;
                
                string currentlyOnlineText = LanguageManager.CurrentLanguage == "ar" ? "متصل حالياً" : "Currently Online";
                string minutesText = LanguageManager.CurrentLanguage == "ar" ? "دقيقة" : "minutes";

                query = @"SELECT
                            id as 'ID',
                            employeeName as 'EmployeeName',
                            username as 'Username',
                            loginTime as 'LoginTime',
                            logoutTime as 'LogoutTime',
                            sessionDate as 'SessionDate',
                            CASE
                                WHEN logoutTime IS NULL THEN '" + currentlyOnlineText + @"'
                                ELSE CAST(DATEDIFF(MINUTE, loginTime, logoutTime) AS VARCHAR) + ' " + minutesText + @"'
                            END as 'SessionDuration'
                          FROM employee_sessions
                          WHERE sessionDate = '" + selectedDate.ToString("yyyy-MM-dd") + @"'
                            AND pharmacy_id = " + SessionManager.CurrentPharmacyId + @"
                          ORDER BY loginTime DESC";

                ds = fn.getData(query);
                guna2DataGridView1.DataSource = ds.Tables[0];
                FormatDataGridView();
                ApplyColumnTranslations();
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Error filtering data") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void txtSearchEmployee_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtSearchEmployee.Text))
                {
                    LoadEmployeeSessions();
                    return;
                }

                string currentlyOnlineText = LanguageManager.CurrentLanguage == "ar" ? "متصل حالياً" : "Currently Online";
                string minutesText = LanguageManager.CurrentLanguage == "ar" ? "دقيقة" : "minutes";

                query = @"SELECT
                            id as 'ID',
                            employeeName as 'EmployeeName',
                            username as 'Username',
                            loginTime as 'LoginTime',
                            logoutTime as 'LogoutTime',
                            sessionDate as 'SessionDate',
                            CASE
                                WHEN logoutTime IS NULL THEN '" + currentlyOnlineText + @"'
                                ELSE CAST(DATEDIFF(MINUTE, loginTime, logoutTime) AS VARCHAR) + ' " + minutesText + @"'
                            END as 'SessionDuration'
                          FROM employee_sessions
                          WHERE pharmacy_id = " + SessionManager.CurrentPharmacyId + @"
                            AND (employeeName LIKE '%" + txtSearchEmployee.Text + @"%'
                             OR username LIKE '%" + txtSearchEmployee.Text + @"%')
                          ORDER BY sessionDate DESC, loginTime DESC";

                ds = fn.getData(query);
                guna2DataGridView1.DataSource = ds.Tables[0];
                FormatDataGridView();
                ApplyColumnTranslations();
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Search error") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OnLanguageChanged(object sender, EventArgs e)
        {
            ApplyLanguage();
            // إعادة تحميل البيانات لتطبيق الترجمة على محتوى الجدول
            LoadEmployeeSessions();
        }

        private void ApplyLanguage()
        {
            // تطبيق الترجمات على العناصر
            if (label1 != null) label1.Text = LanguageManager.GetText("Employee Login/Logout Registration");
            if (btnRefresh != null) btnRefresh.Text = LanguageManager.GetText("Refresh");
            if (btnFilterByDate != null) btnFilterByDate.Text = LanguageManager.GetText("Filter by Date");
            if (lblSearchEmployee != null) lblSearchEmployee.Text = LanguageManager.GetText("Search Employee");
            if (lblSelectDate != null) lblSelectDate.Text = LanguageManager.GetText("Select Date");
            if (btnPrintEmployees != null) btnPrintEmployees.Text = "🖨️ " + LanguageManager.GetText("Print Report");

            // تطبيق الترجمات على أعمدة الجدول
            ApplyColumnTranslations();

            // الحفاظ على التصميم من اليسار لليمين في جميع اللغات
            this.RightToLeft = RightToLeft.No;
        }

        private void btnPrintEmployees_Click(object sender, EventArgs e)
        {
            try
            {
                if (guna2DataGridView1.Rows.Count == 0)
                {
                    MessageBox.Show("لا توجد بيانات للطباعة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                DGVPrinter print = new DGVPrinter();

                // تطبيق إعدادات الطباعة المحفوظة مع التحقق
                PrintHelper.ApplyPrintSettingsWithValidation(print, "جلسات الموظفين");

                // إضافة معلومات إضافية للتذييل إذا كانت متاحة
                string additionalInfo = "";
                if (dateTimePicker1 != null)
                {
                    additionalInfo = $" - التاريخ: {dateTimePicker1.Value:yyyy-MM-dd}";
                }

                if (!string.IsNullOrEmpty(print.Footer))
                {
                    print.Footer = print.Footer + additionalInfo;
                }
                else
                {
                    print.Footer = "تقرير جلسات الموظفين" + additionalInfo;
                }

                // طباعة الجدول مع معاينة
                print.PrintPreviewDataGridView(guna2DataGridView1);
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Print error") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void OnHandleDestroyed(EventArgs e)
        {
            // إلغاء الاشتراك في حدث تغيير اللغة
            LanguageManager.LanguageChanged -= OnLanguageChanged;
            base.OnHandleDestroyed(e);
        }
    }
}
