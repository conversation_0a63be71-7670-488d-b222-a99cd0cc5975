# دليل النظام الموحد للصيدلية
# Unified Pharmacy System Guide

## 🎯 **الهدف من النظام الموحد:**

تم إنشاء نظام موحد يجمع بين:
- ✅ **قاعدة البيانات المحلية** (pharmacy)
- ✅ **قاعدة البيانات الأونلاين** (PharmacyNetworkOnline)
- ✅ **قاعدة بيانات واحدة شاملة** (UnifiedPharmacy)

---

## 🏗️ **مكونات النظام الموحد:**

### **1. قاعدة البيانات الموحدة (UnifiedPharmacy):**
- **pharmacies**: جدول الصيدليات
- **users**: جدول المستخدمين (مرتبط بالصيدليات)
- **medicines**: جدول الأدوية (مرتبط بالصيدليات)
- **sales**: جدول المبيعات
- **employee_sessions**: جدول جلسات الموظفين
- **network_orders**: جدول طلبات الشبكة
- **pharmacy_messages**: جدول المحادثات
- **print_settings**: جدول إعدادات الطباعة

### **2. الكلاسات الجديدة:**
- **SessionManager**: إدارة جلسة المستخدم الحالي
- **UnifiedFunction**: التعامل مع قاعدة البيانات الموحدة
- **PharmacySelectionForm**: نموذج اختيار الصيدلية

### **3. صفحة تسجيل الدخول المحدثة:**
- زر اختيار الصيدلية
- عرض الصيدلية المختارة
- تسجيل دخول موحد لجميع الصيدليات

---

## 🚀 **كيفية الاستخدام:**

### **الخطوة 1: إعداد قاعدة البيانات الموحدة**
1. شغل `create_unified_system.bat`
2. أو شغل الأوامر التالية في SQL Server:
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE UnifiedPharmacy;

-- إنشاء الجداول (راجع create_unified_database.sql)
```

### **الخطوة 2: تسجيل الدخول الجديد**
1. **شغل البرنامج**
2. **اضغط "اختيار الصيدلية"**
3. **اختر صيدلية موجودة أو سجل صيدلية جديدة**
4. **أدخل اسم المستخدم وكلمة المرور**
5. **اضغط "تسجيل الدخول"**

### **الخطوة 3: الحسابات الافتراضية**
```
المدير:
- اسم المستخدم: admin
- كلمة المرور: admin123

الموظف:
- اسم المستخدم: employee  
- كلمة المرور: emp123
```

---

## 🔧 **الميزات الجديدة:**

### **1. إدارة متعددة الصيدليات:**
- كل صيدلية لها بياناتها المستقلة
- مستخدمين منفصلين لكل صيدلية
- أدوية ومبيعات منفصلة

### **2. جلسات المستخدمين:**
- تتبع دخول وخروج الموظفين
- معلومات الجلسة الحالية
- إحصائيات الاستخدام

### **3. الشبكة الأونلاين المدمجة:**
- طلبات بين الصيدليات
- محادثات بين الصيدليات
- مشاركة الأدوية

### **4. إعدادات الطباعة المخصصة:**
- إعدادات منفصلة لكل صيدلية
- تخصيص التقارير
- حفظ التفضيلات

---

## 📊 **هيكل قاعدة البيانات:**

### **جدول الصيدليات (pharmacies):**
```sql
- id (PK)
- pharmacyCode (UNIQUE)
- pharmacyName
- ownerName
- licenseNumber
- address, city, region
- phone, email
- isActive
- registrationDate, lastOnline
- subscriptionType
```

### **جدول المستخدمين (users):**
```sql
- id (PK)
- pharmacyId (FK)
- userRole (Administrator/Employee/Pharmacist)
- name, dob, mobile, email
- username (UNIQUE), pass
- isActive, lastLogin
```

### **جدول الأدوية (medicines):**
```sql
- id (PK)
- pharmacyId (FK)
- mid, mname, mnumber
- mDate, eDate, quantity, perUnit
- lu, br
- newEDate, newQuantity
- dos2, dos2_qty, dos3, dos3_qty, dos4, dos4_qty
- isAvailableForSale, requiresPrescription
- manufacturer, category, dosageForm, strength
- unitPrice, wholesalePrice, expiryDate
```

---

## 🛠️ **التطوير والتخصيص:**

### **إضافة صيدلية جديدة:**
```csharp
// استخدام PharmacySelectionForm
PharmacySelectionForm pharmacyForm = new PharmacySelectionForm();
if (pharmacyForm.ShowDialog() == DialogResult.OK)
{
    int pharmacyId = pharmacyForm.SelectedPharmacyId;
    string pharmacyName = pharmacyForm.SelectedPharmacyName;
}
```

### **التحقق من الصلاحيات:**
```csharp
// استخدام SessionManager
if (SessionManager.HasPermission("add_medicine"))
{
    // السماح بإضافة دواء
}
```

### **التعامل مع قاعدة البيانات الموحدة:**
```csharp
// استخدام UnifiedFunction
UnifiedFunction unifiedFn = new UnifiedFunction();
DataSet ds = unifiedFn.getData("SELECT * FROM medicines WHERE pharmacyId = " + SessionManager.CurrentPharmacyId);
```

---

## 🔄 **الترقية من النظام القديم:**

### **نقل البيانات:**
1. البيانات من `pharmacy` تنتقل للصيدلية الرئيسية
2. البيانات من `PharmacyNetworkOnline` تدمج في النظام الموحد
3. الحفاظ على جميع البيانات الموجودة

### **التوافق مع النظام القديم:**
- النظام يعمل مع قواعد البيانات القديمة كبديل
- تحويل تدريجي للنظام الموحد
- عدم فقدان أي بيانات

---

## 🎯 **الفوائد:**

### **للمدير:**
- ✅ إدارة متعددة الصيدليات من مكان واحد
- ✅ تتبع شامل للمستخدمين والمبيعات
- ✅ تقارير موحدة ومفصلة
- ✅ إعدادات مخصصة لكل صيدلية

### **للموظفين:**
- ✅ واجهة موحدة وسهلة
- ✅ الوصول لجميع الميزات
- ✅ الشبكة الأونلاين المدمجة
- ✅ تتبع الجلسات والأنشطة

### **للنظام:**
- ✅ قاعدة بيانات موحدة ومنظمة
- ✅ أمان محسن مع الصلاحيات
- ✅ قابلية التوسع والنمو
- ✅ سهولة الصيانة والتطوير

---

## 🚨 **ملاحظات مهمة:**

### **متطلبات النظام:**
- SQL Server مع قاعدة البيانات `UnifiedPharmacy`
- .NET Framework
- صلاحيات إدارة قاعدة البيانات

### **الأمان:**
- كلمات مرور مشفرة
- صلاحيات محددة لكل دور
- تتبع جميع العمليات

### **النسخ الاحتياطي:**
- نسخ احتياطي منتظم لقاعدة البيانات الموحدة
- حفظ إعدادات كل صيدلية
- استرداد البيانات عند الحاجة

---

## 🎉 **النتيجة النهائية:**

**نظام صيدلية موحد وشامل يدعم:**
- ✅ **متعدد الصيدليات**
- ✅ **إدارة المستخدمين والصلاحيات**
- ✅ **الشبكة الأونلاين المدمجة**
- ✅ **تتبع شامل للعمليات**
- ✅ **واجهة موحدة وسهلة**

**جرب النظام الآن واستمتع بالميزات الجديدة!** 🚀
