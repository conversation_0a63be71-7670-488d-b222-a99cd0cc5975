﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DGVPrinterHelper;

namespace Pharmacy_Management_System.PharmacistUC
{
    public partial class UC_P_ViewMedicines : UserControl
    {
        Function fn = new Function();
        String query;
        public UC_P_ViewMedicines()
        {
            InitializeComponent();

            // الاشتراك في حدث تغيير اللغة
            LanguageManager.LanguageChanged += OnLanguageChanged;
        }

        private void OnLanguageChanged(object sender, EventArgs e)
        {
            ApplyLanguage();
        }

        private string GetMedicinesQuery(string whereClause = "")
        {
            string medicineId = LanguageManager.GetText("Medicine ID");
            string medicineNumber = LanguageManager.GetText("Medicine Number");
            string medicineName = LanguageManager.GetText("Medicine Name");
            string manufactureDate = LanguageManager.GetText("Manufacture Date");
            string expireDate = LanguageManager.GetText("Expire Date");
            string quantity = LanguageManager.GetText("Quantity");
            string pricePerUnit = LanguageManager.GetText("Price Per Unit");
            string lastUpdate = LanguageManager.GetText("Last Update");
            string brand = LanguageManager.GetText("Brand");
            string originalQuantity = LanguageManager.GetText("Original Quantity");
            string newExpireDate = LanguageManager.GetText("New Expire Date");
            string newQuantity = LanguageManager.GetText("New Quantity");
            string allQuantity = LanguageManager.GetText("All Quantity");
            string dosage = LanguageManager.GetText("Dosage");

            string query = string.Format(@"SELECT
                            id AS '{0}',
                            mid AS '{1}',
                            mname AS '{2}',
                            mDate AS '{3}',
                            eDate AS '{4}',
                            quantity AS '{5}',
                            perUnit AS '{6}',
                            lu AS '{7}',
                            br AS '{8}',
                            originalQuantity AS '{9}',
                            newEDate AS '{10}',
                            newQuantity AS '{11}',
                            allqun AS '{12}',
                            dos2 AS '{13} 2',
                            dos2_qty AS '{13} 2 {5}',
                            dos3 AS '{13} 3',
                            dos3_qty AS '{13} 3 {5}',
                            dos4 AS '{13} 4',
                            dos4_qty AS '{13} 4 {5}'
                        FROM medic
                        WHERE pharmacy_id = {14}",
                        medicineId, medicineNumber, medicineName, manufactureDate, expireDate,
                        quantity, pricePerUnit, lastUpdate, brand, originalQuantity,
                        newExpireDate, newQuantity, allQuantity, dosage, SessionManager.CurrentPharmacyId);

            if (!string.IsNullOrEmpty(whereClause))
            {
                query += " AND " + whereClause;
            }

            return query;
        }

        private void UC_P_ViewMedicines_Load(object sender, EventArgs e)
        {
            // تطبيق اللغة الحالية
            ApplyLanguage();

            query = GetMedicinesQuery();
            setDataGridView(query);
        }

        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            string whereClause = string.Format("mname LIKE '{0}%'", txtSearch.Text);
            query = GetMedicinesQuery(whereClause);
            setDataGridView(query);
        }

        private void setDataGridView(String query)
        {
            DataSet ds = fn.getData(query);
            guna2DataGridView1.DataSource = ds.Tables[0];
        }

        public void ApplyLanguage()
        {
            // تحديث النصوص حسب اللغة المختارة
            if (label1 != null) label1.Text = LanguageManager.GetText("View Medicines");
            if (label2 != null) label2.Text = LanguageManager.GetText("Search");
            if (btnPrintInventory != null) btnPrintInventory.Text = "🖨️ " + LanguageManager.GetText("Print All");
            if (btnDelete != null) btnDelete.Text = LanguageManager.GetText("Delete");

            // إعادة تحميل البيانات لتحديث أسماء الأعمدة
            query = GetMedicinesQuery();
            setDataGridView(query);

            // الحفاظ على التصميم من اليسار لليمين في جميع اللغات
            this.RightToLeft = RightToLeft.No;
        }

        String medicineId;
        private void guna2DataGridView1_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                medicineId = guna2DataGridView1.Rows[e.RowIndex].Cells[1].Value.ToString();
            }
            catch
            {

            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if(MessageBox.Show("هل أنت متأكد من حذف هذا الدواء؟","تأكيد الحذف",MessageBoxButtons.YesNo,MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                query = "delete from medic where mid = '"+medicineId+"'";
                fn.setData(query, "تم حذف سجل الدواء بنجاح.");
                UC_P_ViewMedicines_Load(this, null);
            }

        }

        private void btnSync_Click(object sender, EventArgs e)
        {
            UC_P_ViewMedicines_Load(this, null);
        }

        private void guna2DataGridView1_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {

        }

        private void btnPrintInventory_Click(object sender, EventArgs e)
        {
            try
            {
                if (guna2DataGridView1.Rows.Count == 0)
                {
                    MessageBox.Show("لا توجد بيانات للطباعة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // استخدام DGVPrinter مع إعدادات التصميم
                DGVPrinter print = new DGVPrinter();

                // تطبيق إعدادات الطباعة المحفوظة مع التحقق
                AdministratorUC.PrintHelper.ApplyPrintSettingsWithValidation(print, "جرد الأدوية");

                // طباعة الجدول مع معاينة
                print.PrintPreviewDataGridView(guna2DataGridView1);
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Print error") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void OnHandleDestroyed(EventArgs e)
        {
            // إلغاء الاشتراك في أحداث التغيير
            LanguageManager.LanguageChanged -= OnLanguageChanged;
            base.OnHandleDestroyed(e);
        }
    }
}
