﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy_Management_System
{
    public partial class Adminstrator : Form
    {
        string user = "";
        string userName = "";

        private bool dragging = false;
        private Point startPoint = new Point(0, 0);

        public Adminstrator()
        {
            InitializeComponent();
        }

        public string ID
        {
            get { return user.ToString(); }
        }

        public string UserName
        {
            get { return userName; }
        }

        public Adminstrator(String username)
        {
            InitializeComponent();
            userNameLabel.Text = "مرحباً، " + username;
            user = username;
            userName = username;

            // تحسين إعدادات النموذج للتكبير
            this.WindowState = FormWindowState.Normal;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(1200, 800);

            // تسجيل دخول المدير
            recordAdminLogin(username);

            // تمرير معلومات المستخدم لجميع UserControls

            // تمرير معلومات المستخدم لصفحة تقرير المبيعات إذا كانت تحتاج ذلك
            if (uC_SalesReport1 != null)
            {
                // يمكن إضافة خصائص إضافية هنا إذا لزم الأمر
            }

            // تطبيق التصميم العصري
            ApplyModernDesign();

            // الاشتراك في أحداث التغيير
            LanguageManager.LanguageChanged += OnLanguageChanged;
            ModernTheme.ThemeChanged += OnThemeChanged;

            // تطبيق اللغة الحالية
            ApplyLanguage();
        }
        // دالة لإخفاء جميع UserControls
        private void HideAllUserControls()
        {
            uC_Dashbord1.Visible = false;
            uC_AddUser1.Visible = false;
            uC_EditUser1.Visible = false;
            uC_SalesReport1.Visible = false;
            uC_EmployeeSessions1.Visible = false;
            uC_PrintDesign1.Visible = false;
        }

        // دالة لإعادة تعيين ألوان الأزرار
        private void ResetButtonColors()
        {
            btnDashbord.Checked = false;
            btnAddUser.Checked = false;
            // btnProfile.Checked = false; // تم حذف btnProfile
            btnupdate.Checked = false;
            btnSalesReport.Checked = false;
            btnEmployeeSessions.Checked = false;
            btnPrintDesign.Checked = false;
        }

        private void btnLogOut_Click(object sender, EventArgs e)
        {
            // تأكيد تسجيل الخروج
            DialogResult result = MessageBox.Show(LanguageManager.GetText("Are you sure you want to logout?"), LanguageManager.GetText("Confirm Logout"),
                                                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // تسجيل خروج المدير
                recordAdminLogout();

                // حفظ كود الصيدلية الحالي قبل الخروج
                if (SessionManager.IsLoggedIn && !string.IsNullOrEmpty(SessionManager.CurrentPharmacyCode))
                {
                    PharmacyCodeLoginForm.SavePharmacyCode(SessionManager.CurrentPharmacyCode);
                }

                // العودة إلى واجهة تسجيل الدخول الموحدة
                PharmacyCodeLoginForm loginForm = new PharmacyCodeLoginForm();
                loginForm.Show();
                this.Hide();
            }
        }

        private void recordAdminLogin(string adminName)
        {
            try
            {
                Function fn = new Function();

                // إنشاء جدول جلسات الموظفين إذا لم يكن موجوداً
                string query = @"IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'employee_sessions')
                         BEGIN
                             CREATE TABLE employee_sessions (
                                 id INT IDENTITY(1,1) PRIMARY KEY,
                                 username NVARCHAR(250),
                                 employeeName NVARCHAR(250),
                                 loginTime DATETIME DEFAULT GETDATE(),
                                 logoutTime DATETIME NULL,
                                 sessionDate DATE DEFAULT CONVERT(DATE, GETDATE()),
                                 pharmacy_id INT
                             )
                         END";
                fn.setData(query, "");

                // تسجيل دخول المدير مع معالجة الأسماء العربية
                string safeAdminName = adminName.Replace("'", "''");
                string adminDisplayName = "مدير النظام - " + safeAdminName;

                query = @"INSERT INTO employee_sessions (username, employeeName, loginTime, sessionDate, pharmacy_id)
                         VALUES (N'" + safeAdminName + "', N'" + adminDisplayName + "', GETDATE(), CONVERT(DATE, GETDATE()), " + SessionManager.CurrentPharmacyId + ")";
                fn.setData(query, "");
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ للتشخيص
                System.Diagnostics.Debug.WriteLine($"خطأ في تسجيل دخول المدير: {ex.Message}");
            }
        }

        private void recordAdminLogout()
        {
            try
            {
                Function fn = new Function();

                // تحديث وقت الخروج للجلسة الحالية مع معالجة الأسماء العربية
                string safeUserName = userName.Replace("'", "''");

                string query = @"UPDATE employee_sessions
                         SET logoutTime = GETDATE()
                         WHERE username = N'" + safeUserName + @"'
                           AND logoutTime IS NULL
                           AND sessionDate = CONVERT(DATE, GETDATE())
                           AND pharmacy_id = " + SessionManager.CurrentPharmacyId;
                fn.setData(query, "");
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ للتشخيص
                System.Diagnostics.Debug.WriteLine($"خطأ في تسجيل خروج المدير: {ex.Message}");
            }
        }

        private void btnDashbord_Click(object sender, EventArgs e)
        {
            HideAllUserControls();
            ResetButtonColors();
            btnDashbord.Checked = true;
            uC_Dashbord1.Visible = true;
            uC_Dashbord1.BringToFront();
        }

        private void Adminstrator_Load(object sender, EventArgs e)
        {
            // تطبيق اللغة الحالية
            ApplyLanguage();

            // إخفاء جميع UserControls في البداية
            HideAllUserControls();

            // عرض لوحة التحكم افتراضياً
            btnDashbord.PerformClick();

            // تحسين مظهر النموذج
            this.StartPosition = FormStartPosition.CenterScreen;
        }

        private void btnAddUser_Click(object sender, EventArgs e)
        {
            HideAllUserControls();
            ResetButtonColors();
            btnAddUser.Checked = true;
            uC_AddUser1.Visible = true;
            uC_AddUser1.BringToFront();
        }



        private void btnProfile_Click(object sender, EventArgs e)
        {
            // تم حذف صفحة الملف الشخصي
            MessageBox.Show("تم إزالة صفحة الملف الشخصي من النظام", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnupdate_Click(object sender, EventArgs e)
        {
            HideAllUserControls();
            ResetButtonColors();
            btnupdate.Checked = true;
            uC_EditUser1.Visible = true;
            uC_EditUser1.BringToFront();
        }

        private void btnSalesReport_Click(object sender, EventArgs e)
        {
            HideAllUserControls();
            ResetButtonColors();
            btnSalesReport.Checked = true;
            uC_SalesReport1.Visible = true;
            uC_SalesReport1.BringToFront();
        }

        private void btnEmployeeSessions_Click(object sender, EventArgs e)
        {
            HideAllUserControls();
            ResetButtonColors();
            btnEmployeeSessions.Checked = true;
            uC_EmployeeSessions1.Visible = true;
            uC_EmployeeSessions1.BringToFront();
        }

        private void btnPrintDesign_Click(object sender, EventArgs e)
        {
            HideAllUserControls();
            ResetButtonColors();
            btnPrintDesign.Checked = true;
            uC_PrintDesign1.Visible = true;
            uC_PrintDesign1.BringToFront();
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            // تأكيد الخروج من التطبيق
            DialogResult result = MessageBox.Show(LanguageManager.GetText("Are you sure you want to exit?"), LanguageManager.GetText("Confirm Exit"),
                                                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // تسجيل خروج المدير قبل إغلاق التطبيق
                recordAdminLogout();

                // حفظ كود الصيدلية الحالي قبل الخروج
                if (SessionManager.IsLoggedIn && !string.IsNullOrEmpty(SessionManager.CurrentPharmacyCode))
                {
                    PharmacyCodeLoginForm.SavePharmacyCode(SessionManager.CurrentPharmacyCode);
                }

                Application.Exit();
            }
        }


        private void guna2Button1_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        // دوال التحكم في سحب النافذة
        private void guna2Panel2_MouseMove(object sender, MouseEventArgs e)
        {
            if (dragging)
            {
                Point p = PointToScreen(e.Location);
                this.Location = new Point(p.X - startPoint.X, p.Y - startPoint.Y);
            }
        }

        private void guna2Panel2_MouseUp(object sender, MouseEventArgs e)
        {
            dragging = false;
        }

        // معالج إغلاق النموذج لحفظ تسجيل الخروج
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // تسجيل خروج المدير عند إغلاق التطبيق من زر X
            recordAdminLogout();
            base.OnFormClosing(e);
        }

        private void guna2Panel2_MouseDown(object sender, MouseEventArgs e)
        {
            dragging = true;
            startPoint = new Point(e.X, e.Y);
        }

        // دالة لتحديث معلومات المستخدم
        public void UpdateUserInfo(string username)
        {
            userNameLabel.Text = "مرحباً، " + username;
            user = username;
            userName = username;
        }

        // دالة للحصول على معلومات المستخدم الحالي
        public string GetCurrentUser()
        {
            return userName;
        }

        // دالة لإعادة تحميل البيانات في جميع UserControls
        public void RefreshAllData()
        {
            try
            {
                // إعادة تحميل البيانات في UserControls التي تحتاج ذلك
                if (uC_Dashbord1.Visible)
                {
                    // يمكن إضافة دالة تحديث للوحة التحكم
                }

                if (uC_SalesReport1.Visible)
                {
                    // يمكن إضافة دالة تحديث لتقرير المبيعات
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Data update error") + ": " + ex.Message, LanguageManager.GetText("Error"),
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OnLanguageChanged(object sender, EventArgs e)
        {
            ApplyLanguage();
        }

        private void ApplyLanguage()
        {
            // تطبيق الترجمات على أزرار القائمة
            btnDashbord.Text = LanguageManager.GetText("Dashboard");
            btnAddUser.Text = LanguageManager.GetText("Add User");
            // btnProfile.Text = LanguageManager.GetText("Profile"); // تم حذف btnProfile
            btnupdate.Text = LanguageManager.GetText("Edit Users");
            btnSalesReport.Text = LanguageManager.GetText("Sales Report");
            btnEmployeeSessions.Text = LanguageManager.GetText("Employee Sessions");
            btnPrintDesign.Text = "تصميم الطباعة";
            btnLogOut.Text = LanguageManager.GetText("Logout");

            // تحديث رسالة الترحيب
            if (LanguageManager.CurrentLanguage == "ar")
            {
                userNameLabel.Text = "مرحباً، " + userName;
            }
            else
            {
                userNameLabel.Text = "Welcome, " + userName;
            }

            // الحفاظ على التصميم من اليسار لليمين في جميع اللغات
            this.RightToLeft = RightToLeft.No;
            this.RightToLeftLayout = false;
        }

        private void ApplyModernDesign()
        {
            // تطبيق الوضع الحالي
            ModernTheme.ApplyThemeToForm(this);

            // تحسين شريط العنوان
            if (panel1 != null)
            {
                panel1.BackColor = ModernTheme.Colors.Primary;
            }

            // تحسين القائمة الجانبية
            if (panel2 != null)
            {
                panel2.BackColor = ModernTheme.Colors.Surface;
            }

            // تحسين الأزرار في القائمة الجانبية
            ApplyModernAdminButtons();

            // إضافة زر الوضع الليلي
            CreateDarkModeButton();
        }

        private void CreateDarkModeButton()
        {
            // إنشاء زر الوضع الليلي في الشريط العلوي guna2Panel2
            var topPanel = this.Controls.Find("guna2Panel2", true).FirstOrDefault();
            if (topPanel != null && topPanel.Controls.Find("btnDarkMode", false).Length == 0)
            {
                Guna.UI2.WinForms.Guna2Button btnDarkMode = new Guna.UI2.WinForms.Guna2Button();
                btnDarkMode.Name = "btnDarkMode";
                btnDarkMode.Text = ModernTheme.IsDarkMode ? "🌞 فاتح" : "🌙 ليلي";
                btnDarkMode.Size = new Size(80, 25);
                btnDarkMode.Location = new Point(topPanel.Width - 90, 3);
                btnDarkMode.Anchor = AnchorStyles.Top | AnchorStyles.Right;
                btnDarkMode.BorderRadius = 8;
                btnDarkMode.Font = new Font("Segoe UI", 8F);
                btnDarkMode.Cursor = Cursors.Hand;
                btnDarkMode.FillColor = ModernTheme.Colors.Secondary;
                btnDarkMode.ForeColor = ModernTheme.Colors.TextOnPrimary;
                btnDarkMode.HoverState.FillColor = ModernTheme.Colors.SecondaryDark;
                btnDarkMode.Click += (s, e) => ModernTheme.ToggleDarkMode();

                topPanel.Controls.Add(btnDarkMode);
                btnDarkMode.BringToFront();
            }
        }

        private void OnThemeChanged(object sender, EventArgs e)
        {
            ApplyModernDesign();
            ApplyLanguage();
        }

        private void ApplyModernAdminButtons()
        {
            // تحسين أزرار القائمة الجانبية
            ApplyModernButtonStyle(btnAddUser);
            // btnProfile تم حذفه
            ApplyModernButtonStyle(btnEmployeeSessions);
            ApplyModernButtonStyle(btnSalesReport);

            // زر تسجيل الخروج بلون مختلف
            if (btnLogOut != null)
            {
                btnLogOut.FillColor = Color.Transparent;
                btnLogOut.ForeColor = Color.FromArgb(220, 53, 69);
                btnLogOut.Font = new Font("Segoe UI", 11F);
                btnLogOut.TextAlign = HorizontalAlignment.Left;
                btnLogOut.Cursor = Cursors.Hand;
                btnLogOut.HoverState.FillColor = Color.FromArgb(30, 220, 53, 69);
                btnLogOut.BorderThickness = 0;
            }

            // تحسين تسمية اسم المستخدم (إذا كانت موجودة)
            // userNameLabel غير موجود في هذا التصميم
        }

        private void ApplyModernButtonStyle(Guna.UI2.WinForms.Guna2Button button)
        {
            if (button != null)
            {
                button.FillColor = Color.Transparent;
                button.ForeColor = Color.FromArgb(33, 37, 41);
                button.Font = new Font("Segoe UI", 11F);
                button.TextAlign = HorizontalAlignment.Left;
                button.Cursor = Cursors.Hand;
                button.HoverState.FillColor = Color.FromArgb(144, 164, 255);
                button.BorderThickness = 0;
            }
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            // إلغاء الاشتراك في حدث تغيير اللغة
            LanguageManager.LanguageChanged -= OnLanguageChanged;
            base.OnFormClosed(e);
        }
    }
}
