-- اختبار نشر الأدوية في متجر الصيدلية
-- Test Medicine Publishing in Pharmacy Store

USE UnifiedPharmacy;
GO

PRINT '🧪 بدء اختبار نشر الأدوية...';
PRINT '🧪 Starting medicine publishing test...';
PRINT '';

-- 1. التحقق من وجود الجداول المطلوبة
PRINT '1. التحقق من الجداول المطلوبة...';
PRINT '1. Checking required tables...';

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'published_medicines')
BEGIN
    PRINT '❌ جدول published_medicines غير موجود!';
    PRINT '❌ published_medicines table does not exist!';
    RETURN;
END

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacies')
BEGIN
    PRINT '❌ جدول pharmacies غير موجود!';
    PRINT '❌ pharmacies table does not exist!';
    RETURN;
END

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'medic')
BEGIN
    PRINT '❌ جدول medic غير موجود!';
    PRINT '❌ medic table does not exist!';
    RETURN;
END

PRINT '✅ جميع الجداول موجودة';
PRINT '✅ All tables exist';
PRINT '';

-- 2. عرض هيكل جدول published_medicines
PRINT '2. هيكل جدول published_medicines:';
PRINT '2. published_medicines table structure:';
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'published_medicines'
ORDER BY ORDINAL_POSITION;

PRINT '';

-- 3. عرض البيانات الحالية
PRINT '3. البيانات الحالية في published_medicines:';
PRINT '3. Current data in published_medicines:';
SELECT 
    id,
    pharmacy_id,
    medicine_name,
    quantity_available,
    price_per_unit,
    expiry_date,
    is_available,
    published_date
FROM published_medicines
ORDER BY published_date DESC;

PRINT '';

-- 4. عرض الأدوية المتاحة للنشر
PRINT '4. الأدوية المتاحة للنشر من جدول medic:';
PRINT '4. Available medicines for publishing from medic table:';
SELECT
    id,
    mname as medicine_name,
    mnumber as medicine_number,
    quantity,
    perUnit as price_per_unit,
    eDate as expiry_date
FROM medic
WHERE quantity > 0
AND (eDate IS NULL OR eDate > GETDATE())
ORDER BY mname;

PRINT '';

-- 5. اختبار نشر دواء جديد
PRINT '5. اختبار نشر دواء جديد...';
PRINT '5. Testing new medicine publishing...';

-- البحث عن دواء للنشر
DECLARE @test_medicine_id INT;
DECLARE @test_medicine_name NVARCHAR(255);
DECLARE @test_quantity INT;
DECLARE @test_price DECIMAL(10,2);
DECLARE @test_expiry DATE;

SELECT TOP 1
    @test_medicine_id = id,
    @test_medicine_name = mname,
    @test_quantity = quantity,
    @test_price = perUnit,
    @test_expiry = eDate
FROM medic
WHERE quantity > 0 
AND (eDate IS NULL OR eDate > GETDATE())
ORDER BY id;

IF @test_medicine_id IS NOT NULL
BEGIN
    PRINT '📋 دواء الاختبار:';
    PRINT '📋 Test medicine:';
    PRINT 'ID: ' + CAST(@test_medicine_id AS NVARCHAR(10));
    PRINT 'Name: ' + @test_medicine_name;
    PRINT 'Quantity: ' + CAST(@test_quantity AS NVARCHAR(10));
    PRINT 'Price: ' + CAST(@test_price AS NVARCHAR(20));
    
    -- نشر الدواء
    INSERT INTO published_medicines
    (pharmacy_id, medicine_name, medicine_number, quantity_available, expiry_date, price_per_unit, description, published_date, is_available)
    VALUES
    (1, @test_medicine_name, 'TEST001', 5, @test_expiry, @test_price, N'دواء تجريبي للاختبار', GETDATE(), 1);
    
    PRINT '✅ تم نشر الدواء التجريبي بنجاح!';
    PRINT '✅ Test medicine published successfully!';
END
ELSE
BEGIN
    PRINT '⚠️ لا توجد أدوية متاحة للاختبار';
    PRINT '⚠️ No medicines available for testing';
END

PRINT '';

-- 6. عرض النتائج النهائية
PRINT '6. النتائج النهائية:';
PRINT '6. Final results:';

SELECT 
    'published_medicines' as table_name,
    COUNT(*) as total_count,
    SUM(CASE WHEN is_available = 1 THEN 1 ELSE 0 END) as active_count,
    SUM(CASE WHEN expiry_date > GETDATE() OR expiry_date IS NULL THEN 1 ELSE 0 END) as valid_count
FROM published_medicines
UNION ALL
SELECT 
    'medic' as table_name,
    COUNT(*) as total_count,
    SUM(CASE WHEN quantity > 0 THEN 1 ELSE 0 END) as available_count,
    SUM(CASE WHEN eDate > GETDATE() OR eDate IS NULL THEN 1 ELSE 0 END) as valid_count
FROM medic;

PRINT '';
PRINT '🎉 انتهى اختبار نشر الأدوية!';
PRINT '🎉 Medicine publishing test completed!';
PRINT '';
PRINT 'يمكنك الآن اختبار النظام:';
PRINT 'You can now test the system:';
PRINT '1. افتح نظام إدارة الصيدلية';
PRINT '1. Open Pharmacy Management System';
PRINT '2. اذهب لمتجر الصيدلية';
PRINT '2. Go to Pharmacy Store';
PRINT '3. جرب نشر دواء جديد';
PRINT '3. Try publishing a new medicine';
PRINT '4. تحقق من ظهوره في "أدويتي المعروضة"';
PRINT '4. Check if it appears in "My Published Medicines"';
