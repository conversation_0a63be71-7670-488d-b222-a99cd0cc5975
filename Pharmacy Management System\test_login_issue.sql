-- اختبار مشكلة تسجيل الدخول
-- Test Login Issue

USE pharmacy;

PRINT '========================================';
PRINT '   اختبار مشكلة تسجيل الدخول';
PRINT '   Testing Login Issue';
PRINT '========================================';

-- 1. التحقق من وجود جدول المستخدمين
PRINT '';
PRINT '1. التحقق من وجود جدول المستخدمين...';
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'users')
BEGIN
    PRINT '✅ جدول المستخدمين موجود';
    
    -- عرض هيكل الجدول
    PRINT '';
    PRINT 'هيكل جدول المستخدمين:';
    SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'users'
    ORDER BY ORDINAL_POSITION;
    
    -- عرض عدد المستخدمين
    DECLARE @userCount INT;
    SELECT @userCount = COUNT(*) FROM users;
    PRINT '';
    PRINT 'عدد المستخدمين الموجودين: ' + CAST(@userCount AS VARCHAR(10));
    
END
ELSE
BEGIN
    PRINT '❌ جدول المستخدمين غير موجود!';
END

-- 2. التحقق من وجود جدول الصيدليات
PRINT '';
PRINT '2. التحقق من وجود جدول الصيدليات...';
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'pharmacies')
BEGIN
    PRINT '✅ جدول الصيدليات موجود';
    
    -- عرض عدد الصيدليات
    DECLARE @pharmacyCount INT;
    SELECT @pharmacyCount = COUNT(*) FROM pharmacies;
    PRINT 'عدد الصيدليات الموجودة: ' + CAST(@pharmacyCount AS VARCHAR(10));
    
END
ELSE
BEGIN
    PRINT '❌ جدول الصيدليات غير موجود!';
END

-- 3. عرض جميع المستخدمين مع معلومات الصيدليات
PRINT '';
PRINT '3. عرض جميع المستخدمين:';
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'users')
   AND EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'pharmacies')
BEGIN
    SELECT 
        u.id,
        u.username,
        u.name,
        u.userRole,
        u.pharmacyId,
        p.pharmacyName,
        p.pharmacyCode,
        u.isActive,
        u.pass as 'كلمة المرور (للاختبار فقط)'
    FROM users u
    LEFT JOIN pharmacies p ON u.pharmacyId = p.id
    ORDER BY u.id;
END

-- 4. اختبار تسجيل دخول تجريبي
PRINT '';
PRINT '4. اختبار تسجيل دخول تجريبي...';

-- إنشاء مستخدم تجريبي إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM users WHERE username = 'testuser')
BEGIN
    PRINT 'إنشاء مستخدم تجريبي...';
    
    -- التأكد من وجود صيدلية افتراضية
    IF NOT EXISTS (SELECT * FROM pharmacies WHERE id = 1)
    BEGIN
        INSERT INTO pharmacies (pharmacyName, pharmacyCode, ownerName, city, region, isActive)
        VALUES ('صيدلية تجريبية', 'TEST001', 'مالك تجريبي', 'الرياض', 'الرياض', 1);
        PRINT '✅ تم إنشاء صيدلية تجريبية';
    END
    
    INSERT INTO users (pharmacyId, userRole, name, dob, mobile, email, username, pass, isActive)
    VALUES (1, 'Administrator', 'مستخدم تجريبي', '1990-01-01', 1234567890, '<EMAIL>', 'testuser', 'testpass', 1);
    
    PRINT '✅ تم إنشاء مستخدم تجريبي: testuser / testpass';
END
ELSE
BEGIN
    PRINT '✅ المستخدم التجريبي موجود بالفعل';
END

-- 5. اختبار استعلام تسجيل الدخول
PRINT '';
PRINT '5. اختبار استعلام تسجيل الدخول...';

DECLARE @testUsername VARCHAR(50) = 'testuser';
DECLARE @testPassword VARCHAR(50) = 'testpass';
DECLARE @testPharmacyId INT = 1;

PRINT 'اختبار تسجيل الدخول مع:';
PRINT 'اسم المستخدم: ' + @testUsername;
PRINT 'كلمة المرور: ' + @testPassword;
PRINT 'معرف الصيدلية: ' + CAST(@testPharmacyId AS VARCHAR(10));

-- الاستعلام الأساسي (مع pharmacyId)
PRINT '';
PRINT 'نتيجة الاستعلام مع pharmacyId:';
SELECT 
    u.*, 
    p.pharmacyName, 
    p.pharmacyCode
FROM users u
INNER JOIN pharmacies p ON u.pharmacyId = p.id
WHERE u.username = @testUsername 
  AND u.pass = @testPassword 
  AND u.pharmacyId = @testPharmacyId 
  AND u.isActive = 1;

-- الاستعلام البديل (بدون pharmacyId)
PRINT '';
PRINT 'نتيجة الاستعلام بدون pharmacyId:';
SELECT 
    u.*, 
    COALESCE(p.pharmacyName, 'صيدلية افتراضية') as pharmacyName, 
    COALESCE(p.pharmacyCode, 'DEFAULT') as pharmacyCode
FROM users u
LEFT JOIN pharmacies p ON u.pharmacyId = p.id
WHERE u.username = @testUsername 
  AND u.pass = @testPassword 
  AND u.isActive = 1;

-- 6. التوصيات
PRINT '';
PRINT '========================================';
PRINT '   التوصيات لحل المشكلة:';
PRINT '========================================';
PRINT '1. تأكد من أن pharmacyId صحيح عند إنشاء الحساب';
PRINT '2. تأكد من أن isActive = 1 للمستخدمين الجدد';
PRINT '3. تأكد من وجود صيدلية افتراضية بـ id = 1';
PRINT '4. استخدم الاستعلام البديل إذا فشل الأساسي';
PRINT '';
PRINT '✅ انتهى الاختبار!';
