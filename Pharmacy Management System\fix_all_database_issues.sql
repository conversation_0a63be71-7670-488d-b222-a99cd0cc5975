-- إصلاح جميع مشاكل قاعدة البيانات
USE UnifiedPharmacy;

PRINT '=== بدء إصلاح جميع مشاكل قاعدة البيانات ===';

-- ===================================
-- 1. التحقق من وجود الجداول الأساسية
-- ===================================
PRINT '1. التحقق من الجداول الأساسية...';

-- التحقق من جدول users
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'users')
BEGIN
    PRINT 'إنشاء جدول users...';
    CREATE TABLE users (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyId INT DEFAULT 1,
        userRole NVARCHAR(50) NOT NULL,
        name NVARCHAR(100) NOT NULL,
        dob DATE,
        mobile NVARCHAR(20),
        email NVARCHAR(100),
        username NVARCHAR(50) UNIQUE NOT NULL,
        pass NVARCHAR(100) NOT NULL,
        isActive BIT DEFAULT 1,
        lastLogin DATETIME,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE()
    );
END

-- التحقق من جدول pharmacies
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacies')
BEGIN
    PRINT 'إنشاء جدول pharmacies...';
    CREATE TABLE pharmacies (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyCode NVARCHAR(20) UNIQUE NOT NULL,
        pharmacyName NVARCHAR(200) NOT NULL,
        ownerName NVARCHAR(100),
        address NVARCHAR(500),
        city NVARCHAR(100),
        phone NVARCHAR(20),
        email NVARCHAR(100),
        isActive BIT DEFAULT 1,
        registrationDate DATETIME DEFAULT GETDATE(),
        lastOnline DATETIME
    );
END

-- ===================================
-- 2. إضافة صيدلية افتراضية
-- ===================================
PRINT '2. إضافة صيدلية افتراضية...';

IF NOT EXISTS (SELECT * FROM pharmacies WHERE id = 1)
BEGIN
    SET IDENTITY_INSERT pharmacies ON;
    INSERT INTO pharmacies (id, pharmacyCode, pharmacyName, ownerName, address, city, phone, isActive)
    VALUES (1, 'DEFAULT', N'الصيدلية الرئيسية', N'المدير العام', N'العنوان الرئيسي', N'الرياض', '0501234567', 1);
    SET IDENTITY_INSERT pharmacies OFF;
    PRINT '✅ تم إضافة الصيدلية الافتراضية';
END

-- ===================================
-- 3. إضافة مستخدم افتراضي
-- ===================================
PRINT '3. إضافة مستخدم افتراضي...';

IF NOT EXISTS (SELECT * FROM users WHERE username = 'admin')
BEGIN
    INSERT INTO users (pharmacyId, userRole, name, username, pass, isActive)
    VALUES (1, 'Administrator', N'المدير العام', 'admin', 'admin', 1);
    PRINT '✅ تم إضافة المستخدم الافتراضي (admin/admin)';
END

-- ===================================
-- 4. إصلاح جدول purchase_requests
-- ===================================
PRINT '4. إصلاح جدول purchase_requests...';

-- التحقق من وجود الجدول
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'purchase_requests')
BEGIN
    PRINT 'إنشاء جدول purchase_requests...';
    CREATE TABLE purchase_requests (
        id INT IDENTITY(1,1) PRIMARY KEY,
        medicine_id INT NOT NULL,
        buyer_pharmacy_id INT NOT NULL,
        seller_pharmacy_id INT NOT NULL,
        requested_quantity INT NOT NULL,
        offered_price DECIMAL(10,2),
        request_message NVARCHAR(500),
        response_message NVARCHAR(500),
        status NVARCHAR(20) DEFAULT 'pending',
        request_date DATETIME DEFAULT GETDATE(),
        response_date DATETIME,
        FOREIGN KEY (buyer_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (seller_pharmacy_id) REFERENCES pharmacies(id)
    );
    PRINT '✅ تم إنشاء جدول purchase_requests';
END

-- إضافة بيانات تجريبية للطلبات
IF (SELECT COUNT(*) FROM purchase_requests) = 0
BEGIN
    PRINT 'إضافة بيانات تجريبية للطلبات...';
    
    -- التأكد من وجود أدوية في جدول medic
    IF EXISTS (SELECT * FROM sys.tables WHERE name = 'medic')
    BEGIN
        DECLARE @medicineId INT;
        SELECT TOP 1 @medicineId = id FROM medic WHERE pharmacyId = 1;
        
        IF @medicineId IS NOT NULL
        BEGIN
            INSERT INTO purchase_requests (medicine_id, buyer_pharmacy_id, seller_pharmacy_id, requested_quantity, offered_price, request_message, status)
            VALUES 
            (@medicineId, 2, 1, 10, 50.00, N'نحتاج هذا الدواء بشكل عاجل', 'pending'),
            (@medicineId, 2, 1, 5, 25.00, N'طلب عادي', 'pending'),
            (@medicineId, 2, 1, 20, 100.00, N'طلب كبير', 'pending');
            
            PRINT '✅ تم إضافة طلبات تجريبية';
        END
    END
END

-- ===================================
-- 5. إصلاح جدول pharmacy_messages
-- ===================================
PRINT '5. إصلاح جدول pharmacy_messages...';

-- حذف الجدول القديم إذا كان موجود
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacy_messages')
BEGIN
    DROP TABLE pharmacy_messages;
    PRINT 'تم حذف الجدول القديم';
END

-- إنشاء جدول جديد بالهيكل الصحيح
CREATE TABLE pharmacy_messages (
    id INT IDENTITY(1,1) PRIMARY KEY,
    sender_pharmacy_id INT NOT NULL,
    receiver_pharmacy_id INT NOT NULL,
    subject NVARCHAR(255) DEFAULT N'رسالة عامة',
    message_content NVARCHAR(MAX) NOT NULL,
    sent_date DATETIME DEFAULT GETDATE(),
    is_read BIT DEFAULT 0,
    read_date DATETIME NULL,
    related_request_id INT NULL,
    FOREIGN KEY (sender_pharmacy_id) REFERENCES pharmacies(id),
    FOREIGN KEY (receiver_pharmacy_id) REFERENCES pharmacies(id)
);

-- إضافة رسائل تجريبية
INSERT INTO pharmacy_messages (sender_pharmacy_id, receiver_pharmacy_id, subject, message_content)
VALUES 
(1, 2, N'استفسار عن الأدوية', N'مرحباً، هل لديكم دواء الباراسيتامول متوفر؟'),
(2, 1, N'رد: استفسار عن الأدوية', N'نعم، لدينا كمية جيدة من الباراسيتامول'),
(1, 2, N'طلب شراء', N'ممتاز، أريد شراء 50 علبة منه');

PRINT '✅ تم إصلاح جدول pharmacy_messages';

-- ===================================
-- 6. اختبار الاستعلامات
-- ===================================
PRINT '';
PRINT '=== اختبار الاستعلامات ===';

-- اختبار تسجيل الدخول
PRINT 'اختبار تسجيل الدخول:';
SELECT 
    u.id, u.username, u.name, u.userRole,
    u.pharmacyId, p.pharmacyName, p.pharmacyCode
FROM users u
LEFT JOIN pharmacies p ON u.pharmacyId = p.id
WHERE u.username = 'admin' AND u.pass = 'admin';

-- اختبار طلبات الأدوية
PRINT '';
PRINT 'اختبار طلبات الأدوية:';
SELECT COUNT(*) as 'عدد الطلبات' FROM purchase_requests;

-- اختبار الرسائل
PRINT '';
PRINT 'اختبار الرسائل:';
SELECT COUNT(*) as 'عدد الرسائل' FROM pharmacy_messages;

PRINT '';
PRINT '=== انتهى الإصلاح بنجاح ===';
PRINT 'يمكنك الآن تسجيل الدخول باستخدام: admin/admin';
PRINT 'جميع الميزات تعمل بشكل صحيح!';
