# تحديث قاعدة البيانات الأونلاين - تغيير اسم الجدول
# Online Database Update - Table Name Change

## 🔄 **التغيير المطلوب:**

تم تغيير اسم جدول الأدوية في قاعدة البيانات الأونلاين من:
```
network_medicines  →  networkmedicines
```

## ✅ **التعديلات التي تمت في الكود:**

### **1. ملف OnlineNetworkManager.cs**
```csharp
// تم تغيير:
INSERT INTO network_medicines 
// إلى:
INSERT INTO networkmedicines
```

### **2. ملف database_online_multi_pharmacy.sql**
- تم تغيير اسم الجدول في جميع المواضع
- تم تحديث جميع المراجع (Foreign Keys)
- تم تحديث الفهارس (Indexes)
- تم تحديث الـ Views

### **3. الملفات الجديدة المضافة:**
- `update_online_database_table_name.sql` - سكريبت التحديث
- `update_online_table_name.bat` - ملف تشغيل التحديث

## 🚀 **خطوات التطبيق:**

### **الطريقة الأولى: استخدام الملف الجاهز**
```bash
# تشغيل تحديث اسم الجدول
update_online_table_name.bat
```

### **الطريقة الثانية: الإعداد اليدوي**
1. افتح SQL Server Management Studio
2. اتصل بالخادم `NARUTO`
3. اختر قاعدة البيانات `PharmacyNetworkOnline`
4. شغل السكريبت `update_online_database_table_name.sql`

### **الطريقة الثالثة: إنشاء قاعدة البيانات من جديد**
```bash
# إنشاء قاعدة البيانات الأونلاين بالاسم الجديد
setup_online_database.bat
```

## 📋 **هيكل الجدول الجديد:**

```sql
CREATE TABLE networkmedicines (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacyId INT NOT NULL,
    localMedicineId VARCHAR(250) NOT NULL,
    medicineName NVARCHAR(250) NOT NULL,
    genericName NVARCHAR(250) NULL,
    brandName NVARCHAR(250) NULL,
    manufacturer NVARCHAR(250) NOT NULL,
    category NVARCHAR(100) NULL,
    dosageForm VARCHAR(100) NULL,
    strength VARCHAR(100) NULL,
    availableQuantity INT NOT NULL,
    unitPrice DECIMAL(10, 2) NOT NULL,
    wholesalePrice DECIMAL(10, 2) NULL,
    manufacturingDate DATE NULL,
    expiryDate DATE NOT NULL,
    batchNumber VARCHAR(100) NULL,
    location NVARCHAR(100) NULL,
    isAvailableForSale BIT DEFAULT 1,
    minOrderQuantity INT DEFAULT 1,
    maxOrderQuantity INT NULL,
    description NVARCHAR(1000) NULL,
    sideEffects NVARCHAR(1000) NULL,
    contraindications NVARCHAR(1000) NULL,
    storageConditions NVARCHAR(500) NULL,
    imageUrl VARCHAR(500) NULL,
    createdAt DATETIME DEFAULT GETDATE(),
    updatedAt DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
);
```

## 🔍 **التحقق من التحديث:**

### **استعلام للتحقق من وجود الجدول:**
```sql
USE PharmacyNetworkOnline;
SELECT COUNT(*) FROM networkmedicines;
```

### **استعلام لعرض هيكل الجدول:**
```sql
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'networkmedicines'
ORDER BY ORDINAL_POSITION;
```

## ⚠️ **ملاحظات مهمة:**

1. **النسخ الاحتياطي**: السكريبت يحتفظ بالجدول القديم كنسخة احتياطية
2. **البيانات**: يتم نسخ جميع البيانات من الجدول القديم للجديد
3. **الفهارس**: يتم إنشاء جميع الفهارس المطلوبة للجدول الجديد
4. **المراجع**: يتم تحديث جميع Foreign Keys للإشارة للجدول الجديد

## 🛠️ **استكشاف الأخطاء:**

### **خطأ: الجدول غير موجود**
```bash
# تشغيل إنشاء قاعدة البيانات الأونلاين
setup_online_database.bat
```

### **خطأ: في الاتصال**
- تأكد من تشغيل SQL Server
- تحقق من اسم الخادم `NARUTO`
- تأكد من وجود قاعدة البيانات `PharmacyNetworkOnline`

### **خطأ: في الصلاحيات**
- تأكد من وجود صلاحيات Windows Authentication
- شغل SQL Server Management Studio كمدير

## 📊 **حالة المشروع:**

- ✅ **الكود**: تم تحديثه بالكامل
- ✅ **قاعدة البيانات**: جاهزة للتحديث
- ✅ **البناء**: نجح بدون أخطاء
- ✅ **الاختبار**: جاهز للتشغيل

## 🎯 **الخطوة التالية:**

1. شغل `update_online_table_name.bat`
2. تحقق من نجاح التحديث
3. اختبر النظام الأونلاين
4. تأكد من عمل جميع الميزات

**التحديث جاهز للتطبيق!** 🚀
