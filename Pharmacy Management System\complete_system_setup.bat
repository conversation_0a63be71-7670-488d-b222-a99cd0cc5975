@echo off
chcp 65001 >nul
echo ═══════════════════════════════════════════════════════════════
echo                    إعداد نظام إدارة الصيدلية الشامل
echo                Complete Pharmacy Management System Setup
echo ═══════════════════════════════════════════════════════════════
echo.

echo الخطوة 1: إعداد قاعدة البيانات
echo Step 1: Setting up Database
echo ───────────────────────────────────────────────────────────────
call setup_pharmacy_store_database.bat
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في إعداد قاعدة البيانات
    echo ❌ Database setup failed
    pause
    exit /b 1
)

echo.
echo الخطوة 2: إصلاح مشاكل البناء
echo Step 2: Fixing Build Issues
echo ───────────────────────────────────────────────────────────────
call fix_build_issues.bat
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع
    echo ❌ Project build failed
    pause
    exit /b 1
)

echo.
echo ═══════════════════════════════════════════════════════════════
echo                        ✅ تم الإعداد بنجاح!
echo                     ✅ Setup Completed Successfully!
echo ═══════════════════════════════════════════════════════════════
echo.
echo تم إعداد النظام بالكامل:
echo System has been completely set up:
echo.
echo ✓ قاعدة البيانات الموحدة (UnifiedPharmacy)
echo ✓ Unified Database (UnifiedPharmacy)
echo ✓ جميع الجداول والإجراءات المخزنة
echo ✓ All tables and stored procedures
echo ✓ بناء المشروع وحل مشاكل التجميع
echo ✓ Project build and compilation fixes
echo.
echo يمكنك الآن:
echo You can now:
echo 1. تشغيل البرنامج من Visual Studio
echo 1. Run the application from Visual Studio
echo 2. أو تشغيل الملف التنفيذي من مجلد bin\Debug
echo 2. Or run the executable from bin\Debug folder
echo.
echo معلومات تسجيل الدخول الافتراضية:
echo Default login credentials:
echo Username: admin
echo Password: admin123
echo.
pause
