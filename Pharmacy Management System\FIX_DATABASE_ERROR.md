# 🚨 إصلاح خطأ قاعدة البيانات - Fix Database Error

## **المشكلة الحالية:**
```
Database connection error: Invalid object name 'employee_sessions'
```

**السبب:** قاعدة البيانات الموحدة `UnifiedPharmacy` غير موجودة أو غير مكتملة.

---

## 🔧 **الحل السريع:**

### **الخطوة 1: إنشاء قاعدة البيانات**

1. **افتح SQL Server Management Studio**
2. **اتصل بالخادم** (NARUTO)
3. **انسخ والصق السكريپت التالي:**

```sql
-- إنشاء قاعدة البيانات الموحدة
CREATE DATABASE UnifiedPharmacy;
GO

USE UnifiedPharmacy;
GO

-- 1. جدول الصيدليات
CREATE TABLE pharmacies (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacyCode VARCHAR(20) UNIQUE NOT NULL,
    pharmacyName NVARCHAR(250) NOT NULL,
    ownerName NVARCHAR(250) NOT NULL,
    licenseNumber VARCHAR(100) NOT NULL,
    address NVARCHAR(500) NOT NULL,
    city NVARCHAR(100) NOT NULL,
    region NVARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(250) NOT NULL,
    isActive BIT DEFAULT 1,
    registrationDate DATETIME DEFAULT GETDATE()
);

-- 2. جدول المستخدمين
CREATE TABLE users (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacyId INT NOT NULL,
    userRole VARCHAR(50) NOT NULL,
    name NVARCHAR(250) NOT NULL,
    dob VARCHAR(250) NOT NULL,
    mobile BIGINT NOT NULL,
    email VARCHAR(250) NOT NULL,
    username VARCHAR(250) NOT NULL,
    pass VARCHAR(250) NOT NULL,
    isActive BIT DEFAULT 1,
    FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
);

-- 3. جدول جلسات الموظفين
CREATE TABLE employee_sessions (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacyId INT NOT NULL,
    username VARCHAR(250),
    employeeName NVARCHAR(250),
    loginTime DATETIME,
    logoutTime DATETIME NULL,
    sessionDate DATE,
    FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
);

-- 4. جدول الأدوية
CREATE TABLE medicines (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacyId INT NOT NULL,
    mid VARCHAR(250) NOT NULL,
    mname NVARCHAR(250) NOT NULL,
    mnumber VARCHAR(250) NOT NULL,
    mDate DATE NOT NULL,
    eDate DATE NOT NULL,
    quantity INT NOT NULL,
    perUnit DECIMAL(10,2) NOT NULL,
    lu NVARCHAR(250) NULL,
    br NVARCHAR(250) NULL,
    originalQuantity INT DEFAULT 0,
    originalNewQuantity INT DEFAULT 0,
    dos2 VARCHAR(100) NULL,
    dos2_qty INT NULL DEFAULT 0,
    dos3 VARCHAR(100) NULL,
    dos3_qty INT NULL DEFAULT 0,
    dos4 VARCHAR(100) NULL,
    dos4_qty INT NULL DEFAULT 0,
    FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
);

-- 5. جدول المبيعات
CREATE TABLE sales (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacyId INT NOT NULL,
    mid VARCHAR(250) NOT NULL,
    medicineName NVARCHAR(250) NOT NULL,
    dosage VARCHAR(100) NOT NULL,
    quantity INT NOT NULL,
    pricePerUnit BIGINT NOT NULL,
    totalPrice BIGINT NOT NULL,
    employeeUsername VARCHAR(250) NOT NULL,
    employeeName NVARCHAR(250) NOT NULL,
    saleDate DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
);

-- إضافة البيانات الافتراضية
INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, licenseNumber, address, city, region, phone, email)
VALUES ('MAIN001', 'الصيدلية الرئيسية', 'مدير النظام', 'LIC001', 'العنوان الرئيسي', 'المدينة', 'المنطقة', '**********', '<EMAIL>');

INSERT INTO users (pharmacyId, userRole, name, dob, mobile, email, username, pass)
VALUES 
(1, 'Administrator', 'مدير النظام', '1980-01-01', **********, '<EMAIL>', 'admin', 'admin123'),
(1, 'Employee', 'موظف الصيدلية', '1990-01-01', **********, '<EMAIL>', 'employee', 'emp123');

PRINT 'تم إنشاء قاعدة البيانات بنجاح!';
```

4. **اضغط F5** لتنفيذ السكريپت

---

### **الخطوة 2: اختبار الاتصال**

1. **شغل البرنامج** (F5 في Visual Studio)
2. **اضغط "Select Pharmacy"**
3. **اختر "الصيدلية الرئيسية"**
4. **سجل دخول بـ:**
   - **المدير:** `admin` / `admin123`
   - **الموظف:** `employee` / `emp123`

---

## 🎯 **إذا استمرت المشكلة:**

### **الحل البديل 1: استخدام الملف الجاهز**
1. **افتح SQL Server Management Studio**
2. **اضغط File → Open → File**
3. **اختر الملف:** `CREATE_UNIFIED_DATABASE.sql`
4. **اضغط F5** لتنفيذه

### **الحل البديل 2: إنشاء يدوي**
```sql
-- تحقق من وجود قاعدة البيانات
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'UnifiedPharmacy')
BEGIN
    CREATE DATABASE UnifiedPharmacy;
    PRINT 'تم إنشاء قاعدة البيانات';
END
ELSE
BEGIN
    PRINT 'قاعدة البيانات موجودة بالفعل';
END
```

### **الحل البديل 3: إعادة تعيين الاتصال**
في ملف `UnifiedFunction.cs`، تأكد من سلسلة الاتصال:
```csharp
"data source = NARUTO; database=UnifiedPharmacy; integrated security =True"
```

---

## ✅ **التحقق من النجاح:**

### **في SQL Server:**
```sql
USE UnifiedPharmacy;
SELECT COUNT(*) as 'عدد الصيدليات' FROM pharmacies;
SELECT COUNT(*) as 'عدد المستخدمين' FROM users;
SELECT COUNT(*) as 'عدد الجلسات' FROM employee_sessions;
```

### **في البرنامج:**
- ✅ لا توجد رسائل خطأ عند تسجيل الدخول
- ✅ يظهر زر "Select Pharmacy" 
- ✅ تظهر قائمة الصيدليات
- ✅ تسجيل الدخول يعمل بنجاح

---

## 🚀 **بعد الإصلاح:**

**ستتمكن من:**
- ✅ **اختيار الصيدلية** من القائمة
- ✅ **تسجيل الدخول** بنجاح
- ✅ **إنشاء حسابات جديدة**
- ✅ **استخدام جميع الميزات**
- ✅ **تتبع جلسات المستخدمين**

---

## 📞 **إذا احتجت مساعدة:**

1. **تأكد من تشغيل SQL Server**
2. **تأكد من صحة اسم الخادم** (NARUTO)
3. **تأكد من وجود قاعدة البيانات UnifiedPharmacy**
4. **تأكد من وجود الجداول المطلوبة**

**🎯 بعد تنفيذ هذه الخطوات، ستعمل جميع الميزات بشكل مثالي!**
