@echo off
echo ========================================
echo   إصلاح قاعدة بيانات متجر الصيدلية
echo   Fix Pharmacy Store Database
echo ========================================
echo.

echo 🔧 إصلاح المشاكل المكتشفة...
echo.

echo المشاكل:
echo ❌ خطأ "Cannot find table 0" عند نشر الدواء
echo ❌ أخطاء في الإجراءات المخزنة
echo ❌ أعمدة مفقودة في قاعدة البيانات
echo ❌ جداول متجر الصيدلية غير مكتملة
echo.

echo الحلول:
echo ✅ إنشاء جداول متجر الصيدلية المطلوبة
echo ✅ إصلاح الإجراءات المخزنة
echo ✅ إضافة بيانات افتراضية للاختبار
echo ✅ تصحيح أخطاء قاعدة البيانات
echo.

echo 🚀 تطبيق الإصلاحات...
echo.

echo 1. تشغيل سكريبت إصلاح قاعدة البيانات...
sqlcmd -S . -d UnifiedPharmacy -i "fix_database_pharmacy_store_urgent.sql" -o "database_fix_output.txt"

if %errorlevel%==0 (
    echo    ✅ تم تطبيق إصلاحات قاعدة البيانات بنجاح
) else (
    echo    ❌ فشل في تطبيق إصلاحات قاعدة البيانات
    echo    📋 راجع ملف database_fix_output.txt للتفاصيل
    goto :error
)

echo.
echo 2. التحقق من حالة قاعدة البيانات...

sqlcmd -S . -d UnifiedPharmacy -Q "SELECT COUNT(*) as PharmacyCount FROM pharmacies; SELECT COUNT(*) as PublishedMedicinesCount FROM published_medicines;" -o "database_status.txt"

if %errorlevel%==0 (
    echo    ✅ قاعدة البيانات تعمل بشكل صحيح
    type database_status.txt
) else (
    echo    ❌ مشكلة في الاتصال بقاعدة البيانات
)

echo.
echo 📊 ملخص الإصلاحات المطبقة:
echo.
echo الجداول المضافة/المحدثة:
echo • pharmacies - جدول الصيدليات
echo • published_medicines - جدول الأدوية المنشورة  
echo • purchase_requests - جدول طلبات الشراء
echo • pharmacy_messages - جدول الرسائل
echo.
echo الإجراءات المخزنة المضافة:
echo • sp_PublishMedicine - نشر دواء جديد
echo • sp_GetPublishedMedicines - الحصول على الأدوية المنشورة
echo • sp_UpdatePublishedMedicine - تحديث دواء منشور
echo • sp_CreatePurchaseRequest - إنشاء طلب شراء
echo.
echo البيانات التجريبية المضافة:
echo • 3 صيدليات افتراضية
echo • 5 أدوية تجريبية منشورة
echo.

echo 🎯 خطوات الاختبار:
echo.
echo 1. شغل البرنامج في Visual Studio
echo 2. سجل دخول كموظف
echo 3. اضغط زر "متجر الصيدلية"
echo 4. جرب نشر دواء جديد:
echo    • انتقل لتبويب "نشر دواء"
echo    • املأ البيانات المطلوبة
echo    • اضغط "نشر الدواء"
echo 5. جرب تعديل دواء منشور:
echo    • انتقل لتبويب "أدويتي المعروضة"
echo    • اختر دواء واضغط "تعديل العرض"
echo    • عدل البيانات واحفظ
echo.

echo 🔍 إذا استمرت المشاكل:
echo.
echo أ) مشكلة "Cannot find table 0":
echo    • تأكد من تشغيل SQL Server
echo    • تحقق من connection string في App.config
echo    • راجع ملف database_fix_output.txt
echo.
echo ب) مشاكل نشر الأدوية:
echo    • تأكد من وجود صيدلية افتراضية (pharmacy_id = 1)
echo    • تحقق من صحة البيانات المدخلة
echo    • راجع رسائل الخطأ في البرنامج
echo.
echo ج) مشاكل تعديل الأدوية:
echo    • تأكد من وجود أدوية منشورة
echo    • جرب نشر دواء جديد أولاً
echo    • تحقق من صحة published_medicine_id
echo.

echo 💡 نصائح مهمة:
echo • استخدم SQL Server Management Studio للتحقق من البيانات
echo • راجع جدول published_medicines للتأكد من وجود البيانات
echo • تأكد من أن is_active = 1 للأدوية المنشورة
echo • استخدم pharmacy_id = 1 للاختبار
echo.

goto :success

:error
echo.
echo ❌ فشل في إصلاح قاعدة البيانات!
echo.
echo خطوات استكشاف الأخطاء:
echo 1. تأكد من تشغيل SQL Server
echo 2. تأكد من وجود قاعدة بيانات UnifiedPharmacy
echo 3. تحقق من صلاحيات المستخدم
echo 4. راجع ملف database_fix_output.txt للتفاصيل
echo.
echo إذا استمرت المشكلة، شغل الأوامر يدوياً في SQL Server Management Studio
echo.
pause
exit /b 1

:success
echo.
echo ========================================
echo   تم الإصلاح بنجاح! ✅
echo ========================================
echo.
echo قاعدة البيانات جاهزة الآن لاختبار:
echo • نشر الأدوية
echo • تعديل الأدوية المنشورة  
echo • إنشاء طلبات الشراء
echo • إرسال الرسائل
echo.
echo يمكنك الآن تشغيل البرنامج واختبار متجر الصيدلية!
echo.
echo اضغط أي مفتاح للمتابعة...
pause > nul
