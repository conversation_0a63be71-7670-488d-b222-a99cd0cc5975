@echo off
echo إصلاح مشاكل البناء...
echo Fixing Build Issues...
echo.

REM إنهاء أي عمليات تشغيل للبرنامج
echo إنهاء عمليات البرنامج الجارية...
echo Terminating running processes...
taskkill /f /im "Pharmacy Management System.exe" >nul 2>&1

REM تنظيف مجلدات البناء
echo تنظيف مجلدات البناء...
echo Cleaning build folders...
if exist "Pharmacy Management System\bin" (
    rmdir /s /q "Pharmacy Management System\bin"
    echo تم حذف مجلد bin
    echo Deleted bin folder
)

if exist "Pharmacy Management System\obj" (
    rmdir /s /q "Pharmacy Management System\obj"
    echo تم حذف مجلد obj
    echo Deleted obj folder
)

REM البحث عن MSBuild
echo البحث عن MSBuild...
echo Looking for MSBuild...

set MSBUILD_PATH=""

REM تحقق من Visual Studio 2022
if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    echo تم العثور على MSBuild 2022
    echo Found MSBuild 2022
)

REM تحقق من Visual Studio 2019
if %MSBUILD_PATH%=="" (
    if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
        set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
        echo تم العثور على MSBuild 2019
        echo Found MSBuild 2019
    )
)

REM تحقق من .NET Framework MSBuild
if %MSBUILD_PATH%=="" (
    if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2017\BuildTools\MSBuild\15.0\Bin\MSBuild.exe" (
        set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2017\BuildTools\MSBuild\15.0\Bin\MSBuild.exe"
        echo تم العثور على MSBuild 2017
        echo Found MSBuild 2017
    )
)

if %MSBUILD_PATH%=="" (
    echo ❌ لم يتم العثور على MSBuild
    echo ❌ MSBuild not found
    echo يرجى تثبيت Visual Studio أو Build Tools
    echo Please install Visual Studio or Build Tools
    pause
    exit /b 1
)

REM بناء المشروع
echo بناء المشروع...
echo Building project...
%MSBUILD_PATH% "Pharmacy Management System.sln" /p:Configuration=Debug /p:Platform="Any CPU" /p:TargetFrameworkVersion=v4.7.2

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم بناء المشروع بنجاح!
    echo ✅ Project built successfully!
    echo.
    echo يمكنك الآن تشغيل البرنامج
    echo You can now run the application
) else (
    echo.
    echo ❌ فشل في بناء المشروع
    echo ❌ Project build failed
    echo يرجى التحقق من الأخطاء أعلاه
    echo Please check the errors above
)

echo.
pause
