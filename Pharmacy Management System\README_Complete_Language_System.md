# نظام اللغة الشامل لنظام إدارة الصيدلية

## الوصف
تم تطبيق نظام اللغة الشامل على جميع صفحات نظام إدارة الصيدلية. يمكن للمستخدمين الآن اختيار بين اللغة العربية والإنجليزية وسيتم تطبيق اللغة المختارة على جميع الصفحات فوراً.

## الصفحات المحدثة

### 1. صفحة تسجيل الدخول (Form1)
- أزرار اختيار اللغة (العربية/English)
- ترجمة جميع النصوص والرسائل
- دعم اتجاه النص (RTL/LTR)

### 2. صفحة الصيدلي (Pharmacist)
- ترجمة أزرار القائمة الجانبية
- رسائل التأكيد والخطأ
- تطبيق اتجاه النص

### 3. صفحة بيع الأدوية (UC__P_SellMedicine)
- ترجمة أزرار العمليات
- رسائل الخطأ والتحذير
- تطبيق اتجاه النص

### 4. صفحة المدير (Administrator)
- ترجمة أزرار القائمة
- رسالة الترحيب
- رسائل التأكيد

## الميزات الرئيسية

### 1. نظام LanguageManager
```csharp
// تغيير اللغة
LanguageManager.SetLanguage("ar"); // العربية
LanguageManager.SetLanguage("en"); // الإنجليزية

// الحصول على النص المترجم
string text = LanguageManager.GetText("Dashboard");

// التحقق من اتجاه النص
bool isRTL = LanguageManager.IsRightToLeft();
```

### 2. الترجمات المتاحة
#### القوائم الرئيسية:
- Dashboard / لوحة التحكم
- Add Medicine / إضافة دواء
- View Medicine / عرض الأدوية
- Sell Medicine / بيع الأدوية
- Sales Report / تقرير المبيعات
- Profile / الملف الشخصي
- Logout / تسجيل الخروج

#### العمليات:
- Add to Cart / إضافة للسلة
- Remove / إزالة
- Purchase & Print / شراء وطباعة
- Sync / تحديث
- Search / بحث
- Update / تحديث
- Delete / حذف

#### الرسائل:
- Error / خطأ
- Success / نجح
- Warning / تحذير
- Information / معلومات

### 3. نظام الأحداث
- حدث `LanguageChanged` يتم تشغيله عند تغيير اللغة
- جميع النوافذ تستمع للحدث وتطبق اللغة الجديدة فوراً
- إلغاء الاشتراك التلقائي عند إغلاق النوافذ

### 4. دعم اتجاه النص
- RTL (من اليمين لليسار) للعربية
- LTR (من اليسار لليمين) للإنجليزية
- تطبيق تلقائي على جميع العناصر

## كيفية الاستخدام

### للمستخدمين:
1. افتح البرنامج
2. في صفحة تسجيل الدخول، اختر اللغة المفضلة
3. جميع الصفحات ستظهر باللغة المختارة
4. يمكن تغيير اللغة في أي وقت من صفحة تسجيل الدخول

### للمطورين:
1. **إضافة نص جديد**:
```csharp
// في LanguageManager.cs
["New Text Key"] = "النص العربي",
["New Text Key"] = "English Text",

// في الكود
string text = LanguageManager.GetText("New Text Key");
```

2. **إضافة صفحة جديدة**:
```csharp
public partial class NewPage : Form
{
    public NewPage()
    {
        InitializeComponent();
        LanguageManager.LanguageChanged += OnLanguageChanged;
        ApplyLanguage();
    }

    private void OnLanguageChanged(object sender, EventArgs e)
    {
        ApplyLanguage();
    }

    private void ApplyLanguage()
    {
        // تطبيق الترجمات
        button1.Text = LanguageManager.GetText("Button Text");
        
        // تطبيق اتجاه النص
        if (LanguageManager.IsRightToLeft())
        {
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }
        else
        {
            this.RightToLeft = RightToLeft.No;
            this.RightToLeftLayout = false;
        }
    }

    protected override void OnFormClosed(FormClosedEventArgs e)
    {
        LanguageManager.LanguageChanged -= OnLanguageChanged;
        base.OnFormClosed(e);
    }
}
```

## الملفات المعدلة

### ملفات جديدة:
- `LanguageManager.cs` - نظام إدارة اللغات

### ملفات محدثة:
- `Form1.cs` - صفحة تسجيل الدخول
- `Form1.Designer.cs` - تصميم صفحة تسجيل الدخول
- `Pharmacist.cs` - صفحة الصيدلي
- `Adminstrator.cs` - صفحة المدير
- `UC__P_SellMedicine.cs` - صفحة بيع الأدوية
- `Pharmacy Management System.csproj` - ملف المشروع

## المميزات التقنية

1. **أداء عالي**: تطبيق فوري للغة دون إعادة تشغيل
2. **ذاكرة منخفضة**: استخدام أمثل للذاكرة
3. **قابلية التوسع**: سهولة إضافة لغات جديدة
4. **استقرار**: إدارة آمنة للأحداث والذاكرة

## التطوير المستقبلي

1. **حفظ اللغة**: حفظ اختيار المستخدم في ملف إعدادات
2. **لغات إضافية**: إضافة الفرنسية، الألمانية، إلخ
3. **ترجمة البيانات**: ترجمة محتوى قاعدة البيانات
4. **خطوط مخصصة**: دعم خطوط مختلفة لكل لغة
5. **تخصيص الواجهة**: تخصيص الألوان والأشكال حسب اللغة

## الاختبار

تم اختبار النظام على:
- ✅ صفحة تسجيل الدخول
- ✅ صفحة الصيدلي الرئيسية
- ✅ صفحة بيع الأدوية
- ✅ صفحة المدير
- ✅ رسائل الخطأ والتأكيد
- ✅ اتجاه النص (RTL/LTR)

النظام جاهز للاستخدام ويدعم التبديل السلس بين اللغات! 🎉
