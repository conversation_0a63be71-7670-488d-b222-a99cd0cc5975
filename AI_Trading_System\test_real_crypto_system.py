#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from real_crypto_trading_system import RealCryptoTradingSystem
import time

def test_crypto_system():
    """اختبار شامل لنظام العملات الرقمية"""
    print("🚀 بدء اختبار نظام تداول العملات الرقمية المتقدم")
    print("=" * 60)
    
    # إنشاء النظام
    print("\n1️⃣ إنشاء النظام...")
    system = RealCryptoTradingSystem(demo_mode=True)
    print("✅ تم إنشاء النظام بنجاح")
    
    # اختبار الاتصال
    print("\n2️⃣ اختبار الاتصال...")
    if system.connect():
        print("✅ تم الاتصال بنجاح")
    else:
        print("❌ فشل في الاتصال")
        return False
    
    # اختبار الأسعار الحقيقية
    print("\n3️⃣ اختبار الأسعار الحقيقية...")
    crypto_symbols = ['BTC', 'ETH', 'LTC', 'XRP', 'ADA']
    
    for symbol in crypto_symbols:
        price = system.get_crypto_price(symbol)
        if price:
            print(f"💰 {symbol}: ${price:,.2f}")
        else:
            print(f"❌ فشل في الحصول على سعر {symbol}")
    
    # اختبار أزواج العملات
    print("\n4️⃣ اختبار أزواج العملات المتوفرة...")
    pairs = system.get_available_pairs()
    print(f"📊 عدد الأزواج المتوفرة: {len(pairs)}")
    for pair in pairs:
        print(f"   • {pair}: {system.crypto_pairs[pair]['name']}")
    
    # اختبار إعدادات نسبة الثقة
    print("\n5️⃣ اختبار إعدادات نسبة الثقة...")
    test_confidences = [50, 70, 90, 100]
    
    for confidence in test_confidences:
        if system.set_confidence_threshold(confidence):
            print(f"✅ تم تحديد نسبة الثقة إلى {confidence}%")
        else:
            print(f"❌ فشل في تحديد نسبة الثقة إلى {confidence}%")
    
    # اختبار التحليل الفني
    print("\n6️⃣ اختبار التحليل الفني...")
    test_pairs = ['BTCUSD', 'ETHUSD']
    
    for pair in test_pairs:
        print(f"\n🔍 تحليل {pair}...")
        system.set_current_pair(pair)
        
        analysis = system.analyze_market(pair)
        
        if 'error' in analysis:
            print(f"❌ خطأ في التحليل: {analysis['error']}")
            continue
        
        print(f"   🎯 القرار: {analysis['decision']}")
        print(f"   📊 نسبة الثقة: {analysis['confidence']:.1f}%")
        print(f"   💰 السعر: ${analysis['price']:,.2f}")
        print(f"   📈 RSI: {analysis.get('rsi', 0):.1f}")
        print(f"   📊 MACD: {analysis.get('macd', 0):.4f}")
        
        # اختبار شروط الدخول
        if system.should_enter_trade(analysis):
            print(f"   🚀 فرصة دخول متاحة!")
        else:
            print(f"   ⏳ لا توجد فرصة دخول")
    
    # اختبار معلومات الحساب
    print("\n7️⃣ اختبار معلومات الحساب...")
    account_info = system.get_account_info()
    
    if account_info:
        print(f"💰 الرصيد: ${account_info['balance']:,.2f}")
        print(f"📊 الربح/الخسارة: ${account_info['profit']:,.2f}")
        print(f"📋 الصفقات المفتوحة: {account_info['open_positions']}")
        print(f"🏢 الشركة: {account_info['company']}")
        print(f"🖥️ الخادم: {account_info['server']}")
    else:
        print("❌ فشل في الحصول على معلومات الحساب")
    
    # اختبار تنفيذ صفقة تجريبية
    print("\n8️⃣ اختبار تنفيذ صفقة تجريبية...")
    
    # تحديد نسبة ثقة منخفضة للاختبار
    system.set_confidence_threshold(30)
    
    # تحليل وتنفيذ
    analysis = system.analyze_market('BTCUSD')
    if 'error' not in analysis:
        if system.execute_trade(analysis):
            print("✅ تم تنفيذ الصفقة التجريبية بنجاح")
            
            # عرض الصفقات المفتوحة
            positions = system.get_open_positions()
            if positions:
                print(f"📋 الصفقات المفتوحة: {len(positions)}")
                for pos in positions:
                    print(f"   • {pos['symbol']}: {pos['type']} {pos['volume']:.2f} @ ${pos['price_open']:,.2f}")
            
            # تحديث الصفقات
            print("\n🔄 تحديث الصفقات...")
            system.update_positions()
            
            # عرض معلومات الحساب المحدثة
            updated_account = system.get_account_info()
            if updated_account:
                print(f"💰 الرصيد المحدث: ${updated_account['balance']:,.2f}")
                print(f"📊 الربح/الخسارة المحدث: ${updated_account['profit']:,.2f}")
        else:
            print("⏳ لم يتم تنفيذ الصفقة (شروط الدخول غير متوفرة)")
    
    # اختبار قطع الاتصال
    print("\n9️⃣ اختبار قطع الاتصال...")
    system.disconnect()
    print("✅ تم قطع الاتصال بنجاح")
    
    print("\n" + "=" * 60)
    print("🎉 تم اكتمال جميع الاختبارات بنجاح!")
    print("✨ النظام جاهز للاستخدام مع الأسعار الحقيقية!")
    print("⚠️ تذكر: ابدأ دائماً بالوضع التجريبي!")
    
    return True

def test_price_accuracy():
    """اختبار دقة الأسعار"""
    print("\n🔍 اختبار دقة الأسعار...")
    print("-" * 40)
    
    system = RealCryptoTradingSystem(demo_mode=True)
    
    if not system.connect():
        print("❌ فشل في الاتصال")
        return
    
    # اختبار أسعار متعددة
    symbols = ['BTC', 'ETH', 'LTC', 'XRP']
    
    for symbol in symbols:
        print(f"\n💰 {symbol}:")
        
        # الحصول على السعر 3 مرات للتأكد من الاستقرار
        prices = []
        for i in range(3):
            price = system.get_crypto_price(symbol)
            if price:
                prices.append(price)
                print(f"   المحاولة {i+1}: ${price:,.2f}")
            else:
                print(f"   المحاولة {i+1}: فشل")
            
            if i < 2:  # انتظار بين المحاولات
                time.sleep(2)
        
        if prices:
            avg_price = sum(prices) / len(prices)
            print(f"   📊 متوسط السعر: ${avg_price:,.2f}")
            
            # فحص التقلبات
            if len(prices) > 1:
                max_price = max(prices)
                min_price = min(prices)
                volatility = ((max_price - min_price) / avg_price) * 100
                print(f"   📈 التقلب: {volatility:.2f}%")
        else:
            print(f"   ❌ فشل في الحصول على أي سعر لـ {symbol}")

if __name__ == "__main__":
    try:
        print("🚀 بدء اختبار نظام العملات الرقمية المتقدم")
        print("=" * 60)
        
        # الاختبار الأساسي
        if test_crypto_system():
            print("\n" + "=" * 60)
            
            # اختبار دقة الأسعار
            test_price_accuracy()
            
            print("\n" + "=" * 60)
            print("🎉 جميع الاختبارات اكتملت بنجاح!")
            print("🚀 يمكنك الآن تشغيل الواجهة الرسومية:")
            print("   python advanced_crypto_gui.py")
            print("   أو تشغيل: START_REAL_CRYPTO_TRADING.bat")
        else:
            print("❌ فشل في الاختبارات الأساسية")
            
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {str(e)}")
    finally:
        print("\n👋 انتهى الاختبار")
        input("اضغط Enter للخروج...")
