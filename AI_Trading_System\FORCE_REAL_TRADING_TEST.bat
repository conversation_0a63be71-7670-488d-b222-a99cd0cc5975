@echo off
chcp 65001 > nul
title 🔥 اختبار إجبار التداول الحقيقي - FORCE REAL TRADING TEST

echo.
echo ================================================================
echo 🔥 اختبار إجبار التداول الحقيقي
echo FORCE REAL TRADING TEST
echo ================================================================
echo.

echo 🚨 هذا الاختبار سيتحقق من:
echo    ✅ النظام يبدأ بالوضع الحقيقي (ليس محاكاة)
echo    ✅ الاتصال الحقيقي بـ MetaTrader 5
echo    ✅ عدم التحول التلقائي لوضع المحاكاة
echo    ✅ جاهزية النظام لتنفيذ صفقات حقيقية
echo.

echo 💡 متطلبات الاختبار:
echo    🔌 MetaTrader 5 يعمل ومسجل دخول
echo    ⚙️ التداول الآلي مفعل
echo    📄 ملف config.ini محدث ببيانات حسابك
echo.

echo ⚠️ تحذير:
echo    💰 هذا الاختبار للتأكد من جاهزية التداول الحقيقي
echo    🛡️ لن يتم تنفيذ صفقات حقيقية في الاختبار
echo.

pause

echo 🔄 بدء الاختبار...
echo.

python FORCE_REAL_TRADING_TEST.py

echo.
echo ================================================================
echo ✅ انتهى الاختبار
echo ================================================================
echo.

echo 💡 إذا نجح الاختبار:
echo    🚀 شغّل النظام: run_intelligent_gui_v2.bat
echo    🔥 النظام سيبدأ بالوضع الحقيقي تلقائياً
echo    💰 سيتم تنفيذ صفقات حقيقية على حسابك
echo.

echo ⚠️ إذا فشل الاختبار:
echo    🔧 تحقق من إعدادات MetaTrader 5
echo    📄 تحقق من ملف config.ini
echo    🔌 تأكد من الاتصال بالإنترنت
echo.

pause
