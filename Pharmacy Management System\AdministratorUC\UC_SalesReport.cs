using System;
using System.Data;
using System.Windows.Forms;
using System.Drawing;
using System.Drawing.Printing;
using DGVPrinterHelper;

namespace Pharmacy_Management_System.AdministratorUC
{
    public partial class UC_SalesReport : UserControl
    {
        Function fn = new Function();
        String query;

        public UC_SalesReport()
        {
            InitializeComponent();

            // الاشتراك في أحداث التغيير
            LanguageManager.LanguageChanged += OnLanguageChanged;
            ModernTheme.ThemeChanged += OnThemeChanged;
        }

        private void UC_SalesReport_Load(object sender, EventArgs e)
        {
            // تطبيق اللغة الحالية
            ApplyLanguage();

            loadData();
            loadEmployees();
        }

        private string GetSalesQuery(string whereClause = "")
        {
            string operationId = LanguageManager.GetText("Operation ID");
            string employeeName = LanguageManager.GetText("Employee Name");
            string username = LanguageManager.GetText("Username");
            string medicineName = LanguageManager.GetText("Medicine Name");
            string dosage = LanguageManager.GetText("Dosage");
            string quantity = LanguageManager.GetText("Quantity");
            string pricePerUnit = LanguageManager.GetText("Price Per Unit");
            string totalPrice = LanguageManager.GetText("Total Price");
            string saleDate = LanguageManager.GetText("Sale Date");

            string query = $@"SELECT
                            s.id AS '{operationId}',
                            s.employeeName AS '{employeeName}',
                            s.employeeUsername AS '{username}',
                            s.medicineName AS '{medicineName}',
                            s.dosage AS '{dosage}',
                            s.quantity AS '{quantity}',
                            s.pricePerUnit AS '{pricePerUnit}',
                            s.totalPrice AS '{totalPrice}',
                            CONVERT(VARCHAR, s.saleDate, 120) AS '{saleDate}'
                        FROM sales s
                        WHERE s.pharmacy_id = {SessionManager.CurrentPharmacyId}";

            if (!string.IsNullOrEmpty(whereClause))
            {
                query += " AND " + whereClause;
            }

            query += " ORDER BY s.saleDate DESC";
            return query;
        }

        private void loadEmployees()
        {
            try
            {
                // تحميل قائمة الموظفين من جدول users
                query = "SELECT DISTINCT username, name FROM users WHERE userRole = 'Pharmacist'";
                DataSet ds = fn.getData(query);

                comboBoxEmployees.Items.Clear();
                comboBoxEmployees.Items.Add("جميع الموظفين");

                if (ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow row in ds.Tables[0].Rows)
                    {
                        string employeeText = row["name"].ToString() + " (" + row["username"].ToString() + ")";
                        comboBoxEmployees.Items.Add(employeeText);
                    }
                }
                else
                {
                    // إذا لم يوجد صيادلة، أضف عنصر افتراضي
                    comboBoxEmployees.Items.Add("لا يوجد صيادلة مسجلين");
                }

                comboBoxEmployees.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل قائمة الموظفين: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                comboBoxEmployees.Items.Clear();
                comboBoxEmployees.Items.Add("خطأ في التحميل");
                comboBoxEmployees.SelectedIndex = 0;
            }
        }

        private void loadData()
        {
            try
            {
                // أولاً، نتحقق من وجود جدول المبيعات، وإذا لم يكن موجوداً نقوم بإنشائه
                createSalesTableIfNotExists();

                // استعلام لجلب بيانات المبيعات الفعلية من جدول sales
                query = GetSalesQuery();

                DataSet ds = fn.getData(query);
                if (ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    guna2DataGridView1.DataSource = ds.Tables[0];

                    // حساب إجمالي المبيعات
                    decimal totalSales = 0;
                    string totalPriceColumn = LanguageManager.GetText("Total Price");
                    foreach (DataRow row in ds.Tables[0].Rows)
                    {
                        if (row[totalPriceColumn] != DBNull.Value)
                            totalSales += Convert.ToDecimal(row[totalPriceColumn]);
                    }

                    lblTotalSales.Text = LanguageManager.GetText("Total Sales") + ": " + totalSales.ToString("N2") + " د.ل";
                }
                else
                {
                    // إذا لم توجد مبيعات، عرض رسالة
                    guna2DataGridView1.DataSource = null;
                    lblTotalSales.Text = LanguageManager.GetText("Total Sales") + ": 0.00 د.ل";
                    MessageBox.Show(LanguageManager.GetText("No sales recorded yet"), LanguageManager.GetText("Information"), MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Data loading error message") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblTotalSales.Text = LanguageManager.GetText("Total Sales") + ": 0.00 د.ل";
            }
        }

        private void createSalesTableIfNotExists()
        {
            try
            {
                // إضافة أعمدة الكمية الأصلية إذا لم تكن موجودة في جدول medic
                query = @"IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'originalQuantity')
                         ALTER TABLE medic ADD originalQuantity BIGINT DEFAULT 0";
                fn.setData(query, "");

                query = @"IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'originalNewQuantity')
                         ALTER TABLE medic ADD originalNewQuantity BIGINT DEFAULT 0";
                fn.setData(query, "");

                // تحديث الكميات الأصلية للأدوية الموجودة (فقط إذا كانت فارغة)
                query = @"UPDATE medic
                         SET originalQuantity = quantity
                         WHERE originalQuantity = 0 OR originalQuantity IS NULL";
                fn.setData(query, "");

                query = @"UPDATE medic
                         SET originalNewQuantity = ISNULL(newQuantity, 0)
                         WHERE originalNewQuantity = 0 OR originalNewQuantity IS NULL";
                fn.setData(query, "");

                // إنشاء جدول المبيعات إذا لم يكن موجوداً
                query = @"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='sales' AND xtype='U')
                         CREATE TABLE sales (
                             id INT IDENTITY(1,1) PRIMARY KEY,
                             mid VARCHAR(250),
                             medicineName VARCHAR(250),
                             dosage VARCHAR(100),
                             quantity INT,
                             pricePerUnit BIGINT,
                             totalPrice BIGINT,
                             employeeUsername VARCHAR(250),
                             employeeName VARCHAR(250),
                             saleDate DATETIME DEFAULT GETDATE(),
                             pharmacy_id INT
                         )";
                fn.setData(query, "");

                // إنشاء جدول تسجيل دخول/خروج الموظفين
                query = @"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='employee_sessions' AND xtype='U')
                         CREATE TABLE employee_sessions (
                             id INT IDENTITY(1,1) PRIMARY KEY,
                             username VARCHAR(250),
                             employeeName VARCHAR(250),
                             loginTime DATETIME,
                             logoutTime DATETIME NULL,
                             sessionDate DATE
                         )";
                fn.setData(query, "");
            }
            catch (Exception)
            {
                // تجاهل الأخطاء في إنشاء الجداول إذا كانت موجودة بالفعل
            }
        }

        private void btnSync_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            loadData();
        }

        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtSearch.Text))
            {
                loadData();
                return;
            }

            try
            {
                // البحث في جدول المبيعات
                string whereClause = string.Format(@"s.medicineName LIKE '%{0}%'
                           OR s.employeeName LIKE '%{0}%'
                           OR s.employeeUsername LIKE '%{0}%'", txtSearch.Text);
                query = GetSalesQuery(whereClause);

                DataSet ds = fn.getData(query);
                if (ds.Tables.Count > 0)
                {
                    guna2DataGridView1.DataSource = ds.Tables[0];

                    // حساب إجمالي المبيعات المعروضة
                    decimal totalSales = 0;
                    string totalPriceColumn = LanguageManager.GetText("Total Price");
                    foreach (DataRow row in ds.Tables[0].Rows)
                    {
                        if (row[totalPriceColumn] != DBNull.Value)
                            totalSales += Convert.ToDecimal(row[totalPriceColumn]);
                    }

                    lblTotalSales.Text = LanguageManager.GetText("Displayed Sales Total") + ": " + totalSales.ToString("N2") + " د.ل";
                }
                else
                {
                    guna2DataGridView1.DataSource = null;
                    lblTotalSales.Text = LanguageManager.GetText("Total Sales") + ": 0.00 د.ل";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Search error message") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void comboBoxEmployees_SelectedIndexChanged(object sender, EventArgs e)
        {
            filterByEmployee();
        }

        private void filterByEmployee()
        {
            try
            {
                if (comboBoxEmployees.SelectedItem == null || comboBoxEmployees.SelectedItem.ToString() == "جميع الموظفين")
                {
                    loadData();
                    return;
                }

                string selectedEmployee = comboBoxEmployees.SelectedItem.ToString();
                if (selectedEmployee == "لا يوجد صيادلة مسجلين" || selectedEmployee == "خطأ في التحميل")
                {
                    return;
                }

                // استخراج اسم المستخدم من النص المحدد
                string username = "";
                if (selectedEmployee.Contains("(") && selectedEmployee.Contains(")"))
                {
                    int startIndex = selectedEmployee.LastIndexOf("(") + 1;
                    int endIndex = selectedEmployee.LastIndexOf(")");
                    username = selectedEmployee.Substring(startIndex, endIndex - startIndex);
                }

                // تصفية المبيعات حسب الموظف المحدد
                string whereClause = $"s.employeeUsername = '{username}'";
                query = GetSalesQuery(whereClause);

                DataSet ds = fn.getData(query);
                if (ds.Tables.Count > 0)
                {
                    guna2DataGridView1.DataSource = ds.Tables[0];

                    // حساب إجمالي المبيعات للموظف المحدد
                    decimal totalSales = 0;
                    string totalPriceColumn = LanguageManager.GetText("Total Price");
                    foreach (DataRow row in ds.Tables[0].Rows)
                    {
                        if (row[totalPriceColumn] != DBNull.Value)
                            totalSales += Convert.ToDecimal(row[totalPriceColumn]);
                    }

                    lblTotalSales.Text = LanguageManager.GetText("Employee Sales Total") + " " + selectedEmployee + ": " + totalSales.ToString("N2") + " د.ل";
                }
                else
                {
                    guna2DataGridView1.DataSource = null;
                    lblTotalSales.Text = LanguageManager.GetText("No Sales for Selected Employee");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Data filtering error") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void dateTimePicker1_ValueChanged(object sender, EventArgs e)
        {
            filterByDate();
        }

        private void filterByDate()
        {
            try
            {
                DateTime selectedDate = dateTimePicker1.Value.Date;

                string employeeFilter = "";
                if (comboBoxEmployees.SelectedItem != null &&
                    comboBoxEmployees.SelectedItem.ToString() != "جميع الموظفين" &&
                    comboBoxEmployees.SelectedItem.ToString() != "لا يوجد صيادلة مسجلين" &&
                    comboBoxEmployees.SelectedItem.ToString() != "خطأ في التحميل")
                {
                    string selectedEmployee = comboBoxEmployees.SelectedItem.ToString();
                    string username = "";
                    if (selectedEmployee.Contains("(") && selectedEmployee.Contains(")"))
                    {
                        int startIndex = selectedEmployee.LastIndexOf("(") + 1;
                        int endIndex = selectedEmployee.LastIndexOf(")");
                        username = selectedEmployee.Substring(startIndex, endIndex - startIndex);
                        employeeFilter = " AND s.employeeUsername = '" + username + "'";
                    }
                }

                string whereClause = string.Format("CAST(s.saleDate AS DATE) = '{0}'{1}", selectedDate.ToString("yyyy-MM-dd"), employeeFilter);
                query = GetSalesQuery(whereClause);

                DataSet ds = fn.getData(query);
                if (ds.Tables.Count > 0)
                {
                    guna2DataGridView1.DataSource = ds.Tables[0];

                    // حساب إجمالي المبيعات للتاريخ المحدد
                    decimal totalSales = 0;
                    string totalPriceColumn = LanguageManager.GetText("Total Price");
                    foreach (DataRow row in ds.Tables[0].Rows)
                    {
                        if (row[totalPriceColumn] != DBNull.Value)
                            totalSales += Convert.ToDecimal(row[totalPriceColumn]);
                    }

                    lblTotalSales.Text = LanguageManager.GetText("Date Sales Total") + " " + selectedDate.ToString("yyyy-MM-dd") + ": " + totalSales.ToString("N2") + " د.ل";
                }
                else
                {
                    guna2DataGridView1.DataSource = null;
                    lblTotalSales.Text = LanguageManager.GetText("No Sales for Selected Date");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(LanguageManager.GetText("Date filtering error") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnPrintSales_Click(object sender, EventArgs e)
        {
            try
            {
                if (guna2DataGridView1.Rows.Count == 0)
                {
                    MessageBox.Show("لا توجد بيانات للطباعة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // استخدام DGVPrinter مع إعدادات التصميم
                DGVPrinter print = new DGVPrinter();

                // تطبيق إعدادات الطباعة المحفوظة مع التحقق
                PrintHelper.ApplyPrintSettingsWithValidation(print, "تقرير المبيعات");

                // إضافة معلومات إضافية للتذييل
                if (!string.IsNullOrEmpty(lblTotalSales.Text))
                {
                    print.Footer = print.Footer + " - " + lblTotalSales.Text;
                }

                // طباعة الجدول مع معاينة
                print.PrintPreviewDataGridView(guna2DataGridView1);
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في الطباعة: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OnLanguageChanged(object sender, EventArgs e)
        {
            ApplyLanguage();
        }

        private void ApplyLanguage()
        {
            // تطبيق الترجمات على العناصر
            if (label1 != null) label1.Text = LanguageManager.GetText("Sales Report");
            if (label2 != null) label2.Text = LanguageManager.GetText("Filter by Employee");
            if (label3 != null) label3.Text = LanguageManager.GetText("From Date");
            if (label4 != null) label4.Text = LanguageManager.GetText("To Date");
            if (btnSync != null) btnSync.Text = LanguageManager.GetText("Sync");
            if (btnPrintSales != null) btnPrintSales.Text = "🖨️ " + LanguageManager.GetText("Print Report");

            // تطبيق الترجمات على أعمدة الجدول
            if (guna2DataGridView1.Columns.Count > 0)
            {
                if (guna2DataGridView1.Columns.Count > 0) guna2DataGridView1.Columns[0].HeaderText = LanguageManager.GetText("Employee Name");
                if (guna2DataGridView1.Columns.Count > 1) guna2DataGridView1.Columns[1].HeaderText = LanguageManager.GetText("Medicine Name");
                if (guna2DataGridView1.Columns.Count > 2) guna2DataGridView1.Columns[2].HeaderText = LanguageManager.GetText("Quantity");
                if (guna2DataGridView1.Columns.Count > 3) guna2DataGridView1.Columns[3].HeaderText = LanguageManager.GetText("Price Per Unit");
                if (guna2DataGridView1.Columns.Count > 4) guna2DataGridView1.Columns[4].HeaderText = LanguageManager.GetText("Total Price");
                if (guna2DataGridView1.Columns.Count > 5) guna2DataGridView1.Columns[5].HeaderText = LanguageManager.GetText("Date");
                if (guna2DataGridView1.Columns.Count > 6) guna2DataGridView1.Columns[6].HeaderText = LanguageManager.GetText("Login Time");
                if (guna2DataGridView1.Columns.Count > 7) guna2DataGridView1.Columns[7].HeaderText = LanguageManager.GetText("Logout Time");
            }

            // تحديث نص ComboBox
            if (comboBoxEmployees.Items.Count > 0)
            {
                // حفظ الاختيار الحالي
                string currentSelection = comboBoxEmployees.Text;

                // تحديث العنصر الأول
                if (comboBoxEmployees.Items[0].ToString().Contains("جميع") || comboBoxEmployees.Items[0].ToString().Contains("All"))
                {
                    comboBoxEmployees.Items[0] = LanguageManager.GetText("All Employees");
                }

                // إعادة تعيين الاختيار إذا كان "جميع الموظفين"
                if (currentSelection.Contains("جميع") || currentSelection.Contains("All"))
                {
                    comboBoxEmployees.SelectedIndex = 0;
                }
            }

            // تحديث نص إجمالي المبيعات إذا كان موجوداً
            if (lblTotalSales != null && !string.IsNullOrEmpty(lblTotalSales.Text))
            {
                // إعادة تحميل البيانات لتطبيق الترجمة على النص
                loadData();
            }

            // الحفاظ على التصميم من اليسار لليمين في جميع اللغات
            this.RightToLeft = RightToLeft.No;
        }

        private void OnThemeChanged(object sender, EventArgs e)
        {
            ApplyLanguage();
        }

        protected override void OnHandleDestroyed(EventArgs e)
        {
            // إلغاء الاشتراك في أحداث التغيير
            LanguageManager.LanguageChanged -= OnLanguageChanged;
            ModernTheme.ThemeChanged -= OnThemeChanged;
            base.OnHandleDestroyed(e);
        }
    }
}