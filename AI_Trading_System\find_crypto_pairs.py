#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import MetaTrader5 as mt5
from datetime import datetime

def find_crypto_pairs():
    """البحث عن أزواج العملات الرقمية الحقيقية"""
    
    print("=" * 60)
    print("🔍 البحث عن أزواج العملات الرقمية الحقيقية")
    print("=" * 60)
    
    if not mt5.initialize():
        print("❌ فشل في تهيئة MetaTrader 5")
        return []
    
    try:
        all_symbols = mt5.symbols_get()
        if not all_symbols:
            print("❌ فشل في الحصول على قائمة الرموز")
            return []
        
        print(f"📊 إجمالي الرموز: {len(all_symbols)}")
        print()
        
        # البحث عن أزواج العملات الرقمية
        crypto_pairs = []
        
        # أنماط البحث المختلفة
        search_patterns = [
            # Bitcoin patterns
            'BTCUSD', 'BTC/USD', 'XBTUSD', 'XBT/USD',
            'BTCEUR', 'BTC/EUR', 'XBTEUR', 'XBT/EUR',
            # Ethereum patterns  
            'ETHUSD', 'ETH/USD', 'ETHEUR', 'ETH/EUR',
            # Other cryptos
            'LTCUSD', 'LTC/USD', 'XRPUSD', 'XRP/USD',
            'ADAUSD', 'ADA/USD', 'DOTUSD', 'DOT/USD'
        ]
        
        print("🔍 البحث بالأنماط المختلفة...")
        
        for pattern in search_patterns:
            symbol_info = mt5.symbol_info(pattern)
            if symbol_info:
                # اختبار الحصول على البيانات
                rates = mt5.copy_rates_from_pos(pattern, mt5.TIMEFRAME_M1, 0, 1)
                if rates is not None and len(rates) > 0:
                    crypto_pairs.append({
                        'symbol': pattern,
                        'description': symbol_info.description,
                        'currency_base': symbol_info.currency_base,
                        'currency_profit': symbol_info.currency_profit,
                        'digits': symbol_info.digits,
                        'point': symbol_info.point,
                        'current_price': rates[0]['close'],
                        'trade_mode': symbol_info.trade_mode
                    })
                    print(f"✅ {pattern:<10} - {symbol_info.description}")
                    print(f"   السعر الحالي: {rates[0]['close']:.{symbol_info.digits}f}")
                    print(f"   العملة الأساسية: {symbol_info.currency_base}")
                    print(f"   عملة الربح: {symbol_info.currency_profit}")
                    print()
        
        # البحث في جميع الرموز
        print("🔍 البحث في جميع الرموز...")
        
        for symbol in all_symbols:
            name = symbol.name
            
            # تحقق من أنماط العملات الرقمية
            crypto_indicators = ['BTC', 'ETH', 'LTC', 'XRP', 'ADA', 'DOT', 'LINK', 'UNI']
            currency_indicators = ['USD', 'EUR', 'GBP', 'JPY']
            
            # تحقق من وجود عملة رقمية وعملة تقليدية
            has_crypto = any(crypto in name for crypto in crypto_indicators)
            has_currency = any(curr in name for curr in currency_indicators)
            
            if has_crypto and has_currency and len(name) <= 10:
                # تحقق من أنه ليس ETF
                if not any(etf in symbol.description.upper() for etf in ['ETF', 'TRUST', 'FUND', 'SHARES']):
                    # تحقق من إمكانية التداول
                    if symbol.trade_mode == mt5.SYMBOL_TRADE_MODE_FULL:
                        # اختبار الحصول على البيانات
                        rates = mt5.copy_rates_from_pos(name, mt5.TIMEFRAME_M1, 0, 1)
                        if rates is not None and len(rates) > 0:
                            # تحقق من أن السعر منطقي للعملات الرقمية
                            price = rates[0]['close']
                            if price > 1:  # العملات الرقمية عادة أكبر من 1
                                # تحقق من عدم وجوده مسبقاً
                                if not any(p['symbol'] == name for p in crypto_pairs):
                                    crypto_pairs.append({
                                        'symbol': name,
                                        'description': symbol.description,
                                        'currency_base': symbol.currency_base,
                                        'currency_profit': symbol.currency_profit,
                                        'digits': symbol.digits,
                                        'point': symbol.point,
                                        'current_price': price,
                                        'trade_mode': symbol.trade_mode
                                    })
                                    print(f"✅ {name:<10} - {symbol.description}")
                                    print(f"   السعر الحالي: {price:.{symbol.digits}f}")
                                    print()
        
        print("=" * 60)
        print("📋 ملخص النتائج:")
        print(f"   أزواج العملات الرقمية المتوفرة: {len(crypto_pairs)}")
        
        if crypto_pairs:
            print("\n🎯 الأزواج الجاهزة للتداول:")
            for i, pair in enumerate(crypto_pairs, 1):
                print(f"   {i:2d}. {pair['symbol']:<10} - السعر: {pair['current_price']:.{pair['digits']}f}")
        
        print("=" * 60)
        
        return crypto_pairs
        
    except Exception as e:
        print(f"❌ خطأ في البحث: {str(e)}")
        return []
        
    finally:
        mt5.shutdown()

if __name__ == "__main__":
    try:
        pairs = find_crypto_pairs()
        if pairs:
            print(f"🎉 تم العثور على {len(pairs)} زوج عملة رقمية!")
        else:
            print("⚠️ لم يتم العثور على أزواج عملات رقمية")
            
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البحث")
    except Exception as e:
        print(f"❌ خطأ عام: {str(e)}")
    
    input("\nاضغط Enter للخروج...")
