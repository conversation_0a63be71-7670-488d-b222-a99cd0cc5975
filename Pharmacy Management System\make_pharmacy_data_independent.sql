-- تعديل قاعدة البيانات لجعل بيانات كل صيدلية مستقلة
USE UnifiedPharmacy;

PRINT '=== تعديل قاعدة البيانات لاستقلالية بيانات الصيدليات ===';

-- 1. إن<PERSON><PERSON><PERSON> جدول الصيدليات إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacies')
BEGIN
    CREATE TABLE pharmacies (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacy_code NVARCHAR(50) UNIQUE NOT NULL,
        pharmacy_name NVARCHAR(255) NOT NULL,
        owner_name NVARCHA<PERSON>(255),
        phone NVARCHAR(50),
        mobile NVARCHAR(50),
        email NVARCHAR(255),
        city NVARCHAR(100),
        address NVARCHAR(500),
        license_number NVARCHAR(100),
        created_date DATETIME DEFAULT GETDATE(),
        is_active BIT DEFAULT 1
    );
    PRINT '✅ تم إنشاء جدول pharmacies';
END

-- 2. إضافة pharmacy_id لجدول users إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'pharmacy_id')
BEGIN
    ALTER TABLE users ADD pharmacy_id INT;
    PRINT '✅ تم إضافة pharmacy_id لجدول users';
END

-- 3. إضافة pharmacy_id لجدول medic إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('medic') AND name = 'pharmacy_id')
BEGIN
    ALTER TABLE medic ADD pharmacy_id INT;
    PRINT '✅ تم إضافة pharmacy_id لجدول medic';
END

-- 4. إنشاء جدول sales مع pharmacy_id
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'sales')
BEGIN
    CREATE TABLE sales (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacy_id INT NOT NULL,
        employee_id INT,
        medicine_id INT,
        medicine_name NVARCHAR(255),
        dosage NVARCHAR(100),
        quantity_sold INT,
        unit_price DECIMAL(10,2),
        total_price DECIMAL(10,2),
        sale_date DATETIME DEFAULT GETDATE(),
        customer_name NVARCHAR(255),
        notes NVARCHAR(500)
    );
    PRINT '✅ تم إنشاء جدول sales';
END
ELSE
BEGIN
    -- إضافة pharmacy_id إذا لم يكن موجوداً
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('sales') AND name = 'pharmacy_id')
    BEGIN
        ALTER TABLE sales ADD pharmacy_id INT;
        PRINT '✅ تم إضافة pharmacy_id لجدول sales';
    END
END

-- 5. إنشاء جدول employee_sessions مع pharmacy_id
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'employee_sessions')
BEGIN
    CREATE TABLE employee_sessions (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacy_id INT NOT NULL,
        employee_id INT NOT NULL,
        employee_name NVARCHAR(255),
        login_time DATETIME,
        logout_time DATETIME,
        session_date DATE,
        total_hours DECIMAL(5,2),
        notes NVARCHAR(500)
    );
    PRINT '✅ تم إنشاء جدول employee_sessions';
END
ELSE
BEGIN
    -- إضافة pharmacy_id إذا لم يكن موجوداً
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('employee_sessions') AND name = 'pharmacy_id')
    BEGIN
        ALTER TABLE employee_sessions ADD pharmacy_id INT;
        PRINT '✅ تم إضافة pharmacy_id لجدول employee_sessions';
    END
END

-- 6. تحديث purchase_requests لتتضمن pharmacy_id للبائع والمشتري (موجود بالفعل)
PRINT '⚠️ جدول purchase_requests يحتوي بالفعل على buyer_pharmacy_id و seller_pharmacy_id';

-- 7. تحديث pharmacy_messages لتتضمن pharmacy_id (موجود بالفعل)
PRINT '⚠️ جدول pharmacy_messages يحتوي بالفعل على sender_pharmacy_id و receiver_pharmacy_id';

-- 8. إدراج صيدليات تجريبية
IF NOT EXISTS (SELECT 1 FROM pharmacies)
BEGIN
    INSERT INTO pharmacies (pharmacy_code, pharmacy_name, owner_name, phone, city, address) VALUES
    ('PH001', 'الصيدلية المركزية', 'أحمد محمد', '01234567890', 'القاهرة', 'شارع التحرير، وسط البلد'),
    ('PH002', 'صيدلية النهضة', 'فاطمة علي', '01987654321', 'الجيزة', 'شارع الهرم، الجيزة'),
    ('PH003', 'صيدلية الشفاء', 'محمد حسن', '01122334455', 'الإسكندرية', 'شارع الكورنيش، الإسكندرية'),
    ('PH004', 'صيدلية الأمل', 'سارة أحمد', '01555666777', 'المنصورة', 'شارع الجمهورية، المنصورة');
    PRINT '✅ تم إدراج صيدليات تجريبية';
END

-- 9. تحديث بيانات المستخدمين لربطها بالصيدليات
UPDATE users SET pharmacy_id = 1 WHERE id = 1;
UPDATE users SET pharmacy_id = 2 WHERE id = 2;
UPDATE users SET pharmacy_id = 3 WHERE id = 3;
UPDATE users SET pharmacy_id = 4 WHERE id = 4;
PRINT '✅ تم ربط المستخدمين بالصيدليات';

-- 10. تحديث بيانات الأدوية لربطها بالصيدليات
UPDATE medic SET pharmacy_id = 1 WHERE pharmacy_id IS NULL;
PRINT '✅ تم ربط الأدوية بالصيدلية الافتراضية';

-- 11. إنشاء فهارس لتحسين الأداء
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_users_pharmacy_id')
BEGIN
    CREATE INDEX IX_users_pharmacy_id ON users(pharmacy_id);
    PRINT '✅ تم إنشاء فهرس users.pharmacy_id';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_medic_pharmacy_id')
BEGIN
    CREATE INDEX IX_medic_pharmacy_id ON medic(pharmacy_id);
    PRINT '✅ تم إنشاء فهرس medic.pharmacy_id';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_sales_pharmacy_id')
BEGIN
    CREATE INDEX IX_sales_pharmacy_id ON sales(pharmacy_id);
    PRINT '✅ تم إنشاء فهرس sales.pharmacy_id';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_employee_sessions_pharmacy_id')
BEGIN
    CREATE INDEX IX_employee_sessions_pharmacy_id ON employee_sessions(pharmacy_id);
    PRINT '✅ تم إنشاء فهرس employee_sessions.pharmacy_id';
END

-- 12. عرض ملخص البيانات
PRINT '';
PRINT '=== ملخص البيانات بعد التعديل ===';
SELECT 'pharmacies' as 'الجدول', COUNT(*) as 'عدد السجلات' FROM pharmacies
UNION ALL
SELECT 'users', COUNT(*) FROM users WHERE pharmacy_id IS NOT NULL
UNION ALL
SELECT 'medic', COUNT(*) FROM medic WHERE pharmacy_id IS NOT NULL
UNION ALL
SELECT 'sales', COUNT(*) FROM sales
UNION ALL
SELECT 'employee_sessions', COUNT(*) FROM employee_sessions;

-- 13. عرض الصيدليات والمستخدمين المرتبطين بها
PRINT '';
PRINT '=== الصيدليات والمستخدمين ===';
SELECT 
    p.id as 'معرف الصيدلية',
    p.pharmacy_name as 'اسم الصيدلية',
    p.pharmacy_code as 'كود الصيدلية',
    COUNT(u.id) as 'عدد المستخدمين'
FROM pharmacies p
LEFT JOIN users u ON p.id = u.pharmacy_id
GROUP BY p.id, p.pharmacy_name, p.pharmacy_code
ORDER BY p.id;

PRINT '';
PRINT '=== تم إكمال تعديل قاعدة البيانات بنجاح ===';
PRINT 'الآن كل صيدلية لها بياناتها المستقلة:';
PRINT '- الموظفين (users.pharmacy_id)';
PRINT '- الأدوية (medic.pharmacy_id)';
PRINT '- المبيعات (sales.pharmacy_id)';
PRINT '- جلسات الموظفين (employee_sessions.pharmacy_id)';
PRINT '- الرسائل (pharmacy_messages)';
PRINT '- طلبات الشراء (purchase_requests)';
