-- اختبار استعلامات قاعدة البيانات لنظام إدارة الصيدلية
-- Test Database Queries for Pharmacy Management System

USE pharmacy;
GO

PRINT '========================================';
PRINT 'اختبار استعلامات قاعدة البيانات';
PRINT '========================================';

-- 1. اختبار جدول المستخدمين
PRINT '1. اختبار جدول المستخدمين:';
SELECT COUNT(*) AS 'عدد المستخدمين' FROM users;

-- 2. اختبار جدول الأدوية مع جميع الأعمدة
PRINT '2. اختبار جدول الأدوية:';
SELECT COUNT(*) AS 'عدد الأدوية' FROM medic;

-- 3. عرض أعمدة جدول الأدوية للتأكد من وجودها
PRINT '3. أعمدة جدول الأدوية:';
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'medic'
ORDER BY ORDINAL_POSITION;

-- 4. اختبار الاستعلامات المستخدمة في الكود
PRINT '4. اختبار الاستعلامات الأساسية:';

-- استعلام تسجيل الدخول
PRINT 'اختبار استعلام تسجيل الدخول:';
SELECT COUNT(*) AS 'نتيجة الاختبار' 
FROM users 
WHERE username = 'test' AND pass = 'test';

-- استعلام الأدوية المتاحة
PRINT 'اختبار استعلام الأدوية المتاحة:';
SELECT COUNT(*) AS 'الأدوية المتاحة'
FROM medic 
WHERE eDate >= GETDATE() AND quantity > 0;

-- استعلام الأدوية منتهية الصلاحية
PRINT 'اختبار استعلام الأدوية منتهية الصلاحية:';
SELECT COUNT(*) AS 'الأدوية منتهية الصلاحية'
FROM medic 
WHERE eDate <= GETDATE();

-- استعلام الأدوية قليلة المخزون
PRINT 'اختبار استعلام الأدوية قليلة المخزون:';
SELECT COUNT(*) AS 'الأدوية قليلة المخزون'
FROM medic 
WHERE quantity <= 10;

-- 5. اختبار جدول المبيعات
PRINT '5. اختبار جدول المبيعات:';
SELECT COUNT(*) AS 'عدد المبيعات' FROM sales;

-- 6. اختبار جدول جلسات الموظفين
PRINT '6. اختبار جدول جلسات الموظفين:';
SELECT COUNT(*) AS 'عدد الجلسات' FROM employee_sessions;

-- 7. اختبار استعلامات الجرعات
PRINT '7. اختبار استعلامات الجرعات:';
SELECT 
    COUNT(*) AS 'الأدوية مع جرعة 2',
    SUM(CASE WHEN dos2 IS NOT NULL AND dos2 != '' THEN 1 ELSE 0 END) AS 'لديها جرعة 2',
    SUM(CASE WHEN dos3 IS NOT NULL AND dos3 != '' THEN 1 ELSE 0 END) AS 'لديها جرعة 3',
    SUM(CASE WHEN dos4 IS NOT NULL AND dos4 != '' THEN 1 ELSE 0 END) AS 'لديها جرعة 4'
FROM medic;

-- 8. اختبار استعلام البحث (مثال من الكود)
PRINT '8. اختبار استعلام البحث:';
DECLARE @searchTerm VARCHAR(50) = 'test';
SELECT COUNT(*) AS 'نتائج البحث'
FROM medic 
WHERE mname LIKE '%' + @searchTerm + '%' 
   OR mid LIKE '%' + @searchTerm + '%' 
   OR br LIKE '%' + @searchTerm + '%';

-- 9. اختبار استعلام التحديث للجرعات (محاكاة)
PRINT '9. اختبار استعلام تحديث الجرعات:';
-- هذا مجرد اختبار للتأكد من صحة البنية
SELECT 
    id,
    mname,
    dos2,
    dos2_qty,
    dos3,
    dos3_qty,
    dos4,
    dos4_qty
FROM medic 
WHERE id = 1; -- مجرد مثال

-- 10. اختبار استعلام المبيعات حسب التاريخ
PRINT '10. اختبار استعلام المبيعات حسب التاريخ:';
SELECT 
    CONVERT(DATE, saleDate) AS 'التاريخ',
    COUNT(*) AS 'عدد المبيعات',
    SUM(quantity) AS 'إجمالي الكمية',
    SUM(totalPrice) AS 'إجمالي المبلغ'
FROM sales
WHERE saleDate >= DATEADD(DAY, -30, GETDATE())
GROUP BY CONVERT(DATE, saleDate)
ORDER BY CONVERT(DATE, saleDate) DESC;

-- 11. اختبار استعلام جلسات الموظفين
PRINT '11. اختبار استعلام جلسات الموظفين:';
SELECT 
    employeeName,
    COUNT(*) AS 'عدد الجلسات',
    MAX(loginTime) AS 'آخر تسجيل دخول'
FROM employee_sessions
WHERE sessionDate >= DATEADD(DAY, -7, GETDATE())
GROUP BY employeeName
ORDER BY MAX(loginTime) DESC;

-- 12. التحقق من سلامة البيانات
PRINT '12. التحقق من سلامة البيانات:';
SELECT 
    'medic' AS 'الجدول',
    COUNT(*) AS 'إجمالي السجلات',
    SUM(CASE WHEN quantity IS NULL THEN 1 ELSE 0 END) AS 'كمية فارغة',
    SUM(CASE WHEN mname IS NULL OR mname = '' THEN 1 ELSE 0 END) AS 'اسم فارغ',
    SUM(CASE WHEN originalQuantity IS NULL THEN 1 ELSE 0 END) AS 'كمية أصلية فارغة'
FROM medic

UNION ALL

SELECT 
    'users' AS 'الجدول',
    COUNT(*) AS 'إجمالي السجلات',
    SUM(CASE WHEN username IS NULL OR username = '' THEN 1 ELSE 0 END) AS 'اسم مستخدم فارغ',
    SUM(CASE WHEN pass IS NULL OR pass = '' THEN 1 ELSE 0 END) AS 'كلمة مرور فارغة',
    0 AS 'غير مستخدم'
FROM users;

PRINT '========================================';
PRINT 'انتهى اختبار قاعدة البيانات';
PRINT '========================================';

-- إظهار معلومات مفيدة للمطور
PRINT 'معلومات إضافية:';
PRINT 'اسم قاعدة البيانات: ' + DB_NAME();
PRINT 'اسم الخادم: ' + @@SERVERNAME;
PRINT 'تاريخ الاختبار: ' + CONVERT(VARCHAR, GETDATE(), 120);
