#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام تداول العملات الرقمية
Test Cryptocurrency Trading System
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from crypto_trading_system import CryptoTradingSystem
import time
from datetime import datetime

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    print("🧪 اختبار الوظائف الأساسية...")
    
    # إنشاء النظام
    system = CryptoTradingSystem(demo_mode=True)
    
    # اختبار الحصول على السعر
    print("📊 اختبار الحصول على الأسعار...")
    price_data = system.get_crypto_price('BTCUSDT')
    
    if price_data:
        print(f"✅ سعر Bitcoin: ${price_data['price']:.2f}")
    else:
        print("❌ فشل في الحصول على السعر")
        return False
        
    # اختبار البيانات التاريخية
    print("📈 اختبار البيانات التاريخية...")
    df = system.get_historical_data('BTCUSDT', '1h', 50)
    
    if df is not None and not df.empty:
        print(f"✅ تم الحصول على {len(df)} نقطة بيانات")
        print(f"   آخر سعر إغلاق: ${df['close'].iloc[-1]:.2f}")
    else:
        print("❌ فشل في الحصول على البيانات التاريخية")
        return False
        
    # اختبار المؤشرات الفنية
    print("📊 اختبار المؤشرات الفنية...")
    df_with_indicators = system.calculate_technical_indicators(df)
    
    if 'rsi' in df_with_indicators.columns:
        rsi = df_with_indicators['rsi'].iloc[-1]
        print(f"✅ RSI: {rsi:.2f}")
    else:
        print("❌ فشل في حساب المؤشرات الفنية")
        return False
        
    # اختبار تحليل السوق
    print("🎯 اختبار تحليل السوق...")
    analysis = system.analyze_market_sentiment(df_with_indicators)
    
    if analysis and 'decision' in analysis:
        print(f"✅ قرار التحليل: {analysis['decision']}")
        print(f"   مستوى الثقة: {analysis['confidence']:.2%}")
        print(f"   RSI: {analysis.get('rsi', 0):.2f}")
    else:
        print("❌ فشل في تحليل السوق")
        return False
        
    print("🎉 جميع الاختبارات الأساسية نجحت!")
    return True

def test_demo_trading():
    """اختبار التداول التجريبي"""
    print("\n💼 اختبار التداول التجريبي...")
    
    system = CryptoTradingSystem(demo_mode=True)
    initial_balance = system.balance
    
    print(f"💰 الرصيد الابتدائي: ${initial_balance:.2f}")
    
    # محاكاة قرار شراء
    demo_decision = {
        'decision': 'buy',
        'confidence': 0.75,
        'price': 45000.0,
        'symbol': 'BTCUSDT'
    }
    
    print("🟢 اختبار صفقة شراء تجريبية...")
    success = system.execute_demo_trade(demo_decision)
    
    if success:
        print(f"✅ تم فتح صفقة شراء")
        print(f"📊 الصفقات المفتوحة: {len(system.positions)}")
    else:
        print("❌ فشل في فتح الصفقة")
        return False
        
    # محاكاة تغيير السعر وإغلاق الصفقة
    print("📈 محاكاة تغيير السعر...")
    
    # تعديل السعر في النظام للاختبار
    for trade_id, position in system.positions.items():
        # محاكاة ارتفاع السعر لتفعيل جني الربح
        system.positions[trade_id]['take_profit'] = 44000.0  # سعر أقل لتفعيل الإغلاق
        
    # فحص الصفقات (سيتم إغلاقها)
    system.check_positions()
    
    print(f"💰 الرصيد النهائي: ${system.balance:.2f}")
    print(f"📊 الصفقات المفتوحة: {len(system.positions)}")
    print(f"📈 تاريخ الصفقات: {len(system.trade_history)}")
    
    if len(system.trade_history) > 0:
        last_trade = system.trade_history[-1]
        print(f"✅ آخر صفقة: {last_trade['close_reason']} - ربح/خسارة: ${last_trade['pnl']:.2f}")
    
    print("🎉 اختبار التداول التجريبي نجح!")
    return True

def test_learning_system():
    """اختبار نظام التعلم"""
    print("\n🧠 اختبار نظام التعلم...")
    
    system = CryptoTradingSystem(demo_mode=True)
    
    # إضافة بيانات تعلم وهمية
    print("📚 إضافة بيانات تعلم وهمية...")
    
    for i in range(25):  # إضافة 25 نقطة بيانات
        fake_learning_data = {
            'features': [45000 + i*100, 0.6, 50, 30, 0.1, 0.05, 0.02, 0.01, 0.03, 0.04, 0.02, 0.01, 0.005, 0.01],
            'target': 0.02 if i % 2 == 0 else -0.01,  # ربح/خسارة متناوبة
            'timestamp': datetime.now(),
            'symbol': 'BTCUSDT',
            'confidence': 0.7
        }
        system.learning_data.append(fake_learning_data)
    
    print(f"✅ تم إضافة {len(system.learning_data)} نقطة تعلم")
    
    # اختبار تدريب النموذج
    print("🤖 اختبار تدريب النموذج...")
    success = system.train_ml_model()
    
    if success:
        print("✅ تم تدريب النموذج بنجاح")
        print(f"🧠 النموذج مدرب: {system.model_trained}")
    else:
        print("❌ فشل في تدريب النموذج")
        return False
        
    # اختبار التنبؤ
    print("🔮 اختبار التنبؤ...")
    
    # الحصول على بيانات حقيقية للتنبؤ
    df = system.get_historical_data('BTCUSDT', '1h', 50)
    if df is not None:
        df = system.calculate_technical_indicators(df)
        features = system.prepare_ml_features(df)
        
        if features.size > 0:
            prediction = system.predict_price_movement(features)
            if prediction is not None:
                print(f"✅ توقع حركة السعر: {prediction:.4f}")
            else:
                print("❌ فشل في التنبؤ")
                return False
    
    print("🎉 اختبار نظام التعلم نجح!")
    return True

def test_full_analysis():
    """اختبار التحليل الكامل"""
    print("\n🎯 اختبار التحليل الكامل...")
    
    system = CryptoTradingSystem(demo_mode=True)
    
    # إضافة بيانات تعلم وتدريب النموذج
    for i in range(30):
        fake_data = {
            'features': [45000 + i*50, 0.6, 50, 30, 0.1, 0.05, 0.02, 0.01, 0.03, 0.04, 0.02, 0.01, 0.005, 0.01],
            'target': 0.01 if i % 3 == 0 else -0.005,
            'timestamp': datetime.now(),
            'symbol': 'BTCUSDT',
            'confidence': 0.7
        }
        system.learning_data.append(fake_data)
    
    system.train_ml_model()
    
    # تحليل كامل للسوق
    print("📊 تحليل كامل للسوق...")
    analysis = system.analyze_and_trade()
    
    if analysis:
        print(f"✅ نتائج التحليل:")
        print(f"   🎯 القرار: {analysis['decision']}")
        print(f"   📈 الثقة: {analysis['confidence']:.2%}")
        print(f"   📊 نقاط الاتجاه: {analysis.get('trend_score', 0)}")
        print(f"   ⚡ نقاط الزخم: {analysis.get('momentum_score', 0)}")
        print(f"   💰 السعر: ${analysis.get('price', 0):.2f}")
        
        if 'ml_prediction' in analysis:
            print(f"   🤖 توقع ML: {analysis['ml_prediction']:.4f}")
    else:
        print("❌ فشل في التحليل الكامل")
        return False
        
    print("🎉 اختبار التحليل الكامل نجح!")
    return True

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار نظام تداول العملات الرقمية")
    print("=" * 50)
    
    tests = [
        ("الوظائف الأساسية", test_basic_functionality),
        ("التداول التجريبي", test_demo_trading),
        ("نظام التعلم", test_learning_system),
        ("التحليل الكامل", test_full_analysis)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 اختبار: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
            
        time.sleep(1)  # انتظار قصير بين الاختبارات
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت - راجع الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    try:
        success = main()
        input("\nاضغط Enter للخروج...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
