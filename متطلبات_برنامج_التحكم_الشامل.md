# 🎯 متطلبات البرنامج الثاني للتحكم في نظام إدارة الصيدلية

## 📋 **نظرة عامة على النظام الحالي:**

بعد تحليل شامل لبرنامج إدارة الصيدلية، تم تحديد البنية الكاملة للنظام والمتطلبات اللازمة لبرنامج التحكم المركزي.

---

## 🏗️ **بنية النظام الحالي:**

### **🗄️ قواعد البيانات:**
- **UnifiedPharmacy** - قاعدة البيانات الرئيسية الموحدة
- **PharmacyNetworkOnline** - قاعدة بيانات الشبكة الأونلاين
- **PharmacyAdminSystem** - قاعدة بيانات إدارة النظام (موجودة جزئياً)

### **📊 الجداول الرئيسية في UnifiedPharmacy:**
1. **pharmacies** - بيانات الصيدليات (كود، اسم، مالك، ترخيص، عنوان، هاتف، إيميل، حالة النشاط)
2. **users** - المستخدمين (دور، اسم، تاريخ ميلاد، موبايل، إيميل، اسم مستخدم، كلمة مرور، معرف الصيدلية)
3. **medic** - الأدوية (معرف، اسم، رقم، تاريخ تصنيع، انتهاء، كمية، سعر، جرعات متعددة)
4. **sales** - المبيعات (معرف، اسم الدواء، كمية، سعر، تاريخ، مستخدم البيع)
5. **employee_sessions** - جلسات الموظفين (صيدلية، مستخدم، دخول، خروج)
6. **published_medicines** - الأدوية المنشورة للبيع بين الصيدليات
7. **purchase_requests** - طلبات الشراء بين الصيدليات
8. **pharmacy_messages** - الرسائل والمحادثات بين الصيدليات
9. **notifications** - الإشعارات للصيدليات
10. **print_settings** - إعدادات الطباعة لكل صيدلية

### **🖥️ الواجهات الرئيسية:**
1. **PharmacyCodeLoginForm** - تسجيل دخول بكود الصيدلية
2. **Administrator** - واجهة المدير (6 صفحات فرعية)
3. **Pharmacist** - واجهة الصيدلي (7 صفحات فرعية)
4. **PharmacyChatForm** - نظام المحادثات
5. **NotificationManager** - إدارة الإشعارات

---

## 🎯 **متطلبات البرنامج الثاني - نظام التحكم المركزي:**

### **1. 🔐 نظام المصادقة والأمان:**

#### **أ. تسجيل الدخول للمدير العام:**
- واجهة تسجيل دخول منفصلة للمديرين العامين
- مستويات صلاحيات متعددة (Super Admin, Admin, Moderator)
- نظام OTP للأمان الإضافي
- تسجيل جميع عمليات الدخول والخروج
- إدارة الجلسات مع انتهاء صلاحية تلقائي

#### **ب. إدارة المديرين:**
- إضافة/تعديل/حذف حسابات المديرين
- تحديد الصلاحيات لكل مدير
- تتبع نشاطات المديرين
- نظام الموافقات المتدرجة

### **2. 🏥 إدارة الصيدليات الشاملة:**

#### **أ. مراقبة الصيدليات:**
- عرض جميع الصيدليات المسجلة مع حالة النشاط
- إحصائيات مفصلة لكل صيدلية (عدد المستخدمين، الأدوية، المبيعات)
- خريطة جغرافية تفاعلية لمواقع الصيدليات
- مراقبة آخر نشاط لكل صيدلية (Online/Offline)
- تقييمات الصيدليات من المستخدمين

#### **ب. إدارة التسجيل والموافقات:**
- مراجعة طلبات تسجيل الصيدليات الجديدة
- التحقق من صحة بيانات الترخيص
- نظام موافقة متدرج للتسجيل
- إرسال إشعارات الموافقة/الرفض
- إدارة تجديد التراخيص

#### **ج. إدارة الاشتراكات:**
- أنواع اشتراكات متعددة (Basic, Premium, Enterprise)
- تتبع تواريخ انتهاء الاشتراكات
- إدارة الفواتير والمدفوعات
- تقارير الإيرادات الشهرية/السنوية
- نظام تنبيهات انتهاء الاشتراك

### **3. 👥 إدارة المستخدمين المركزية:**

#### **أ. مراقبة المستخدمين:**
- عرض جميع مستخدمي النظام عبر كل الصيدليات
- تصنيف المستخدمين حسب الدور (Administrator, Pharmacist, Employee)
- تتبع نشاطات المستخدمين وأوقات الدخول/الخروج
- إحصائيات استخدام النظام لكل مستخدم

#### **ب. إدارة الصلاحيات:**
- تعديل صلاحيات المستخدمين عن بُعد
- تعطيل/تفعيل حسابات المستخدمين
- إعادة تعيين كلمات المرور
- إدارة الأدوار والصلاحيات المخصصة

### **4. 💊 إدارة الأدوية والمخزون:**

#### **أ. مراقبة المخزون العام:**
- عرض جميع الأدوية عبر كل الصيدليات
- تتبع الأدوية منتهية الصلاحية
- تقارير نقص المخزون
- إحصائيات الأدوية الأكثر مبيعاً
- تحليل اتجاهات السوق

#### **ب. إدارة الأدوية المنشورة:**
- مراقبة الأدوية المنشورة للبيع بين الصيدليات
- إدارة طلبات الشراء والموافقة عليها
- تتبع المعاملات بين الصيدليات
- حل النزاعات التجارية

### **5. 📊 التقارير والإحصائيات:**

#### **أ. تقارير الأداء:**
- تقارير مبيعات شاملة لكل الصيدليات
- إحصائيات الاستخدام اليومية/الشهرية/السنوية
- تقارير الأرباح والخسائر
- تحليل أداء الصيدليات مقارنة ببعضها

#### **ب. تقارير النظام:**
- تقارير الأخطاء والمشاكل التقنية
- إحصائيات استخدام قاعدة البيانات
- تقارير الأمان والوصول غير المصرح
- تحليل أداء الخادم والشبكة

### **6. 💾 إدارة النسخ الاحتياطية:**

#### **أ. النسخ الاحتياطية التلقائية:**
- جدولة نسخ احتياطية يومية/أسبوعية/شهرية
- نسخ احتياطية لكل صيدلية منفصلة
- نسخ احتياطية للنظام الكامل
- تشفير النسخ الاحتياطية

#### **ب. الاستعادة والصيانة:**
- استعادة البيانات لصيدلية محددة
- استعادة النظام الكامل
- تنظيف قاعدة البيانات من البيانات القديمة
- ضغط وأرشفة البيانات القديمة

### **7. 🔧 إدارة النظام والصيانة:**

#### **أ. مراقبة الأداء:**
- مراقبة استخدام الخادم (CPU, Memory, Disk)
- مراقبة اتصالات قاعدة البيانات
- تتبع أوقات الاستجابة
- تنبيهات الأداء المنخفض

#### **ب. إدارة التحديثات:**
- نشر تحديثات النظام لكل الصيدليات
- إدارة إصدارات البرنامج
- تتبع حالة التحديثات لكل صيدلية
- إعادة تشغيل النظام عن بُعد

### **8. 📱 نظام الإشعارات والتنبيهات:**

#### **أ. إشعارات النظام:**
- تنبيهات انتهاء الاشتراكات
- إشعارات الأخطاء الحرجة
- تنبيهات الأمان والوصول المشبوه
- إشعارات النسخ الاحتياطية

#### **ب. التواصل مع الصيدليات:**
- إرسال رسائل جماعية للصيدليات
- إشعارات الصيانة المجدولة
- تنبيهات التحديثات المطلوبة
- نظام الدعم الفني المباشر

### **9. 🌐 إدارة الشبكة والاتصالات:**

#### **أ. مراقبة الاتصالات:**
- حالة اتصال كل صيدلية بالشبكة
- إحصائيات استخدام البيانات
- مراقبة جودة الاتصال
- تتبع انقطاعات الشبكة

#### **ب. إدارة الخوادم:**
- مراقبة خوادم قاعدة البيانات
- إدارة خوادم التطبيقات
- توزيع الأحمال
- إدارة النطاقات والشهادات

### **10. 🛡️ الأمان والحماية:**

#### **أ. مراقبة الأمان:**
- تتبع محاولات الدخول المشبوهة
- مراقبة الوصول غير المصرح
- تحليل أنماط الاستخدام غير الطبيعية
- نظام إنذار الأمان

#### **ب. إدارة الصلاحيات:**
- مراجعة صلاحيات المستخدمين دورياً
- إدارة كلمات المرور وسياسات الأمان
- تشفير البيانات الحساسة
- إدارة شهادات الأمان

---

## 🛠️ **المتطلبات التقنية للبرنامج الثاني:**

### **📋 تقنيات التطوير:**
- **اللغة:** C# .NET Framework 4.7.2+
- **قاعدة البيانات:** SQL Server 2019+
- **واجهة المستخدم:** WinForms مع مكتبات حديثة (Guna UI2)
- **التقارير:** Crystal Reports أو DevExpress
- **الخرائط:** Google Maps API أو Bing Maps

### **🗄️ قاعدة البيانات الجديدة:**
- **PharmacyAdminSystem** - قاعدة بيانات منفصلة للإدارة
- جداول إضافية للمديرين، الصلاحيات، السجلات، التقارير
- ربط مع قاعدة البيانات الرئيسية UnifiedPharmacy للقراءة

### **🔗 الاتصالات:**
- API للتواصل مع النظام الرئيسي
- خدمات ويب للتحديثات المباشرة
- نظام إشعارات فوري (SignalR)
- اتصالات آمنة مشفرة

### **📊 واجهات المستخدم المطلوبة:**
1. **لوحة التحكم الرئيسية** - نظرة عامة وإحصائيات
2. **إدارة الصيدليات** - قائمة، تفاصيل، موافقات
3. **إدارة المستخدمين** - مراقبة، صلاحيات، نشاطات
4. **إدارة الاشتراكات** - أنواع، فواتير، تجديدات
5. **التقارير والإحصائيات** - تقارير مفصلة وتحليلات
6. **النسخ الاحتياطية** - جدولة، تنفيذ، استعادة
7. **مراقبة النظام** - أداء، أخطاء، تنبيهات
8. **الإعدادات العامة** - تكوين النظام والأمان

---

## 🎯 **الأهداف الرئيسية للبرنامج الثاني:**

1. **المراقبة الشاملة** - رؤية كاملة لجميع الصيدليات والمستخدمين
2. **الإدارة المركزية** - تحكم كامل في النظام من مكان واحد
3. **الأمان المتقدم** - حماية شاملة للبيانات والنظام
4. **التقارير الذكية** - تحليلات متقدمة لاتخاذ القرارات
5. **الصيانة التلقائية** - نظام صيانة ذاتي للحفاظ على الأداء
6. **التوسع المستقبلي** - قابلية إضافة ميزات جديدة بسهولة

هذا النص يوفر خارطة طريق شاملة لتطوير برنامج التحكم المركزي الذي سيكمل منظومة إدارة الصيدليات.

---

## 📁 **هيكل الملفات المقترح للبرنامج الثاني:**

### **🏗️ الملفات الأساسية:**
```
PharmacyAdminSystem/
├── Program.cs                          # نقطة الدخول الرئيسية
├── App.config                          # إعدادات التطبيق
├── AdminLoginForm.cs/.Designer.cs      # واجهة تسجيل دخول المدير العام
├── MainAdminDashboard.cs/.Designer.cs  # لوحة التحكم الرئيسية
└── Resources/                          # الموارد والصور
```

### **🗄️ إدارة قاعدة البيانات:**
```
Database/
├── AdminDatabaseManager.cs             # مدير قاعدة البيانات الرئيسي
├── PharmacyDataAccess.cs               # الوصول لبيانات الصيدليات
├── UserDataAccess.cs                   # الوصول لبيانات المستخدمين
├── ReportDataAccess.cs                 # الوصول لبيانات التقارير
└── BackupManager.cs                    # إدارة النسخ الاحتياطية
```

### **🖥️ واجهات المستخدم:**
```
Forms/
├── PharmacyManagement/
│   ├── PharmacyListForm.cs             # قائمة الصيدليات
│   ├── PharmacyDetailsForm.cs          # تفاصيل الصيدلية
│   ├── PharmacyApprovalForm.cs         # موافقة التسجيل
│   └── PharmacyMapForm.cs              # خريطة الصيدليات
├── UserManagement/
│   ├── UserListForm.cs                 # قائمة المستخدمين
│   ├── UserPermissionsForm.cs          # إدارة الصلاحيات
│   └── UserActivityForm.cs             # نشاطات المستخدمين
├── Reports/
│   ├── SalesReportForm.cs              # تقارير المبيعات
│   ├── SystemReportForm.cs             # تقارير النظام
│   └── CustomReportForm.cs             # تقارير مخصصة
├── Subscriptions/
│   ├── SubscriptionListForm.cs         # قائمة الاشتراكات
│   ├── BillingForm.cs                  # الفواتير
│   └── PaymentForm.cs                  # المدفوعات
└── System/
    ├── BackupForm.cs                   # النسخ الاحتياطية
    ├── SystemMonitorForm.cs            # مراقبة النظام
    └── SettingsForm.cs                 # الإعدادات
```

### **⚙️ الخدمات والمدراء:**
```
Services/
├── PharmacyService.cs                  # خدمات الصيدليات
├── UserService.cs                      # خدمات المستخدمين
├── NotificationService.cs              # خدمة الإشعارات
├── ReportService.cs                    # خدمة التقارير
├── BackupService.cs                    # خدمة النسخ الاحتياطية
├── SecurityService.cs                  # خدمة الأمان
└── SystemMonitorService.cs             # خدمة مراقبة النظام
```

### **📊 التقارير والتحليلات:**
```
Reports/
├── ReportGenerator.cs                  # مولد التقارير
├── ChartManager.cs                     # إدارة الرسوم البيانية
├── ExportManager.cs                    # تصدير التقارير
└── Templates/                          # قوالب التقارير
    ├── SalesTemplate.rpt
    ├── UserActivityTemplate.rpt
    └── SystemPerformanceTemplate.rpt
```

### **🔧 المساعدات والأدوات:**
```
Utilities/
├── Logger.cs                           # نظام السجلات
├── Encryption.cs                       # التشفير
├── EmailService.cs                     # خدمة البريد الإلكتروني
├── SMSService.cs                       # خدمة الرسائل النصية
├── FileManager.cs                      # إدارة الملفات
└── ConfigManager.cs                    # إدارة الإعدادات
```

---

## 🗄️ **قاعدة البيانات المطلوبة - PharmacyAdminSystem:**

### **جداول المديرين والصلاحيات:**
```sql
-- جدول المديرين العامين
admin_users (id, username, password, fullName, email, phone, role, isActive, lastLogin, createdDate)

-- جدول الصلاحيات
admin_permissions (id, permissionName, description, category)

-- جدول ربط المديرين بالصلاحيات
admin_user_permissions (adminId, permissionId, grantedDate, grantedBy)

-- جدول أدوار المديرين
admin_roles (id, roleName, description, permissions)
```

### **جداول التتبع والسجلات:**
```sql
-- سجل نشاطات المديرين
admin_activity_log (id, adminId, action, targetType, targetId, details, timestamp, ipAddress)

-- سجل تسجيل الدخول
admin_login_log (id, adminId, loginTime, logoutTime, ipAddress, userAgent, status)

-- سجل تغييرات النظام
system_changes_log (id, adminId, changeType, tableName, recordId, oldValues, newValues, timestamp)
```

### **جداول الاشتراكات والفواتير:**
```sql
-- خطط الاشتراك
subscription_plans (id, planName, planNameAr, monthlyPrice, yearlyPrice, features, maxUsers, maxMedicines)

-- اشتراكات الصيدليات
pharmacy_subscriptions (id, pharmacyId, planId, startDate, endDate, status, paymentStatus)

-- الفواتير
invoices (id, pharmacyId, subscriptionId, amount, issueDate, dueDate, paidDate, status)

-- المدفوعات
payments (id, invoiceId, amount, paymentDate, paymentMethod, transactionId, status)
```

### **جداول النسخ الاحتياطية:**
```sql
-- سجل النسخ الاحتياطية
backup_history (id, backupType, pharmacyId, fileName, filePath, fileSize, createdDate, createdBy, status)

-- جدولة النسخ الاحتياطية
backup_schedule (id, pharmacyId, backupType, frequency, nextRunDate, isActive, createdBy)
```

### **جداول مراقبة النظام:**
```sql
-- مراقبة أداء النظام
system_performance (id, timestamp, cpuUsage, memoryUsage, diskUsage, activeConnections, responseTime)

-- سجل الأخطاء
error_log (id, timestamp, errorLevel, errorMessage, stackTrace, userId, pharmacyId, resolved)

-- إحصائيات الاستخدام
usage_statistics (id, date, totalLogins, totalSales, totalMedicines, activePharmacies, systemUptime)
```

---

## 🔗 **التكامل مع النظام الحالي:**

### **قراءة البيانات من UnifiedPharmacy:**
- اتصال للقراءة فقط مع قاعدة البيانات الرئيسية
- استعلامات محسنة للحصول على البيانات المطلوبة
- تحديث دوري للإحصائيات والتقارير

### **التحكم في النظام الرئيسي:**
- API endpoints للتحكم في الصيدليات عن بُعد
- إرسال الإشعارات والتنبيهات
- تحديث الإعدادات والصلاحيات

### **مزامنة البيانات:**
- مزامنة دورية لبيانات الصيدليات والمستخدمين
- تحديث فوري للتغييرات الحرجة
- نظام queue للعمليات المؤجلة

---

## 🎨 **التصميم والواجهات:**

### **لوحة التحكم الرئيسية:**
- إحصائيات سريعة (عدد الصيدليات، المستخدمين، المبيعات)
- رسوم بيانية للأداء والاتجاهات
- تنبيهات وإشعارات مهمة
- خريطة تفاعلية للصيدليات

### **تصميم موحد:**
- استخدام نفس مكتبة Guna UI2 للتناسق
- ألوان وخطوط متسقة مع النظام الرئيسي
- دعم اللغتين العربية والإنجليزية
- تصميم responsive للشاشات المختلفة

---

## 🚀 **خطة التطوير المقترحة:**

### **المرحلة الأولى (4 أسابيع):**
- إعداد قاعدة البيانات الإدارية
- تطوير نظام تسجيل الدخول والمصادقة
- لوحة التحكم الأساسية
- إدارة الصيدليات الأساسية

### **المرحلة الثانية (4 أسابيع):**
- إدارة المستخدمين والصلاحيات
- نظام التقارير الأساسي
- إدارة الاشتراكات
- نظام النسخ الاحتياطية

### **المرحلة الثالثة (4 أسابيع):**
- مراقبة النظام والأداء
- التقارير المتقدمة والتحليلات
- نظام الإشعارات المتقدم
- الأمان والحماية المتقدمة

### **المرحلة الرابعة (2 أسابيع):**
- الاختبار الشامل
- التحسينات والتطوير
- التوثيق والتدريب
- النشر والتشغيل

---

## ✅ **الخلاصة:**

هذا النص يوفر دليلاً شاملاً لتطوير برنامج التحكم المركزي الذي سيكمل منظومة إدارة الصيدليات. البرنامج سيوفر تحكماً كاملاً في النظام مع مراقبة شاملة وإدارة متقدمة لجميع جوانب العمل.
