﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy_Management_System.AdministratorUC
{
    public partial class UC_AddUser : UserControl
    {
        UnifiedPharmacyFunction unifiedDb = new UnifiedPharmacyFunction();

        public UC_AddUser()
        {
            InitializeComponent();

            // الاشتراك في حدث تغيير اللغة
            LanguageManager.LanguageChanged += OnLanguageChanged;
        }

        private void btnSignUp_Click(object sender, EventArgs e)
        {
            String role = txtUserRole.Text.Trim();
            String name = txtName.Text.Trim();
            String dob = txtDob.Text.Trim();
            String mobileText = txtMobileNo.Text.Trim();
            String email = txtEmail.Text.Trim();
            String username = txtUsername.Text.Trim();
            String pass = txtPassword.Text.Trim();

            if (string.IsNullOrEmpty(role) || string.IsNullOrEmpty(name) || string.IsNullOrEmpty(dob) ||
                string.IsNullOrEmpty(mobileText) || string.IsNullOrEmpty(email) || string.IsNullOrEmpty(username) ||
                string.IsNullOrEmpty(pass))
            {
                MessageBox.Show(LanguageManager.GetText("All fields must be filled out"), LanguageManager.GetText("Validation Error"), MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!Int64.TryParse(mobileText, out Int64 mobile))
            {
                MessageBox.Show(LanguageManager.GetText("Invalid mobile number format"), LanguageManager.GetText("Validation Error"), MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                // التحقق من عدم وجود اسم المستخدم مسبقاً
                if (unifiedDb.IsUsernameExists(username))
                {
                    MessageBox.Show(LanguageManager.GetText("Username already exists"), LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // الحصول على معرف الصيدلية الحالية من الجلسة
                int currentPharmacyId = SessionManager.CurrentPharmacyId;

                // تحويل تاريخ الميلاد
                DateTime birthDate;
                if (!DateTime.TryParse(dob, out birthDate))
                {
                    MessageBox.Show(LanguageManager.GetText("Invalid date format"), LanguageManager.GetText("Validation Error"), MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // إنشاء المستخدم الجديد في قاعدة البيانات الموحدة
                bool success = unifiedDb.CreateUser(
                    currentPharmacyId,
                    role,
                    name,
                    birthDate,
                    mobile,
                    email,
                    username,
                    pass
                );

                if (success)
                {
                    MessageBox.Show(LanguageManager.GetText("Sign Up Successful"), LanguageManager.GetText("Success"), MessageBoxButtons.OK, MessageBoxIcon.Information);
                    clearAll();
                }
                else
                {
                    MessageBox.Show(LanguageManager.GetText("Failed to create user"), LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في إنشاء المستخدم: " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void binReset_Click(object sender, EventArgs e)
        {
            clearAll();
        }

        public void clearAll()
        {
            txtName.Clear();
            txtDob.ResetText();
            txtMobileNo.Clear();
            txtEmail.Clear();
            txtUsername.Clear();
            txtPassword.Clear();
            txtUserRole.SelectedIndex = -1;
        }

        private void txtUsername_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtUsername.Text))
                {
                    pictureBox1.BackColor = Color.Transparent;
                    return;
                }

                // التحقق من توفر اسم المستخدم في قاعدة البيانات الموحدة
                bool exists = unifiedDb.IsUsernameExists(txtUsername.Text.Trim());

                if (!exists)
                {
                    // اسم المستخدم متاح - لون أخضر
                    pictureBox1.BackColor = Color.Green;
                }
                else
                {
                    // اسم المستخدم غير متاح - لون أحمر
                    pictureBox1.BackColor = Color.Red;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في التحقق من اسم المستخدم: " + ex.Message);
                pictureBox1.BackColor = Color.Transparent;
            }
        }

        private void pictureBox1_Click(object sender, EventArgs e)
        {

        }

        private void OnLanguageChanged(object sender, EventArgs e)
        {
            ApplyLanguage();
        }

        public void ApplyLanguage()
        {
            // تحديث النصوص حسب اللغة المختارة
            if (label1 != null) label1.Text = LanguageManager.GetText("Add User");
            if (label2 != null) label2.Text = LanguageManager.GetText("User Role");
            if (label3 != null) label3.Text = LanguageManager.GetText("Name");
            if (label4 != null) label4.Text = LanguageManager.GetText("Date of Birth");
            if (label5 != null) label5.Text = LanguageManager.GetText("Mobile Number");
            if (label6 != null) label6.Text = LanguageManager.GetText("Email");
            if (label7 != null) label7.Text = LanguageManager.GetText("Username");
            if (label8 != null) label8.Text = LanguageManager.GetText("Password");
            if (btnSignUp != null) btnSignUp.Text = LanguageManager.GetText("Sign Up");
            if (binReset != null) binReset.Text = LanguageManager.GetText("Reset");

            // الحفاظ على التصميم من اليسار لليمين في جميع اللغات
            this.RightToLeft = RightToLeft.No;
        }

        protected override void OnHandleDestroyed(EventArgs e)
        {
            // إلغاء الاشتراك في أحداث التغيير
            LanguageManager.LanguageChanged -= OnLanguageChanged;
            base.OnHandleDestroyed(e);
        }
    }
}