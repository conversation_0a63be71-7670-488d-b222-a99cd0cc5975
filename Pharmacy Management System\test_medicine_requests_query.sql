-- اختبار استعلام طلبات الأدوية
USE UnifiedPharmacy;

PRINT '=== اختبار استعلام طلبات الأدوية ===';

-- المتغيرات
DECLARE @pharmacyId INT = 1;

PRINT '1. فحص البيانات الأساسية:';

-- فحص الصيدليات
SELECT 'الصيدليات المتوفرة:' as Info;
SELECT id, pharmacyName, pharmacyCode FROM pharmacies;

-- فحص الأدوية للصيدلية الأولى
SELECT 'أدوية الصيدلية الأولى:' as Info;
SELECT id, mname, mnumber, quantity, pharmacyId FROM medic WHERE pharmacyId = 1;

-- فحص طلبات الشراء
SELECT 'جميع طلبات الشراء:' as Info;
SELECT 
    id, 
    medicine_id, 
    buyer_pharmacy_id, 
    seller_pharmacy_id, 
    requested_quantity, 
    status,
    request_date
FROM purchase_requests;

PRINT '';
PRINT '2. اختبار الاستعلام الأصلي (بدون تعديل):';

SELECT
    pr.id,
    pr.requested_quantity as requestedQuantity,
    pr.offered_price as offeredPrice,
    pr.request_date as requestDate,
    pr.status,
    ISNULL(pr.response_message, '') as responseMessage,
    ISNULL(pr.request_message, '') as requestMessage,
    m.mname as medicineName,
    ISNULL(m.mnumber, '') as medicineNumber,
    m.perUnit as originalPrice,
    p_buyer.pharmacyName as buyerPharmacyName,
    ISNULL(p_buyer.phone, '') as buyerPhone,
    ISNULL(p_buyer.city, '') as buyerCity
FROM purchase_requests pr
INNER JOIN medic m ON pr.medicine_id = m.id
INNER JOIN pharmacies p_buyer ON pr.buyer_pharmacy_id = p_buyer.id
WHERE pr.seller_pharmacy_id = @pharmacyId
ORDER BY pr.request_date DESC;

PRINT '';
PRINT '3. اختبار الاستعلام المحدث (مع فلترة pharmacyId):';

SELECT
    pr.id,
    pr.requested_quantity as requestedQuantity,
    pr.offered_price as offeredPrice,
    pr.request_date as requestDate,
    pr.status,
    ISNULL(pr.response_message, '') as responseMessage,
    ISNULL(pr.request_message, '') as requestMessage,
    m.mname as medicineName,
    ISNULL(m.mnumber, '') as medicineNumber,
    m.perUnit as originalPrice,
    p_buyer.pharmacyName as buyerPharmacyName,
    ISNULL(p_buyer.phone, '') as buyerPhone,
    ISNULL(p_buyer.city, '') as buyerCity
FROM purchase_requests pr
INNER JOIN medic m ON pr.medicine_id = m.id AND m.pharmacyId = pr.seller_pharmacy_id
INNER JOIN pharmacies p_buyer ON pr.buyer_pharmacy_id = p_buyer.id
WHERE pr.seller_pharmacy_id = @pharmacyId
ORDER BY pr.request_date DESC;

PRINT '';
PRINT '4. فحص العلاقات:';

-- فحص إذا كانت medicine_id في purchase_requests تطابق id في medic
SELECT 'فحص العلاقة بين purchase_requests و medic:' as Info;
SELECT 
    pr.id as request_id,
    pr.medicine_id,
    pr.seller_pharmacy_id,
    m.id as medic_id,
    m.pharmacyId as medic_pharmacy_id,
    CASE 
        WHEN m.id IS NULL THEN 'لا يوجد دواء مطابق'
        WHEN m.pharmacyId != pr.seller_pharmacy_id THEN 'الدواء لا ينتمي للصيدلية البائعة'
        ELSE 'صحيح'
    END as status
FROM purchase_requests pr
LEFT JOIN medic m ON pr.medicine_id = m.id
WHERE pr.seller_pharmacy_id = @pharmacyId;

PRINT '';
PRINT '5. إحصائيات:';
SELECT 
    'إجمالي طلبات الشراء' as Type,
    COUNT(*) as Count
FROM purchase_requests
UNION ALL
SELECT 
    'طلبات للصيدلية الأولى' as Type,
    COUNT(*) as Count
FROM purchase_requests 
WHERE seller_pharmacy_id = 1
UNION ALL
SELECT 
    'أدوية الصيدلية الأولى' as Type,
    COUNT(*) as Count
FROM medic 
WHERE pharmacyId = 1;
