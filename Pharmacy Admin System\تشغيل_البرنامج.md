# 🚀 كيفية تشغيل نظام إدارة الصيدليات المركزي

## 📍 الموقع الحالي للمشروع:
```
C:\Users\<USER>\source\repos\Pharmacy Admin System\
```

## 🔧 خطوات التشغيل:

### الخطوة 1: إعداد قاعدة البيانات
1. **افتح SQL Server Management Studio**
2. **اتصل بالخادم NARUTO**
3. **افتح ملف:** `quick_setup.sql`
4. **اضغط F5** لتنفيذ السكريپت

**أو شغل هذا الأمر في Command Prompt:**
```cmd
cd "C:\Users\<USER>\source\repos\Pharmacy Admin System"
sqlcmd -S NARUTO -E -i quick_setup.sql
```

### الخطوة 2: فتح المشروع في Visual Studio
1. **افتح Visual Studio**
2. **اختر "Open a project or solution"**
3. **انتقل إلى:** `C:\Users\<USER>\source\repos\Pharmacy Admin System\`
4. **افتح ملف:** `Pharmacy Admin System.sln`

### الخطوة 3: تشغيل البرنامج
1. **في Visual Studio اضغط F5** أو **Ctrl+F5**
2. **أو اضغط على زر "Start"** الأخضر

### الخطوة 4: تسجيل الدخول
```
اسم المستخدم: superadmin
كلمة المرور: admin2025
```

---

## 🎯 ما ستراه عند التشغيل:

### 1. واجهة تسجيل الدخول
- تصميم حديث باللون الأزرق والرمادي
- حقول اسم المستخدم وكلمة المرور
- زر تسجيل الدخول وزر الإغلاق

### 2. الواجهة الرئيسية
- **لوحة التحكم** مع إحصائيات سريعة
- **قائمة جانبية** بجميع الوظائف:
  - لوحة التحكم
  - الصيدليات المسجلة
  - طلبات التسجيل
  - إدارة الاشتراكات
  - النسخ الاحتياطية
  - إدارة المستخدمين
  - التقارير
  - إعدادات النظام

### 3. البيانات التجريبية
- **3 صيدليات تجريبية** جاهزة للاختبار
- **4 خطط اشتراك** (أساسي، قياسي، مميز، مؤسسي)
- **إعدادات النظام** الافتراضية

---

## 🔍 استكشاف الأخطاء:

### ❌ خطأ في قاعدة البيانات:
```
الحل: تأكد من تشغيل SQL Server
تحقق من اسم الخادم NARUTO في App.config
```

### ❌ خطأ في البناء:
```
الحل: تأكد من تثبيت .NET Framework 4.7.2
أعد بناء المشروع (Build > Rebuild Solution)
```

### ❌ خطأ في تسجيل الدخول:
```
الحل: تأكد من تنفيذ quick_setup.sql بنجاح
استخدم البيانات الافتراضية المذكورة أعلاه
```

---

## 🎊 الميزات المتاحة للاختبار:

### ✅ لوحة التحكم
- عرض إحصائيات الصيدليات
- عرض الإيرادات
- عرض النشاطات الأخيرة

### ✅ إدارة الصيدليات
- عرض قائمة الصيدليات المسجلة
- تفاصيل كل صيدلية
- البحث والفلترة

### ✅ طلبات التسجيل
- عرض الطلبات المعلقة
- الموافقة أو الرفض
- إضافة ملاحظات

### ✅ إدارة الاشتراكات
- عرض جميع الاشتراكات
- تتبع تواريخ الانتهاء
- تجديد الاشتراكات

### ✅ النسخ الاحتياطية
- إنشاء نسخ احتياطية
- عرض تاريخ النسخ
- استعادة البيانات

### ✅ التقارير
- تقارير الإيرادات
- إحصائيات الصيدليات
- تقارير النشاطات

---

## 📞 في حالة المشاكل:

### 🆘 مشكلة في الإعداد؟
1. تأكد من تشغيل SQL Server
2. تحقق من صلاحيات قاعدة البيانات
3. أعد تشغيل quick_setup.sql

### 🆘 مشكلة في التشغيل؟
1. أعد بناء المشروع في Visual Studio
2. تحقق من ملف App.config
3. تأكد من تثبيت .NET Framework

### 🆘 مشكلة في الواجهة؟
1. تحقق من دقة الشاشة
2. جرب تشغيل البرنامج كمدير
3. أعد تشغيل Visual Studio

---

## 🎯 نصائح للاختبار:

### 🔄 اختبر الوظائف الأساسية:
1. تسجيل الدخول والخروج
2. عرض الصيدليات والبحث
3. الموافقة على طلب تسجيل
4. إنشاء نسخة احتياطية
5. عرض التقارير

### 📊 اختبر البيانات:
1. أضف صيدلية جديدة
2. عدل اشتراك صيدلية
3. سجل دفعة جديدة
4. أنشئ تقرير مخصص

### 🔐 اختبر الأمان:
1. جرب تسجيل دخول خاطئ
2. تحقق من سجل النشاطات
3. اختبر الصلاحيات

---

**🚀 البرنامج جاهز للتشغيل والاختبار!**

**📧 في حالة الحاجة لمساعدة إضافية، أخبرني بالخطأ المحدد الذي تواجهه.**
