#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import messagebox, ttk
import threading
import time
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import random
from mt5_real_crypto_system import MT5RealCryptoSystem
from advanced_learning_system import AdvancedLearningSystem
from demo_trading_system import DemoTradingSystem

class AdvancedTradingGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 Advanced Trading System with Backtesting")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2b2b2b')
        
        # System
        self.trading_system = None
        self.learning_system = AdvancedLearningSystem()
        self.is_connected = False
        self.is_trading = False
        self.is_backtesting = False
        self.current_session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Control variables
        self.symbol_var = tk.StringVar(value='BTCUSD')
        self.confidence_var = tk.DoubleVar(value=70.0)
        self.demo_var = tk.BooleanVar(value=True)
        self.backtest_var = tk.BooleanVar(value=False)
        self.backtest_period_var = tk.StringVar(value='3_months')
        
        # Backtesting results
        self.backtest_results = None
        self.live_backtest_stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_profit': 0.0,
            'current_balance': 10000.0,
            'max_balance': 10000.0,
            'min_balance': 10000.0,
            'current_drawdown': 0.0,
            'max_drawdown': 0.0
        }
        
        # Create interface
        self.create_widgets()
        
        # Start data updates
        self.update_data()
        
    def create_widgets(self):
        """Create interface elements"""
        # Title
        title_label = tk.Label(self.root, text="🚀 Advanced Trading System with Historical Backtesting", 
                              font=('Arial', 16, 'bold'), fg='#00ff88', bg='#2b2b2b')
        title_label.pack(pady=15)
        
        # Main container
        main_container = tk.Frame(self.root, bg='#2b2b2b')
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Left panel (Controls)
        left_panel = tk.Frame(main_container, bg='#343a40', relief=tk.RAISED, bd=2, width=400)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        left_panel.pack_propagate(False)
        
        # Right panel (Results)
        right_panel = tk.Frame(main_container, bg='#495057', relief=tk.RAISED, bd=2)
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        self.create_control_panel(left_panel)
        self.create_results_panel(right_panel)
        
    def create_control_panel(self, parent):
        """Create control panel"""
        # Control Panel Title
        tk.Label(parent, text="🎛️ Control Panel", 
                font=('Arial', 14, 'bold'), fg='white', bg='#343a40').pack(pady=10)
        
        # Connection section
        conn_frame = tk.Frame(parent, bg='#343a40')
        conn_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(conn_frame, text="🔌 Connection", 
                font=('Arial', 12, 'bold'), fg='white', bg='#343a40').pack()
        
        conn_buttons = tk.Frame(conn_frame, bg='#343a40')
        conn_buttons.pack(pady=5)
        
        self.connect_btn = tk.Button(conn_buttons, text="Connect to MT5", 
                                   command=self.connect_mt5, bg='#007bff', fg='white', 
                                   font=('Arial', 9, 'bold'), width=12)
        self.connect_btn.pack(side=tk.LEFT, padx=2)
        
        self.disconnect_btn = tk.Button(conn_buttons, text="Disconnect", 
                                      command=self.disconnect_system, bg='#6c757d', fg='white', 
                                      font=('Arial', 9, 'bold'), width=12, state='disabled')
        self.disconnect_btn.pack(side=tk.LEFT, padx=2)
        
        self.status_label = tk.Label(conn_frame, text="❌ Not Connected", 
                                    fg='#dc3545', bg='#343a40', font=('Arial', 10, 'bold'))
        self.status_label.pack(pady=5)
        
        # Symbol selection
        symbol_frame = tk.Frame(parent, bg='#343a40')
        symbol_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Label(symbol_frame, text="💰 Symbol Selection", 
                font=('Arial', 12, 'bold'), fg='white', bg='#343a40').pack()
        
        # Symbol search
        search_frame = tk.Frame(symbol_frame, bg='#343a40')
        search_frame.pack(pady=5)
        
        tk.Label(search_frame, text="Symbol:", fg='white', bg='#343a40', 
                font=('Arial', 10)).pack()
        
        self.symbol_entry = tk.Entry(search_frame, textvariable=self.symbol_var, 
                                    width=15, font=('Arial', 10))
        self.symbol_entry.pack(pady=2)
        self.symbol_entry.bind('<KeyRelease>', self.on_symbol_search)
        
        # Quick symbol buttons
        quick_frame = tk.Frame(search_frame, bg='#343a40')
        quick_frame.pack(pady=5)
        
        quick_symbols = ['BTCUSD', 'ETHUSD', 'EURUSD', 'GBPUSD', 'AUDUSD', 'XAUUSD']
        for i, symbol in enumerate(quick_symbols):
            if i % 3 == 0:
                row_frame = tk.Frame(quick_frame, bg='#343a40')
                row_frame.pack()
            btn = tk.Button(row_frame, text=symbol, 
                           command=lambda s=symbol: self.set_symbol(s),
                           bg='#17a2b8', fg='white', font=('Arial', 8), width=8)
            btn.pack(side=tk.LEFT, padx=1, pady=1)
        
        # Search MT5 button
        search_btn = tk.Button(search_frame, text="🔍 Search MT5 Symbols", 
                              command=self.search_mt5_symbols, bg='#28a745', fg='white', 
                              font=('Arial', 9), width=18)
        search_btn.pack(pady=5)
        
        # Trading mode selection
        mode_frame = tk.Frame(parent, bg='#343a40')
        mode_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Label(mode_frame, text="🎯 Trading Mode", 
                font=('Arial', 12, 'bold'), fg='white', bg='#343a40').pack()
        
        # Live trading option
        live_frame = tk.Frame(mode_frame, bg='#343a40')
        live_frame.pack(pady=2)
        
        live_radio = tk.Radiobutton(live_frame, text="📈 Live Trading", 
                                   variable=self.backtest_var, value=False,
                                   fg='white', bg='#343a40', selectcolor='#343a40',
                                   font=('Arial', 10), command=self.on_mode_change)
        live_radio.pack(anchor='w')
        
        # Backtesting option
        backtest_frame = tk.Frame(mode_frame, bg='#343a40')
        backtest_frame.pack(pady=2)
        
        backtest_radio = tk.Radiobutton(backtest_frame, text="🔄 Historical Backtesting", 
                                       variable=self.backtest_var, value=True,
                                       fg='white', bg='#343a40', selectcolor='#343a40',
                                       font=('Arial', 10), command=self.on_mode_change)
        backtest_radio.pack(anchor='w')
        
        # Backtesting period selection
        self.period_frame = tk.Frame(mode_frame, bg='#343a40')
        
        tk.Label(self.period_frame, text="📅 Backtest Period:", 
                fg='white', bg='#343a40', font=('Arial', 9)).pack()
        
        period_combo = ttk.Combobox(self.period_frame, textvariable=self.backtest_period_var,
                                   values=['1_month', '3_months', '6_months', '1_year', '2_years'],
                                   width=12, state='readonly')
        period_combo.pack(pady=2)
        
        # Confidence settings
        conf_frame = tk.Frame(parent, bg='#343a40')
        conf_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Label(conf_frame, text="🎯 Confidence Settings", 
                font=('Arial', 12, 'bold'), fg='white', bg='#343a40').pack()
        
        # Confidence slider
        slider_frame = tk.Frame(conf_frame, bg='#343a40')
        slider_frame.pack(pady=5)
        
        tk.Label(slider_frame, text="Confidence Threshold:", 
                fg='white', bg='#343a40', font=('Arial', 10)).pack()
        
        self.confidence_scale = tk.Scale(slider_frame, from_=30, to=100, orient=tk.HORIZONTAL,
                                        variable=self.confidence_var, bg='#343a40', fg='white',
                                        highlightbackground='#343a40', length=200,
                                        command=self.update_confidence_label)
        self.confidence_scale.pack()
        
        self.confidence_label = tk.Label(slider_frame, text="70%", 
                                        fg='#ffc107', bg='#343a40', font=('Arial', 11, 'bold'))
        self.confidence_label.pack()
        
        # Quick confidence buttons
        quick_conf_frame = tk.Frame(conf_frame, bg='#343a40')
        quick_conf_frame.pack(pady=5)
        
        for conf in [30, 50, 70, 90]:
            btn = tk.Button(quick_conf_frame, text=f"{conf}%", 
                           command=lambda c=conf: self.set_confidence(c),
                           bg='#17a2b8', fg='white', font=('Arial', 8), width=6)
            btn.pack(side=tk.LEFT, padx=2)
        
        # Demo mode
        demo_frame = tk.Frame(parent, bg='#343a40')
        demo_frame.pack(fill=tk.X, padx=10, pady=5)
        
        demo_check = tk.Checkbutton(demo_frame, text="🛡️ Demo Mode (Safe)", 
                                   variable=self.demo_var, fg='white', bg='#343a40',
                                   selectcolor='#343a40', font=('Arial', 10))
        demo_check.pack()
        
        # Control buttons
        control_frame = tk.Frame(parent, bg='#343a40')
        control_frame.pack(fill=tk.X, padx=10, pady=15)
        
        self.start_btn = tk.Button(control_frame, text="🚀 Start Trading", 
                                 command=self.start_trading, bg='#28a745', fg='white', 
                                 font=('Arial', 11, 'bold'), width=18)
        self.start_btn.pack(pady=2)
        
        self.backtest_btn = tk.Button(control_frame, text="🔄 Run Backtest", 
                                    command=self.run_backtest, bg='#fd7e14', fg='white', 
                                    font=('Arial', 11, 'bold'), width=18)
        self.backtest_btn.pack(pady=2)
        
        self.stop_btn = tk.Button(control_frame, text="⏹️ Stop",
                                command=self.stop_trading, bg='#dc3545', fg='white',
                                font=('Arial', 11, 'bold'), width=18, state='disabled')
        self.stop_btn.pack(pady=2)

        # Learning system button
        self.learning_btn = tk.Button(control_frame, text="🧠 Learning Stats",
                                    command=self.show_learning_stats, bg='#6f42c1', fg='white',
                                    font=('Arial', 11, 'bold'), width=18)
        self.learning_btn.pack(pady=2)
        
    def create_results_panel(self, parent):
        """Create results panel"""
        # Results Panel Title
        tk.Label(parent, text="📊 Results & Analysis", 
                font=('Arial', 14, 'bold'), fg='white', bg='#495057').pack(pady=10)
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Live Trading Tab
        self.live_frame = tk.Frame(self.notebook, bg='#495057')
        self.notebook.add(self.live_frame, text="📈 Live Trading")
        
        # Backtesting Tab
        self.backtest_frame = tk.Frame(self.notebook, bg='#495057')
        self.notebook.add(self.backtest_frame, text="🔄 Backtesting")
        
        # Account Info Tab
        self.account_frame = tk.Frame(self.notebook, bg='#495057')
        self.notebook.add(self.account_frame, text="💰 Account Info")
        
        self.create_live_tab()
        self.create_backtest_tab()
        self.create_account_tab()
        
    def create_live_tab(self):
        """Create live trading tab"""
        # Current analysis
        analysis_frame = tk.Frame(self.live_frame, bg='#6c757d', relief=tk.RAISED, bd=2)
        analysis_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(analysis_frame, text="🔍 Current Analysis", 
                font=('Arial', 12, 'bold'), fg='white', bg='#6c757d').pack(pady=5)
        
        # Confidence display
        conf_display_frame = tk.Frame(analysis_frame, bg='#6c757d')
        conf_display_frame.pack(pady=5)
        
        tk.Label(conf_display_frame, text="Current Confidence:", 
                fg='white', bg='#6c757d', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.current_confidence_label = tk.Label(conf_display_frame, text="0%", 
                                                fg='#ffc107', bg='#6c757d', font=('Arial', 12, 'bold'))
        self.current_confidence_label.pack(side=tk.LEFT, padx=(10, 30))
        
        tk.Label(conf_display_frame, text="Required:", 
                fg='white', bg='#6c757d', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.required_confidence_label = tk.Label(conf_display_frame, text="70%", 
                                                 fg='#17a2b8', bg='#6c757d', font=('Arial', 12, 'bold'))
        self.required_confidence_label.pack(side=tk.LEFT, padx=(10, 30))
        
        tk.Label(conf_display_frame, text="Status:", 
                fg='white', bg='#6c757d', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.confidence_status_label = tk.Label(conf_display_frame, text="Waiting", 
                                               fg='#6c757d', bg='#6c757d', font=('Arial', 11, 'bold'))
        self.confidence_status_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # Live log
        log_frame = tk.Frame(self.live_frame, bg='#1e1e1e', relief=tk.RAISED, bd=2)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        tk.Label(log_frame, text="📝 Live Trading Log", 
                font=('Arial', 12, 'bold'), fg='white', bg='#1e1e1e').pack(pady=5)
        
        # Text area with scrollbar
        text_frame = tk.Frame(log_frame, bg='#1e1e1e')
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        self.live_log_text = tk.Text(text_frame, bg='#2d2d2d', fg='#ffffff', 
                                    font=('Consolas', 9), wrap=tk.WORD, 
                                    insertbackground='white')
        
        live_scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.live_log_text.yview)
        self.live_log_text.configure(yscrollcommand=live_scrollbar.set)
        
        self.live_log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        live_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Welcome message
        welcome_msg = """🚀 Welcome to Advanced Trading System

✨ Features:
• Live trading with MT5 integration
• Historical backtesting simulation
• Advanced AI analysis
• Real-time confidence display
• Comprehensive symbol search
• Multi-timeframe analysis

📋 Getting Started:
1. Connect to MetaTrader 5
2. Choose your symbol
3. Select trading mode (Live/Backtest)
4. Set confidence threshold
5. Start trading or run backtest

⚠️ Always test with Demo Mode first!

"""
        self.live_log_text.insert(tk.END, welcome_msg)

    def create_backtest_tab(self):
        """Create backtesting tab"""
        # Backtest controls
        control_frame = tk.Frame(self.backtest_frame, bg='#6c757d', relief=tk.RAISED, bd=2)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(control_frame, text="🔄 Backtesting Controls",
                font=('Arial', 12, 'bold'), fg='white', bg='#6c757d').pack(pady=5)

        # Progress bar
        self.progress_frame = tk.Frame(control_frame, bg='#6c757d')
        self.progress_frame.pack(pady=5)

        tk.Label(self.progress_frame, text="Progress:",
                fg='white', bg='#6c757d', font=('Arial', 10)).pack()

        self.progress_bar = ttk.Progressbar(self.progress_frame, length=300, mode='determinate')
        self.progress_bar.pack(pady=2)

        self.progress_label = tk.Label(self.progress_frame, text="Ready to start",
                                      fg='#ffc107', bg='#6c757d', font=('Arial', 10))
        self.progress_label.pack()

        # Live stats during backtesting
        live_stats_frame = tk.Frame(control_frame, bg='#6c757d')
        live_stats_frame.pack(pady=5)

        tk.Label(live_stats_frame, text="🔄 Live Backtest Stats:",
                fg='white', bg='#6c757d', font=('Arial', 10, 'bold')).pack()

        stats_grid = tk.Frame(live_stats_frame, bg='#6c757d')
        stats_grid.pack(pady=2)

        # Current balance and profit
        balance_row = tk.Frame(stats_grid, bg='#6c757d')
        balance_row.pack(fill=tk.X, pady=1)

        tk.Label(balance_row, text="Balance:", fg='white', bg='#6c757d',
                font=('Arial', 9, 'bold'), width=12).pack(side=tk.LEFT)
        self.live_balance_label = tk.Label(balance_row, text="$10,000.00", fg='#28a745', bg='#6c757d',
                                          font=('Arial', 9, 'bold'))
        self.live_balance_label.pack(side=tk.LEFT, padx=(5, 20))

        tk.Label(balance_row, text="Profit:", fg='white', bg='#6c757d',
                font=('Arial', 9, 'bold')).pack(side=tk.LEFT)
        self.live_profit_label = tk.Label(balance_row, text="$0.00", fg='#ffc107', bg='#6c757d',
                                         font=('Arial', 9, 'bold'))
        self.live_profit_label.pack(side=tk.LEFT, padx=(5, 0))

        # Trades and win rate
        trades_row = tk.Frame(stats_grid, bg='#6c757d')
        trades_row.pack(fill=tk.X, pady=1)

        tk.Label(trades_row, text="Trades:", fg='white', bg='#6c757d',
                font=('Arial', 9, 'bold'), width=12).pack(side=tk.LEFT)
        self.live_trades_label = tk.Label(trades_row, text="0", fg='#17a2b8', bg='#6c757d',
                                         font=('Arial', 9, 'bold'))
        self.live_trades_label.pack(side=tk.LEFT, padx=(5, 20))

        tk.Label(trades_row, text="Win Rate:", fg='white', bg='#6c757d',
                font=('Arial', 9, 'bold')).pack(side=tk.LEFT)
        self.live_winrate_label = tk.Label(trades_row, text="0%", fg='#28a745', bg='#6c757d',
                                          font=('Arial', 9, 'bold'))
        self.live_winrate_label.pack(side=tk.LEFT, padx=(5, 0))

        # Drawdown
        dd_row = tk.Frame(stats_grid, bg='#6c757d')
        dd_row.pack(fill=tk.X, pady=1)

        tk.Label(dd_row, text="Drawdown:", fg='white', bg='#6c757d',
                font=('Arial', 9, 'bold'), width=12).pack(side=tk.LEFT)
        self.live_drawdown_label = tk.Label(dd_row, text="0%", fg='#dc3545', bg='#6c757d',
                                           font=('Arial', 9, 'bold'))
        self.live_drawdown_label.pack(side=tk.LEFT, padx=(5, 0))

        # Results summary
        summary_frame = tk.Frame(self.backtest_frame, bg='#6c757d', relief=tk.RAISED, bd=2)
        summary_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(summary_frame, text="📊 Backtest Results Summary",
                font=('Arial', 12, 'bold'), fg='white', bg='#6c757d').pack(pady=5)

        # Results grid
        results_grid = tk.Frame(summary_frame, bg='#6c757d')
        results_grid.pack(pady=5)

        # Row 1
        row1 = tk.Frame(results_grid, bg='#6c757d')
        row1.pack(fill=tk.X, pady=2)

        tk.Label(row1, text="Total Trades:", fg='white', bg='#6c757d',
                font=('Arial', 10, 'bold'), width=15).pack(side=tk.LEFT)
        self.total_trades_label = tk.Label(row1, text="0", fg='#17a2b8', bg='#6c757d',
                                          font=('Arial', 10, 'bold'))
        self.total_trades_label.pack(side=tk.LEFT, padx=(10, 30))

        tk.Label(row1, text="Win Rate:", fg='white', bg='#6c757d',
                font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.win_rate_label = tk.Label(row1, text="0%", fg='#28a745', bg='#6c757d',
                                      font=('Arial', 10, 'bold'))
        self.win_rate_label.pack(side=tk.LEFT, padx=(10, 0))

        # Row 2
        row2 = tk.Frame(results_grid, bg='#6c757d')
        row2.pack(fill=tk.X, pady=2)

        tk.Label(row2, text="Total Profit:", fg='white', bg='#6c757d',
                font=('Arial', 10, 'bold'), width=15).pack(side=tk.LEFT)
        self.total_profit_label = tk.Label(row2, text="$0.00", fg='#ffc107', bg='#6c757d',
                                          font=('Arial', 10, 'bold'))
        self.total_profit_label.pack(side=tk.LEFT, padx=(10, 30))

        tk.Label(row2, text="Max Drawdown:", fg='white', bg='#6c757d',
                font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.max_drawdown_label = tk.Label(row2, text="0%", fg='#dc3545', bg='#6c757d',
                                          font=('Arial', 10, 'bold'))
        self.max_drawdown_label.pack(side=tk.LEFT, padx=(10, 0))

        # Row 3
        row3 = tk.Frame(results_grid, bg='#6c757d')
        row3.pack(fill=tk.X, pady=2)

        tk.Label(row3, text="Avg Trade:", fg='white', bg='#6c757d',
                font=('Arial', 10, 'bold'), width=15).pack(side=tk.LEFT)
        self.avg_trade_label = tk.Label(row3, text="$0.00", fg='#17a2b8', bg='#6c757d',
                                       font=('Arial', 10, 'bold'))
        self.avg_trade_label.pack(side=tk.LEFT, padx=(10, 30))

        tk.Label(row3, text="Sharpe Ratio:", fg='white', bg='#6c757d',
                font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.sharpe_ratio_label = tk.Label(row3, text="0.00", fg='#ffc107', bg='#6c757d',
                                          font=('Arial', 10, 'bold'))
        self.sharpe_ratio_label.pack(side=tk.LEFT, padx=(10, 0))

        # Detailed results
        details_frame = tk.Frame(self.backtest_frame, bg='#1e1e1e', relief=tk.RAISED, bd=2)
        details_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        tk.Label(details_frame, text="📋 Detailed Backtest Log",
                font=('Arial', 12, 'bold'), fg='white', bg='#1e1e1e').pack(pady=5)

        # Text area with scrollbar
        details_text_frame = tk.Frame(details_frame, bg='#1e1e1e')
        details_text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        self.backtest_log_text = tk.Text(details_text_frame, bg='#2d2d2d', fg='#ffffff',
                                        font=('Consolas', 9), wrap=tk.WORD,
                                        insertbackground='white')

        backtest_scrollbar = tk.Scrollbar(details_text_frame, orient=tk.VERTICAL,
                                         command=self.backtest_log_text.yview)
        self.backtest_log_text.configure(yscrollcommand=backtest_scrollbar.set)

        self.backtest_log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        backtest_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_account_tab(self):
        """Create account info tab"""
        # Account information
        info_frame = tk.Frame(self.account_frame, bg='#6c757d', relief=tk.RAISED, bd=2)
        info_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(info_frame, text="💰 MetaTrader 5 Account Information",
                font=('Arial', 12, 'bold'), fg='white', bg='#6c757d').pack(pady=10)

        # Account details grid
        details_grid = tk.Frame(info_frame, bg='#6c757d')
        details_grid.pack(pady=10)

        # Row 1
        acc_row1 = tk.Frame(details_grid, bg='#6c757d')
        acc_row1.pack(fill=tk.X, pady=5)

        tk.Label(acc_row1, text="🏢 Company:", fg='white', bg='#6c757d',
                font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.company_label = tk.Label(acc_row1, text="Not Connected", fg='#17a2b8', bg='#6c757d',
                                     font=('Arial', 10))
        self.company_label.pack(side=tk.LEFT, padx=(10, 30))

        tk.Label(acc_row1, text="🖥️ Server:", fg='white', bg='#6c757d',
                font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.server_label = tk.Label(acc_row1, text="Not Connected", fg='#17a2b8', bg='#6c757d',
                                    font=('Arial', 10))
        self.server_label.pack(side=tk.LEFT, padx=(10, 0))

        # Row 2
        acc_row2 = tk.Frame(details_grid, bg='#6c757d')
        acc_row2.pack(fill=tk.X, pady=5)

        tk.Label(acc_row2, text="💰 Balance:", fg='white', bg='#6c757d',
                font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.balance_label = tk.Label(acc_row2, text="$0.00", fg='#28a745', bg='#6c757d',
                                     font=('Arial', 12, 'bold'))
        self.balance_label.pack(side=tk.LEFT, padx=(10, 30))

        tk.Label(acc_row2, text="📊 Equity:", fg='white', bg='#6c757d',
                font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.equity_label = tk.Label(acc_row2, text="$0.00", fg='#17a2b8', bg='#6c757d',
                                    font=('Arial', 12, 'bold'))
        self.equity_label.pack(side=tk.LEFT, padx=(10, 0))

        # Row 3
        acc_row3 = tk.Frame(details_grid, bg='#6c757d')
        acc_row3.pack(fill=tk.X, pady=5)

        tk.Label(acc_row3, text="📈 Profit/Loss:", fg='white', bg='#6c757d',
                font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.profit_label = tk.Label(acc_row3, text="$0.00", fg='white', bg='#6c757d',
                                    font=('Arial', 12, 'bold'))
        self.profit_label.pack(side=tk.LEFT, padx=(10, 30))

        tk.Label(acc_row3, text="📋 Positions:", fg='white', bg='#6c757d',
                font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.positions_label = tk.Label(acc_row3, text="0", fg='#ffc107', bg='#6c757d',
                                       font=('Arial', 12, 'bold'))
        self.positions_label.pack(side=tk.LEFT, padx=(10, 0))

        # System status
        status_frame = tk.Frame(self.account_frame, bg='#6c757d', relief=tk.RAISED, bd=2)
        status_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(status_frame, text="🔄 System Status",
                font=('Arial', 12, 'bold'), fg='white', bg='#6c757d').pack(pady=5)

        status_grid = tk.Frame(status_frame, bg='#6c757d')
        status_grid.pack(pady=5)

        tk.Label(status_grid, text="Trading Status:", fg='white', bg='#6c757d',
                font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.trading_status_label = tk.Label(status_grid, text="Stopped", fg='#ffc107', bg='#6c757d',
                                            font=('Arial', 11, 'bold'))
        self.trading_status_label.pack(side=tk.LEFT, padx=(10, 30))

        tk.Label(status_grid, text="Connection:", fg='white', bg='#6c757d',
                font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.connection_status_label = tk.Label(status_grid, text="Disconnected", fg='#dc3545', bg='#6c757d',
                                               font=('Arial', 11, 'bold'))
        self.connection_status_label.pack(side=tk.LEFT, padx=(10, 0))

    def on_mode_change(self):
        """Handle trading mode change"""
        if self.backtest_var.get():
            # Backtesting mode
            self.period_frame.pack(pady=5)
            self.start_btn.config(text="🚀 Start Live Trading", state='disabled')
            self.backtest_btn.config(state='normal')
        else:
            # Live trading mode
            self.period_frame.pack_forget()
            self.start_btn.config(text="🚀 Start Live Trading", state='normal' if self.is_connected else 'disabled')
            self.backtest_btn.config(state='disabled')

    def update_confidence_label(self, value=None):
        """Update confidence label"""
        confidence = self.confidence_var.get()
        self.confidence_label.config(text=f"{confidence:.0f}%")

        # Update required confidence in display
        if hasattr(self, 'required_confidence_label'):
            self.required_confidence_label.config(text=f"{confidence:.0f}%")

        # Change color based on percentage
        if confidence >= 90:
            color = '#28a745'  # Green
        elif confidence >= 70:
            color = '#ffc107'  # Yellow
        elif confidence >= 50:
            color = '#fd7e14'  # Orange
        else:
            color = '#dc3545'  # Red

        self.confidence_label.config(fg=color)

    def set_confidence(self, value):
        """Set confidence quickly"""
        self.confidence_var.set(value)
        self.update_confidence_label()

    def set_symbol(self, symbol):
        """Set symbol quickly"""
        self.symbol_var.set(symbol)
        self.log_message(f"📊 Symbol changed to: {symbol}")

    def on_symbol_search(self, event=None):
        """Handle symbol search"""
        # This could be enhanced with autocomplete
        pass

    def search_mt5_symbols(self):
        """Search MT5 symbols"""
        if not self.is_connected or not self.trading_system:
            messagebox.showwarning("Warning", "Must connect to MT5 first to search symbols!")
            return

        try:
            self.log_message("🔍 Searching available MT5 symbols...")

            # Get all symbols from MT5
            import MetaTrader5 as mt5
            symbols = mt5.symbols_get()

            if symbols:
                # Show symbols window
                self.show_symbols_window(symbols)
                self.log_message(f"✅ Found {len(symbols)} symbols in MT5")
            else:
                self.log_message("❌ No symbols found in MT5")

        except Exception as e:
            self.log_message(f"❌ Search error: {str(e)}")

    def show_symbols_window(self, symbols):
        """Show symbols selection window"""
        symbols_window = tk.Toplevel(self.root)
        symbols_window.title("🔍 Available MT5 Symbols")
        symbols_window.geometry("600x500")
        symbols_window.configure(bg='#2b2b2b')

        # Search frame
        search_frame = tk.Frame(symbols_window, bg='#343a40')
        search_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(search_frame, text="🔍 Search:", fg='white', bg='#343a40',
                font=('Arial', 10, 'bold')).pack(side=tk.LEFT)

        search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=search_var, width=20, font=('Arial', 10))
        search_entry.pack(side=tk.LEFT, padx=10)

        # Categories
        categories_frame = tk.Frame(symbols_window, bg='#2b2b2b')
        categories_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        notebook = ttk.Notebook(categories_frame)
        notebook.pack(fill=tk.BOTH, expand=True)

        # Categorize symbols
        categories = {
            "Cryptocurrencies": [s.name for s in symbols if any(crypto in s.name for crypto in ['BTC', 'ETH', 'XRP', 'LTC', 'ADA'])],
            "Major Forex": [s.name for s in symbols if s.name in ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD']],
            "Cross Pairs": [s.name for s in symbols if len(s.name) == 6 and 'USD' not in s.name and any(curr in s.name for curr in ['EUR', 'GBP', 'AUD', 'CAD', 'CHF', 'JPY', 'NZD'])],
            "Metals": [s.name for s in symbols if any(metal in s.name for metal in ['XAU', 'XAG', 'GOLD', 'SILVER'])],
            "Indices": [s.name for s in symbols if any(index in s.name for index in ['US30', 'US500', 'NAS100', 'GER30', 'UK100'])],
            "All": [s.name for s in symbols]
        }

        for category, symbol_list in categories.items():
            if symbol_list:
                frame = tk.Frame(notebook, bg='#2b2b2b')
                notebook.add(frame, text=f"{category} ({len(symbol_list)})")

                # Listbox with scrollbar
                list_frame = tk.Frame(frame, bg='#2b2b2b')
                list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

                listbox = tk.Listbox(list_frame, bg='#343a40', fg='white',
                                   font=('Arial', 10), selectmode=tk.SINGLE)
                scrollbar = tk.Scrollbar(list_frame, orient=tk.VERTICAL, command=listbox.yview)
                listbox.configure(yscrollcommand=scrollbar.set)

                for symbol in sorted(symbol_list):
                    listbox.insert(tk.END, symbol)

                listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
                scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

                # Double-click to select
                listbox.bind('<Double-Button-1>', lambda e, lb=listbox: self.select_symbol_from_window(lb, symbols_window))

        # Buttons
        buttons_frame = tk.Frame(symbols_window, bg='#2b2b2b')
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        select_btn = tk.Button(buttons_frame, text="✅ Select",
                              command=lambda: self.select_current_symbol(notebook, symbols_window),
                              bg='#28a745', fg='white', font=('Arial', 10, 'bold'))
        select_btn.pack(side=tk.LEFT, padx=5)

        close_btn = tk.Button(buttons_frame, text="❌ Close",
                             command=symbols_window.destroy,
                             bg='#dc3545', fg='white', font=('Arial', 10, 'bold'))
        close_btn.pack(side=tk.LEFT, padx=5)

    def select_symbol_from_window(self, listbox, window):
        """Select symbol from window"""
        if listbox.curselection():
            selected = listbox.get(listbox.curselection()[0])
            self.symbol_var.set(selected)
            window.destroy()
            self.log_message(f"✅ Symbol selected: {selected}")

    def select_current_symbol(self, notebook, window):
        """Select current symbol"""
        current_tab = notebook.select()
        current_frame = notebook.nametowidget(current_tab)

        # Find listbox in current frame
        for widget in current_frame.winfo_children():
            if isinstance(widget, tk.Frame):
                for child in widget.winfo_children():
                    if isinstance(child, tk.Listbox) and child.curselection():
                        selected = child.get(child.curselection()[0])
                        self.symbol_var.set(selected)
                        window.destroy()
                        self.log_message(f"✅ Symbol selected: {selected}")
                        return

        messagebox.showwarning("Warning", "Please select a symbol from the list first!")

    def log_message(self, message, log_type='live'):
        """Add message to log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        if log_type == 'live':
            self.live_log_text.insert(tk.END, formatted_message)
            self.live_log_text.see(tk.END)
        elif log_type == 'backtest':
            self.backtest_log_text.insert(tk.END, formatted_message)
            self.backtest_log_text.see(tk.END)

        self.root.update_idletasks()

    def connect_mt5(self):
        """Connect to MetaTrader 5"""
        try:
            self.log_message("🔌 Connecting to MetaTrader 5...")

            # Initialize trading systems
            self.trading_system = MT5RealCryptoSystem()
            self.demo_system = DemoTradingSystem()
            self.use_demo_mode = False

            # Try MT5 first
            if self.trading_system.connect_mt5():
                self.is_connected = True
                self.use_demo_mode = False
                self.status_label.config(text="✅ Connected to MT5", fg='#28a745')
                self.connect_btn.config(state='disabled')
                self.disconnect_btn.config(state='normal')
                self.start_btn.config(state='normal')
                self.backtest_btn.config(state='normal')

                # Update connection status
                self.connection_status_label.config(text="Connected to MT5", fg='#28a745')

                self.log_message("✅ Successfully connected to MT5!")

                # Update account info
                self.update_account_info()

            else:
                # Fallback to demo mode
                self.log_message("⚠️ MT5 connection failed, switching to Demo Mode...")
                if self.demo_system.connect():
                    self.is_connected = True
                    self.use_demo_mode = True
                    self.status_label.config(text="✅ Connected to Demo", fg='#ffc107')
                    self.connect_btn.config(state='disabled')
                    self.disconnect_btn.config(state='normal')
                    self.start_btn.config(state='normal')
                    self.backtest_btn.config(state='normal')

                    # Update connection status
                    self.connection_status_label.config(text="Connected to Demo", fg='#ffc107')

                    self.log_message("✅ Successfully connected to Demo Trading System!")

                    # Update account info for demo
                    self.update_account_info()

                    # Update available symbols for demo
                    self.update_available_symbols()
                else:
                    self.log_message("❌ Failed to connect to both MT5 and Demo systems")
                    messagebox.showerror("Connection Error", "Failed to connect to both MT5 and Demo systems!")

        except Exception as e:
            self.log_message(f"❌ Connection error: {str(e)}")
            messagebox.showerror("Error", f"Connection error: {str(e)}")

    def disconnect_system(self):
        """Disconnect from system"""
        try:
            if self.is_trading:
                self.stop_trading()

            # Disconnect from appropriate system
            if self.use_demo_mode and self.demo_system:
                self.demo_system.disconnect()
                self.log_message("🔌 Disconnected from Demo System")
            elif self.trading_system:
                self.trading_system.disconnect_mt5()
                self.log_message("🔌 Disconnected from MT5")

            self.is_connected = False
            self.use_demo_mode = False
            self.status_label.config(text="❌ Not Connected", fg='#dc3545')
            self.connect_btn.config(state='normal')
            self.disconnect_btn.config(state='disabled')
            self.start_btn.config(state='disabled')
            self.backtest_btn.config(state='disabled')

            # Update connection status
            self.connection_status_label.config(text="Disconnected", fg='#dc3545')

        except Exception as e:
            self.log_message(f"❌ Disconnect error: {str(e)}")

    def update_account_info(self):
        """Update account information"""
        if not self.is_connected:
            return

        try:
            if self.use_demo_mode and self.demo_system:
                # Use demo system
                account_info = self.demo_system.get_account_info()

                if account_info:
                    self.company_label.config(text=account_info.get('company', 'Demo Trading'))
                    self.server_label.config(text=account_info.get('server', 'Demo-Server'))
                    self.balance_label.config(text=f"${account_info.get('balance', 0):,.2f}")
                    self.equity_label.config(text=f"${account_info.get('equity', 0):,.2f}")

                    profit = account_info.get('profit', 0)
                    if profit > 0:
                        self.profit_label.config(text=f"+${profit:,.2f}", fg='#28a745')
                    elif profit < 0:
                        self.profit_label.config(text=f"-${abs(profit):,.2f}", fg='#dc3545')
                    else:
                        self.profit_label.config(text="$0.00", fg='white')

                # Get positions count from demo system
                positions = self.demo_system.get_positions()
                self.positions_label.config(text=str(len(positions)))

            elif self.trading_system:
                # Use MT5 system
                import MetaTrader5 as mt5
                account_info = mt5.account_info()

                if account_info:
                    self.company_label.config(text=account_info.company)
                    self.server_label.config(text=account_info.server)
                    self.balance_label.config(text=f"${account_info.balance:,.2f}")
                    self.equity_label.config(text=f"${account_info.equity:,.2f}")

                    profit = account_info.profit
                    if profit > 0:
                        self.profit_label.config(text=f"+${profit:,.2f}", fg='#28a745')
                    elif profit < 0:
                        self.profit_label.config(text=f"-${abs(profit):,.2f}", fg='#dc3545')
                    else:
                        self.profit_label.config(text="$0.00", fg='white')

                # Get positions count from MT5
                positions = mt5.positions_get()
                if positions:
                    self.positions_label.config(text=str(len(positions)))
                else:
                    self.positions_label.config(text="0")

        except Exception as e:
            self.log_message(f"❌ Account info error: {str(e)}")

    def update_available_symbols(self):
        """Update available symbols dropdown"""
        try:
            if self.use_demo_mode and self.demo_system:
                # Get symbols from demo system
                symbols = self.demo_system.get_available_symbols()

                # Update symbol dropdown
                if hasattr(self, 'symbol_combo'):
                    current_symbol = self.symbol_var.get()
                    self.symbol_combo['values'] = symbols

                    # Keep current symbol if available, otherwise use first one
                    if current_symbol not in symbols and symbols:
                        self.symbol_var.set(symbols[0])

                self.log_message(f"📊 تم تحديث الرموز المتوفرة: {len(symbols)} رمز")

            elif self.trading_system and hasattr(self.trading_system, 'get_available_pairs'):
                # Get symbols from MT5 system
                symbols = self.trading_system.get_available_pairs()

                if hasattr(self, 'symbol_combo') and symbols:
                    current_symbol = self.symbol_var.get()
                    self.symbol_combo['values'] = symbols

                    if current_symbol not in symbols:
                        self.symbol_var.set(symbols[0])

                self.log_message(f"📊 تم تحديث الرموز المتوفرة من MT5: {len(symbols)} رمز")

        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث الرموز: {str(e)}")

    def start_trading(self):
        """Start live trading"""
        if not self.is_connected:
            messagebox.showwarning("Warning", "Must connect to MT5 first!")
            return

        if self.backtest_var.get():
            messagebox.showinfo("Info", "Switch to Live Trading mode first!")
            return

        try:
            self.is_trading = True
            self.start_btn.config(state='disabled')
            self.stop_btn.config(state='normal')
            self.trading_status_label.config(text="Running", fg='#28a745')

            symbol = self.symbol_var.get()
            confidence_threshold = self.confidence_var.get()
            demo_mode = self.demo_var.get()

            self.log_message(f"🚀 Starting live trading...")
            self.log_message(f"📊 Symbol: {symbol}")
            self.log_message(f"🎯 Confidence Threshold: {confidence_threshold}%")
            self.log_message(f"🛡️ Demo Mode: {'ON' if demo_mode else 'OFF'}")

            # Start trading thread
            trading_thread = threading.Thread(target=self.trading_loop, daemon=True)
            trading_thread.start()

        except Exception as e:
            self.log_message(f"❌ Start trading error: {str(e)}")
            self.is_trading = False
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')

    def stop_trading(self):
        """Stop trading"""
        self.is_trading = False
        self.is_backtesting = False
        self.start_btn.config(state='normal' if self.is_connected else 'disabled')
        self.stop_btn.config(state='disabled')
        self.trading_status_label.config(text="Stopped", fg='#ffc107')

        self.log_message("⏹️ Trading stopped")

    def trading_loop(self):
        """Main trading loop with advanced learning"""
        symbol = self.symbol_var.get()
        confidence_threshold = self.confidence_var.get()
        demo_mode = self.demo_var.get()

        self.log_message(f"🚀 بدء التداول المتقدم مع نظام التعلم")
        self.log_message(f"📊 الرمز: {symbol} | الثقة المطلوبة: {confidence_threshold}%")
        self.log_message(f"🛡️ الوضع: {'تجريبي' if demo_mode else 'حقيقي'}")

        # عرض إحصائيات الذاكرة
        memory_stats = self.learning_system.get_memory_stats()
        self.log_message(f"🧠 ذاكرة التعلم: {memory_stats['total_trades']} صفقة سابقة")

        while self.is_trading:
            try:
                self.log_message(f"🔍 تحليل {symbol} بالذكاء الاصطناعي...")

                # Analyze market using appropriate system
                if self.use_demo_mode and self.demo_system:
                    # Use demo system for analysis (simplified)
                    current_price = self.demo_system.get_current_price(symbol)
                    if current_price > 0:
                        # Simple analysis for demo
                        decisions = ['buy', 'sell', 'hold']
                        decision = random.choice(decisions)
                        confidence = random.uniform(20, 90)

                        analysis = {
                            'decision': decision,
                            'confidence': confidence,
                            'price': current_price,
                            'symbol': symbol,
                            'market_conditions': {
                                'volatility': random.uniform(0.01, 0.05),
                                'trend': random.choice(['up', 'down', 'sideways'])
                            },
                            'indicators': {
                                'rsi': random.uniform(30, 70),
                                'macd': random.uniform(-0.1, 0.1)
                            }
                        }
                    else:
                        analysis = None
                else:
                    # Use MT5 system for analysis
                    analysis = self.trading_system.analyze_market(symbol)

                if analysis:
                    decision = analysis.get('decision', 'hold')
                    original_confidence = analysis.get('confidence', 0)
                    price = analysis.get('price', 0)

                    # تطبيق التعلم المتقدم - تعديل الثقة
                    adjusted_confidence = self.learning_system.get_confidence_adjustment(
                        symbol, decision, original_confidence
                    )

                    # إضافة زيادة تكيفية للثقة
                    adaptive_boost = self.learning_system.get_adaptive_confidence_boost(symbol)
                    adjusted_confidence += adaptive_boost

                    # إعادة تعيين الثقة إذا كانت منخفضة جداً
                    adjusted_confidence = self.learning_system.reset_confidence_if_too_low(adjusted_confidence)

                    # فحص ما إذا كان يجب تجنب الصفقة
                    should_avoid, avoid_reason = self.learning_system.should_avoid_trade(
                        symbol, decision, adjusted_confidence
                    )

                    # Update confidence display
                    self.root.after(0, self.update_confidence_display, adjusted_confidence, confidence_threshold)

                    self.log_message(f"📊 نتيجة التحليل:")
                    self.log_message(f"   🎯 القرار: {decision}")
                    self.log_message(f"   📈 الثقة الأصلية: {original_confidence:.1f}%")
                    if adjusted_confidence != original_confidence:
                        self.log_message(f"   🧠 الثقة المعدلة: {adjusted_confidence:.1f}%")
                    self.log_message(f"   💰 السعر: ${price:,.2f}")
                    self.log_message(f"   🎯 المطلوب: {confidence_threshold}%")

                    # فحص تجنب الصفقة
                    if should_avoid:
                        self.log_message(avoid_reason)
                        self.log_message("🚫 تم تجنب الصفقة بناء على التعلم السابق")
                    elif adjusted_confidence >= confidence_threshold and decision in ['buy', 'sell']:
                        self.log_message(f"✅ شروط الدخول متوفرة! الثقة: {adjusted_confidence:.1f}% >= المطلوب: {confidence_threshold}%")

                        # تحضير بيانات الصفقة للتعلم
                        trade_data = {
                            'symbol': symbol,
                            'decision': decision,
                            'confidence': adjusted_confidence,
                            'entry_price': price,
                            'session_id': self.current_session_id,
                            'market_conditions': analysis.get('market_conditions', {}),
                            'indicators': analysis.get('indicators', {})
                        }

                        # تنفيذ الصفقة (حقيقية أو تجريبية)
                        if self.use_demo_mode and self.demo_system:
                            # استخدام النظام التجريبي
                            self.log_message("🛡️ تنفيذ صفقة تجريبية...")
                            result = self.demo_system.execute_trade(symbol, decision, adjusted_confidence, price)

                            if result['success']:
                                self.log_message(f"✅ تم تنفيذ الصفقة التجريبية بنجاح!")
                                self.log_message(f"   رقم التذكرة: {result['ticket']}")
                                self.log_message(f"   السعر المنفذ: ${result['price']:.5f}")
                                self.log_message("🧠 النظام يتعلم من هذه الصفقة...")

                                # تسجيل الصفقة للتعلم
                                trade_data.update({
                                    'ticket': result['ticket'],
                                    'executed_price': result['price'],
                                    'volume': result.get('volume', 0.01)
                                })
                                # سيتم تسجيل النتيجة النهائية عند إغلاق الصفقة

                            else:
                                self.log_message(f"❌ فشل في تنفيذ الصفقة التجريبية: {result['message']}")

                                # تسجيل فشل التنفيذ للتعلم
                                trade_data.update({
                                    'exit_price': price,
                                    'profit': -10,  # خسارة رمزية لفشل التنفيذ
                                    'profit_pct': -0.1,
                                    'exit_reason': 'execution_failed'
                                })
                                self.learning_system.record_trade(trade_data)

                        elif demo_mode:
                            # الوضع التجريبي القديم (محاكاة فقط)
                            self.log_message("🛡️ الوضع التجريبي: محاكاة الصفقة (لا يوجد تنفيذ حقيقي)")
                            self.log_message(f"📝 سيتم تنفيذ: {decision.upper()} {symbol} عند ${price:,.2f}")

                            # محاكاة نتيجة الصفقة للتعلم
                            simulated_profit = np.random.uniform(-50, 100)  # محاكاة ربح/خسارة
                            trade_data.update({
                                'exit_price': price + (simulated_profit * 0.01),
                                'profit': simulated_profit,
                                'profit_pct': (simulated_profit / (10000 * 0.1)) * 100,
                                'exit_reason': 'simulated'
                            })

                            # تسجيل الصفقة المحاكاة للتعلم
                            self.learning_system.record_trade(trade_data)
                            self.log_message(f"🧠 تم تسجيل الصفقة المحاكاة للتعلم: ربح ${simulated_profit:.2f}")

                        else:
                            # تنفيذ الصفقة الحقيقية على MT5
                            self.log_message("🚀 تنفيذ الصفقة الحقيقية على MT5...")

                            # تحديث analysis بالثقة المعدلة
                            analysis['confidence'] = adjusted_confidence

                            if self.trading_system.execute_trade_mt5(analysis):
                                self.log_message("✅ تم تنفيذ الصفقة بنجاح!")
                                self.log_message("🧠 النظام يتعلم من هذه الصفقة...")

                                # سيتم تسجيل الصفقة الحقيقية عند الإغلاق
                                # هنا نسجل فقط بداية الصفقة
                                self.log_message("📝 تم تسجيل بداية الصفقة في نظام التعلم")

                            else:
                                self.log_message("❌ فشل في تنفيذ الصفقة")

                                # تسجيل فشل التنفيذ للتعلم
                                trade_data.update({
                                    'exit_price': price,
                                    'profit': -10,  # خسارة رمزية لفشل التنفيذ
                                    'profit_pct': -0.1,
                                    'exit_reason': 'execution_failed'
                                })
                                self.learning_system.record_trade(trade_data)

                    else:
                        if decision == 'hold':
                            self.log_message("⏸️ التحليل يقترح الانتظار - لا يوجد إجراء")
                        else:
                            self.log_message(f"⏳ انتظار ظروف أفضل. الحالي: {adjusted_confidence:.1f}% < المطلوب: {confidence_threshold}%")
                else:
                    self.log_message("❌ فشل التحليل - بيانات غير كافية")

                # عرض رؤى التعلم كل 5 تحليلات
                if hasattr(self, 'analysis_count'):
                    self.analysis_count += 1
                else:
                    self.analysis_count = 1

                if self.analysis_count % 5 == 0:
                    insights = self.learning_system.get_learning_insights()
                    if insights:
                        self.log_message("🧠 رؤى التعلم:")
                        for insight in insights[:3]:  # أول 3 رؤى فقط
                            self.log_message(f"   {insight}")

                # Update demo positions if using demo mode
                if self.use_demo_mode and self.demo_system:
                    self.demo_system.update_positions()

                # Update account info
                self.root.after(0, self.update_account_info)

                # Wait before next analysis
                for i in range(30):  # 30 seconds
                    if not self.is_trading:
                        break
                    time.sleep(1)

            except Exception as e:
                self.log_message(f"❌ خطأ في حلقة التداول: {str(e)}")
                time.sleep(5)

        # عند انتهاء التداول، عرض التوصيات الاستراتيجية
        if not self.is_trading:
            self.log_message("🎓 جلسة التداول انتهت - تحليل التعلم النهائي:")
            recommendations = self.learning_system.generate_strategy_recommendations()
            for rec in recommendations:
                self.log_message(f"   {rec}")

            # حفظ الذاكرة
            self.learning_system.save_memory()
            self.log_message("💾 تم حفظ ذاكرة التعلم")

    def update_confidence_display(self, current_confidence, required_confidence):
        """Update confidence display in UI"""
        self.current_confidence_label.config(text=f"{current_confidence:.1f}%")

        # Update status and colors
        if current_confidence >= required_confidence:
            self.confidence_status_label.config(text="Ready to Trade", fg='#28a745')
            self.current_confidence_label.config(fg='#28a745')
        else:
            self.confidence_status_label.config(text="Waiting", fg='#ffc107')
            self.current_confidence_label.config(fg='#ffc107')

    def run_backtest(self):
        """Run historical backtesting"""
        if not self.is_connected:
            messagebox.showwarning("Warning", "Must connect to MT5 first!")
            return

        if not self.backtest_var.get():
            messagebox.showinfo("Info", "Switch to Backtesting mode first!")
            return

        try:
            self.is_backtesting = True
            self.backtest_btn.config(state='disabled')
            self.stop_btn.config(state='normal')

            # Clear previous results
            self.backtest_log_text.delete(1.0, tk.END)
            self.reset_backtest_results()

            symbol = self.symbol_var.get()
            confidence_threshold = self.confidence_var.get()
            period = self.backtest_period_var.get()

            self.log_message(f"🔄 Starting historical backtesting...", 'backtest')
            self.log_message(f"📊 Symbol: {symbol}", 'backtest')
            self.log_message(f"🎯 Confidence Threshold: {confidence_threshold}%", 'backtest')
            self.log_message(f"📅 Period: {period.replace('_', ' ').title()}", 'backtest')

            # Start backtesting thread
            backtest_thread = threading.Thread(target=self.backtest_loop,
                                             args=(symbol, confidence_threshold, period),
                                             daemon=True)
            backtest_thread.start()

        except Exception as e:
            self.log_message(f"❌ Backtest start error: {str(e)}", 'backtest')
            self.is_backtesting = False
            self.backtest_btn.config(state='normal')
            self.stop_btn.config(state='disabled')

    def backtest_loop(self, symbol, confidence_threshold, period):
        """Main backtesting loop"""
        learning_data = []  # Initialize outside try block
        try:
            # Calculate date range
            end_date = datetime.now()

            if period == '1_month':
                start_date = end_date - timedelta(days=30)
            elif period == '3_months':
                start_date = end_date - timedelta(days=90)
            elif period == '6_months':
                start_date = end_date - timedelta(days=180)
            elif period == '1_year':
                start_date = end_date - timedelta(days=365)
            elif period == '2_years':
                start_date = end_date - timedelta(days=730)
            else:
                start_date = end_date - timedelta(days=90)  # Default 3 months

            self.log_message(f"📅 Backtesting from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}", 'backtest')

            # Get historical data
            self.log_message("📊 Fetching historical data...", 'backtest')
            historical_data = self.get_historical_data_for_backtest(symbol, start_date, end_date)

            if historical_data is None or len(historical_data) < 50:
                self.log_message("❌ Insufficient historical data for backtesting", 'backtest')
                return

            self.log_message(f"✅ Loaded {len(historical_data)} data points", 'backtest')

            # Initialize backtest variables
            trades = []
            balance = 10000.0  # Starting balance
            initial_balance = balance
            position = None  # Current position
            max_balance = balance
            min_balance = balance

            # Initialize live stats
            self.live_backtest_stats = {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'total_profit': 0.0,
                'current_balance': balance,
                'max_balance': balance,
                'min_balance': balance,
                'current_drawdown': 0.0,
                'max_drawdown': 0.0
            }

            total_points = len(historical_data)
            processed_points = 0

            # Progress update
            self.root.after(0, self.update_progress, 0, "Initializing backtest...")
            self.root.after(0, self.update_live_backtest_display)

            # Learning system initialization (already initialized above)

            # Simulate trading through historical data
            for i in range(50, len(historical_data)):  # Start from 50 to have enough data for indicators
                if not self.is_backtesting:
                    break

                # Get data slice for analysis
                data_slice = historical_data.iloc[:i+1].copy()
                current_price = data_slice.iloc[-1]['close']
                current_time = data_slice.index[-1]

                try:
                    # Simulate analysis (simplified version)
                    analysis = self.simulate_analysis(data_slice, symbol)

                    if analysis:
                        decision = analysis.get('decision', 'hold')
                        confidence = analysis.get('confidence', 0)

                        # تطبيق نظام التعلم المتقدم
                        original_confidence = confidence
                        adjusted_confidence = self.learning_system.get_confidence_adjustment(
                            symbol, decision, confidence
                        )

                        # فحص تجنب الصفقة
                        should_avoid, avoid_reason = self.learning_system.should_avoid_trade(
                            symbol, decision, adjusted_confidence
                        )

                        # Check for entry conditions with learning system
                        if position is None and not should_avoid and adjusted_confidence >= confidence_threshold and decision in ['buy', 'sell']:
                            # Enter position
                            position = {
                                'type': decision,
                                'entry_price': current_price,
                                'entry_time': current_time,
                                'confidence': adjusted_confidence,
                                'original_confidence': original_confidence
                            }

                            confidence_msg = f"(Confidence: {adjusted_confidence:.1f}%"
                            if adjusted_confidence != original_confidence:
                                confidence_msg += f", Original: {original_confidence:.1f}%"
                            confidence_msg += ")"

                            self.log_message(f"📈 ENTRY: {decision.upper()} at ${current_price:.2f} {confidence_msg}", 'backtest')

                        elif position is None and should_avoid and decision in ['buy', 'sell']:
                            # تسجيل تجنب الصفقة
                            self.log_message(f"🚫 AVOIDED: {decision.upper()} at ${current_price:.2f} - {avoid_reason}", 'backtest')

                        elif position is not None:
                            # Check for exit conditions
                            should_exit = False
                            exit_reason = ""

                            if position['type'] == 'buy':
                                # Take profit or stop loss for buy position
                                profit_pct = (current_price - position['entry_price']) / position['entry_price'] * 100

                                if profit_pct >= 2.0:  # 2% profit
                                    should_exit = True
                                    exit_reason = "Take Profit"
                                elif profit_pct <= -1.0:  # 1% loss
                                    should_exit = True
                                    exit_reason = "Stop Loss"
                                elif decision == 'sell' and confidence >= confidence_threshold:
                                    should_exit = True
                                    exit_reason = "Signal Reversal"

                            elif position['type'] == 'sell':
                                # Take profit or stop loss for sell position
                                profit_pct = (position['entry_price'] - current_price) / position['entry_price'] * 100

                                if profit_pct >= 2.0:  # 2% profit
                                    should_exit = True
                                    exit_reason = "Take Profit"
                                elif profit_pct <= -1.0:  # 1% loss
                                    should_exit = True
                                    exit_reason = "Stop Loss"
                                elif decision == 'buy' and confidence >= confidence_threshold:
                                    should_exit = True
                                    exit_reason = "Signal Reversal"

                            if should_exit:
                                # Exit position
                                if position['type'] == 'buy':
                                    profit = (current_price - position['entry_price']) / position['entry_price'] * balance * 0.1  # 10% of balance per trade
                                else:  # sell
                                    profit = (position['entry_price'] - current_price) / position['entry_price'] * balance * 0.1

                                balance += profit
                                max_balance = max(max_balance, balance)
                                min_balance = min(min_balance, balance)

                                trade_result = {
                                    'entry_time': position['entry_time'],
                                    'exit_time': current_time,
                                    'type': position['type'],
                                    'entry_price': position['entry_price'],
                                    'exit_price': current_price,
                                    'profit': profit,
                                    'confidence': position['confidence'],
                                    'exit_reason': exit_reason
                                }

                                trades.append(trade_result)

                                # Update live statistics
                                self.live_backtest_stats['total_trades'] += 1
                                self.live_backtest_stats['current_balance'] = balance
                                self.live_backtest_stats['total_profit'] = balance - initial_balance
                                self.live_backtest_stats['max_balance'] = max_balance
                                self.live_backtest_stats['min_balance'] = min_balance

                                if profit > 0:
                                    self.live_backtest_stats['winning_trades'] += 1
                                else:
                                    self.live_backtest_stats['losing_trades'] += 1

                                # Calculate current drawdown
                                current_drawdown = ((max_balance - balance) / max_balance) * 100 if max_balance > 0 else 0
                                self.live_backtest_stats['current_drawdown'] = current_drawdown
                                self.live_backtest_stats['max_drawdown'] = max(self.live_backtest_stats['max_drawdown'], current_drawdown)

                                # Update display in real-time
                                self.root.after(0, self.update_live_backtest_display)

                                # تسجيل الصفقة في نظام التعلم المتقدم
                                trade_data = {
                                    'symbol': symbol,
                                    'decision': position['type'],
                                    'confidence': position['confidence'],
                                    'entry_price': position['entry_price'],
                                    'exit_price': current_price,
                                    'profit': profit,
                                    'profit_pct': (profit / (balance * 0.1)) * 100,
                                    'exit_reason': exit_reason,
                                    'session_id': f"backtest_{self.current_session_id}",
                                    'market_conditions': {
                                        'volatility': abs(current_price - position['entry_price']) / position['entry_price'],
                                        'price_change': (current_price - position['entry_price']) / position['entry_price']
                                    },
                                    'indicators': analysis.get('indicators', {}) if 'analysis' in locals() else {}
                                }

                                # تسجيل في نظام التعلم المتقدم
                                self.learning_system.record_trade(trade_data)

                                # الاحتفاظ بالنظام القديم للتوافق
                                learning_entry = {
                                    'confidence': position['confidence'],
                                    'profit': profit,
                                    'profit_pct': (profit / (balance * 0.1)) * 100,
                                    'decision': position['type'],
                                    'exit_reason': exit_reason,
                                    'market_conditions': {
                                        'volatility': abs(current_price - position['entry_price']) / position['entry_price'],
                                        'price_change': (current_price - position['entry_price']) / position['entry_price']
                                    }
                                }
                                learning_data.append(learning_entry)

                                # Apply learning every 10 trades
                                if len(learning_data) >= 10 and len(learning_data) % 10 == 0:
                                    self.apply_learning(learning_data, confidence_threshold)

                                profit_str = f"+${profit:.2f}" if profit > 0 else f"-${abs(profit):.2f}"
                                color_indicator = "🟢" if profit > 0 else "🔴"

                                self.log_message(f"📉 EXIT: {position['type'].upper()} at ${current_price:.2f} | {exit_reason} | {color_indicator} {profit_str}", 'backtest')

                                # Learning feedback
                                if len(learning_data) % 10 == 0:
                                    self.log_message(f"🧠 LEARNING: Applied insights from {len(learning_data)} trades", 'backtest')

                                position = None

                except Exception as e:
                    self.log_message(f"❌ Analysis error at {current_time}: {str(e)}", 'backtest')

                # Update progress and live stats
                processed_points += 1
                if processed_points % 20 == 0:  # Update every 20 points for more frequent updates
                    progress = (processed_points / total_points) * 100
                    self.root.after(0, self.update_progress, progress, f"Processing... {processed_points}/{total_points}")

                    # Update live stats even without trades
                    self.live_backtest_stats['current_balance'] = balance
                    current_drawdown = ((max_balance - balance) / max_balance) * 100 if max_balance > 0 else 0
                    self.live_backtest_stats['current_drawdown'] = current_drawdown
                    self.root.after(0, self.update_live_backtest_display)

            # Close any remaining position
            if position is not None:
                current_price = historical_data.iloc[-1]['close']
                if position['type'] == 'buy':
                    profit = (current_price - position['entry_price']) / position['entry_price'] * balance * 0.1
                else:
                    profit = (position['entry_price'] - current_price) / position['entry_price'] * balance * 0.1

                balance += profit

                trade_result = {
                    'entry_time': position['entry_time'],
                    'exit_time': historical_data.index[-1],
                    'type': position['type'],
                    'entry_price': position['entry_price'],
                    'exit_price': current_price,
                    'profit': profit,
                    'confidence': position['confidence'],
                    'exit_reason': 'End of Period'
                }

                trades.append(trade_result)
                self.log_message(f"📉 FINAL EXIT: {position['type'].upper()} at ${current_price:.2f} | End of Period", 'backtest')

            # Calculate results
            self.calculate_backtest_results(trades, initial_balance, balance, max_balance, min_balance)

            # Complete
            self.root.after(0, self.update_progress, 100, "Backtest completed!")
            self.log_message("🎉 Backtesting completed!", 'backtest')

        except Exception as e:
            self.log_message(f"❌ Backtest error: {str(e)}", 'backtest')
        finally:
            self.is_backtesting = False
            # Final learning application
            if learning_data:
                self.apply_final_learning(learning_data)
                self.log_message(f"🎓 FINAL LEARNING: Processed {len(learning_data)} total trades for future optimization", 'backtest')

            self.root.after(0, self.backtest_completed)

    def apply_learning(self, learning_data, current_confidence_threshold):
        """Apply learning from recent trades"""
        try:
            if len(learning_data) < 5:
                return

            # Analyze recent performance
            recent_trades = learning_data[-10:]  # Last 10 trades

            # Calculate success rate by confidence level
            high_conf_trades = [t for t in recent_trades if t['confidence'] >= 70]
            med_conf_trades = [t for t in recent_trades if 50 <= t['confidence'] < 70]
            low_conf_trades = [t for t in recent_trades if t['confidence'] < 50]

            # Calculate win rates
            high_conf_wins = len([t for t in high_conf_trades if t['profit'] > 0])
            med_conf_wins = len([t for t in med_conf_trades if t['profit'] > 0])
            low_conf_wins = len([t for t in low_conf_trades if t['profit'] > 0])

            high_conf_rate = (high_conf_wins / len(high_conf_trades)) * 100 if high_conf_trades else 0
            med_conf_rate = (med_conf_wins / len(med_conf_trades)) * 100 if med_conf_trades else 0
            low_conf_rate = (low_conf_wins / len(low_conf_trades)) * 100 if low_conf_trades else 0

            # Learning insights
            insights = []

            if high_conf_rate > 70:
                insights.append("High confidence trades performing well")
            elif high_conf_rate < 50:
                insights.append("High confidence threshold may be too aggressive")

            if med_conf_rate > high_conf_rate:
                insights.append("Medium confidence trades outperforming high confidence")

            if low_conf_rate > 60:
                insights.append("Low confidence trades surprisingly successful")

            # Average profit analysis
            avg_profit = sum([t['profit'] for t in recent_trades]) / len(recent_trades)
            if avg_profit < 0:
                insights.append("Recent trades showing losses - consider strategy adjustment")
            elif avg_profit > 50:
                insights.append("Recent trades highly profitable - strategy working well")

            # Log learning insights
            if insights:
                self.log_message("🧠 LEARNING INSIGHTS:", 'backtest')
                for insight in insights:
                    self.log_message(f"   💡 {insight}", 'backtest')

        except Exception as e:
            self.log_message(f"❌ Learning error: {str(e)}", 'backtest')

    def apply_final_learning(self, learning_data):
        """Apply comprehensive learning from all trades"""
        try:
            if len(learning_data) < 10:
                return

            # Comprehensive analysis
            total_trades = len(learning_data)
            winning_trades = len([t for t in learning_data if t['profit'] > 0])
            win_rate = (winning_trades / total_trades) * 100

            # Confidence level analysis
            conf_ranges = {
                'very_high': [t for t in learning_data if t['confidence'] >= 80],
                'high': [t for t in learning_data if 70 <= t['confidence'] < 80],
                'medium': [t for t in learning_data if 50 <= t['confidence'] < 70],
                'low': [t for t in learning_data if t['confidence'] < 50]
            }

            best_range = None
            best_rate = 0

            for range_name, trades in conf_ranges.items():
                if trades:
                    wins = len([t for t in trades if t['profit'] > 0])
                    rate = (wins / len(trades)) * 100
                    if rate > best_rate:
                        best_rate = rate
                        best_range = range_name

            # Market condition analysis
            volatile_trades = [t for t in learning_data if t['market_conditions']['volatility'] > 0.02]
            stable_trades = [t for t in learning_data if t['market_conditions']['volatility'] <= 0.02]

            volatile_wins = len([t for t in volatile_trades if t['profit'] > 0]) if volatile_trades else 0
            stable_wins = len([t for t in stable_trades if t['profit'] > 0]) if stable_trades else 0

            volatile_rate = (volatile_wins / len(volatile_trades)) * 100 if volatile_trades else 0
            stable_rate = (stable_wins / len(stable_trades)) * 100 if stable_trades else 0

            # Generate comprehensive learning report
            self.log_message("", 'backtest')
            self.log_message("🎓 COMPREHENSIVE LEARNING ANALYSIS", 'backtest')
            self.log_message("=" * 40, 'backtest')
            self.log_message(f"📊 Total Trades Analyzed: {total_trades}", 'backtest')
            self.log_message(f"🎯 Overall Win Rate: {win_rate:.1f}%", 'backtest')

            if best_range:
                self.log_message(f"🏆 Best Confidence Range: {best_range.replace('_', ' ').title()} ({best_rate:.1f}% win rate)", 'backtest')

            self.log_message(f"📈 Volatile Market Performance: {volatile_rate:.1f}% ({len(volatile_trades)} trades)", 'backtest')
            self.log_message(f"📊 Stable Market Performance: {stable_rate:.1f}% ({len(stable_trades)} trades)", 'backtest')

            # Strategic recommendations
            recommendations = []

            if best_range == 'very_high':
                recommendations.append("Use very high confidence (80%+) for best results")
            elif best_range == 'high':
                recommendations.append("High confidence (70-80%) appears optimal")
            elif best_range == 'medium':
                recommendations.append("Medium confidence (50-70%) showing good balance")
            else:
                recommendations.append("Lower confidence levels performing surprisingly well")

            if volatile_rate > stable_rate + 10:
                recommendations.append("Strategy performs better in volatile markets")
            elif stable_rate > volatile_rate + 10:
                recommendations.append("Strategy performs better in stable markets")

            if win_rate > 60:
                recommendations.append("Overall strategy is performing well")
            elif win_rate < 40:
                recommendations.append("Consider strategy adjustments or higher confidence threshold")

            if recommendations:
                self.log_message("", 'backtest')
                self.log_message("💡 STRATEGIC RECOMMENDATIONS:", 'backtest')
                for i, rec in enumerate(recommendations, 1):
                    self.log_message(f"   {i}. {rec}", 'backtest')

            self.log_message("=" * 40, 'backtest')

        except Exception as e:
            self.log_message(f"❌ Final learning error: {str(e)}", 'backtest')

    def get_historical_data_for_backtest(self, symbol, start_date, end_date):
        """Get historical data for backtesting"""
        try:
            import MetaTrader5 as mt5
            from datetime import timezone

            # Convert to UTC
            utc_from = start_date.replace(tzinfo=timezone.utc)
            utc_to = end_date.replace(tzinfo=timezone.utc)

            # Get data from MT5
            rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_H1, utc_from, utc_to)

            if rates is not None and len(rates) > 0:
                # Convert to DataFrame
                df = pd.DataFrame(rates)
                df['time'] = pd.to_datetime(df['time'], unit='s')
                df.set_index('time', inplace=True)
                df.rename(columns={'tick_volume': 'volume'}, inplace=True)

                return df
            else:
                self.log_message("❌ No historical data available from MT5", 'backtest')
                return None

        except Exception as e:
            self.log_message(f"❌ Historical data error: {str(e)}", 'backtest')
            return None

    def simulate_analysis(self, data, symbol):
        """Simulate trading analysis for backtesting"""
        try:
            if len(data) < 20:
                return None

            # Calculate basic indicators
            data['sma_20'] = data['close'].rolling(window=20).mean()
            data['sma_50'] = data['close'].rolling(window=50).mean() if len(data) >= 50 else data['close'].rolling(window=len(data)//2).mean()

            # RSI calculation
            delta = data['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            data['rsi'] = 100 - (100 / (1 + rs))

            # MACD calculation
            exp1 = data['close'].ewm(span=12).mean()
            exp2 = data['close'].ewm(span=26).mean()
            data['macd'] = exp1 - exp2
            data['macd_signal'] = data['macd'].ewm(span=9).mean()

            # Get latest values
            latest = data.iloc[-1]

            # Simple decision logic
            signals = []
            confidence_factors = []

            # Moving average signals
            if latest['close'] > latest['sma_20']:
                signals.append('buy')
                confidence_factors.append(15)
            elif latest['close'] < latest['sma_20']:
                signals.append('sell')
                confidence_factors.append(15)

            # RSI signals
            if not pd.isna(latest['rsi']):
                if latest['rsi'] < 30:  # Oversold
                    signals.append('buy')
                    confidence_factors.append(25)
                elif latest['rsi'] > 70:  # Overbought
                    signals.append('sell')
                    confidence_factors.append(25)

            # MACD signals
            if not pd.isna(latest['macd']) and not pd.isna(latest['macd_signal']):
                if latest['macd'] > latest['macd_signal']:
                    signals.append('buy')
                    confidence_factors.append(20)
                elif latest['macd'] < latest['macd_signal']:
                    signals.append('sell')
                    confidence_factors.append(20)

            # Determine final decision
            if not signals:
                return {'decision': 'hold', 'confidence': 0, 'price': latest['close']}

            buy_signals = signals.count('buy')
            sell_signals = signals.count('sell')

            if buy_signals > sell_signals:
                decision = 'buy'
                confidence = sum(confidence_factors[:buy_signals])
            elif sell_signals > buy_signals:
                decision = 'sell'
                confidence = sum(confidence_factors[:sell_signals])
            else:
                decision = 'hold'
                confidence = 0

            # Add some randomness to simulate real market conditions
            confidence += np.random.normal(0, 5)  # Add noise
            confidence = max(0, min(100, confidence))  # Clamp between 0-100

            return {
                'decision': decision,
                'confidence': confidence,
                'price': latest['close']
            }

        except Exception as e:
            return None

    def calculate_backtest_results(self, trades, initial_balance, final_balance, max_balance, min_balance):
        """Calculate and display backtest results"""
        try:
            if not trades:
                self.log_message("📊 No trades executed during backtest period", 'backtest')
                return

            # Basic statistics
            total_trades = len(trades)
            winning_trades = len([t for t in trades if t['profit'] > 0])
            losing_trades = len([t for t in trades if t['profit'] < 0])
            win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0

            total_profit = final_balance - initial_balance
            total_profit_pct = (total_profit / initial_balance) * 100

            max_drawdown = ((max_balance - min_balance) / max_balance) * 100 if max_balance > 0 else 0

            avg_trade = total_profit / total_trades if total_trades > 0 else 0

            # Calculate Sharpe ratio (simplified)
            if trades:
                returns = [t['profit'] / initial_balance for t in trades]
                avg_return = np.mean(returns)
                std_return = np.std(returns)
                sharpe_ratio = (avg_return / std_return) if std_return > 0 else 0
            else:
                sharpe_ratio = 0

            # Update UI
            self.root.after(0, self.update_backtest_results, {
                'total_trades': total_trades,
                'win_rate': win_rate,
                'total_profit': total_profit,
                'max_drawdown': max_drawdown,
                'avg_trade': avg_trade,
                'sharpe_ratio': sharpe_ratio
            })

            # Log detailed results
            self.log_message("", 'backtest')
            self.log_message("=" * 50, 'backtest')
            self.log_message("📊 BACKTEST RESULTS SUMMARY", 'backtest')
            self.log_message("=" * 50, 'backtest')
            self.log_message(f"💰 Initial Balance: ${initial_balance:,.2f}", 'backtest')
            self.log_message(f"💰 Final Balance: ${final_balance:,.2f}", 'backtest')
            self.log_message(f"📈 Total Profit: ${total_profit:+,.2f} ({total_profit_pct:+.2f}%)", 'backtest')
            self.log_message(f"📊 Total Trades: {total_trades}", 'backtest')
            self.log_message(f"✅ Winning Trades: {winning_trades}", 'backtest')
            self.log_message(f"❌ Losing Trades: {losing_trades}", 'backtest')
            self.log_message(f"🎯 Win Rate: {win_rate:.1f}%", 'backtest')
            self.log_message(f"📉 Max Drawdown: {max_drawdown:.2f}%", 'backtest')
            self.log_message(f"⚖️ Average Trade: ${avg_trade:+.2f}", 'backtest')
            self.log_message(f"📊 Sharpe Ratio: {sharpe_ratio:.2f}", 'backtest')
            self.log_message("=" * 50, 'backtest')

            # Log individual trades
            self.log_message("", 'backtest')
            self.log_message("📋 INDIVIDUAL TRADES:", 'backtest')
            self.log_message("-" * 50, 'backtest')

            for i, trade in enumerate(trades, 1):
                profit_str = f"+${trade['profit']:.2f}" if trade['profit'] > 0 else f"-${abs(trade['profit']):.2f}"
                color_indicator = "🟢" if trade['profit'] > 0 else "🔴"

                self.log_message(f"{i:2d}. {trade['entry_time'].strftime('%Y-%m-%d %H:%M')} | "
                               f"{trade['type'].upper()} | "
                               f"${trade['entry_price']:.2f} → ${trade['exit_price']:.2f} | "
                               f"{color_indicator} {profit_str} | "
                               f"{trade['exit_reason']}", 'backtest')

        except Exception as e:
            self.log_message(f"❌ Results calculation error: {str(e)}", 'backtest')

    def update_backtest_results(self, results):
        """Update backtest results in UI"""
        self.total_trades_label.config(text=str(results['total_trades']))
        self.win_rate_label.config(text=f"{results['win_rate']:.1f}%")

        profit = results['total_profit']
        if profit > 0:
            self.total_profit_label.config(text=f"+${profit:,.2f}", fg='#28a745')
        elif profit < 0:
            self.total_profit_label.config(text=f"-${abs(profit):,.2f}", fg='#dc3545')
        else:
            self.total_profit_label.config(text="$0.00", fg='#ffc107')

        self.max_drawdown_label.config(text=f"{results['max_drawdown']:.2f}%")

        avg_trade = results['avg_trade']
        if avg_trade > 0:
            self.avg_trade_label.config(text=f"+${avg_trade:.2f}", fg='#28a745')
        elif avg_trade < 0:
            self.avg_trade_label.config(text=f"-${abs(avg_trade):.2f}", fg='#dc3545')
        else:
            self.avg_trade_label.config(text="$0.00", fg='#ffc107')

        self.sharpe_ratio_label.config(text=f"{results['sharpe_ratio']:.2f}")

    def update_progress(self, value, text):
        """Update progress bar"""
        self.progress_bar['value'] = value

        # Enhanced progress text with stats
        if hasattr(self, 'live_backtest_stats') and self.live_backtest_stats['total_trades'] > 0:
            stats = self.live_backtest_stats
            enhanced_text = f"{text} | Trades: {stats['total_trades']} | Balance: ${stats['current_balance']:,.0f}"
            self.progress_label.config(text=enhanced_text)
        else:
            self.progress_label.config(text=text)

    def reset_backtest_results(self):
        """Reset backtest results display"""
        self.total_trades_label.config(text="0")
        self.win_rate_label.config(text="0%")
        self.total_profit_label.config(text="$0.00", fg='#ffc107')
        self.max_drawdown_label.config(text="0%")
        self.avg_trade_label.config(text="$0.00", fg='#ffc107')
        self.sharpe_ratio_label.config(text="0.00")
        self.progress_bar['value'] = 0
        self.progress_label.config(text="Ready to start")

        # Reset live stats
        self.live_backtest_stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_profit': 0.0,
            'current_balance': 10000.0,
            'max_balance': 10000.0,
            'min_balance': 10000.0,
            'current_drawdown': 0.0,
            'max_drawdown': 0.0
        }
        self.update_live_backtest_display()

    def update_live_backtest_display(self):
        """Update live backtest statistics display"""
        stats = self.live_backtest_stats

        # Update balance
        self.live_balance_label.config(text=f"${stats['current_balance']:,.2f}")

        # Update profit with color
        profit = stats['total_profit']
        if profit > 0:
            self.live_profit_label.config(text=f"+${profit:,.2f}", fg='#28a745')
        elif profit < 0:
            self.live_profit_label.config(text=f"-${abs(profit):,.2f}", fg='#dc3545')
        else:
            self.live_profit_label.config(text="$0.00", fg='#ffc107')

        # Update trades count
        self.live_trades_label.config(text=str(stats['total_trades']))

        # Update win rate
        if stats['total_trades'] > 0:
            win_rate = (stats['winning_trades'] / stats['total_trades']) * 100
            self.live_winrate_label.config(text=f"{win_rate:.1f}%")
        else:
            self.live_winrate_label.config(text="0%")

        # Update drawdown
        self.live_drawdown_label.config(text=f"{stats['current_drawdown']:.1f}%")

    def backtest_completed(self):
        """Handle backtest completion"""
        self.backtest_btn.config(state='normal')
        self.stop_btn.config(state='disabled')

    def update_data(self):
        """Update data periodically"""
        if self.is_connected and not self.is_trading and not self.is_backtesting:
            self.update_account_info()

        # Schedule next update
        self.root.after(5000, self.update_data)  # Update every 5 seconds

    def show_learning_stats(self):
        """عرض إحصائيات نظام التعلم"""
        try:
            # إنشاء نافذة جديدة
            stats_window = tk.Toplevel(self.root)
            stats_window.title("🧠 إحصائيات نظام التعلم")
            stats_window.geometry("600x500")
            stats_window.configure(bg='#2b2b2b')

            # عنوان
            title_label = tk.Label(stats_window, text="🧠 إحصائيات نظام التعلم المتقدم",
                                  font=('Arial', 16, 'bold'), fg='#00ff88', bg='#2b2b2b')
            title_label.pack(pady=10)

            # إطار التمرير
            canvas = tk.Canvas(stats_window, bg='#2b2b2b')
            scrollbar = ttk.Scrollbar(stats_window, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg='#2b2b2b')

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # الحصول على الإحصائيات
            memory_stats = self.learning_system.get_memory_stats()
            insights = self.learning_system.get_learning_insights()
            recommendations = self.learning_system.generate_strategy_recommendations()

            # عرض الإحصائيات العامة
            stats_frame = tk.Frame(scrollable_frame, bg='#343a40', relief=tk.RAISED, bd=2)
            stats_frame.pack(fill=tk.X, padx=10, pady=5)

            tk.Label(stats_frame, text="📊 الإحصائيات العامة",
                    font=('Arial', 14, 'bold'), fg='white', bg='#343a40').pack(pady=5)

            stats_text = f"""📈 إجمالي الصفقات: {memory_stats['total_trades']}
✅ أنماط النجاح: {memory_stats['success_patterns']}
❌ أنماط الأخطاء: {memory_stats['mistake_patterns']}
📊 أنماط السوق: {memory_stats['market_patterns']}
🔄 صفقات الجلسة الحالية: {memory_stats['current_session_trades']}"""

            tk.Label(stats_frame, text=stats_text,
                    font=('Arial', 10), fg='white', bg='#343a40', justify=tk.LEFT).pack(pady=5)

            # عرض الرؤى
            if insights:
                insights_frame = tk.Frame(scrollable_frame, bg='#495057', relief=tk.RAISED, bd=2)
                insights_frame.pack(fill=tk.X, padx=10, pady=5)

                tk.Label(insights_frame, text="💡 رؤى التعلم",
                        font=('Arial', 14, 'bold'), fg='white', bg='#495057').pack(pady=5)

                for insight in insights:
                    tk.Label(insights_frame, text=insight,
                            font=('Arial', 10), fg='white', bg='#495057',
                            wraplength=550, justify=tk.LEFT).pack(pady=2, padx=10)

            # عرض التوصيات
            if recommendations:
                rec_frame = tk.Frame(scrollable_frame, bg='#6c757d', relief=tk.RAISED, bd=2)
                rec_frame.pack(fill=tk.X, padx=10, pady=5)

                tk.Label(rec_frame, text="🎯 التوصيات الاستراتيجية",
                        font=('Arial', 14, 'bold'), fg='white', bg='#6c757d').pack(pady=5)

                for rec in recommendations:
                    tk.Label(rec_frame, text=rec,
                            font=('Arial', 10), fg='white', bg='#6c757d',
                            wraplength=550, justify=tk.LEFT).pack(pady=2, padx=10)

            # أزرار التحكم
            button_frame = tk.Frame(scrollable_frame, bg='#2b2b2b')
            button_frame.pack(fill=tk.X, padx=10, pady=10)

            tk.Button(button_frame, text="🔄 تحديث",
                     command=lambda: self.refresh_learning_stats(stats_window),
                     bg='#17a2b8', fg='white', font=('Arial', 10, 'bold')).pack(side=tk.LEFT, padx=5)

            tk.Button(button_frame, text="💾 حفظ الذاكرة",
                     command=self.save_learning_memory,
                     bg='#28a745', fg='white', font=('Arial', 10, 'bold')).pack(side=tk.LEFT, padx=5)

            tk.Button(button_frame, text="🗑️ إعادة تعيين الجلسة",
                     command=self.reset_learning_session,
                     bg='#ffc107', fg='black', font=('Arial', 10, 'bold')).pack(side=tk.LEFT, padx=5)

            tk.Button(button_frame, text="🔄 إعادة تعيين الذاكرة",
                     command=self.reset_learning_memory,
                     bg='#dc3545', fg='white', font=('Arial', 10, 'bold')).pack(side=tk.LEFT, padx=5)

            # تعبئة الإطار
            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض إحصائيات التعلم: {str(e)}")

    def refresh_learning_stats(self, window):
        """تحديث نافذة إحصائيات التعلم"""
        window.destroy()
        self.show_learning_stats()

    def save_learning_memory(self):
        """حفظ ذاكرة التعلم"""
        try:
            self.learning_system.save_memory()
            messagebox.showinfo("نجح", "تم حفظ ذاكرة التعلم بنجاح!")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الذاكرة: {str(e)}")

    def reset_learning_session(self):
        """إعادة تعيين جلسة التعلم الحالية"""
        try:
            result = messagebox.askyesno("تأكيد", "هل تريد إعادة تعيين جلسة التعلم الحالية؟")
            if result:
                self.learning_system.reset_session()
                self.current_session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
                messagebox.showinfo("نجح", "تم إعادة تعيين جلسة التعلم!")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إعادة التعيين: {str(e)}")

    def reset_learning_memory(self):
        """إعادة تعيين ذاكرة التعلم بالكامل"""
        try:
            result = messagebox.askyesno(
                "تحذير",
                "هل تريد إعادة تعيين ذاكرة التعلم بالكامل؟\n"
                "سيتم حذف جميع الأنماط المتعلمة والأخطاء المسجلة.\n"
                "هذا الإجراء لا يمكن التراجع عنه!"
            )
            if result:
                # إعادة تعيين الذاكرة
                self.learning_system.memory = {
                    'trades': [],
                    'patterns': {},
                    'mistakes': {},
                    'successes': {},
                    'strategies': {},
                    'confidence_adjustments': {},
                    'last_updated': datetime.now().isoformat()
                }

                # حفظ الذاكرة الجديدة
                self.learning_system.save_memory()

                # إعادة تعيين الجلسة
                self.current_session_id = datetime.now().strftime("%Y%m%d_%H%M%S")

                messagebox.showinfo("نجح", "تم إعادة تعيين ذاكرة التعلم بالكامل!\nالنظام سيبدأ التعلم من جديد.")

                # تسجيل الحدث
                self.log_message("🔄 تم إعادة تعيين ذاكرة التعلم - بداية جديدة", 'info')

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إعادة تعيين الذاكرة: {str(e)}")

    def run(self):
        """Run the application"""
        self.root.mainloop()

if __name__ == "__main__":
    print("⚠️ Warning: TA-Lib library not available, using alternative indicators")
    print("Successfully imported file")

    app = AdvancedTradingGUI()
    app.run()
