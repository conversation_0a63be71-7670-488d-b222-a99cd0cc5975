@echo off
chcp 65001 >nul
echo ═══════════════════════════════════════════════════════════════
echo                    إصلاح مشكلة SimpleLoginForm
echo                    Fix SimpleLoginForm Issue
echo ═══════════════════════════════════════════════════════════════
echo.

echo 🔧 المشكلة: SimpleLoginForm غير موجود في المشروع
echo 🔧 Issue: SimpleLoginForm not found in project
echo.
echo ✅ الحل المطبق:
echo ✅ Applied solution:
echo ───────────────────────────────────────────────────────────────
echo 1. تم إضافة SimpleLoginForm.cs إلى ملف المشروع
echo 1. Added SimpleLoginForm.cs to project file
echo 2. تم إضافة SimpleLoginForm.Designer.cs إلى ملف المشروع
echo 2. Added SimpleLoginForm.Designer.cs to project file
echo 3. تم تحديث ملف .csproj
echo 3. Updated .csproj file
echo.

echo الخطوة 1: إعادة تحميل المشروع في Visual Studio
echo Step 1: Reload project in Visual Studio
echo ───────────────────────────────────────────────────────────────
echo.
echo 📋 تعليمات Visual Studio:
echo 📋 Visual Studio instructions:
echo.
echo 1. في Visual Studio، اضغط بالزر الأيمن على المشروع
echo 1. In Visual Studio, right-click on the project
echo.
echo 2. اختر "Reload Project" أو "إعادة تحميل المشروع"
echo 2. Choose "Reload Project"
echo.
echo 3. أو أغلق Visual Studio وأعد فتحه
echo 3. Or close Visual Studio and reopen it
echo.

echo الخطوة 2: تنظيف وإعادة بناء المشروع
echo Step 2: Clean and rebuild project
echo ───────────────────────────────────────────────────────────────

if exist "bin" (
    echo 🗑️ حذف مجلد bin...
    echo 🗑️ Deleting bin folder...
    rmdir /s /q "bin" >nul 2>&1
)

if exist "obj" (
    echo 🗑️ حذف مجلد obj...
    echo 🗑️ Deleting obj folder...
    rmdir /s /q "obj" >nul 2>&1
)

echo ✅ تم تنظيف المشروع
echo ✅ Project cleaned

echo.
echo الخطوة 3: بناء المشروع
echo Step 3: Building project
echo ───────────────────────────────────────────────────────────────

REM البحث عن MSBuild
set MSBUILD_PATH=""
if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
) else (
    echo ⚠️ MSBuild غير موجود، محاولة استخدام dotnet build
    echo ⚠️ MSBuild not found, trying dotnet build
    dotnet build "Pharmacy Management System.csproj" --configuration Debug --verbosity quiet
    goto CHECK_BUILD
)

echo 🔨 بناء المشروع...
echo 🔨 Building project...
%MSBUILD_PATH% "Pharmacy Management System.csproj" /p:Configuration=Debug /p:Platform="Any CPU" /nologo /verbosity:minimal

:CHECK_BUILD
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم بناء المشروع بنجاح!
    echo ✅ Project built successfully!
    echo.
    echo 🎯 تشغيل النظام...
    echo 🎯 Starting system...
    
    if exist "bin\Debug\Pharmacy Management System.exe" (
        start "" "bin\Debug\Pharmacy Management System.exe"
        echo.
        echo ✅ تم تشغيل النظام بنجاح!
        echo ✅ System started successfully!
        echo.
        echo 📋 معلومات تسجيل الدخول:
        echo 📋 Login credentials:
        echo • المدير: admin / admin123
        echo • Administrator: admin / admin123
        echo • الموظف: employee1 / emp123
        echo • Employee: employee1 / emp123
        echo.
        echo 🧪 اختبار الإصلاحات:
        echo 🧪 Testing fixes:
        echo ───────────────────────────────────────────────────────────────
        echo 1. ✅ يجب أن تظهر نافذة تسجيل دخول بسيطة
        echo 1. ✅ Simple login window should appear
        echo 2. ✅ لا توجد أخطاء SimpleLoginForm
        echo 2. ✅ No SimpleLoginForm errors
        echo 3. ✅ صفحة طلب الدواء تظهر كاملة
        echo 3. ✅ Medicine request page appears completely
    ) else (
        echo ❌ ملف التشغيل غير موجود
        echo ❌ Executable file not found
    )
) else (
    echo.
    echo ❌ فشل في بناء المشروع
    echo ❌ Project build failed
    echo.
    echo 🔍 خطوات استكشاف الأخطاء:
    echo 🔍 Troubleshooting steps:
    echo ───────────────────────────────────────────────────────────────
    echo 1. أعد تشغيل Visual Studio كمدير
    echo 1. Restart Visual Studio as administrator
    echo.
    echo 2. في Visual Studio:
    echo 2. In Visual Studio:
    echo    • Build → Clean Solution
    echo    • Build → Rebuild Solution
    echo.
    echo 3. تحقق من Error List في Visual Studio
    echo 3. Check Error List in Visual Studio
    echo.
    echo 4. تأكد من إعادة تحميل المشروع
    echo 4. Make sure to reload the project
)

echo.
echo ═══════════════════════════════════════════════════════════════
echo                        🎉 انتهى الإصلاح!
echo                     🎉 Fix Completed!
echo ═══════════════════════════════════════════════════════════════
echo.
echo ملاحظات مهمة:
echo Important notes:
echo • تم إضافة SimpleLoginForm إلى ملف المشروع
echo • SimpleLoginForm added to project file
echo • قد تحتاج لإعادة تحميل المشروع في Visual Studio
echo • You may need to reload project in Visual Studio
echo • استخدم Clean Solution ثم Rebuild Solution
echo • Use Clean Solution then Rebuild Solution
echo.
pause
