#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار أوضاع التنفيذ في MetaTrader 5
Test Filling Modes in MetaTrader 5
"""

import sys
import os
import configparser

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    import MetaTrader5 as mt5
    MT5_AVAILABLE = True
except ImportError:
    print("❌ مكتبة MetaTrader5 غير مثبتة")
    print("💡 قم بتثبيتها: pip install MetaTrader5")
    MT5_AVAILABLE = False
    sys.exit(1)

def load_config():
    """تحميل الإعدادات"""
    config = configparser.ConfigParser()
    config_file = 'config.ini'
    
    if os.path.exists(config_file):
        config.read(config_file)
        return config
    else:
        print(f"❌ ملف الإعدادات غير موجود: {config_file}")
        return None

def connect_to_mt5():
    """الاتصال بـ MetaTrader 5"""
    try:
        print("🔄 محاولة الاتصال بـ MetaTrader 5...")
        
        if not mt5.initialize():
            error = mt5.last_error()
            print(f"❌ فشل في تهيئة MT5: {error}")
            return False
        
        # تحميل الإعدادات
        config = load_config()
        if not config:
            return False
        
        # تسجيل الدخول
        login = int(config.get('MT5_CONNECTION', 'login', fallback='0'))
        password = config.get('MT5_CONNECTION', 'password', fallback='')
        server = config.get('MT5_CONNECTION', 'server', fallback='')
        
        print(f"🔐 محاولة تسجيل الدخول - حساب: {login}, خادم: {server}")
        
        if not mt5.login(login, password, server):
            error = mt5.last_error()
            print(f"❌ فشل في تسجيل الدخول: {error}")
            return False
        
        # التحقق من معلومات الحساب
        account_info = mt5.account_info()
        if not account_info:
            print("❌ لا يمكن الحصول على معلومات الحساب")
            return False
        
        print("✅ تم الاتصال بنجاح!")
        print(f"📊 حساب: {account_info.login}")
        print(f"💰 رصيد: ${account_info.balance:.2f}")
        print(f"🏢 شركة: {account_info.company}")
        print(f"🔒 التداول مسموح: {'✅' if account_info.trade_allowed else '❌'}")
        
        # فحص إعدادات الطرفية
        terminal_info = mt5.terminal_info()
        if terminal_info:
            print(f"🤖 التداول الآلي: {'✅' if terminal_info.trade_allowed else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def test_filling_modes(symbol):
    """اختبار أوضاع التنفيذ للرمز"""
    try:
        print(f"\n🔍 اختبار أوضاع التنفيذ للرمز: {symbol}")
        print("=" * 50)
        
        # الحصول على معلومات الرمز
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            print(f"❌ لا يمكن الحصول على معلومات الرمز {symbol}")
            return False
        
        filling_mode = symbol_info.filling_mode
        print(f"📊 قيمة وضع التنفيذ: {filling_mode} (binary: {bin(filling_mode)})")
        
        # اختبار كل وضع
        modes = [
            (1, "ORDER_FILLING_FOK", "Fill or Kill - تنفيذ كامل أو إلغاء"),
            (2, "ORDER_FILLING_IOC", "Immediate or Cancel - تنفيذ فوري أو إلغاء"),
            (4, "ORDER_FILLING_RETURN", "Return - إرجاع الباقي للسوق")
        ]
        
        supported_modes = []
        print("\n📋 نتائج الاختبار:")
        
        for bit, const_name, description in modes:
            if filling_mode & bit:
                print(f"   ✅ {const_name} - {description}")
                supported_modes.append((const_name, getattr(mt5, const_name)))
            else:
                print(f"   ❌ {const_name} - {description}")
        
        print(f"\n📈 إجمالي الأوضاع المدعومة: {len(supported_modes)}")
        
        if len(supported_modes) == 0:
            print("⚠️ تحذير: لا توجد أوضاع تنفيذ مدعومة!")
            return False
        
        # اختبار أمر تجريبي (بدون تنفيذ فعلي)
        print(f"\n🧪 اختبار أمر تجريبي للرمز {symbol}:")
        
        # الحصول على السعر الحالي
        tick = mt5.symbol_info_tick(symbol)
        if tick is None:
            print(f"❌ لا يمكن الحصول على سعر {symbol}")
            return False
        
        print(f"💹 السعر الحالي: Bid={tick.bid}, Ask={tick.ask}")
        
        # اختبار كل وضع مدعوم
        for const_name, filling_const in supported_modes:
            print(f"\n🔄 اختبار وضع {const_name}...")
            
            # إنشاء أمر تجريبي (حجم صغير جداً)
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": 0.01,  # أصغر حجم ممكن
                "type": mt5.ORDER_TYPE_BUY,
                "price": tick.ask,
                "type_filling": filling_const,
                "comment": f"Test {const_name}",
            }
            
            # فحص الأمر بدون تنفيذ
            result = mt5.order_check(request)
            if result:
                if result.retcode == mt5.TRADE_RETCODE_DONE:
                    print(f"   ✅ {const_name}: الأمر صالح للتنفيذ")
                else:
                    print(f"   ⚠️ {const_name}: {result.retcode} - {result.comment}")
            else:
                print(f"   ❌ {const_name}: فشل في فحص الأمر")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار أوضاع التنفيذ: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار أوضاع التنفيذ في MetaTrader 5")
    print("=" * 60)
    
    if not MT5_AVAILABLE:
        return
    
    # الاتصال بـ MT5
    if not connect_to_mt5():
        print("\n❌ فشل في الاتصال بـ MetaTrader 5")
        return
    
    # اختبار الرموز الشائعة
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD']
    
    for symbol in symbols:
        if not test_filling_modes(symbol):
            print(f"⚠️ فشل في اختبار {symbol}")
        print("\n" + "-" * 50)
    
    # إغلاق الاتصال
    mt5.shutdown()
    print("\n✅ تم الانتهاء من الاختبار")

if __name__ == "__main__":
    main()
