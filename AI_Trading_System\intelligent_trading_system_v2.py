#!/usr/bin/env python3
"""
Intelligent Trading System V2 - نظام التداول الذكي المتطور
نظام تداول متقدم مع تحليل ذكي وإدارة رأس المال والمخاطر
"""

import sys
import os
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# إضافة المسار الحالي للبحث عن الوحدات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    import MetaTrader5 as mt5
    MT5_AVAILABLE = True
except ImportError:
    MT5_AVAILABLE = False
    print("⚠️ MetaTrader5 library not available")

import pandas as pd
import numpy as np
import configparser

# استيراد الوحدات المطورة
try:
    from advanced_analysis_engine import AdvancedAnalysisEngine
    from smart_money_manager import SmartMoneyManager
    ADVANCED_MODULES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Advanced modules not available: {e}")
    ADVANCED_MODULES_AVAILABLE = False

class IntelligentTradingSystemV2:
    """
    نظام التداول الذكي المتطور - الإصدار الثاني
    """
    
    def __init__(self, config_file: str = 'config.ini', demo_mode: bool = False):
        """
        تهيئة النظام المتطور
        """
        self.config_file = config_file
        self.logger = self._setup_logger()

        # قراءة الإعدادات
        self.config = self._load_config()

        # إعدادات النظام
        self.symbol = self.config.get('TRADING_SETTINGS', 'symbol', fallback='EURUSD')
        self.min_balance = float(self.config.get('SAFETY', 'min_balance', fallback='10.0'))

        # إعدادات MT5
        self.mt5_config = {
            'server': self.config.get('MT5_CONNECTION', 'server', fallback='MetaQuotes-Demo'),
            'login': int(self.config.get('MT5_CONNECTION', 'login', fallback='0')),
            'password': self.config.get('MT5_CONNECTION', 'password', fallback=''),
            'timeout': int(self.config.get('MT5_CONNECTION', 'timeout', fallback='10'))
        }

        # تهيئة المكونات المتقدمة
        if ADVANCED_MODULES_AVAILABLE:
            self.analysis_engine = AdvancedAnalysisEngine(self.symbol, self.min_balance)
            self.money_manager = SmartMoneyManager(self.symbol, self.min_balance)
            self.logger.info("✅ تم تحميل المكونات المتقدمة")
        else:
            self.analysis_engine = None
            self.money_manager = None
            self.logger.warning("⚠️ المكونات المتقدمة غير متوفرة - سيتم استخدام النظام الأساسي")

        # حالة النظام
        self.is_connected = False
        self.is_trading = False
        self.last_analysis_time = None
        self.demo_mode = demo_mode  # وضع التداول: True = محاكاة، False = فعلي

        # رسالة الوضع
        mode_text = "🧪 وضع المحاكاة" if demo_mode else "🔥 وضع التداول الفعلي"
        self.logger.info(f"⚙️ تم تهيئة النظام في {mode_text}")

        if not demo_mode:
            self.logger.info("🚨 تحذير: النظام في وضع التداول الحقيقي!")
            self.logger.info("💰 سيتم تنفيذ صفقات فعلية على حسابك!")
        
        # إحصائيات التداول
        self.trading_stats = {
            'total_trades': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_profit': 0.0,
            'current_balance': 0.0,
            'max_drawdown': 0.0,
            'win_rate': 0.0,
            'start_time': datetime.now()
        }
    
    def _setup_logger(self) -> logging.Logger:
        """
        إعداد نظام السجلات
        """
        logger = logging.getLogger('IntelligentTradingV2')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # إنشاء مجلد السجلات
            os.makedirs('logs', exist_ok=True)
            
            # ملف السجل
            file_handler = logging.FileHandler(
                f'logs/intelligent_trading_v2_{datetime.now().strftime("%Y%m%d")}.log',
                encoding='utf-8'
            )
            
            # وحدة التحكم
            console_handler = logging.StreamHandler()
            
            # تنسيق السجلات
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            logger.addHandler(file_handler)
            logger.addHandler(console_handler)
        
        return logger

    def set_trading_mode(self, demo_mode: bool):
        """
        تحديد وضع التداول
        """
        self.demo_mode = demo_mode
        mode_text = "🧪 وضع المحاكاة" if demo_mode else "🔥 وضع التداول الفعلي"
        self.logger.info(f"⚙️ تم تغيير النظام إلى {mode_text}")

    def _load_config(self) -> configparser.ConfigParser:
        """
        تحميل ملف الإعدادات
        """
        config = configparser.ConfigParser()
        
        if os.path.exists(self.config_file):
            config.read(self.config_file)
            self.logger.info(f"✅ تم تحميل الإعدادات من {self.config_file}")
        else:
            # إنشاء إعدادات افتراضية
            self._create_default_config(config)
            self.logger.info("✅ تم إنشاء إعدادات افتراضية")
        
        return config
    
    def _create_default_config(self, config: configparser.ConfigParser):
        """
        إنشاء إعدادات افتراضية
        """
        # إعدادات MT5
        config.add_section('MT5_CONNECTION')
        config.set('MT5_CONNECTION', 'server', 'MetaQuotes-Demo')
        config.set('MT5_CONNECTION', 'login', '96406085')
        config.set('MT5_CONNECTION', 'password', 'D!2qKdJy')
        config.set('MT5_CONNECTION', 'timeout', '10')
        
        # إعدادات التداول
        config.add_section('TRADING_SETTINGS')
        config.set('TRADING_SETTINGS', 'symbol', 'EURUSD')
        config.set('TRADING_SETTINGS', 'lot_size', '0.01')
        config.set('TRADING_SETTINGS', 'sl_pips', '50')
        config.set('TRADING_SETTINGS', 'tp_pips', '100')
        config.set('TRADING_SETTINGS', 'max_positions', '3')
        
        # إعدادات الأمان
        config.add_section('SAFETY')
        config.set('SAFETY', 'demo_mode', 'true')
        config.set('SAFETY', 'min_balance', '10.0')
        config.set('SAFETY', 'max_daily_trades', '5')
        config.set('SAFETY', 'max_daily_loss', '50')
        
        # إعدادات التحليل الذكي
        config.add_section('INTELLIGENT_ANALYSIS')
        config.set('INTELLIGENT_ANALYSIS', 'min_confidence', '0.45')
        config.set('INTELLIGENT_ANALYSIS', 'max_risk_per_trade', '0.02')
        config.set('INTELLIGENT_ANALYSIS', 'analysis_interval', '300')
        config.set('INTELLIGENT_ANALYSIS', 'learning_enabled', 'true')
        
        # حفظ الإعدادات
        with open(self.config_file, 'w') as f:
            config.write(f)
    
    def connect_to_mt5(self) -> bool:
        """
        الاتصال بـ MetaTrader 5
        """
        try:
            if not MT5_AVAILABLE:
                self.logger.error("❌ مكتبة MetaTrader5 غير متوفرة")
                return False
            
            self.logger.info("🔄 محاولة الاتصال بـ MetaTrader 5...")
            
            # تهيئة MT5
            if not mt5.initialize():
                error = mt5.last_error()
                self.logger.error(f"❌ فشل في تهيئة MT5: {error}")
                return False
            
            # تسجيل الدخول
            if not mt5.login(
                login=self.mt5_config['login'],
                password=self.mt5_config['password'],
                server=self.mt5_config['server']
            ):
                error = mt5.last_error()
                self.logger.error(f"❌ فشل في تسجيل الدخول: {error}")
                return False
            
            # التحقق من معلومات الحساب
            account_info = mt5.account_info()
            if not account_info:
                self.logger.error("❌ لا يمكن الحصول على معلومات الحساب")
                return False
            
            # التحقق من صلاحيات التداول
            if not account_info.trade_allowed:
                self.logger.error("❌ التداول غير مسموح في الحساب")
                return False
            
            # التحقق من إعدادات الطرفية
            terminal_info = mt5.terminal_info()
            if not terminal_info or not terminal_info.trade_allowed:
                self.logger.error("❌ التداول الآلي معطل في الطرفية")
                return False
            
            # تحديث إحصائيات التداول
            self.trading_stats['current_balance'] = account_info.balance
            
            self.is_connected = True
            self.logger.info("✅ تم الاتصال بـ MetaTrader 5 بنجاح")
            self.logger.info(f"📊 الحساب: {account_info.login}")
            self.logger.info(f"💰 الرصيد: ${account_info.balance:.2f}")
            self.logger.info(f"🏢 الشركة: {account_info.company}")

            # اختبار أوضاع التنفيذ للرمز
            self.logger.info(f"🔍 فحص أوضاع التنفيذ للرمز {self.symbol}...")
            supported_modes = self.test_filling_modes(self.symbol)
            if supported_modes:
                self.logger.info(f"✅ تم العثور على {len(supported_modes)} وضع تنفيذ مدعوم")
            else:
                self.logger.warning("⚠️ لم يتم العثور على أوضاع تنفيذ مدعومة")

            return True
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في الاتصال بـ MT5: {e}")
            return False
    
    def analyze_market_intelligently(self) -> Optional[Dict]:
        """
        تحليل السوق بذكاء متقدم
        """
        try:
            if not self.analysis_engine:
                self.logger.warning("⚠️ محرك التحليل المتقدم غير متوفر")
                return None
            
            self.logger.info("🧠 بدء التحليل الذكي للسوق...")
            
            # الحصول على البيانات من إطارات زمنية متعددة
            multi_data = self.analysis_engine.get_multi_timeframe_data()
            
            if not multi_data:
                self.logger.error("❌ لا توجد بيانات للتحليل")
                return None
            
            # حساب المؤشرات المتقدمة لكل إطار زمني
            for timeframe, df in multi_data.items():
                multi_data[timeframe] = self.analysis_engine.calculate_advanced_indicators(df)
            
            # الحصول على الرصيد الحالي
            account_info = mt5.account_info()
            current_balance = account_info.balance if account_info else self.min_balance
            
            # اتخاذ قرار ذكي
            decision = self.analysis_engine.make_intelligent_decision(multi_data, current_balance)
            
            # تحديث وقت آخر تحليل
            self.last_analysis_time = datetime.now()
            
            self.logger.info(f"🧠 نتيجة التحليل: {decision['decision']}")
            self.logger.info(f"🧠 مستوى الثقة: {decision['confidence']:.2%}")
            self.logger.info(f"🧠 السبب: {decision['reason']}")
            
            return decision
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في التحليل الذكي: {e}")
            return None
    
    def execute_intelligent_trade(self, decision: Dict) -> bool:
        """
        تنفيذ صفقة ذكية بناءً على القرار
        """
        try:
            if decision['decision'] == 'hold':
                self.logger.info("⏸️ قرار الانتظار - لا يتم تنفيذ صفقة")
                return True

            # التحقق من وضع التداول
            if self.demo_mode:
                return self._execute_demo_trade(decision)
            else:
                return self._execute_real_trade(decision)
            
            # التحقق من إمكانية فتح صفقة باستخدام مدير رأس المال
            if self.money_manager:
                can_trade_check = self.money_manager.can_open_position(
                    current_balance, 
                    decision.get('risk_amount', current_balance * 0.02)
                )
                
                if not can_trade_check['can_open']:
                    self.logger.warning(f"❌ لا يمكن فتح صفقة: {', '.join(can_trade_check['reasons'])}")
                    return False
            
            # الحصول على معلومات الرمز
            symbol_info = mt5.symbol_info(self.symbol)
            if not symbol_info:
                self.logger.error(f"❌ الرمز {self.symbol} غير متوفر")
                return False
            
            # تحديد نوع الأمر
            order_type = mt5.ORDER_TYPE_BUY if decision['decision'] == 'buy' else mt5.ORDER_TYPE_SELL
            
            # الحصول على السعر الحالي
            tick = mt5.symbol_info_tick(self.symbol)
            if not tick:
                self.logger.error("❌ لا يمكن الحصول على السعر الحالي")
                return False
            
            price = tick.ask if decision['decision'] == 'buy' else tick.bid
            
            # إعداد طلب التداول
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": self.symbol,
                "volume": decision['position_size'],
                "type": order_type,
                "price": price,
                "sl": decision['stop_loss'],
                "tp": decision['take_profit'],
                "deviation": 20,
                "magic": 234000,
                "comment": f"Intelligent Trade - Confidence: {decision['confidence']:.2%}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": self._get_filling_mode(self.symbol),
            }
            
            self.logger.info(f"🚀 تنفيذ صفقة {decision['decision'].upper()}:")
            self.logger.info(f"   الحجم: {decision['position_size']}")
            self.logger.info(f"   السعر: {price}")
            self.logger.info(f"   إيقاف الخسارة: {decision['stop_loss']}")
            self.logger.info(f"   جني الربح: {decision['take_profit']}")
            
            # تنفيذ الأمر مع إعادة المحاولة بأوضاع مختلفة
            result = self._execute_order_with_retry(request)

            if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                self.logger.info("🎉 تم تنفيذ الصفقة بنجاح!")
                self.logger.info(f"   رقم الأمر: {result.order}")
                self.logger.info(f"   رقم الصفقة: {result.deal}")
                self.logger.info(f"   الحجم المنفذ: {result.volume}")
                self.logger.info(f"   السعر المنفذ: {result.price}")

                # تسجيل الصفقة في مدير رأس المال
                if self.money_manager:
                    trade_info = {
                        'symbol': self.symbol,
                        'type': decision['decision'],
                        'volume': result.volume,
                        'risk_amount': decision.get('risk_amount', 0),
                        'margin_required': decision.get('margin_required', 0),
                        'confidence': decision['confidence'],
                        'balance': current_balance
                    }
                    self.money_manager.log_trade(trade_info)

                # تحديث الإحصائيات
                self.trading_stats['total_trades'] += 1

                return True

            else:
                error_msg = f"فشل في تنفيذ الصفقة نهائياً"
                if result:
                    error_msg += f": {result.retcode} - {result.comment}"

                self.logger.error(f"❌ {error_msg}")
                return False
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في تنفيذ الصفقة الذكية: {e}")
            return False

    def _execute_demo_trade(self, decision: Dict) -> bool:
        """
        تنفيذ صفقة تجريبية (محاكاة)
        """
        try:
            self.logger.info(f"🧪 محاكاة صفقة {decision['decision']} - ثقة: {decision['confidence']:.2%}")

            # محاكاة تأخير التنفيذ
            import time
            time.sleep(0.5)

            # محاكاة نجاح الصفقة (85% نجاح)
            import random
            success = random.random() < 0.85

            if success:
                self.logger.info("🎉 تم تنفيذ الصفقة التجريبية بنجاح!")
                self.logger.info(f"   نوع الصفقة: {decision['decision']}")
                self.logger.info(f"   حجم الصفقة: {decision['position_size']}")
                self.logger.info(f"   وقف الخسارة: {decision['stop_loss']}")
                self.logger.info(f"   جني الربح: {decision['take_profit']}")

                # تحديث الإحصائيات
                self.trading_stats['total_trades'] += 1
                self.trading_stats['successful_trades'] += 1

                return True
            else:
                self.logger.warning("⚠️ فشلت الصفقة التجريبية")
                self.trading_stats['total_trades'] += 1
                self.trading_stats['failed_trades'] += 1
                return False

        except Exception as e:
            self.logger.error(f"❌ خطأ في تنفيذ الصفقة التجريبية: {e}")
            return False

    def _execute_real_trade(self, decision: Dict) -> bool:
        """
        تنفيذ صفقة حقيقية في MetaTrader 5
        """
        try:
            if not self.is_connected:
                self.logger.error("❌ غير متصل بـ MT5")
                return False

            self.logger.info(f"🔥 تنفيذ صفقة حقيقية {decision['decision']} - ثقة: {decision['confidence']:.2%}")

            # الحصول على معلومات الحساب
            account_info = mt5.account_info()
            if not account_info:
                self.logger.error("❌ لا يمكن الحصول على معلومات الحساب")
                return False

            current_balance = account_info.balance

            # التحقق من إمكانية فتح صفقة باستخدام مدير رأس المال
            if self.money_manager:
                can_trade_check = self.money_manager.can_open_position(
                    current_balance,
                    decision.get('risk_amount', current_balance * 0.02)
                )

                if not can_trade_check['can_open']:
                    self.logger.warning(f"❌ لا يمكن فتح صفقة: {', '.join(can_trade_check['reasons'])}")
                    return False

            # الحصول على معلومات الرمز
            symbol_info = mt5.symbol_info(self.symbol)
            if not symbol_info:
                self.logger.error(f"❌ الرمز {self.symbol} غير متوفر")
                return False

            # تحديد نوع الأمر
            order_type = mt5.ORDER_TYPE_BUY if decision['decision'] == 'buy' else mt5.ORDER_TYPE_SELL

            # الحصول على السعر الحالي
            tick = mt5.symbol_info_tick(self.symbol)
            if not tick:
                self.logger.error("❌ لا يمكن الحصول على السعر الحالي")
                return False

            price = tick.ask if decision['decision'] == 'buy' else tick.bid

            # إعداد طلب التداول
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": self.symbol,
                "volume": decision['position_size'],
                "type": order_type,
                "price": price,
                "sl": decision['stop_loss'],
                "tp": decision['take_profit'],
                "deviation": 20,
                "magic": 234000,
                "comment": f"Real Trade - Confidence: {decision['confidence']:.2%}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": self._get_filling_mode(self.symbol),
            }

            # تنفيذ الأمر مع إعادة المحاولة بأوضاع مختلفة
            result = self._execute_order_with_retry(request)

            if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                self.logger.info("🎉 تم تنفيذ الصفقة الحقيقية بنجاح!")
                self.logger.info(f"   رقم الأمر: {result.order}")
                self.logger.info(f"   رقم الصفقة: {result.deal}")
                self.logger.info(f"   الحجم المنفذ: {result.volume}")
                self.logger.info(f"   السعر المنفذ: {result.price}")

                # تسجيل الصفقة في مدير رأس المال
                if self.money_manager:
                    trade_info = {
                        'symbol': self.symbol,
                        'type': decision['decision'],
                        'volume': result.volume,
                        'risk_amount': decision.get('risk_amount', 0),
                        'margin_required': decision.get('margin_required', 0),
                        'confidence': decision['confidence'],
                        'balance': current_balance
                    }
                    self.money_manager.log_trade(trade_info)

                # تحديث الإحصائيات
                self.trading_stats['total_trades'] += 1
                self.trading_stats['successful_trades'] += 1

                return True

            else:
                error_msg = f"فشل في تنفيذ الصفقة الحقيقية نهائياً"
                if result:
                    error_msg += f": {result.retcode} - {result.comment}"

                self.logger.error(f"❌ {error_msg}")
                self.trading_stats['total_trades'] += 1
                self.trading_stats['failed_trades'] += 1
                return False

        except Exception as e:
            self.logger.error(f"❌ خطأ في تنفيذ الصفقة الحقيقية: {e}")
            return False

    def _execute_order_with_retry(self, request):
        """
        تنفيذ الأمر مع إعادة المحاولة بأوضاع تنفيذ مختلفة
        """
        # قائمة أوضاع التنفيذ للمحاولة
        filling_modes = [
            ("FOK", mt5.ORDER_FILLING_FOK),
            ("IOC", mt5.ORDER_FILLING_IOC),
            ("RETURN", mt5.ORDER_FILLING_RETURN)
        ]

        for mode_name, filling_mode in filling_modes:
            try:
                # تحديث وضع التنفيذ
                request["type_filling"] = filling_mode

                self.logger.info(f"🔄 محاولة تنفيذ الأمر بوضع {mode_name}...")

                # تنفيذ الأمر
                result = mt5.order_send(request)

                if result:
                    if result.retcode == mt5.TRADE_RETCODE_DONE:
                        self.logger.info(f"✅ نجح التنفيذ بوضع {mode_name}")
                        return result
                    elif result.retcode == 10030:  # Unsupported filling mode
                        self.logger.warning(f"⚠️ وضع {mode_name} غير مدعوم، جاري المحاولة بوضع آخر...")
                        continue
                    else:
                        self.logger.warning(f"⚠️ فشل بوضع {mode_name}: {result.retcode} - {result.comment}")
                        # إذا كان الخطأ ليس متعلق بوضع التنفيذ، توقف
                        if result.retcode not in [10030, 10031]:
                            return result
                else:
                    self.logger.error(f"❌ لا يوجد نتيجة من الأمر بوضع {mode_name}")

            except Exception as e:
                self.logger.error(f"❌ خطأ في تنفيذ الأمر بوضع {mode_name}: {e}")
                continue

        # إذا فشلت جميع المحاولات
        self.logger.error("❌ فشل في تنفيذ الأمر بجميع أوضاع التنفيذ")
        return None

    def _get_filling_mode(self, symbol: str):
        """
        الحصول على وضع التنفيذ المناسب للرمز مع اختبار جميع الأوضاع
        """
        try:
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                self.logger.warning(f"⚠️ لا يمكن الحصول على معلومات الرمز {symbol}")
                return mt5.ORDER_FILLING_FOK

            filling_mode = symbol_info.filling_mode
            self.logger.info(f"📊 أوضاع التنفيذ المدعومة للرمز {symbol}: {filling_mode}")

            # اختبار الأوضاع بالترتيب الأفضل
            if filling_mode & 1:  # ORDER_FILLING_FOK (Fill or Kill)
                self.logger.info("✅ استخدام وضع FOK (Fill or Kill)")
                return mt5.ORDER_FILLING_FOK
            elif filling_mode & 2:  # ORDER_FILLING_IOC (Immediate or Cancel)
                self.logger.info("✅ استخدام وضع IOC (Immediate or Cancel)")
                return mt5.ORDER_FILLING_IOC
            elif filling_mode & 4:  # ORDER_FILLING_RETURN (Return)
                self.logger.info("✅ استخدام وضع RETURN")
                return mt5.ORDER_FILLING_RETURN
            else:
                # إذا لم يكن هناك وضع مدعوم، جرب FOK كافتراضي
                self.logger.warning("⚠️ لا يوجد وضع تنفيذ واضح، استخدام FOK كافتراضي")
                return mt5.ORDER_FILLING_FOK

        except Exception as e:
            self.logger.error(f"❌ خطأ في تحديد وضع التنفيذ: {e}")
            return mt5.ORDER_FILLING_FOK

    def test_filling_modes(self, symbol: str):
        """
        اختبار جميع أوضاع التنفيذ المتاحة للرمز
        """
        try:
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                self.logger.error(f"❌ لا يمكن الحصول على معلومات الرمز {symbol}")
                return None

            filling_mode = symbol_info.filling_mode
            self.logger.info(f"🔍 اختبار أوضاع التنفيذ للرمز {symbol}:")
            self.logger.info(f"   Filling Mode Value: {filling_mode}")

            # اختبار كل وضع
            modes = [
                (1, "ORDER_FILLING_FOK", mt5.ORDER_FILLING_FOK),
                (2, "ORDER_FILLING_IOC", mt5.ORDER_FILLING_IOC),
                (4, "ORDER_FILLING_RETURN", mt5.ORDER_FILLING_RETURN)
            ]

            supported_modes = []
            for bit, name, mode_const in modes:
                if filling_mode & bit:
                    supported_modes.append((name, mode_const))
                    self.logger.info(f"   ✅ {name} مدعوم")
                else:
                    self.logger.info(f"   ❌ {name} غير مدعوم")

            return supported_modes

        except Exception as e:
            self.logger.error(f"❌ خطأ في اختبار أوضاع التنفيذ: {e}")
            return None

    def run_intelligent_trading_cycle(self) -> bool:
        """
        تشغيل دورة تداول ذكية واحدة
        """
        try:
            self.logger.info("🔄 بدء دورة تداول ذكية...")

            # التحقق من الاتصال
            if not self.is_connected:
                if not self.connect_to_mt5():
                    return False

            # تحليل السوق بذكاء
            decision = self.analyze_market_intelligently()
            if not decision:
                self.logger.warning("⚠️ لم يتم الحصول على قرار تداول")
                return False

            # تنفيذ الصفقة إذا كان القرار مناسباً
            if decision['decision'] != 'hold':
                success = self.execute_intelligent_trade(decision)

                # التعلم من النتيجة
                if self.analysis_engine and hasattr(self.analysis_engine, 'learn_from_results'):
                    # محاكاة نتيجة الصفقة للتعلم (في التطبيق الحقيقي، ستأتي من MT5)
                    mock_result = {'profit': 10.0 if success else -5.0}
                    self.analysis_engine.learn_from_results(mock_result, decision)

                return success

            return True

        except Exception as e:
            self.logger.error(f"❌ خطأ في دورة التداول الذكية: {e}")
            return False

    def run_continuous_intelligent_trading(self, duration_hours: float = 1.0,
                                         analysis_interval: int = 300) -> Dict:
        """
        تشغيل التداول الذكي المستمر
        """
        try:
            self.logger.info(f"🚀 بدء التداول الذكي المستمر لمدة {duration_hours} ساعة")
            self.logger.info(f"⏱️ فترة التحليل: {analysis_interval} ثانية")

            start_time = time.time()
            end_time = start_time + (duration_hours * 3600)

            cycle_count = 0
            successful_cycles = 0

            self.is_trading = True

            while time.time() < end_time and self.is_trading:
                cycle_start = time.time()

                self.logger.info(f"🔄 الدورة #{cycle_count + 1}")

                # تشغيل دورة تداول ذكية
                success = self.run_intelligent_trading_cycle()

                if success:
                    successful_cycles += 1

                cycle_count += 1

                # عرض تقرير المخاطر كل 5 دورات
                if cycle_count % 5 == 0 and self.money_manager:
                    account_info = mt5.account_info()
                    if account_info:
                        risk_report = self.money_manager.get_risk_report(account_info.balance)
                        self.logger.info(f"📊 تقرير المخاطر: {risk_report['risk_status']}")
                        self.logger.info(f"📊 المخاطر اليومية: {risk_report['daily_risk']['used']:.1f}%")

                # انتظار حتى الدورة التالية
                cycle_duration = time.time() - cycle_start
                sleep_time = max(0, analysis_interval - cycle_duration)

                if sleep_time > 0:
                    self.logger.info(f"⏳ انتظار {sleep_time:.1f} ثانية حتى الدورة التالية...")
                    time.sleep(sleep_time)

            self.is_trading = False

            # إحصائيات النهائية
            total_duration = time.time() - start_time
            success_rate = (successful_cycles / cycle_count * 100) if cycle_count > 0 else 0

            result = {
                'total_cycles': cycle_count,
                'successful_cycles': successful_cycles,
                'success_rate': success_rate,
                'total_duration': total_duration,
                'average_cycle_time': total_duration / cycle_count if cycle_count > 0 else 0,
                'trading_stats': self.trading_stats.copy()
            }

            self.logger.info(f"🏁 انتهاء التداول المستمر:")
            self.logger.info(f"   إجمالي الدورات: {cycle_count}")
            self.logger.info(f"   الدورات الناجحة: {successful_cycles}")
            self.logger.info(f"   معدل النجاح: {success_rate:.1f}%")
            self.logger.info(f"   المدة الإجمالية: {total_duration/60:.1f} دقيقة")

            return result

        except KeyboardInterrupt:
            self.logger.info("⏹️ تم إيقاف التداول بواسطة المستخدم")
            self.is_trading = False
            return {'stopped_by_user': True}

        except Exception as e:
            self.logger.error(f"❌ خطأ في التداول المستمر: {e}")
            self.is_trading = False
            return {'error': str(e)}

    def get_system_status(self) -> Dict:
        """
        الحصول على حالة النظام
        """
        try:
            status = {
                'timestamp': datetime.now(),
                'is_connected': self.is_connected,
                'is_trading': self.is_trading,
                'last_analysis_time': self.last_analysis_time,
                'symbol': self.symbol,
                'min_balance': self.min_balance,
                'advanced_modules_available': ADVANCED_MODULES_AVAILABLE,
                'mt5_available': MT5_AVAILABLE,
                'trading_stats': self.trading_stats.copy()
            }

            # إضافة معلومات الحساب إذا كان متصلاً
            if self.is_connected and MT5_AVAILABLE:
                account_info = mt5.account_info()
                if account_info:
                    status['account_info'] = {
                        'login': account_info.login,
                        'balance': account_info.balance,
                        'equity': account_info.equity,
                        'margin': account_info.margin,
                        'free_margin': account_info.margin_free,
                        'margin_level': account_info.margin_level,
                        'company': account_info.company,
                        'trade_allowed': account_info.trade_allowed
                    }

            # إضافة تقرير المخاطر
            if self.money_manager and self.is_connected:
                account_info = mt5.account_info()
                if account_info:
                    status['risk_report'] = self.money_manager.get_risk_report(account_info.balance)

            return status

        except Exception as e:
            self.logger.error(f"❌ خطأ في الحصول على حالة النظام: {e}")
            return {
                'timestamp': datetime.now(),
                'error': str(e),
                'is_connected': False,
                'is_trading': False
            }

    def stop_trading(self):
        """
        إيقاف التداول
        """
        self.is_trading = False
        self.logger.info("⏹️ تم إيقاف التداول")

    def disconnect(self):
        """
        قطع الاتصال وتنظيف الموارد
        """
        try:
            self.is_trading = False
            self.is_connected = False

            if MT5_AVAILABLE:
                mt5.shutdown()

            self.logger.info("👋 تم قطع الاتصال وتنظيف الموارد")

        except Exception as e:
            self.logger.error(f"❌ خطأ في قطع الاتصال: {e}")

    def run_backtest(self, days: int = 30) -> Optional[Dict]:
        """
        تشغيل اختبار تاريخي للاستراتيجية
        """
        try:
            if not self.analysis_engine:
                self.logger.error("❌ محرك التحليل غير متوفر للاختبار التاريخي")
                return None

            self.logger.info(f"🧪 بدء الاختبار التاريخي لآخر {days} يوم...")

            # الحصول على البيانات التاريخية
            if not MT5_AVAILABLE:
                self.logger.error("❌ MT5 غير متوفر للحصول على البيانات التاريخية")
                return None

            # الحصول على البيانات
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)

            rates = mt5.copy_rates_range(self.symbol, mt5.TIMEFRAME_H1, start_time, end_time)

            if rates is None or len(rates) < 100:
                self.logger.error("❌ بيانات تاريخية غير كافية")
                return None

            # تحويل إلى DataFrame
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            df.set_index('time', inplace=True)
            df.columns = ['Open', 'High', 'Low', 'Close', 'Volume', 'Spread', 'Real_Volume']

            # تشغيل الاختبار التاريخي
            backtest_result = self.analysis_engine.backtest_strategy(df, initial_balance=1000.0)

            if 'error' in backtest_result:
                self.logger.error(f"❌ خطأ في الاختبار التاريخي: {backtest_result['error']}")
                return None

            self.logger.info("🧪 انتهاء الاختبار التاريخي:")
            self.logger.info(f"   العائد الإجمالي: {backtest_result['total_return']:.2f}%")
            self.logger.info(f"   إجمالي الصفقات: {backtest_result['total_trades']}")
            self.logger.info(f"   معدل الفوز: {backtest_result['statistics']['win_rate']:.2f}%")

            return backtest_result

        except Exception as e:
            self.logger.error(f"❌ خطأ في الاختبار التاريخي: {e}")
            return None

def main():
    """
    الدالة الرئيسية لتشغيل النظام
    """
    print("🚀 نظام التداول الذكي المتطور - الإصدار الثاني")
    print("="*60)

    # إنشاء النظام
    system = IntelligentTradingSystemV2()

    try:
        # الاتصال بـ MT5
        if not system.connect_to_mt5():
            print("❌ فشل في الاتصال بـ MetaTrader 5")
            return

        # عرض حالة النظام
        status = system.get_system_status()
        print(f"✅ النظام جاهز للتداول")
        print(f"📊 الرمز: {status['symbol']}")
        print(f"💰 الرصيد: ${status.get('account_info', {}).get('balance', 0):.2f}")

        # تشغيل اختبار تاريخي سريع
        print("\n🧪 تشغيل اختبار تاريخي سريع...")
        backtest_result = system.run_backtest(days=7)

        if backtest_result:
            print(f"📈 نتائج الاختبار التاريخي (7 أيام):")
            print(f"   العائد: {backtest_result['total_return']:.2f}%")
            print(f"   الصفقات: {backtest_result['total_trades']}")

        # تشغيل دورة تداول واحدة
        print("\n🔄 تشغيل دورة تداول ذكية واحدة...")
        success = system.run_intelligent_trading_cycle()

        if success:
            print("✅ تمت الدورة بنجاح")
        else:
            print("⚠️ لم يتم تنفيذ صفقة في هذه الدورة")

        # عرض تقرير المخاطر النهائي
        final_status = system.get_system_status()
        if 'risk_report' in final_status:
            risk_report = final_status['risk_report']
            print(f"\n📊 تقرير المخاطر النهائي:")
            print(f"   حالة المخاطر: {risk_report['risk_status']}")
            print(f"   المخاطر اليومية: {risk_report['daily_risk']['used']:.1f}%")
            print(f"   نوع الحساب: {risk_report['account_type']}")

    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف النظام بواسطة المستخدم")

    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")

    finally:
        # تنظيف الموارد
        system.disconnect()
        print("👋 تم إغلاق النظام")

if __name__ == "__main__":
    main()
