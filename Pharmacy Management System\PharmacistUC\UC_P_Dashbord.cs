﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy_Management_System.PharmacistUC
{
    public partial class UC_P_Dashbord : UserControl
    {
        Function fn = new Function();
        String query;
        DataSet ds;
        Int64 count;
        
        public UC_P_Dashbord()
        {
            InitializeComponent();

            // الاشتراك في حدث تغيير اللغة
            LanguageManager.LanguageChanged += OnLanguageChanged;
        }

        private void chart1_Click(object sender, EventArgs e)
        {

        }

        private void UC_P_Dashbord_Load(object sender, EventArgs e)
        {
            try
            {
                // تحميل الرسم البياني أولاً
                loadChart();

                // ثم تطبيق اللغة الحالية
                ApplyLanguage();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine(string.Format("خطأ في تحميل Dashboard: {0}", ex.Message));
            }
        }

        public void loadChart()
        {
            // استخدام الأسماء الإنجليزية الأصلية للسلاسل (كما تم إنشاؤها في Designer)
            string chartTitle = LanguageManager.GetText("Medicine Validity Chart");

            query = "select count (mname) from medic where eDate >=getdate()";
            ds = fn.getData(query);
            count = Int64.Parse(ds.Tables[0].Rows[0][0].ToString());
            this.chart1.Series["Valid Medicines"].Points.AddXY(chartTitle, count);

            query = "select count(mname) from medic where eDate <= getdate()";
            ds = fn.getData(query);
            count = Int64.Parse(ds.Tables[0].Rows[0][0].ToString());
            this.chart1.Series["Expired Medicines"].Points.AddXY(chartTitle, count);

            query = "SELECT COUNT(mname) FROM medic WHERE quantity < 10";
            ds = fn.getData(query);
            count = Int64.Parse(ds.Tables[0].Rows[0][0].ToString());
            this.chart1.Series["Low Stock Medicines"].Points.AddXY(chartTitle, count);

            // استعلام لعرض عدد الأدوية التي مدة نفاذها أقل من 6 أشهر
            query = "SELECT COUNT(mname) FROM medic WHERE eDate > GETDATE() AND eDate <= DATEADD(MONTH, 6, GETDATE())";
            ds = fn.getData(query);
            count = Int64.Parse(ds.Tables[0].Rows[0][0].ToString());
            this.chart1.Series["Medications that will expire"].Points.AddXY(chartTitle, count);
        }

        private void btnReload_Click(object sender, EventArgs e)
        {
            // استخدام الأسماء الإنجليزية الأصلية للسلاسل
            chart1.Series["Valid Medicines"].Points.Clear();
            chart1.Series["Expired Medicines"].Points.Clear();
            chart1.Series["Low Stock Medicines"].Points.Clear();
            chart1.Series["Medications that will expire"].Points.Clear();
            loadChart();
        }

        private void OnLanguageChanged(object sender, EventArgs e)
        {
            ApplyLanguage();
        }

        public void ApplyLanguage()
        {
            // تحديث النصوص حسب اللغة المختارة
            if (label1 != null) label1.Text = LanguageManager.GetText("Dashboard");
            if (btnReload != null) btnReload.Text = LanguageManager.GetText("Reload");

            // تحديث أسماء السلاسل في الرسم البياني للعرض
            UpdateChartSeriesNames();

            // إعادة تحميل الرسم البياني لتطبيق الترجمة
            btnReload_Click(null, null);

            // الحفاظ على التصميم من اليسار لليمين في جميع اللغات
            this.RightToLeft = RightToLeft.No;
        }

        private void UpdateChartSeriesNames()
        {
            try
            {
                // تحديث أسماء السلاسل للعرض (Legend) فقط
                if (chart1.Series.Count >= 4)
                {
                    chart1.Series["Valid Medicines"].LegendText = LanguageManager.GetText("Valid Medicines");
                    chart1.Series["Expired Medicines"].LegendText = LanguageManager.GetText("Expired Medicines");
                    chart1.Series["Low Stock Medicines"].LegendText = LanguageManager.GetText("Low Stock Medicines");
                    chart1.Series["Medications that will expire"].LegendText = LanguageManager.GetText("Medications that will expire");
                }
            }
            catch (Exception ex)
            {
                // تجاهل الأخطاء في تحديث أسماء السلاسل
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث أسماء السلاسل: {ex.Message}");
            }
        }

        protected override void OnHandleDestroyed(EventArgs e)
        {
            // إلغاء الاشتراك في أحداث التغيير
            LanguageManager.LanguageChanged -= OnLanguageChanged;
            base.OnHandleDestroyed(e);
        }

        private void panel1_Paint(object sender, PaintEventArgs e)
        {

        }
    }
}
