@echo off
echo Building Pharmacy Management System...
cd "C:\Users\<USER>\source\repos\Pharmacy Management System"

echo Trying MSBuild 2022...
if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" "Pharmacy Management System.sln" /p:Configuration=Debug /p:Platform="Any CPU" /verbosity:minimal
    goto :check_result
)

echo Trying MSBuild 2019...
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" "Pharmacy Management System.sln" /p:Configuration=Debug /p:Platform="Any CPU" /verbosity:minimal
    goto :check_result
)

echo Trying dotnet build...
dotnet build "Pharmacy Management System.sln" --configuration Debug --verbosity minimal

:check_result
if %ERRORLEVEL% EQU 0 (
    echo Build successful!
    echo Starting application...
    start "" "Pharmacy Management System\bin\Debug\Pharmacy Management System.exe"
) else (
    echo Build failed with error code %ERRORLEVEL%
    echo Please check the error messages above.
)
pause
