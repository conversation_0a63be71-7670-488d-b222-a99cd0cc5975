@echo off
echo ========================================
echo   إصلاح مشاكل قاعدة البيانات الأونلاين
echo   Fix Online Database Issues NOW
echo ========================================
echo.

echo 🔧 إصلاح المشاكل التالية:
echo - حذف الجدول المكرر network_medicines
echo - إصلاح المراجع في inter_pharmacy_orders
echo - إنشاء الإجراء المخزن sp_RegisterPharmacy
echo - إنشاء باقي الإجراءات المخزنة المطلوبة
echo.

echo 🚀 بدء الإصلاح...
echo.

sqlcmd -S NARUTO -E -i fix_online_database_issues.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo   ✅ تم إصلاح جميع المشاكل بنجاح!
    echo ========================================
    echo.
    echo المشاكل التي تم حلها:
    echo ✅ حذف الجدول المكرر network_medicines
    echo ✅ إصلاح المراجع في inter_pharmacy_orders
    echo ✅ إنشاء إجراء sp_RegisterPharmacy
    echo ✅ إنشاء إجراء sp_SearchMedicines
    echo ✅ إنشاء إجراء sp_GetActivePharmacies
    echo ✅ إنشاء الفهارس المطلوبة
    echo.
    echo 🎯 النتيجة:
    echo - تسجيل الصيدليات سيعمل الآن بدون أخطاء
    echo - النظام الأونلاين جاهز للاستخدام
    echo - جميع الميزات متاحة
    echo.
    echo 🧪 اختبر الآن:
    echo 1. شغل البرنامج
    echo 2. اذهب لصفحة الموظف
    echo 3. اضغط "الشبكة الأونلاين"
    echo 4. اضغط "تسجيل صيدلية جديدة"
    echo 5. املأ البيانات واضغط "تسجيل"
    echo.
) else (
    echo.
    echo ========================================
    echo   ❌ حدث خطأ أثناء الإصلاح!
    echo ========================================
    echo.
    echo تأكد من:
    echo 1. تشغيل SQL Server
    echo 2. وجود قاعدة البيانات PharmacyNetworkOnline
    echo 3. صحة اسم الخادم (NARUTO)
    echo 4. وجود صلاحيات الإدارة
    echo.
    echo إذا كانت قاعدة البيانات غير موجودة:
    echo 1. افتح SQL Server Management Studio
    echo 2. اتصل بالخادم NARUTO
    echo 3. شغل الأمر: CREATE DATABASE PharmacyNetworkOnline
    echo 4. ثم شغل هذا الملف مرة أخرى
)

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
