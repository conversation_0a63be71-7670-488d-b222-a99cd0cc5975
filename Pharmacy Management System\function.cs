﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy_Management_System
{
    internal class Function
    {
        public SqlConnection getConnection()
        {
            SqlConnection con = new SqlConnection();
            // تم التحويل من pharmacy إلى UnifiedPharmacy - إلغاء استخدام قاعدة البيانات القديمة
            con.ConnectionString = "data source = NARUTO; database=UnifiedPharmacy; integrated security =True";
            return con;
        }

        // دالة للاتصال بقاعدة البيانات الموحدة (نفس الاتصال الآن)
        public SqlConnection getUnifiedConnection()
        {
            SqlConnection con = new SqlConnection();
            con.ConnectionString = "data source = NARUTO; database=UnifiedPharmacy; integrated security =True";
            return con;
        }

        // دالة لجلب البيانات من قاعدة البيانات الموحدة
        public DataSet getUnifiedData(String query)
        {
            SqlConnection con = null;
            try
            {
                con = getUnifiedConnection();
                SqlCommand cmd = new SqlCommand();
                cmd.Connection = con;
                cmd.CommandText = query;
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                DataSet ds = new DataSet();
                da.Fill(ds);
                return ds;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في جلب البيانات الموحدة: " + ex.Message);
                System.Diagnostics.Debug.WriteLine("الاستعلام: " + query);
                throw;
            }
            finally
            {
                if (con != null && con.State == ConnectionState.Open)
                {
                    con.Close();
                }
            }
        }

        // دالة لتنفيذ استعلامات في قاعدة البيانات الموحدة
        public void setUnifiedData(String query, String msg)
        {
            SqlConnection con = null;
            try
            {
                con = getUnifiedConnection();
                SqlCommand cmd = new SqlCommand();
                cmd.Connection = con;
                cmd.CommandText = query;

                con.Open();
                cmd.ExecuteNonQuery();

                if (!string.IsNullOrEmpty(msg) && !string.IsNullOrWhiteSpace(msg))
                {
                    MessageBox.Show(msg, "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في تنفيذ الاستعلام الموحد: " + ex.Message);
                System.Diagnostics.Debug.WriteLine("الاستعلام: " + query);
                throw;
            }
            finally
            {
                if (con != null && con.State == ConnectionState.Open)
                {
                    con.Close();
                }
            }
        }

        public DataSet getData(String query)
        {
            SqlConnection con = null;
            try
            {
                con = getConnection();
                SqlCommand cmd = new SqlCommand();
                cmd.Connection = con;
                cmd.CommandText = query;
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                DataSet ds = new DataSet();
                da.Fill(ds);
                return ds;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ للتشخيص
                System.Diagnostics.Debug.WriteLine("خطأ في جلب البيانات: " + ex.Message);
                System.Diagnostics.Debug.WriteLine("الاستعلام: " + query);
                throw; // إعادة رمي الاستثناء للمعالجة في المستوى الأعلى
            }
            finally
            {
                if (con != null && con.State == ConnectionState.Open)
                {
                    con.Close();
                }
            }
        }

        public void setData(String query, String msg)
        {
            SqlConnection con = null;
            try
            {
                con = getConnection();
                SqlCommand cmd = new SqlCommand();
                cmd.Connection = con;
                cmd.CommandText = query;

                con.Open();
                cmd.ExecuteNonQuery();

                // عرض الرسالة فقط إذا لم تكن فارغة
                if (!string.IsNullOrEmpty(msg) && !string.IsNullOrWhiteSpace(msg))
                {
                    MessageBox.Show(msg, "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ للتشخيص
                System.Diagnostics.Debug.WriteLine("خطأ في تنفيذ الاستعلام: " + ex.Message);
                System.Diagnostics.Debug.WriteLine("الاستعلام: " + query);
                throw; // إعادة رمي الاستثناء للمعالجة في المستوى الأعلى
            }
            finally
            {
                if (con != null && con.State == ConnectionState.Open)
                {
                    con.Close();
                }
            }
        }


    }   
}
