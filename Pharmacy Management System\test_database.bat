@echo off
echo ========================================
echo   اختبار قاعدة بيانات نظام إدارة الصيدلية
echo ========================================
echo.

echo 🧪 تشغيل اختبارات قاعدة البيانات...
echo.

REM تشغيل اختبارات قاعدة البيانات
sqlcmd -S NARUTO -E -i test_database_queries.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم تشغيل جميع الاختبارات بنجاح!
    echo.
    echo النتائج تظهر أعلاه. تأكد من:
    echo - وجود الجداول المطلوبة
    echo - وجود الأعمدة المطلوبة
    echo - عمل الاستعلامات بشكل صحيح
    echo.
) else (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل الاختبارات!
    echo.
    echo تأكد من:
    echo 1. تشغيل SQL Server
    echo 2. وجود قاعدة البيانات pharmacy
    echo 3. صحة اسم الخادم (NARUTO)
    echo 4. وجود الجداول المطلوبة
)

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
