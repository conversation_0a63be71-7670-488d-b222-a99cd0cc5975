# حل مشكلة تسجيل الصيدلية الجديدة
# Fix Pharmacy Registration Error

## 🚨 **المشكلة:**
```
Error: Could not find stored procedure 'sp_RegisterPharmacy'
```

## 🔍 **سبب المشكلة:**
الإجراء المخزن `sp_RegisterPharmacy` غير موجود في قاعدة البيانات الأونلاين `PharmacyNetworkOnline`.

## ✅ **الحل:**

### **الطريقة السريعة (موصى بها):**
```bash
# تشغيل إعداد النظام الأونلاين الكامل
setup_complete_online_system.bat
```

### **الطريقة التدريجية:**

#### **الخطوة 1: إنشاء قاعدة البيانات والجداول**
```bash
sqlcmd -S NARUTO -E -i setup_complete_online_database.sql
```

#### **الخطوة 2: إنشاء الإجراءات المخزنة**
```bash
sqlcmd -S NARUTO -E -i create_missing_stored_procedures.sql
```

### **الطريقة اليدوية:**
1. افتح **SQL Server Management Studio**
2. اتصل بالخادم `NARUTO`
3. شغل السكريپت `setup_complete_online_database.sql`
4. شغل السكريپت `create_missing_stored_procedures.sql`

## 📋 **الإجراءات المخزنة التي سيتم إنشاؤها:**

### **1. sp_RegisterPharmacy**
- **الوظيفة**: تسجيل صيدلية جديدة في الشبكة
- **المعاملات**: اسم الصيدلية، المالك، الترخيص، العنوان، إلخ
- **المخرجات**: معرف الصيدلية، كود الصيدلية، رسالة النتيجة

### **2. sp_SearchMedicines**
- **الوظيفة**: البحث عن الأدوية في الشبكة
- **المعاملات**: نص البحث، الفئة، السعر، إلخ
- **المخرجات**: قائمة الأدوية المتاحة

### **3. sp_GetActivePharmacies**
- **الوظيفة**: الحصول على قائمة الصيدليات النشطة
- **المخرجات**: معلومات الصيدليات مع الإحصائيات

## 🗄️ **الجداول التي سيتم إنشاؤها:**

### **1. pharmacies** - جدول الصيدليات
```sql
- معرف الصيدلية وكودها الفريد
- معلومات المالك والترخيص
- العنوان والموقع الجغرافي
- حالة النشاط ونوع الاشتراك
```

### **2. network_users** - جدول المستخدمين
```sql
- معلومات المستخدمين في الشبكة
- أدوار المستخدمين والصلاحيات
- كلمات المرور المشفرة
```

### **3. networkmedicines** - جدول الأدوية
```sql
- معلومات الأدوية المشتركة
- الكميات والأسعار
- تواريخ الإنتاج والانتهاء
```

### **4. inter_pharmacy_orders** - جدول الطلبات
```sql
- طلبات الشراء بين الصيدليات
- حالة الطلبات والمبالغ
- تواريخ الطلب والتسليم
```

## 🧪 **اختبار الحل:**

### **1. اختبار الاتصال:**
```sql
USE PharmacyNetworkOnline;
SELECT COUNT(*) FROM pharmacies;
```

### **2. اختبار الإجراء المخزن:**
```sql
EXEC sp_GetActivePharmacies;
```

### **3. اختبار تسجيل صيدلية:**
1. شغل البرنامج
2. اذهب لصفحة الموظف
3. اضغط "الشبكة الأونلاين"
4. اضغط "تسجيل صيدلية جديدة"
5. املأ البيانات واضغط "تسجيل"

## ⚠️ **ملاحظات مهمة:**

### **قواعد البيانات:**
- **المحلية**: `pharmacy` (للعمليات اليومية)
- **الأونلاين**: `PharmacyNetworkOnline` (للشبكة بين الصيدليات)

### **متطلبات النظام:**
- SQL Server يعمل على الخادم `NARUTO`
- Windows Authentication مفعل
- صلاحيات إنشاء قواعد البيانات

### **الأمان:**
- كلمات المرور يتم تشفيرها
- التحقق من عدم تكرار أسماء المستخدمين
- التحقق من عدم تكرار أرقام التراخيص

## 🛠️ **استكشاف الأخطاء:**

### **خطأ: لا يمكن الاتصال بالخادم**
```
الحل: تأكد من تشغيل SQL Server وصحة اسم الخادم
```

### **خطأ: ليس لديك صلاحيات**
```
الحل: شغل Command Prompt كمدير أو استخدم SQL Server Management Studio
```

### **خطأ: الملف غير موجود**
```
الحل: تأكد من وجود ملفات SQL في نفس مجلد البرنامج
```

## 🎯 **النتيجة المتوقعة:**

بعد تشغيل الحل:
- ✅ قاعدة البيانات الأونلاين جاهزة
- ✅ جميع الجداول منشأة
- ✅ جميع الإجراءات المخزنة جاهزة
- ✅ تسجيل الصيدليات يعمل بدون أخطاء
- ✅ النظام الأونلاين جاهز للاستخدام

**الحل جاهز للتطبيق!** 🚀
