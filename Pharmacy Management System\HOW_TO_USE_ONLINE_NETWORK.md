# كيفية استخدام الشبكة الأونلاين
# How to Use Online Network

## 🎯 **الهدف:**
الآن قاعدة البيانات الأونلاين جاهزة ومليئة بالأدوية! إليك كيفية استخدام النظام:

## 📋 **البيانات المتاحة حالياً:**
- ✅ **1 صيدلية نشطة** (صيدلية النور)
- ✅ **2 مستخدم نشط** 
- ✅ **10 أدوية متاحة** للبيع

## 🚀 **خطوات الاستخدام:**

### **الخطوة 1: تشغيل البرنامج**
1. شغل `Pharmacy Management System.exe`
2. سجل دخول كموظف أو مدير

### **الخطوة 2: الوصول للشبكة الأونلاين**
1. من صفحة الموظف، اضغط **"الشبكة الأونلاين"**
2. أو اضغط **"متجر الصيدلية"**

### **الخطوة 3: تسجيل الدخول للشبكة**
يمكنك:

#### **أ) تسجيل صيدلية جديدة:**
- اضغط **"تسجيل صيدلية جديدة"**
- املأ البيانات المطلوبة
- ستحصل على كود صيدلية فريد

#### **ب) تسجيل الدخول بحساب موجود:**
- اسم المستخدم: `admin_nour`
- كلمة المرور: `hashed_password_123`

### **الخطوة 4: استكشاف الأدوية**
بعد تسجيل الدخول:

#### **في صفحة البحث:**
- ستجد **10 أدوية متاحة**
- يمكنك البحث بالاسم أو الفئة
- يمكنك تصفية حسب السعر

#### **في متجر الصيدلية:**
- ستظهر بطاقات الأدوية
- كل بطاقة تحتوي على:
  - اسم الدواء والشركة المصنعة
  - الكمية المتاحة والسعر
  - معلومات الصيدلية البائعة
  - أزرار الطلب والمحادثة

## 💊 **الأدوية المتاحة حالياً:**

### **من صيدلية النور:**
1. **باراسيتامول** - 500mg - 100 قطعة - 5.50 ج.م
2. **أموكسيسيلين** - 250mg - 50 قطعة - 15.75 ج.م
3. **فيتامين سي** - 1000mg - 75 قطعة - 12.00 ج.م
4. **إيبوبروفين** - 400mg - 80 قطعة - 8.25 ج.م
5. **شراب السعال** - 120ml - 30 قطعة - 18.50 ج.م
6. **أسبرين** - 100mg - 120 قطعة - 3.25 ج.م
7. **أوميجا 3** - 1000mg - 60 قطعة - 25.50 ج.م
8. **سيتريزين** - 5mg/5ml - 35 قطعة - 14.25 ج.م
9. **كالسيوم + فيتامين د** - 600mg+400IU - 80 قطعة - 22.00 ج.م
10. **مضاد للفطريات** - 1% - 40 قطعة - 24.75 ج.م

## 🔧 **إذا لم تظهر الأدوية:**

### **تحقق من الاتصال:**
1. تأكد من تشغيل SQL Server
2. تأكد من وجود قاعدة البيانات `PharmacyNetworkOnline`
3. اضغط زر **"اختبار الاتصال"** في صفحة الشبكة

### **تحقق من تسجيل الدخول:**
1. تأكد من تسجيل الدخول للشبكة الأونلاين
2. يجب أن ترى رسالة **"متصل بالشبكة"**

### **إعادة تحميل البيانات:**
1. اضغط **F5** أو أعد فتح الصفحة
2. جرب البحث بكلمة فارغة لعرض جميع الأدوية

## 🛠️ **ميزات إضافية:**

### **مشاركة الأدوية:**
- اضغط **"مشاركة دواء"**
- اختر دواء من مخزونك المحلي
- سيتم إضافته للشبكة الأونلاين

### **إرسال طلبات:**
- اضغط **"طلب"** على أي دواء
- املأ الكمية المطلوبة
- سيتم إرسال الطلب للصيدلية البائعة

### **المحادثة:**
- اضغط **"محادثة"** للتواصل مع صيدلية أخرى
- يمكنك إرسال استفسارات أو مناقشة الطلبات

## 📊 **اختبار النظام:**

### **من SQL Server Management Studio:**
```sql
USE PharmacyNetworkOnline;

-- عرض جميع الأدوية
EXEC sp_SearchMedicines @searchTerm = '', @pharmacyId = 0;

-- عرض الصيدليات النشطة
EXEC sp_GetActivePharmacies;

-- اختبار البيانات
EXEC sp_TestNetworkData;
```

## ✅ **النتيجة المتوقعة:**

بعد اتباع هذه الخطوات:
- ✅ ستظهر الأدوية في صفحة البحث
- ✅ ستظهر بطاقات الأدوية في المتجر
- ✅ ستتمكن من إرسال الطلبات
- ✅ ستتمكن من المحادثة مع الصيدليات

## 🆘 **إذا واجهت مشاكل:**

1. **تأكد من تشغيل السكريپت:** `EXEC sp_TestNetworkData`
2. **تحقق من الأخطاء في:** Event Viewer أو SQL Server Logs
3. **أعد تشغيل البرنامج** وجرب مرة أخرى

**النظام جاهز للاستخدام!** 🚀
