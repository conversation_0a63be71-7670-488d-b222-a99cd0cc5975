-- تعديل قاعدة البيانات لجعل بيانات كل صيدلية مستقلة
USE UnifiedPharmacy;

-- إضافة pharmacy_id لجدول medic إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('medic') AND name = 'pharmacy_id')
BEGIN
    ALTER TABLE medic ADD pharmacy_id INT;
    PRINT 'تم إضافة pharmacy_id لجدول medic';
END

-- إضافة pharmacy_id لجدول sales إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('sales') AND name = 'pharmacy_id')
BEGIN
    ALTER TABLE sales ADD pharmacy_id INT;
    PRINT 'تم إضافة pharmacy_id لجدول sales';
END

-- إضافة pharmacy_id لجدول employee_sessions إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('employee_sessions') AND name = 'pharmacy_id')
BEGIN
    ALTER TABLE employee_sessions ADD pharmacy_id INT;
    PRINT 'تم إضافة pharmacy_id لجدول employee_sessions';
END

-- تحديث البيانات الموجودة لتعيين pharmacy_id = 1 (الصيدلية الافتراضية)
UPDATE medic SET pharmacy_id = 1 WHERE pharmacy_id IS NULL;
UPDATE sales SET pharmacy_id = 1 WHERE pharmacy_id IS NULL;
UPDATE employee_sessions SET pharmacy_id = 1 WHERE pharmacy_id IS NULL;

PRINT 'تم تحديث جميع البيانات الموجودة لتعيين pharmacy_id = 1';
PRINT 'تم إكمال تعديل قاعدة البيانات بنجاح';
