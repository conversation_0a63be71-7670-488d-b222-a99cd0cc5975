#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from datetime import datetime
import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from mt5_crypto_trading_system import MT5CryptoTradingSystem
except ImportError as e:
    print(f"خطأ في استيراد النظام: {e}")
    sys.exit(1)

class CryptoTradingGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام تداول العملات الرقمية - MetaTrader 5")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1e1e1e')
        
        # النظام
        self.trading_system = None
        self.is_connected = False
        self.is_trading = False
        
        # إعداد النمط
        self.setup_style()
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # بدء تحديث البيانات
        self.update_data()
        
    def setup_style(self):
        """إعداد النمط"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # ألوان داكنة
        style.configure('TFrame', background='#1e1e1e')
        style.configure('TLabel', background='#1e1e1e', foreground='#ffffff', font=('Arial', 10))
        style.configure('TButton', background='#404040', foreground='white', font=('Arial', 9))
        style.configure('TEntry', background='#404040', foreground='white')
        style.configure('TCombobox', background='#404040', foreground='white')
        style.configure('TLabelFrame', background='#1e1e1e', foreground='#ffffff', font=('Arial', 10, 'bold'))
        
        # أزرار ملونة
        style.configure('Success.TButton', background='#28a745', foreground='white', font=('Arial', 9, 'bold'))
        style.configure('Danger.TButton', background='#dc3545', foreground='white', font=('Arial', 9, 'bold'))
        style.configure('Warning.TButton', background='#ffc107', foreground='black', font=('Arial', 9, 'bold'))
        style.configure('Info.TButton', background='#17a2b8', foreground='white', font=('Arial', 9, 'bold'))
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # شريط العنوان
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        title_label = ttk.Label(title_frame, text="🚀 نظام تداول العملات الرقمية الذكي", 
                               font=('Arial', 18, 'bold'), foreground='#00d4ff')
        title_label.pack()
        
        subtitle_label = ttk.Label(title_frame, text="MetaTrader 5 + الذكاء الاصطناعي", 
                                  font=('Arial', 12), foreground='#888888')
        subtitle_label.pack()
        
        # لوحة التحكم
        self.create_control_panel(main_frame)
        
        # لوحة المعلومات
        self.create_info_panel(main_frame)
        
        # لوحة العملات الرقمية
        self.create_crypto_panel(main_frame)
        
        # لوحة السجل
        self.create_log_panel(main_frame)
        
    def create_control_panel(self, parent):
        """إنشاء لوحة التحكم"""
        control_frame = ttk.LabelFrame(parent, text="🎮 لوحة التحكم")
        control_frame.pack(fill=tk.X, pady=(0, 15))
        
        # الصف الأول - الاتصال
        row1 = ttk.Frame(control_frame)
        row1.pack(fill=tk.X, padx=15, pady=15)
        
        self.connect_btn = ttk.Button(row1, text="🔌 اتصال", command=self.connect_mt5,
                                     style='Success.TButton', width=12)
        self.connect_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.disconnect_btn = ttk.Button(row1, text="🔌 قطع الاتصال", command=self.disconnect_mt5,
                                        style='Danger.TButton', state='disabled', width=12)
        self.disconnect_btn.pack(side=tk.LEFT, padx=(0, 20))
        
        # حالة الاتصال
        self.status_label = ttk.Label(row1, text="❌ غير متصل", foreground='#dc3545', font=('Arial', 10, 'bold'))
        self.status_label.pack(side=tk.LEFT, padx=(20, 0))
        
        # الصف الثاني - التداول
        row2 = ttk.Frame(control_frame)
        row2.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        # اختيار العملة الرقمية
        ttk.Label(row2, text="💰 العملة الرقمية:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.symbol_var = tk.StringVar(value="BTC")
        self.symbol_combo = ttk.Combobox(row2, textvariable=self.symbol_var, width=15, font=('Arial', 10))
        self.symbol_combo.pack(side=tk.LEFT, padx=(10, 30))
        
        # وضع التداول
        self.demo_var = tk.BooleanVar(value=True)
        self.demo_check = ttk.Checkbutton(row2, text="🛡️ وضع تجريبي آمن", variable=self.demo_var,
                                         style='TCheckbutton')
        self.demo_check.pack(side=tk.LEFT, padx=(0, 30))
        
        # أزرار التداول
        self.start_btn = ttk.Button(row2, text="🚀 بدء التداول", command=self.start_trading,
                                   style='Success.TButton', state='disabled', width=15)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_btn = ttk.Button(row2, text="⏹️ إيقاف التداول", command=self.stop_trading,
                                  style='Danger.TButton', state='disabled', width=15)
        self.stop_btn.pack(side=tk.LEFT)
        
    def create_info_panel(self, parent):
        """إنشاء لوحة المعلومات"""
        info_frame = ttk.LabelFrame(parent, text="📊 معلومات الحساب")
        info_frame.pack(fill=tk.X, pady=(0, 15))
        
        # شبكة المعلومات
        grid = ttk.Frame(info_frame)
        grid.pack(fill=tk.X, padx=15, pady=15)
        
        # الصف الأول
        ttk.Label(grid, text="💰 الرصيد:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky='w', padx=(0, 10))
        self.balance_label = ttk.Label(grid, text="$0.00", foreground='#28a745', font=('Arial', 10, 'bold'))
        self.balance_label.grid(row=0, column=1, sticky='w', padx=(0, 40))
        
        ttk.Label(grid, text="📈 الربح/الخسارة:", font=('Arial', 10, 'bold')).grid(row=0, column=2, sticky='w', padx=(0, 10))
        self.profit_label = ttk.Label(grid, text="$0.00", font=('Arial', 10, 'bold'))
        self.profit_label.grid(row=0, column=3, sticky='w', padx=(0, 40))
        
        ttk.Label(grid, text="🏢 الشركة:", font=('Arial', 10, 'bold')).grid(row=0, column=4, sticky='w', padx=(0, 10))
        self.company_label = ttk.Label(grid, text="غير متصل")
        self.company_label.grid(row=0, column=5, sticky='w')
        
        # الصف الثاني
        ttk.Label(grid, text="📋 الصفقات المفتوحة:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky='w', padx=(0, 10))
        self.positions_label = ttk.Label(grid, text="0", font=('Arial', 10, 'bold'))
        self.positions_label.grid(row=1, column=1, sticky='w', padx=(0, 40))
        
        ttk.Label(grid, text="🎯 آخر تحليل:", font=('Arial', 10, 'bold')).grid(row=1, column=2, sticky='w', padx=(0, 10))
        self.analysis_label = ttk.Label(grid, text="لا يوجد")
        self.analysis_label.grid(row=1, column=3, sticky='w', padx=(0, 40))
        
        ttk.Label(grid, text="🔄 حالة النظام:", font=('Arial', 10, 'bold')).grid(row=1, column=4, sticky='w', padx=(0, 10))
        self.system_status_label = ttk.Label(grid, text="متوقف", foreground='#ffc107')
        self.system_status_label.grid(row=1, column=5, sticky='w')
        
    def create_crypto_panel(self, parent):
        """إنشاء لوحة العملات الرقمية"""
        crypto_frame = ttk.LabelFrame(parent, text="💎 العملات الرقمية المتوفرة")
        crypto_frame.pack(fill=tk.X, pady=(0, 15))
        
        # قائمة العملات الرقمية
        crypto_list_frame = ttk.Frame(crypto_frame)
        crypto_list_frame.pack(fill=tk.X, padx=15, pady=15)
        
        self.crypto_listbox = tk.Listbox(crypto_list_frame, height=4, bg='#2d2d2d', fg='white',
                                        font=('Arial', 10), selectbackground='#0078d4')
        self.crypto_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # شريط التمرير
        crypto_scrollbar = ttk.Scrollbar(crypto_list_frame, orient=tk.VERTICAL, command=self.crypto_listbox.yview)
        self.crypto_listbox.config(yscrollcommand=crypto_scrollbar.set)
        crypto_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # أزرار العملات الرقمية
        crypto_buttons_frame = ttk.Frame(crypto_frame)
        crypto_buttons_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        ttk.Button(crypto_buttons_frame, text="🔄 تحديث القائمة", command=self.refresh_crypto_list,
                  style='Info.TButton').pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(crypto_buttons_frame, text="📊 تحليل العملة المحددة", command=self.analyze_selected_crypto,
                  style='Warning.TButton').pack(side=tk.LEFT)
        
    def create_log_panel(self, parent):
        """إنشاء لوحة السجل"""
        log_frame = ttk.LabelFrame(parent, text="📝 سجل الأحداث والتحليلات")
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, 
                                                 bg='#0d1117', fg='#58a6ff', 
                                                 font=('Consolas', 9),
                                                 insertbackground='white')
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # إضافة رسالة ترحيب
        welcome_msg = """
🚀 مرحباً بك في نظام تداول العملات الرقمية الذكي!

📋 خطوات البدء:
1. اضغط "اتصال" للاتصال بـ MetaTrader 5
2. اختر العملة الرقمية المفضلة من القائمة
3. حدد وضع التداول (تجريبي أو حقيقي)
4. اضغط "بدء التداول" لتشغيل النظام الذكي

⚠️ تنبيه: ابدأ دائماً بالوضع التجريبي للتأكد من عمل النظام!

💡 النظام يستخدم الذكاء الاصطناعي لتحليل السوق واتخاذ قرارات التداول.
        """
        self.log_text.insert(tk.END, welcome_msg)
        
    def log_message(self, message):
        """إضافة رسالة للسجل"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, full_message)
        self.log_text.see(tk.END)
        
    def connect_mt5(self):
        """الاتصال بـ MT5"""
        try:
            self.log_message("🔌 محاولة الاتصال بـ MetaTrader 5...")
            self.trading_system = MT5CryptoTradingSystem()
            
            if self.trading_system.connect_mt5():
                self.is_connected = True
                self.status_label.config(text="✅ متصل", foreground='#28a745')
                self.connect_btn.config(state='disabled')
                self.disconnect_btn.config(state='normal')
                self.start_btn.config(state='normal')
                
                # تحديث قائمة العملات الرقمية
                self.refresh_crypto_list()
                
                self.log_message("✅ تم الاتصال بنجاح!")
                self.log_message(f"📊 تم العثور على {len(self.trading_system.crypto_symbols)} عملة رقمية")
                
                # تحديث معلومات الحساب
                self.update_account_info()
                
            else:
                self.log_message("❌ فشل في الاتصال!")
                messagebox.showerror("خطأ", "فشل في الاتصال بـ MetaTrader 5\nتأكد من:\n- تشغيل MT5\n- تسجيل الدخول\n- تفعيل التداول الآلي")
                
        except Exception as e:
            self.log_message(f"❌ خطأ في الاتصال: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في الاتصال: {str(e)}")
            
    def disconnect_mt5(self):
        """قطع الاتصال"""
        if self.is_trading:
            self.stop_trading()
            
        if self.trading_system:
            self.trading_system.disconnect_mt5()
            
        self.is_connected = False
        self.status_label.config(text="❌ غير متصل", foreground='#dc3545')
        self.connect_btn.config(state='normal')
        self.disconnect_btn.config(state='disabled')
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='disabled')
        
        # مسح المعلومات
        self.balance_label.config(text="$0.00")
        self.profit_label.config(text="$0.00", foreground='white')
        self.positions_label.config(text="0")
        self.company_label.config(text="غير متصل")
        self.analysis_label.config(text="لا يوجد")
        self.system_status_label.config(text="متوقف", foreground='#ffc107')
        
        self.crypto_listbox.delete(0, tk.END)
        
        self.log_message("🔌 تم قطع الاتصال")
        
    def refresh_crypto_list(self):
        """تحديث قائمة العملات الرقمية"""
        if not self.is_connected or not self.trading_system:
            return
            
        self.crypto_listbox.delete(0, tk.END)
        
        symbols = self.trading_system.crypto_symbols
        self.symbol_combo['values'] = symbols
        
        if symbols:
            self.symbol_combo.set(symbols[0])
            
            for i, symbol in enumerate(symbols, 1):
                # إضافة وصف للعملة
                if 'BTC' in symbol:
                    desc = "Bitcoin"
                elif 'ETH' in symbol:
                    desc = "Ethereum"
                else:
                    desc = "عملة رقمية"
                    
                self.crypto_listbox.insert(tk.END, f"{i:2d}. {symbol:<10} - {desc}")
                
        self.log_message(f"🔄 تم تحديث قائمة العملات الرقمية: {len(symbols)} عملة")
        
    def analyze_selected_crypto(self):
        """تحليل العملة المحددة"""
        if not self.is_connected:
            messagebox.showwarning("تحذير", "يجب الاتصال أولاً!")
            return
            
        symbol = self.symbol_var.get()
        if not symbol:
            messagebox.showwarning("تحذير", "يجب اختيار عملة رقمية!")
            return
            
        self.log_message(f"📊 بدء تحليل {symbol}...")
        
        # تشغيل التحليل في خيط منفصل
        threading.Thread(target=self.run_analysis, args=(symbol,), daemon=True).start()
        
    def run_analysis(self, symbol):
        """تشغيل التحليل"""
        try:
            analysis = self.trading_system.analyze_crypto_market(symbol)
            
            if analysis and 'decision' in analysis:
                decision = analysis['decision']
                confidence = analysis.get('confidence', 0)
                
                # تحديث واجهة التحليل
                self.root.after(0, lambda: self.analysis_label.config(
                    text=f"{decision} ({confidence:.1f}%)"))
                
                self.log_message(f"📈 نتيجة التحليل لـ {symbol}:")
                self.log_message(f"   🎯 القرار: {decision}")
                self.log_message(f"   📊 نسبة الثقة: {confidence:.1f}%")
                
                if 'price' in analysis:
                    self.log_message(f"   💰 السعر الحالي: ${analysis['price']:.5f}")
                    
            else:
                self.log_message(f"❌ فشل في تحليل {symbol}")
                
        except Exception as e:
            self.log_message(f"❌ خطأ في التحليل: {str(e)}")

    def start_trading(self):
        """بدء التداول"""
        if not self.is_connected:
            messagebox.showwarning("تحذير", "يجب الاتصال أولاً!")
            return

        symbol = self.symbol_var.get()
        if not symbol:
            messagebox.showwarning("تحذير", "يجب اختيار عملة رقمية!")
            return

        self.is_trading = True
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.system_status_label.config(text="🚀 يعمل", foreground='#28a745')

        # تحديث إعدادات النظام
        self.trading_system.demo_mode = self.demo_var.get()
        self.trading_system.current_symbol = symbol

        mode_text = "تجريبي آمن" if self.demo_var.get() else "حقيقي"
        self.log_message(f"🚀 بدء التداول الذكي:")
        self.log_message(f"   💰 العملة: {symbol}")
        self.log_message(f"   🛡️ الوضع: {mode_text}")
        self.log_message(f"   🧠 الذكاء الاصطناعي: مفعل")

        # بدء التداول في خيط منفصل
        threading.Thread(target=self.trading_loop, daemon=True).start()

    def stop_trading(self):
        """إيقاف التداول"""
        self.is_trading = False
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.system_status_label.config(text="⏹️ متوقف", foreground='#ffc107')
        self.log_message("⏹️ تم إيقاف التداول")

    def trading_loop(self):
        """حلقة التداول الذكية"""
        while self.is_trading and self.is_connected:
            try:
                symbol = self.symbol_var.get()
                self.log_message(f"🔍 تحليل {symbol} بالذكاء الاصطناعي...")

                # تحليل السوق
                analysis = self.trading_system.analyze_crypto_market(symbol)

                if analysis and 'decision' in analysis:
                    decision = analysis['decision']
                    confidence = analysis.get('confidence', 0)
                    price = analysis.get('price', 0)

                    # تحديث واجهة التحليل
                    self.root.after(0, lambda: self.analysis_label.config(
                        text=f"{decision} ({confidence:.1f}%)"))

                    self.log_message(f"📊 نتيجة التحليل:")
                    self.log_message(f"   🎯 القرار: {decision}")
                    self.log_message(f"   📈 الثقة: {confidence:.1f}%")
                    self.log_message(f"   💰 السعر: ${price:.5f}")

                    # تنفيذ التداول إذا كانت الثقة عالية
                    if confidence > 60:
                        self.log_message(f"🚀 الثقة عالية! محاولة تنفيذ صفقة {decision}...")

                        if self.trading_system.execute_crypto_trade(analysis):
                            self.log_message("✅ تم تنفيذ الصفقة بنجاح!")
                            self.log_message("🧠 النظام يتعلم من هذه الصفقة...")
                        else:
                            self.log_message("❌ فشل في تنفيذ الصفقة")
                    else:
                        self.log_message(f"⏳ الثقة منخفضة ({confidence:.1f}%) - انتظار فرصة أفضل")

                # تحديث معلومات الحساب
                self.root.after(0, self.update_account_info)

                # انتظار قبل التحليل التالي (30 ثانية)
                for i in range(30):
                    if not self.is_trading:
                        break
                    time.sleep(1)

            except Exception as e:
                self.log_message(f"❌ خطأ في التداول: {str(e)}")
                time.sleep(10)

    def update_account_info(self):
        """تحديث معلومات الحساب"""
        if not self.is_connected or not self.trading_system:
            return

        try:
            account_summary = self.trading_system.get_account_summary()

            if 'error' not in account_summary:
                balance = account_summary.get('balance', 0)
                equity = account_summary.get('equity', 0)
                profit = equity - balance

                self.balance_label.config(text=f"${balance:.2f}")

                # تلوين الربح/الخسارة
                if profit > 0:
                    self.profit_label.config(text=f"+${profit:.2f}", foreground='#28a745')
                elif profit < 0:
                    self.profit_label.config(text=f"${profit:.2f}", foreground='#dc3545')
                else:
                    self.profit_label.config(text="$0.00", foreground='white')

                # معلومات إضافية
                open_positions = account_summary.get('open_positions', 0)
                self.positions_label.config(text=str(open_positions))

                # معلومات الشركة (من account_info إذا كانت متوفرة)
                if hasattr(self.trading_system, 'account_info') and self.trading_system.account_info:
                    company = self.trading_system.account_info.company
                    self.company_label.config(text=company)

            else:
                self.log_message(f"⚠️ خطأ في معلومات الحساب: {account_summary['error']}")

        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث المعلومات: {str(e)}")

    def update_data(self):
        """تحديث البيانات دورياً"""
        if self.is_connected:
            self.update_account_info()

        # جدولة التحديث التالي (كل 5 ثوانٍ)
        self.root.after(5000, self.update_data)

    def run(self):
        """تشغيل التطبيق"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log_message("⏹️ تم إيقاف التطبيق")
        finally:
            if self.trading_system:
                self.trading_system.disconnect_mt5()

    def on_closing(self):
        """عند إغلاق التطبيق"""
        if self.is_trading:
            if messagebox.askokcancel("تأكيد الإغلاق", "النظام يعمل حالياً. هل تريد إيقافه والخروج؟"):
                self.stop_trading()
                time.sleep(1)  # انتظار قصير لإيقاف التداول
                self.disconnect_mt5()
                self.root.destroy()
        else:
            self.disconnect_mt5()
            self.root.destroy()

if __name__ == "__main__":
    try:
        app = CryptoTradingGUI()
        app.run()
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")
