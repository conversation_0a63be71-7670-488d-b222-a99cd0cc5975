#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بسيط لنظام تداول العملات الرقمية على MetaTrader 5
Simple MT5 Cryptocurrency Trading System Test
"""

import sys
import os

print("="*60)
print("🧪 اختبار بسيط لنظام تداول العملات الرقمية")
print("="*60)

# اختبار استيراد المكتبات
print("\n🔍 فحص المكتبات المطلوبة...")

try:
    import MetaTrader5 as mt5
    print("✅ MetaTrader5: متوفر")
except ImportError as e:
    print(f"❌ MetaTrader5: غير متوفر - {e}")
    print("💡 قم بتثبيته: pip install MetaTrader5")

try:
    import pandas as pd
    print("✅ pandas: متوفر")
except ImportError as e:
    print(f"❌ pandas: غير متوفر - {e}")
    print("💡 قم بتثبيته: pip install pandas")

try:
    import numpy as np
    print("✅ numpy: متوفر")
except ImportError as e:
    print(f"❌ numpy: غير متوفر - {e}")
    print("💡 قم بتثبيته: pip install numpy")

try:
    import sklearn
    print("✅ scikit-learn: متوفر")
except ImportError as e:
    print(f"❌ scikit-learn: غير متوفر - {e}")
    print("💡 قم بتثبيته: pip install scikit-learn")

try:
    import ta
    print("✅ ta (Technical Analysis): متوفر")
except ImportError as e:
    print(f"❌ ta: غير متوفر - {e}")
    print("💡 قم بتثبيته: pip install ta")

try:
    import tkinter as tk
    print("✅ tkinter: متوفر")
except ImportError as e:
    print(f"❌ tkinter: غير متوفر - {e}")
    print("💡 tkinter يأتي مع Python عادة")

# اختبار الاتصال بـ MT5
print("\n🔌 اختبار الاتصال بـ MetaTrader 5...")

try:
    # تهيئة MT5
    if not mt5.initialize():
        print("❌ فشل في تهيئة MT5")
        print("💡 تأكد من:")
        print("   • تشغيل MetaTrader 5")
        print("   • إغلاق MT5 وإعادة تشغيله")
        print("   • تشغيل هذا السكريبت كمدير")
    else:
        print("✅ تم تهيئة MT5 بنجاح")
        
        # محاولة تسجيل الدخول
        login = ********
        password = "D!2qKdJy"
        server = "MetaQuotes-Demo"
        
        print(f"🔑 محاولة تسجيل الدخول للحساب: {login}")
        
        if mt5.login(login, password, server):
            print("✅ تم تسجيل الدخول بنجاح!")
            
            # معلومات الحساب
            account_info = mt5.account_info()
            if account_info:
                print(f"   📊 رقم الحساب: {account_info.login}")
                print(f"   💰 الرصيد: ${account_info.balance:.2f}")
                print(f"   🏢 الشركة: {account_info.company}")
                print(f"   🔄 التداول مسموح: {'نعم' if account_info.trade_allowed else 'لا'}")
            
            # اختبار العملات الرقمية
            print("\n💎 فحص العملات الرقمية المتوفرة...")
            
            crypto_symbols = [
                'BTCUSD', 'ETHUSD', 'LTCUSD', 'XRPUSD', 'BCHUSD',
                'EOSUSD', 'XLMUSD', 'ADAUSD', 'TRXUSD', 'BNBUSD'
            ]
            
            available_cryptos = []
            
            for symbol in crypto_symbols:
                symbol_info = mt5.symbol_info(symbol)
                if symbol_info:
                    available_cryptos.append(symbol)
                    print(f"   ✅ {symbol}: متوفر")
                else:
                    # محاولة إضافة الرمز
                    if mt5.symbol_select(symbol, True):
                        available_cryptos.append(symbol)
                        print(f"   ✅ {symbol}: تم إضافته")
                    else:
                        print(f"   ❌ {symbol}: غير متوفر")
            
            print(f"\n📊 إجمالي العملات الرقمية المتوفرة: {len(available_cryptos)}")
            
            if available_cryptos:
                # اختبار استرجاع البيانات
                print(f"\n📈 اختبار استرجاع بيانات {available_cryptos[0]}...")
                
                rates = mt5.copy_rates_from_pos(available_cryptos[0], mt5.TIMEFRAME_H1, 0, 10)
                
                if rates is not None and len(rates) > 0:
                    print(f"✅ تم استرجاع {len(rates)} شمعة")
                    print(f"   💰 آخر سعر إغلاق: ${rates[-1]['close']:.4f}")
                    print(f"   📊 أعلى سعر: ${max(rates['high']):.4f}")
                    print(f"   📉 أقل سعر: ${min(rates['low']):.4f}")
                else:
                    print("❌ فشل في استرجاع البيانات")
            else:
                print("⚠️ لا توجد عملات رقمية متوفرة للاختبار")
                print("💡 تأكد من أن الوسيط يدعم العملات الرقمية")
            
        else:
            error = mt5.last_error()
            print(f"❌ فشل في تسجيل الدخول: {error}")
            print("💡 تحقق من:")
            print("   • رقم الحساب وكلمة المرور")
            print("   • اسم الخادم")
            print("   • الاتصال بالإنترنت")
        
        # إغلاق الاتصال
        mt5.shutdown()
        
except Exception as e:
    print(f"❌ خطأ في اختبار MT5: {e}")

# اختبار استيراد النظام
print("\n🚀 اختبار استيراد نظام التداول...")

try:
    from mt5_crypto_trading_system import MT5CryptoTradingSystem
    print("✅ تم استيراد النظام بنجاح")
    
    # إنشاء مثيل من النظام
    system = MT5CryptoTradingSystem(demo_mode=True)
    print("✅ تم إنشاء مثيل من النظام")
    
    print(f"   💎 العملات المدعومة: {len(system.crypto_symbols)}")
    print(f"   🎯 العملة الحالية: {system.current_symbol}")
    print(f"   🛡️ وضع التجريبي: {system.demo_mode}")
    
except Exception as e:
    print(f"❌ خطأ في استيراد النظام: {e}")

# اختبار الواجهة الرسومية
print("\n🖥️ اختبار الواجهة الرسومية...")

try:
    from mt5_crypto_gui import MT5CryptoGUI
    print("✅ تم استيراد الواجهة الرسومية بنجاح")
    
except Exception as e:
    print(f"❌ خطأ في استيراد الواجهة: {e}")

# النتيجة النهائية
print("\n" + "="*60)
print("📊 ملخص الاختبار")
print("="*60)

print("✅ إذا رأيت معظم العلامات خضراء، فالنظام جاهز للاستخدام")
print("❌ إذا رأيت علامات حمراء، راجع الأخطاء وأصلحها")

print("\n🚀 للتشغيل:")
print("   python mt5_crypto_gui.py")

print("\n💡 نصائح:")
print("   • تأكد من تشغيل MetaTrader 5")
print("   • تحقق من بيانات الحساب")
print("   • ابدأ بالوضع التجريبي")
print("   • راقب النظام في البداية")

print("\n⚠️ تحذير:")
print("   • العملات الرقمية عالية المخاطر")
print("   • لا تستثمر أكثر مما تستطيع خسارته")
print("   • استخدم وقف الخسارة دائماً")

input("\n📋 اضغط Enter للخروج...")
