USE PharmacyNetworkOnline;
GO

CREATE PROCEDURE sp_RegisterPharmacy
    @pharmacyName NVARCHAR(250),
    @ownerName NVARCHAR(250),
    @licenseNumber VARCHAR(100),
    @address NVARCHAR(500),
    @city NVARCHAR(100),
    @region NVARCHAR(100),
    @phone VARCHAR(20),
    @email VARCHAR(250),
    @adminName NVARCHAR(250),
    @adminUsername VARCHAR(250),
    @adminPassword VARCHAR(500)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @pharmacyId INT;
    DECLARE @pharmacyCode VARCHAR(20);
    DECLARE @errorMessage NVARCHAR(500);

    -- التحقق من عدم وجود رقم الترخيص مسبقاً
    IF EXISTS (SELECT 1 FROM pharmacies WHERE licenseNumber = @licenseNumber)
    BEGIN
        SELECT 0 as PharmacyId, '' as PharmacyCode, 'رقم الترخيص موجود مسبقاً' as Message;
        RETURN;
    END

    -- التحقق من عدم وجود اسم المستخدم مسبقاً
    IF EXISTS (SELECT 1 FROM network_users WHERE username = @adminUsername)
    BEGIN
        SELECT 0 as PharmacyId, '' as PharmacyCode, 'اسم المستخدم موجود مسبقاً' as Message;
        RETURN;
    END

    -- إنشاء كود فريد للصيدلية
    DECLARE @nextNumber INT;
    SELECT @nextNumber = ISNULL(MAX(CAST(SUBSTRING(pharmacyCode, 3, 3) AS INT)), 0) + 1 FROM pharmacies;
    SET @pharmacyCode = 'PH' + RIGHT('000' + CAST(@nextNumber AS VARCHAR), 3);

    BEGIN TRANSACTION;

    BEGIN TRY
        -- إدراج الصيدلية
        INSERT INTO pharmacies (
            pharmacyCode, pharmacyName, ownerName, licenseNumber, 
            address, city, region, phone, email, isActive, 
            registrationDate, lastOnline, subscriptionType
        )
        VALUES (
            @pharmacyCode, @pharmacyName, @ownerName, @licenseNumber,
            @address, @city, @region, @phone, @email, 1,
            GETDATE(), GETDATE(), 'Basic'
        );

        SET @pharmacyId = SCOPE_IDENTITY();

        -- إدراج المدير
        INSERT INTO network_users (
            pharmacyId, userRole, name, username, passwordHash, 
            email, isActive, createdAt, updatedAt
        )
        VALUES (
            @pharmacyId, 'Admin', @adminName, @adminUsername, @adminPassword,
            @email, 1, GETDATE(), GETDATE()
        );

        COMMIT TRANSACTION;

        -- إرجاع النتيجة الناجحة
        SELECT @pharmacyId as PharmacyId, @pharmacyCode as PharmacyCode, 'تم تسجيل الصيدلية بنجاح' as Message;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        
        SET @errorMessage = ERROR_MESSAGE();
        SELECT 0 as PharmacyId, '' as PharmacyCode, 'خطأ في التسجيل: ' + @errorMessage as Message;
        
    END CATCH
END;
GO
