@echo off
chcp 65001 > nul
title نظام تداول العملات الرقمية المتقدم - الأسعار الحقيقية

echo.
echo ========================================
echo 🚀 نظام تداول العملات الرقمية المتقدم
echo ========================================
echo.
echo ✨ المميزات الجديدة:
echo • الأسعار الحقيقية من CoinGecko API
echo • إعدادات نسبة الثقة قابلة للتخصيص
echo • تداول أزواج العملات الحقيقية (BTCUSD, ETHUSD)
echo • تحليل فني متقدم مع الذكاء الاصطناعي
echo • وضع تجريبي آمن مع محاكاة واقعية
echo.
echo ⚠️ تنبيه: ابدأ دائماً بالوضع التجريبي!
echo.

echo 🔍 فحص المتطلبات...

python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت!
    echo يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر

echo.
echo 📦 فحص المكتبات المطلوبة...

python -c "import requests, pandas, numpy, sklearn" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ بعض المكتبات مفقودة، جاري التثبيت...
    pip install requests pandas numpy scikit-learn
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات!
        pause
        exit /b 1
    )
)

echo ✅ جميع المكتبات متوفرة

echo.
echo 🚀 بدء تشغيل النظام...
echo.

python advanced_crypto_gui.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل النظام!
    echo تحقق من:
    echo • اتصال الإنترنت
    echo • تثبيت Python بشكل صحيح
    echo • وجود جميع الملفات المطلوبة
    echo.
    pause
)

echo.
echo 👋 شكراً لاستخدام نظام تداول العملات الرقمية!
pause
