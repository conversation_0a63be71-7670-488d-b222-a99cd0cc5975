#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 Test Multi-Currency Intelligent Trading System V3
Comprehensive testing for the advanced multi-currency trading system
"""

import sys
import time
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def test_system_initialization():
    """Test system initialization"""
    print("🧪 Testing System Initialization...")
    
    try:
        from multi_currency_intelligent_system import MultiCurrencyIntelligentSystem
        
        # Initialize system
        system = MultiCurrencyIntelligentSystem()
        
        # Check basic attributes
        assert hasattr(system, 'active_symbols'), "❌ Missing active_symbols"
        assert hasattr(system, 'strategies'), "❌ Missing strategies"
        assert hasattr(system, 'all_timeframes'), "❌ Missing timeframes"
        assert hasattr(system, 'experience_memory'), "❌ Missing experience_memory"
        
        print(f"✅ System initialized successfully")
        print(f"   📊 Active symbols: {len(system.active_symbols)}")
        print(f"   🧠 Strategies: {len(system.strategies)}")
        print(f"   ⏰ Timeframes: {len(system.all_timeframes)}")
        
        return system
        
    except Exception as e:
        print(f"❌ System initialization failed: {e}")
        return None

def test_currency_setup(system):
    """Test currency pair setup"""
    print("\n🧪 Testing Currency Setup...")
    
    try:
        # Check currency lists
        assert len(system.active_symbols) > 0, "❌ No active symbols"
        assert 'EURUSD' in system.active_symbols, "❌ EURUSD not in active symbols"
        
        # Check limits
        assert system.max_positions_total > 0, "❌ Invalid max_positions_total"
        assert system.max_positions_per_symbol > 0, "❌ Invalid max_positions_per_symbol"
        
        print(f"✅ Currency setup verified")
        print(f"   💱 Active symbols: {system.active_symbols}")
        print(f"   📊 Max total positions: {system.max_positions_total}")
        print(f"   📈 Max per symbol: {system.max_positions_per_symbol}")
        
        return True
        
    except Exception as e:
        print(f"❌ Currency setup test failed: {e}")
        return False

def test_strategy_setup(system):
    """Test strategy setup"""
    print("\n🧪 Testing Strategy Setup...")
    
    try:
        # Check strategies
        expected_strategies = ['trend_following', 'mean_reversion', 'breakout', 'momentum']
        
        for strategy in expected_strategies:
            assert strategy in system.strategies, f"❌ Missing strategy: {strategy}"
            assert strategy in system.strategy_configs, f"❌ Missing config for: {strategy}"
        
        # Check strategy weights
        assert len(system.strategy_weights) > 0, "❌ No strategy weights"
        
        print(f"✅ Strategy setup verified")
        print(f"   🧠 Strategies: {system.strategies}")
        print(f"   ⚖️ Strategy weights: {system.strategy_weights}")
        
        return True
        
    except Exception as e:
        print(f"❌ Strategy setup test failed: {e}")
        return False

def test_timeframe_setup(system):
    """Test timeframe setup"""
    print("\n🧪 Testing Timeframe Setup...")
    
    try:
        # Check timeframes
        assert len(system.all_timeframes) > 0, "❌ No timeframes configured"
        assert len(system.primary_timeframes) > 0, "❌ No primary timeframes"
        
        # Check timeframe mapping
        for tf in system.primary_timeframes:
            assert tf in system.timeframe_mapping, f"❌ Missing mapping for {tf}"
        
        print(f"✅ Timeframe setup verified")
        print(f"   ⏰ All timeframes: {system.all_timeframes}")
        print(f"   🎯 Primary timeframes: {system.primary_timeframes}")
        
        return True
        
    except Exception as e:
        print(f"❌ Timeframe setup test failed: {e}")
        return False

def test_learning_system(system):
    """Test learning system setup"""
    print("\n🧪 Testing Learning System...")
    
    try:
        # Check learning attributes
        assert hasattr(system, 'continuous_learning'), "❌ Missing continuous_learning"
        assert hasattr(system, 'experience_memory'), "❌ Missing experience_memory"
        assert hasattr(system, 'adaptive_parameters'), "❌ Missing adaptive_parameters"
        
        # Check adaptive parameters structure
        assert 'confidence_thresholds' in system.adaptive_parameters, "❌ Missing confidence_thresholds"
        assert 'risk_levels' in system.adaptive_parameters, "❌ Missing risk_levels"
        assert 'strategy_preferences' in system.adaptive_parameters, "❌ Missing strategy_preferences"
        
        # Check that all symbols have parameters
        for symbol in system.active_symbols:
            assert symbol in system.adaptive_parameters['confidence_thresholds'], f"❌ Missing confidence threshold for {symbol}"
            assert symbol in system.adaptive_parameters['risk_levels'], f"❌ Missing risk level for {symbol}"
            assert symbol in system.adaptive_parameters['strategy_preferences'], f"❌ Missing strategy preferences for {symbol}"
        
        print(f"✅ Learning system verified")
        print(f"   🧠 Continuous learning: {system.continuous_learning}")
        print(f"   📚 Memory size limit: {system.experience_memory_size}")
        print(f"   🎯 Adaptive parameters configured for {len(system.active_symbols)} symbols")
        
        return True
        
    except Exception as e:
        print(f"❌ Learning system test failed: {e}")
        return False

def test_technical_indicators():
    """Test technical indicators calculation"""
    print("\n🧪 Testing Technical Indicators...")
    
    try:
        import pandas as pd
        import numpy as np
        
        # Create sample data
        dates = pd.date_range(start='2024-01-01', periods=100, freq='H')
        np.random.seed(42)
        
        # Generate realistic price data
        base_price = 1.0850
        returns = np.random.normal(0, 0.001, 100)
        prices = [base_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        # Create DataFrame
        df = pd.DataFrame({
            'time': dates,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.0005))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.0005))) for p in prices],
            'close': prices,
            'tick_volume': np.random.randint(100, 1000, 100)
        })
        
        df.set_index('time', inplace=True)
        
        # Test indicator calculation
        from multi_currency_intelligent_system import MultiCurrencyIntelligentSystem
        system = MultiCurrencyIntelligentSystem()
        
        df_with_indicators = system.calculate_technical_indicators(df, 'EURUSD')
        
        # Check that indicators were added
        expected_indicators = ['SMA_20', 'EMA_12', 'MACD', 'RSI', 'BB_Upper', 'ATR', 'Stoch_K', 'ADX']
        
        for indicator in expected_indicators:
            assert indicator in df_with_indicators.columns, f"❌ Missing indicator: {indicator}"
        
        print(f"✅ Technical indicators calculated successfully")
        print(f"   📊 Original columns: {len(df.columns)}")
        print(f"   📈 With indicators: {len(df_with_indicators.columns)}")
        print(f"   🎯 Added indicators: {len(df_with_indicators.columns) - len(df.columns)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Technical indicators test failed: {e}")
        return False

def test_market_analysis():
    """Test market condition analysis"""
    print("\n🧪 Testing Market Analysis...")
    
    try:
        import pandas as pd
        import numpy as np
        
        # Create sample data with indicators
        dates = pd.date_range(start='2024-01-01', periods=100, freq='H')
        np.random.seed(42)
        
        df = pd.DataFrame({
            'time': dates,
            'close': np.random.uniform(1.08, 1.09, 100),
            'high': np.random.uniform(1.085, 1.095, 100),
            'low': np.random.uniform(1.075, 1.085, 100),
            'SMA_20': np.random.uniform(1.08, 1.09, 100),
            'SMA_50': np.random.uniform(1.08, 1.09, 100),
            'MACD': np.random.uniform(-0.001, 0.001, 100),
            'MACD_Signal': np.random.uniform(-0.001, 0.001, 100),
            'RSI': np.random.uniform(30, 70, 100),
            'ATR': np.random.uniform(0.0005, 0.002, 100),
            'ADX': np.random.uniform(15, 35, 100),
            'Price_Above_SMA20': np.random.choice([True, False], 100),
            'Price_Above_SMA50': np.random.choice([True, False], 100),
            'SMA20_Above_SMA50': np.random.choice([True, False], 100),
            'Volatility': np.random.uniform(0.1, 0.3, 100),
            'Support_Resistance_Ratio': np.random.uniform(0.2, 0.8, 100)
        })
        
        df.set_index('time', inplace=True)
        
        # Test market analysis
        from multi_currency_intelligent_system import MultiCurrencyIntelligentSystem
        system = MultiCurrencyIntelligentSystem()
        
        market_conditions = system.analyze_market_conditions('EURUSD', 'H1', df)
        
        # Check analysis results
        required_fields = ['symbol', 'timeframe', 'trend_strength', 'momentum_strength', 
                          'volatility_level', 'overall_condition', 'confidence']
        
        for field in required_fields:
            assert field in market_conditions, f"❌ Missing field: {field}"
        
        assert 0 <= market_conditions['confidence'] <= 1, "❌ Invalid confidence value"
        assert market_conditions['symbol'] == 'EURUSD', "❌ Wrong symbol in analysis"
        
        print(f"✅ Market analysis completed successfully")
        print(f"   📊 Trend strength: {market_conditions['trend_strength']:.2f}")
        print(f"   ⚡ Momentum strength: {market_conditions['momentum_strength']:.2f}")
        print(f"   📈 Overall condition: {market_conditions['overall_condition']}")
        print(f"   🎯 Confidence: {market_conditions['confidence']:.2%}")
        
        return True
        
    except Exception as e:
        print(f"❌ Market analysis test failed: {e}")
        return False

def test_strategy_analysis():
    """Test individual strategy analysis"""
    print("\n🧪 Testing Strategy Analysis...")
    
    try:
        import pandas as pd
        import numpy as np
        
        # Create sample data
        latest_data = pd.Series({
            'close': 1.0850,
            'MACD': 0.0005,
            'MACD_Signal': 0.0003,
            'RSI': 45,
            'Price_Above_SMA20': True,
            'SMA20_Above_SMA50': True,
            'ADX': 28,
            'ATR': 0.001,
            'BB_Position': 0.6,
            'Stoch_K': 55,
            'Stoch_D': 50
        })
        
        market_conditions = {
            'trend_strength': 0.7,
            'momentum_strength': 0.6,
            'overall_condition': 'strong_trend',
            'volatility_level': 'medium'
        }
        
        # Test strategy analysis
        from multi_currency_intelligent_system import MultiCurrencyIntelligentSystem
        system = MultiCurrencyIntelligentSystem()
        
        # Test trend following strategy
        result = system._analyze_trend_following(latest_data, market_conditions, {})
        
        assert 'signal' in result, "❌ Missing signal in result"
        assert 'confidence' in result, "❌ Missing confidence in result"
        assert 'reasons' in result, "❌ Missing reasons in result"
        assert 0 <= result['confidence'] <= 1, "❌ Invalid confidence value"
        
        print(f"✅ Strategy analysis completed successfully")
        print(f"   📊 Signal: {result['signal']}")
        print(f"   🎯 Confidence: {result['confidence']:.2%}")
        print(f"   📝 Reasons: {result['reasons']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Strategy analysis test failed: {e}")
        return False

def run_comprehensive_test():
    """Run comprehensive system test"""
    print("🚀 Starting Comprehensive Multi-Currency System Test")
    print("=" * 70)
    
    test_results = []
    
    # Test 1: System Initialization
    system = test_system_initialization()
    test_results.append(system is not None)
    
    if system is None:
        print("❌ Cannot continue tests - system initialization failed")
        return False
    
    # Test 2: Currency Setup
    test_results.append(test_currency_setup(system))
    
    # Test 3: Strategy Setup
    test_results.append(test_strategy_setup(system))
    
    # Test 4: Timeframe Setup
    test_results.append(test_timeframe_setup(system))
    
    # Test 5: Learning System
    test_results.append(test_learning_system(system))
    
    # Test 6: Technical Indicators
    test_results.append(test_technical_indicators())
    
    # Test 7: Market Analysis
    test_results.append(test_market_analysis())
    
    # Test 8: Strategy Analysis
    test_results.append(test_strategy_analysis())
    
    # Summary
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print("\n" + "=" * 70)
    print("🏁 TEST SUMMARY")
    print("=" * 70)
    print(f"✅ Passed: {passed_tests}/{total_tests}")
    print(f"❌ Failed: {total_tests - passed_tests}/{total_tests}")
    print(f"📊 Success Rate: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL TESTS PASSED! System is ready for trading.")
        return True
    else:
        print(f"\n⚠️ {total_tests - passed_tests} tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    try:
        success = run_comprehensive_test()
        
        if success:
            print("\n🚀 Multi-Currency Intelligent Trading System V3 is ready!")
            print("💡 You can now run: python multi_currency_intelligent_system.py")
        else:
            print("\n❌ System has issues. Please fix them before trading.")
            
    except KeyboardInterrupt:
        print("\n⏸️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test error: {e}")
