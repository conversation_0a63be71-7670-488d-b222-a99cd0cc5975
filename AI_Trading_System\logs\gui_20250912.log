2025-09-12 02:09:52,289 - INFO - تم بدء تشغيل نظام التداول الذكي
2025-09-12 02:09:52,289 - INFO - AI Trading System started
2025-09-12 02:09:54,043 - INFO - تم تشغيل الواجهة الرسومية بنجاح
2025-09-12 02:09:54,044 - INFO - GUI launched successfully
2025-09-12 02:09:58,538 - INFO - تم الاتصال بنجاح - الحساب: 96406085
2025-09-12 02:09:58,539 - INFO - الرصيد: 99992.76
2025-09-12 02:09:58,539 - INFO - نوع الحساب: Demo
2025-09-12 02:09:58,546 - INFO - تم قطع الاتصال مع MT5
2025-09-12 02:10:00,878 - INFO - <PERSON><PERSON><PERSON> البيانات لـ EURUSD من alpha_vantage
2025-09-12 02:10:00,879 - INFO - جل<PERSON> ال<PERSON>يانات من Alpha Vantage لـ EURUSD (EURUSD)
2025-09-12 02:10:01,773 - INFO - جلب البيانات لـ EURUSD من alpha_vantage
2025-09-12 02:10:01,774 - INFO - جلب البيانات من Alpha Vantage لـ EURUSD (EURUSD)
2025-09-12 02:10:01,954 - INFO - تم جلب 100 صف من البيانات من Alpha Vantage
2025-09-12 02:10:01,966 - ERROR - خطأ في حساب المؤشرات التقنية: module 'ta.volume' has no attribute 'volume_sma'
2025-09-12 02:10:01,968 - ERROR - خطأ في إنشاء الميزات: 'RSI'
2025-09-12 02:10:01,972 - INFO - تم حفظ البيانات في data/EURUSD_20250912.csv
2025-09-12 02:10:02,667 - INFO - تم جلب 100 صف من البيانات من Alpha Vantage
2025-09-12 02:10:02,679 - INFO - 🤖 بدء تدريب النموذج الأصلي...
2025-09-12 02:10:02,679 - INFO - 📊 البيانات الأصلية: 100 صف، 5 عمود
2025-09-12 02:10:02,679 - INFO - 📊 أعمدة البيانات: ['Open', 'High', 'Low', 'Close', 'Volume']
2025-09-12 02:10:02,682 - INFO - ✅ تم إنشاء عمود الهدف (Target)
2025-09-12 02:10:02,684 - INFO - ✅ تم تحضير البيانات: 90 صف، 3 ميزة
2025-09-12 02:10:02,685 - INFO - ✅ الميزات المستخدمة: ['SMA_5', 'SMA_10', 'Price_Change']
2025-09-12 02:10:02,685 - INFO - ✅ توزيع الفئات: {1: 50, 0: 40}
2025-09-12 02:10:02,686 - INFO - ✅ تم تحضير البيانات: 90 صف، 3 ميزة
2025-09-12 02:10:02,686 - INFO - بيانات التدريب: 72 صف
2025-09-12 02:10:02,686 - INFO - بيانات الاختبار: 18 صف
2025-09-12 02:10:02,686 - INFO - ✅ تم تقسيم البيانات: تدريب 72، اختبار 18
2025-09-12 02:10:02,687 - INFO - 🤖 بدء تدريب النموذج...
2025-09-12 02:10:02,687 - INFO - 📊 بيانات التدريب: 72 صف، 3 ميزة
2025-09-12 02:10:02,687 - INFO - 📊 توزيع الهدف في التدريب: {1: 41, 0: 31}
2025-09-12 02:10:02,687 - INFO - 📊 عدد الفئات: 2, القيم: [np.int64(0), np.int64(1)]
2025-09-12 02:10:02,688 - INFO - ✅ تم إعداد النموذج للتصنيف الثنائي
2025-09-12 02:10:02,688 - INFO - 🚀 بدء عملية التدريب...
2025-09-12 02:10:02,712 - INFO - ✅ تم تدريب النموذج بنجاح
2025-09-12 02:10:02,713 - INFO - 📊 أفضل تكرار: 0
2025-09-12 02:10:02,713 - INFO - ✅ تم تدريب النموذج
2025-09-12 02:10:02,713 - INFO - 🔍 بدء تقييم النموذج...
2025-09-12 02:10:02,713 - INFO - 📊 بيانات الاختبار: 18 صف، 3 ميزة
2025-09-12 02:10:02,714 - INFO - 📊 توزيع الهدف في الاختبار: {0: 9, 1: 9}
2025-09-12 02:10:02,715 - INFO - ✅ تم التنبؤ بدون best_iteration
2025-09-12 02:10:02,715 - INFO - 📊 شكل التنبؤات: (18,)
2025-09-12 02:10:02,716 - INFO - 📊 التنبؤات النهائية: (array([0]), array([18]))
2025-09-12 02:10:02,718 - INFO - ✅ دقة النموذج: 0.5000
2025-09-12 02:10:02,731 - INFO - ✅ تم إنشاء تقرير التصنيف
2025-09-12 02:10:02,733 - INFO - ✅ تم إنشاء مصفوفة الخلط
2025-09-12 02:10:02,733 - INFO - 🎉 تم تقييم النموذج بنجاح!
2025-09-12 02:10:02,734 - INFO - ✅ تم تقييم النموذج: دقة 0.500
2025-09-12 02:10:02,739 - INFO - تم حفظ النموذج في models/trading_model.joblib
2025-09-12 02:10:02,739 - INFO - ✅ تم حفظ النموذج
2025-09-12 02:10:02,739 - INFO - 🎉 تم تدريب النموذج بنجاح!
2025-09-12 02:10:06,128 - INFO - 🤖 بدء تدريب الاستراتيجيات...
2025-09-12 02:10:06,128 - INFO - 📊 تدريب استراتيجية MA...
2025-09-12 02:10:06,242 - INFO -    ✅ MA: معدل النجاح 42.86%, الربح 0.0181, النقاط 30.59
2025-09-12 02:10:06,242 - INFO - 📊 تدريب استراتيجية RSI...
2025-09-12 02:10:06,355 - INFO -    ✅ RSI: معدل النجاح 40.00%, الربح 0.0146, النقاط 27.14
2025-09-12 02:10:06,355 - INFO - 📊 تدريب استراتيجية MACD...
2025-09-12 02:10:06,469 - INFO -    ✅ MACD: معدل النجاح 33.33%, الربح 0.0124, النقاط 25.46
2025-09-12 02:10:06,469 - INFO - 📊 تدريب استراتيجية BB...
2025-09-12 02:10:06,581 - INFO -    ✅ BB: معدل النجاح 56.25%, الربح -0.0187, النقاط 32.50
2025-09-12 02:10:06,582 - INFO - 📊 تدريب استراتيجية Combined...
2025-09-12 02:10:06,711 - INFO -    ✅ Combined: معدل النجاح 0.00%, الربح 0.0000, النقاط 0.00
2025-09-12 02:10:06,711 - INFO - 🏆 أفضل استراتيجية: BB (النقاط: 32.50)
2025-09-12 02:10:10,208 - INFO - تم تهيئة نظام التداول المستمر
2025-09-12 02:10:10,214 - INFO - 🧠 بدء تدريب الاستراتيجيات...
2025-09-12 02:10:10,215 - ERROR - خطأ في جلب البيانات: DataCollector.fetch_historical_data() got an unexpected keyword argument 'days'
2025-09-12 02:10:10,220 - ERROR - خطأ في تدريب MovingAverageStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:10:10,224 - ERROR - خطأ في تدريب RSIStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:10:10,227 - ERROR - خطأ في تدريب MACDStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:10:10,229 - ERROR - خطأ في تدريب BollingerBandsStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:10:10,245 - ERROR - خطأ في تدريب CombinedStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:10:10,245 - WARNING - لا توجد استراتيجيات مدربة
2025-09-12 02:10:10,246 - INFO - ✅ تم التدريب بنجاح في 2025-09-12 02:10:10.246093
2025-09-12 02:11:15,861 - INFO - 🤖 بدء تدريب الاستراتيجيات...
2025-09-12 02:11:15,862 - INFO - 📊 تدريب استراتيجية MA...
2025-09-12 02:11:15,985 - INFO -    ✅ MA: معدل النجاح 42.86%, الربح 0.0181, النقاط 30.59
2025-09-12 02:11:15,985 - INFO - 📊 تدريب استراتيجية RSI...
2025-09-12 02:11:16,110 - INFO -    ✅ RSI: معدل النجاح 40.00%, الربح 0.0146, النقاط 27.14
2025-09-12 02:11:16,110 - INFO - 📊 تدريب استراتيجية MACD...
2025-09-12 02:11:16,232 - INFO -    ✅ MACD: معدل النجاح 33.33%, الربح 0.0124, النقاط 25.46
2025-09-12 02:11:16,232 - INFO - 📊 تدريب استراتيجية BB...
2025-09-12 02:11:16,346 - INFO -    ✅ BB: معدل النجاح 56.25%, الربح -0.0187, النقاط 32.50
2025-09-12 02:11:16,346 - INFO - 📊 تدريب استراتيجية Combined...
2025-09-12 02:11:16,475 - INFO -    ✅ Combined: معدل النجاح 0.00%, الربح 0.0000, النقاط 0.00
2025-09-12 02:11:16,475 - INFO - 🏆 أفضل استراتيجية: BB (النقاط: 32.50)
2025-09-12 02:11:17,898 - INFO - 🧠 بدء تدريب الاستراتيجيات...
2025-09-12 02:11:17,900 - ERROR - خطأ في جلب البيانات: DataCollector.fetch_historical_data() got an unexpected keyword argument 'days'
2025-09-12 02:11:17,904 - ERROR - خطأ في تدريب MovingAverageStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:11:17,907 - ERROR - خطأ في تدريب RSIStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:11:17,910 - ERROR - خطأ في تدريب MACDStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:11:17,913 - ERROR - خطأ في تدريب BollingerBandsStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:11:17,928 - ERROR - خطأ في تدريب CombinedStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:11:17,928 - WARNING - لا توجد استراتيجيات مدربة
2025-09-12 02:11:17,928 - INFO - ✅ تم التدريب بنجاح في 2025-09-12 02:11:17.928842
2025-09-12 02:11:53,077 - INFO - جلب البيانات لـ EURUSD من alpha_vantage
2025-09-12 02:11:53,077 - INFO - جلب البيانات من Alpha Vantage لـ EURUSD (EURUSD)
2025-09-12 02:11:55,751 - INFO - تم جلب 100 صف من البيانات من Alpha Vantage
2025-09-12 02:11:55,763 - ERROR - خطأ في حساب المؤشرات التقنية: module 'ta.volume' has no attribute 'volume_sma'
2025-09-12 02:11:55,764 - ERROR - خطأ في إنشاء الميزات: 'RSI'
2025-09-12 02:11:55,765 - INFO - تم حفظ البيانات في data/EURUSD_20250912.csv
2025-09-12 02:11:59,441 - INFO - تم تحميل النموذج بنجاح
2025-09-12 02:12:01,830 - INFO - 🤖 بدء تدريب الاستراتيجيات...
2025-09-12 02:12:01,831 - INFO - 📊 تدريب استراتيجية MA...
2025-09-12 02:12:01,945 - INFO -    ✅ MA: معدل النجاح 42.86%, الربح 0.0181, النقاط 30.59
2025-09-12 02:12:01,945 - INFO - 📊 تدريب استراتيجية RSI...
2025-09-12 02:12:02,060 - INFO -    ✅ RSI: معدل النجاح 40.00%, الربح 0.0146, النقاط 27.14
2025-09-12 02:12:02,060 - INFO - 📊 تدريب استراتيجية MACD...
2025-09-12 02:12:02,174 - INFO -    ✅ MACD: معدل النجاح 33.33%, الربح 0.0124, النقاط 25.46
2025-09-12 02:12:02,174 - INFO - 📊 تدريب استراتيجية BB...
2025-09-12 02:12:02,287 - INFO -    ✅ BB: معدل النجاح 56.25%, الربح -0.0187, النقاط 32.50
2025-09-12 02:12:02,287 - INFO - 📊 تدريب استراتيجية Combined...
2025-09-12 02:12:02,420 - INFO -    ✅ Combined: معدل النجاح 0.00%, الربح 0.0000, النقاط 0.00
2025-09-12 02:12:02,420 - INFO - 🏆 أفضل استراتيجية: BB (النقاط: 32.50)
2025-09-12 02:12:03,160 - INFO - 🧠 بدء تدريب الاستراتيجيات...
2025-09-12 02:12:03,162 - ERROR - خطأ في جلب البيانات: DataCollector.fetch_historical_data() got an unexpected keyword argument 'days'
2025-09-12 02:12:03,166 - ERROR - خطأ في تدريب MovingAverageStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:12:03,169 - ERROR - خطأ في تدريب RSIStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:12:03,172 - ERROR - خطأ في تدريب MACDStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:12:03,175 - ERROR - خطأ في تدريب BollingerBandsStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:12:03,191 - ERROR - خطأ في تدريب CombinedStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:12:03,191 - WARNING - لا توجد استراتيجيات مدربة
2025-09-12 02:12:03,192 - INFO - ✅ تم التدريب بنجاح في 2025-09-12 02:12:03.192115
2025-09-12 02:12:38,650 - INFO - 🤖 بدء تدريب الاستراتيجيات...
2025-09-12 02:12:38,650 - INFO - 📊 تدريب استراتيجية MA...
2025-09-12 02:12:38,760 - INFO -    ✅ MA: معدل النجاح 42.86%, الربح 0.0181, النقاط 30.59
2025-09-12 02:12:38,760 - INFO - 📊 تدريب استراتيجية RSI...
2025-09-12 02:12:38,872 - INFO -    ✅ RSI: معدل النجاح 40.00%, الربح 0.0146, النقاط 27.14
2025-09-12 02:12:38,873 - INFO - 📊 تدريب استراتيجية MACD...
2025-09-12 02:12:38,984 - INFO -    ✅ MACD: معدل النجاح 33.33%, الربح 0.0124, النقاط 25.46
2025-09-12 02:12:38,984 - INFO - 📊 تدريب استراتيجية BB...
2025-09-12 02:12:39,095 - INFO -    ✅ BB: معدل النجاح 56.25%, الربح -0.0187, النقاط 32.50
2025-09-12 02:12:39,095 - INFO - 📊 تدريب استراتيجية Combined...
2025-09-12 02:12:39,220 - INFO -    ✅ Combined: معدل النجاح 0.00%, الربح 0.0000, النقاط 0.00
2025-09-12 02:12:39,220 - INFO - 🏆 أفضل استراتيجية: BB (النقاط: 32.50)
2025-09-12 02:12:48,772 - INFO - 🧠 بدء تدريب الاستراتيجيات...
2025-09-12 02:12:48,773 - ERROR - خطأ في جلب البيانات: DataCollector.fetch_historical_data() got an unexpected keyword argument 'days'
2025-09-12 02:12:48,777 - ERROR - خطأ في تدريب MovingAverageStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:12:48,781 - ERROR - خطأ في تدريب RSIStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:12:48,784 - ERROR - خطأ في تدريب MACDStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:12:48,787 - ERROR - خطأ في تدريب BollingerBandsStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:12:48,803 - ERROR - خطأ في تدريب CombinedStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:12:48,803 - WARNING - لا توجد استراتيجيات مدربة
2025-09-12 02:12:48,804 - INFO - ✅ تم التدريب بنجاح في 2025-09-12 02:12:48.803989
2025-09-12 02:14:33,114 - INFO - ⏹️ تم إيقاف التداول
2025-09-12 02:27:49,648 - INFO - تم بدء تشغيل نظام التداول الذكي
2025-09-12 02:27:49,648 - INFO - AI Trading System started
2025-09-12 02:27:51,310 - INFO - تم تشغيل الواجهة الرسومية بنجاح
2025-09-12 02:27:51,310 - INFO - GUI launched successfully
2025-09-12 02:27:54,229 - INFO - تم الاتصال بنجاح - الحساب: 96406085
2025-09-12 02:27:54,229 - INFO - الرصيد: 99992.76
2025-09-12 02:27:54,229 - INFO - نوع الحساب: Demo
2025-09-12 02:27:54,249 - INFO - تم قطع الاتصال مع MT5
2025-09-12 02:27:55,879 - INFO - تم تحميل النموذج بنجاح
2025-09-12 02:27:57,744 - INFO - 🤖 بدء تدريب الاستراتيجيات...
2025-09-12 02:27:57,745 - INFO - 📊 تدريب استراتيجية MA...
2025-09-12 02:27:57,921 - INFO -    ✅ MA: معدل النجاح 42.86%, الربح 0.0181, النقاط 30.59
2025-09-12 02:27:57,921 - INFO - 📊 تدريب استراتيجية RSI...
2025-09-12 02:27:58,115 - INFO -    ✅ RSI: معدل النجاح 40.00%, الربح 0.0146, النقاط 27.14
2025-09-12 02:27:58,115 - INFO - 📊 تدريب استراتيجية MACD...
2025-09-12 02:27:58,284 - INFO -    ✅ MACD: معدل النجاح 33.33%, الربح 0.0124, النقاط 25.46
2025-09-12 02:27:58,285 - INFO - 📊 تدريب استراتيجية BB...
2025-09-12 02:27:58,451 - INFO -    ✅ BB: معدل النجاح 56.25%, الربح -0.0187, النقاط 32.50
2025-09-12 02:27:58,452 - INFO - 📊 تدريب استراتيجية Combined...
2025-09-12 02:27:58,658 - INFO -    ✅ Combined: معدل النجاح 0.00%, الربح 0.0000, النقاط 0.00
2025-09-12 02:27:58,658 - INFO - 🏆 أفضل استراتيجية: BB (النقاط: 32.50)
2025-09-12 02:27:59,650 - INFO - تم تهيئة نظام التداول المستمر
2025-09-12 02:27:59,658 - INFO - 🧠 بدء تدريب الاستراتيجيات...
2025-09-12 02:27:59,660 - ERROR - خطأ في جلب البيانات: DataCollector.fetch_historical_data() got an unexpected keyword argument 'days'
2025-09-12 02:27:59,666 - ERROR - خطأ في تدريب MovingAverageStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:27:59,673 - ERROR - خطأ في تدريب RSIStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:27:59,682 - ERROR - خطأ في تدريب MACDStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:27:59,687 - ERROR - خطأ في تدريب BollingerBandsStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:27:59,716 - ERROR - خطأ في تدريب CombinedStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:27:59,716 - WARNING - لا توجد استراتيجيات مدربة
2025-09-12 02:27:59,717 - INFO - ✅ تم التدريب بنجاح في 2025-09-12 02:27:59.717094
2025-09-12 02:29:34,853 - INFO - جلب البيانات لـ EURUSD من alpha_vantage
2025-09-12 02:29:34,853 - INFO - جلب البيانات من Alpha Vantage لـ EURUSD (EURUSD)
2025-09-12 02:29:35,812 - INFO - جلب البيانات لـ EURUSD من alpha_vantage
2025-09-12 02:29:35,812 - INFO - جلب البيانات من Alpha Vantage لـ EURUSD (EURUSD)
2025-09-12 02:29:38,332 - INFO - تم جلب 100 صف من البيانات من Alpha Vantage
2025-09-12 02:29:38,348 - ERROR - خطأ في حساب المؤشرات التقنية: module 'ta.volume' has no attribute 'volume_sma'
2025-09-12 02:29:38,349 - ERROR - خطأ في إنشاء الميزات: 'RSI'
2025-09-12 02:29:38,353 - INFO - تم حفظ البيانات في data/EURUSD_20250912.csv
2025-09-12 02:29:39,725 - INFO - تم جلب 100 صف من البيانات من Alpha Vantage
2025-09-12 02:29:39,744 - INFO - 🤖 بدء تدريب النموذج الأصلي...
2025-09-12 02:29:39,745 - INFO - 📊 البيانات الأصلية: 100 صف، 5 عمود
2025-09-12 02:29:39,745 - INFO - 📊 أعمدة البيانات: ['Open', 'High', 'Low', 'Close', 'Volume']
2025-09-12 02:29:39,750 - INFO - ✅ تم إنشاء عمود الهدف (Target)
2025-09-12 02:29:39,751 - INFO - ✅ تم تحضير البيانات: 90 صف، 3 ميزة
2025-09-12 02:29:39,752 - INFO - ✅ الميزات المستخدمة: ['SMA_5', 'SMA_10', 'Price_Change']
2025-09-12 02:29:39,752 - INFO - ✅ توزيع الفئات: {1: 50, 0: 40}
2025-09-12 02:29:39,753 - INFO - ✅ تم تحضير البيانات: 90 صف، 3 ميزة
2025-09-12 02:29:39,753 - INFO - بيانات التدريب: 72 صف
2025-09-12 02:29:39,753 - INFO - بيانات الاختبار: 18 صف
2025-09-12 02:29:39,753 - INFO - ✅ تم تقسيم البيانات: تدريب 72، اختبار 18
2025-09-12 02:29:39,754 - INFO - 🤖 بدء تدريب النموذج...
2025-09-12 02:29:39,754 - INFO - 📊 بيانات التدريب: 72 صف، 3 ميزة
2025-09-12 02:29:39,754 - INFO - 📊 توزيع الهدف في التدريب: {1: 41, 0: 31}
2025-09-12 02:29:39,755 - INFO - 📊 عدد الفئات: 2, القيم: [np.int64(0), np.int64(1)]
2025-09-12 02:29:39,755 - INFO - ✅ تم إعداد النموذج للتصنيف الثنائي
2025-09-12 02:29:39,755 - INFO - 🚀 بدء عملية التدريب...
2025-09-12 02:29:39,796 - INFO - ✅ تم تدريب النموذج بنجاح
2025-09-12 02:29:39,796 - INFO - 📊 أفضل تكرار: 0
2025-09-12 02:29:39,797 - INFO - ✅ تم تدريب النموذج
2025-09-12 02:29:39,797 - INFO - 🔍 بدء تقييم النموذج...
2025-09-12 02:29:39,797 - INFO - 📊 بيانات الاختبار: 18 صف، 3 ميزة
2025-09-12 02:29:39,798 - INFO - 📊 توزيع الهدف في الاختبار: {0: 9, 1: 9}
2025-09-12 02:29:39,799 - INFO - ✅ تم التنبؤ بدون best_iteration
2025-09-12 02:29:39,800 - INFO - 📊 شكل التنبؤات: (18,)
2025-09-12 02:29:39,800 - INFO - 📊 التنبؤات النهائية: (array([0]), array([18]))
2025-09-12 02:29:39,803 - INFO - ✅ دقة النموذج: 0.5000
2025-09-12 02:29:39,817 - INFO - ✅ تم إنشاء تقرير التصنيف
2025-09-12 02:29:39,820 - INFO - ✅ تم إنشاء مصفوفة الخلط
2025-09-12 02:29:39,820 - INFO - 🎉 تم تقييم النموذج بنجاح!
2025-09-12 02:29:39,820 - INFO - ✅ تم تقييم النموذج: دقة 0.500
2025-09-12 02:29:39,825 - INFO - تم حفظ النموذج في models/trading_model.joblib
2025-09-12 02:29:39,825 - INFO - ✅ تم حفظ النموذج
2025-09-12 02:29:39,825 - INFO - 🎉 تم تدريب النموذج بنجاح!
2025-09-12 02:29:39,874 - INFO - تم تحميل النموذج بنجاح
2025-09-12 02:46:09,697 - INFO - ⏹️ تم إيقاف التداول
2025-09-12 02:46:20,910 - INFO - تم بدء تشغيل نظام التداول الذكي
2025-09-12 02:46:20,911 - INFO - AI Trading System started
2025-09-12 02:46:22,584 - INFO - تم تشغيل الواجهة الرسومية بنجاح
2025-09-12 02:46:22,584 - INFO - GUI launched successfully
2025-09-12 02:46:25,516 - INFO - تم الاتصال بنجاح - الحساب: 96406085
2025-09-12 02:46:25,517 - INFO - الرصيد: 99965.76
2025-09-12 02:46:25,517 - INFO - نوع الحساب: Demo
2025-09-12 02:46:25,530 - INFO - تم قطع الاتصال مع MT5
2025-09-12 02:46:26,484 - INFO - تم تحميل النموذج بنجاح
2025-09-12 02:46:29,513 - INFO - 🤖 بدء تدريب الاستراتيجيات...
2025-09-12 02:46:29,513 - INFO - 📊 تدريب استراتيجية MA...
2025-09-12 02:46:29,689 - INFO -    ✅ MA: معدل النجاح 42.86%, الربح 0.0181, النقاط 30.59
2025-09-12 02:46:29,689 - INFO - 📊 تدريب استراتيجية RSI...
2025-09-12 02:46:29,847 - INFO -    ✅ RSI: معدل النجاح 40.00%, الربح 0.0146, النقاط 27.14
2025-09-12 02:46:29,847 - INFO - 📊 تدريب استراتيجية MACD...
2025-09-12 02:46:30,005 - INFO -    ✅ MACD: معدل النجاح 33.33%, الربح 0.0124, النقاط 25.46
2025-09-12 02:46:30,005 - INFO - 📊 تدريب استراتيجية BB...
2025-09-12 02:46:30,203 - INFO -    ✅ BB: معدل النجاح 56.25%, الربح -0.0187, النقاط 32.50
2025-09-12 02:46:30,204 - INFO - 📊 تدريب استراتيجية Combined...
2025-09-12 02:46:30,375 - INFO -    ✅ Combined: معدل النجاح 0.00%, الربح 0.0000, النقاط 0.00
2025-09-12 02:46:30,375 - INFO - 🏆 أفضل استراتيجية: BB (النقاط: 32.50)
2025-09-12 02:46:32,016 - INFO - تم تهيئة نظام التداول المستمر
2025-09-12 02:46:32,031 - INFO - 🧠 بدء تدريب الاستراتيجيات...
2025-09-12 02:46:32,032 - ERROR - خطأ في جلب البيانات: DataCollector.fetch_historical_data() got an unexpected keyword argument 'days'
2025-09-12 02:46:32,038 - ERROR - خطأ في تدريب MovingAverageStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:46:32,042 - ERROR - خطأ في تدريب RSIStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:46:32,048 - ERROR - خطأ في تدريب MACDStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:46:32,054 - ERROR - خطأ في تدريب BollingerBandsStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:46:32,079 - ERROR - خطأ في تدريب CombinedStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:46:32,080 - WARNING - لا توجد استراتيجيات مدربة
2025-09-12 02:46:32,080 - INFO - ✅ تم التدريب بنجاح في 2025-09-12 02:46:32.080724
2025-09-12 02:51:45,389 - INFO - 🤖 بدء تدريب الاستراتيجيات...
2025-09-12 02:51:45,389 - INFO - 📊 تدريب استراتيجية MA...
2025-09-12 02:51:45,528 - INFO -    ✅ MA: معدل النجاح 42.86%, الربح 0.0181, النقاط 30.59
2025-09-12 02:51:45,529 - INFO - 📊 تدريب استراتيجية RSI...
2025-09-12 02:51:45,681 - INFO -    ✅ RSI: معدل النجاح 40.00%, الربح 0.0146, النقاط 27.14
2025-09-12 02:51:45,682 - INFO - 📊 تدريب استراتيجية MACD...
2025-09-12 02:51:45,836 - INFO -    ✅ MACD: معدل النجاح 33.33%, الربح 0.0124, النقاط 25.46
2025-09-12 02:51:45,836 - INFO - 📊 تدريب استراتيجية BB...
2025-09-12 02:51:45,980 - INFO -    ✅ BB: معدل النجاح 56.25%, الربح -0.0187, النقاط 32.50
2025-09-12 02:51:45,980 - INFO - 📊 تدريب استراتيجية Combined...
2025-09-12 02:51:46,129 - INFO -    ✅ Combined: معدل النجاح 0.00%, الربح 0.0000, النقاط 0.00
2025-09-12 02:51:46,129 - INFO - 🏆 أفضل استراتيجية: BB (النقاط: 32.50)
2025-09-12 02:51:47,619 - INFO - 🧠 بدء تدريب الاستراتيجيات...
2025-09-12 02:51:47,620 - ERROR - خطأ في جلب البيانات: DataCollector.fetch_historical_data() got an unexpected keyword argument 'days'
2025-09-12 02:51:47,625 - ERROR - خطأ في تدريب MovingAverageStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:51:47,632 - ERROR - خطأ في تدريب RSIStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:51:47,638 - ERROR - خطأ في تدريب MACDStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:51:47,642 - ERROR - خطأ في تدريب BollingerBandsStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:51:47,667 - ERROR - خطأ في تدريب CombinedStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 02:51:47,667 - WARNING - لا توجد استراتيجيات مدربة
2025-09-12 02:51:47,667 - INFO - ✅ تم التدريب بنجاح في 2025-09-12 02:51:47.667767
2025-09-12 03:13:57,481 - INFO - 🤖 بدء تدريب الاستراتيجيات...
2025-09-12 03:13:57,481 - INFO - 📊 تدريب استراتيجية MA...
2025-09-12 03:13:57,631 - INFO -    ✅ MA: معدل النجاح 42.86%, الربح 0.0181, النقاط 30.59
2025-09-12 03:13:57,632 - INFO - 📊 تدريب استراتيجية RSI...
2025-09-12 03:13:57,826 - INFO -    ✅ RSI: معدل النجاح 40.00%, الربح 0.0146, النقاط 27.14
2025-09-12 03:13:57,826 - INFO - 📊 تدريب استراتيجية MACD...
2025-09-12 03:13:57,996 - INFO -    ✅ MACD: معدل النجاح 33.33%, الربح 0.0124, النقاط 25.46
2025-09-12 03:13:57,997 - INFO - 📊 تدريب استراتيجية BB...
2025-09-12 03:13:58,254 - INFO -    ✅ BB: معدل النجاح 56.25%, الربح -0.0187, النقاط 32.50
2025-09-12 03:13:58,265 - INFO - 📊 تدريب استراتيجية Combined...
2025-09-12 03:13:58,492 - INFO -    ✅ Combined: معدل النجاح 0.00%, الربح 0.0000, النقاط 0.00
2025-09-12 03:13:58,492 - INFO - 🏆 أفضل استراتيجية: BB (النقاط: 32.50)
2025-09-12 03:14:37,009 - INFO - 🧠 بدء تدريب الاستراتيجيات...
2025-09-12 03:14:37,011 - ERROR - خطأ في جلب البيانات: DataCollector.fetch_historical_data() got an unexpected keyword argument 'days'
2025-09-12 03:14:37,016 - ERROR - خطأ في تدريب MovingAverageStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:14:37,020 - ERROR - خطأ في تدريب RSIStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:14:37,024 - ERROR - خطأ في تدريب MACDStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:14:37,028 - ERROR - خطأ في تدريب BollingerBandsStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:14:37,050 - ERROR - خطأ في تدريب CombinedStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:14:37,050 - WARNING - لا توجد استراتيجيات مدربة
2025-09-12 03:14:37,051 - INFO - ✅ تم التدريب بنجاح في 2025-09-12 03:14:37.051150
2025-09-12 03:17:09,587 - INFO - 🤖 بدء تدريب الاستراتيجيات...
2025-09-12 03:17:09,587 - INFO - 📊 تدريب استراتيجية MA...
2025-09-12 03:17:09,729 - INFO -    ✅ MA: معدل النجاح 42.86%, الربح 0.0181, النقاط 30.59
2025-09-12 03:17:09,729 - INFO - 📊 تدريب استراتيجية RSI...
2025-09-12 03:17:09,858 - INFO -    ✅ RSI: معدل النجاح 40.00%, الربح 0.0146, النقاط 27.14
2025-09-12 03:17:09,858 - INFO - 📊 تدريب استراتيجية MACD...
2025-09-12 03:17:09,989 - INFO -    ✅ MACD: معدل النجاح 33.33%, الربح 0.0124, النقاط 25.46
2025-09-12 03:17:09,989 - INFO - 📊 تدريب استراتيجية BB...
2025-09-12 03:17:10,152 - INFO -    ✅ BB: معدل النجاح 56.25%, الربح -0.0187, النقاط 32.50
2025-09-12 03:17:10,152 - INFO - 📊 تدريب استراتيجية Combined...
2025-09-12 03:17:10,330 - INFO -    ✅ Combined: معدل النجاح 0.00%, الربح 0.0000, النقاط 0.00
2025-09-12 03:17:10,331 - INFO - 🏆 أفضل استراتيجية: BB (النقاط: 32.50)
2025-09-12 03:17:11,153 - INFO - 🧠 بدء تدريب الاستراتيجيات...
2025-09-12 03:17:11,157 - ERROR - خطأ في جلب البيانات: DataCollector.fetch_historical_data() got an unexpected keyword argument 'days'
2025-09-12 03:17:11,163 - ERROR - خطأ في تدريب MovingAverageStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:17:11,170 - ERROR - خطأ في تدريب RSIStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:17:11,177 - ERROR - خطأ في تدريب MACDStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:17:11,182 - ERROR - خطأ في تدريب BollingerBandsStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:17:11,210 - ERROR - خطأ في تدريب CombinedStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:17:11,210 - WARNING - لا توجد استراتيجيات مدربة
2025-09-12 03:17:11,210 - INFO - ✅ تم التدريب بنجاح في 2025-09-12 03:17:11.210950
2025-09-12 03:17:19,639 - INFO - ⏹️ تم إيقاف التداول
2025-09-12 03:17:20,163 - INFO - 🧠 بدء تدريب الاستراتيجيات...
2025-09-12 03:17:20,165 - ERROR - خطأ في جلب البيانات: DataCollector.fetch_historical_data() got an unexpected keyword argument 'days'
2025-09-12 03:17:20,171 - ERROR - خطأ في تدريب MovingAverageStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:17:20,177 - ERROR - خطأ في تدريب RSIStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:17:20,184 - ERROR - خطأ في تدريب MACDStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:17:20,188 - ERROR - خطأ في تدريب BollingerBandsStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:17:20,217 - ERROR - خطأ في تدريب CombinedStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:17:20,217 - WARNING - لا توجد استراتيجيات مدربة
2025-09-12 03:17:20,217 - INFO - ✅ تم التدريب بنجاح في 2025-09-12 03:17:20.217830
2025-09-12 03:18:22,321 - INFO - ⏹️ تم إيقاف التداول
2025-09-12 03:18:22,774 - INFO - 🧠 بدء تدريب الاستراتيجيات...
2025-09-12 03:18:22,776 - ERROR - خطأ في جلب البيانات: DataCollector.fetch_historical_data() got an unexpected keyword argument 'days'
2025-09-12 03:18:22,782 - ERROR - خطأ في تدريب MovingAverageStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:18:22,787 - ERROR - خطأ في تدريب RSIStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:18:22,791 - ERROR - خطأ في تدريب MACDStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:18:22,795 - ERROR - خطأ في تدريب BollingerBandsStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:18:22,817 - ERROR - خطأ في تدريب CombinedStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:18:22,817 - WARNING - لا توجد استراتيجيات مدربة
2025-09-12 03:18:22,818 - INFO - ✅ تم التدريب بنجاح في 2025-09-12 03:18:22.818142
2025-09-12 03:18:24,360 - INFO - 🤖 بدء تدريب الاستراتيجيات...
2025-09-12 03:18:24,360 - INFO - 📊 تدريب استراتيجية MA...
2025-09-12 03:18:24,487 - INFO -    ✅ MA: معدل النجاح 42.86%, الربح 0.0181, النقاط 30.59
2025-09-12 03:18:24,488 - INFO - 📊 تدريب استراتيجية RSI...
2025-09-12 03:18:24,617 - INFO -    ✅ RSI: معدل النجاح 40.00%, الربح 0.0146, النقاط 27.14
2025-09-12 03:18:24,617 - INFO - 📊 تدريب استراتيجية MACD...
2025-09-12 03:18:24,746 - INFO -    ✅ MACD: معدل النجاح 33.33%, الربح 0.0124, النقاط 25.46
2025-09-12 03:18:24,746 - INFO - 📊 تدريب استراتيجية BB...
2025-09-12 03:18:24,874 - INFO -    ✅ BB: معدل النجاح 56.25%, الربح -0.0187, النقاط 32.50
2025-09-12 03:18:24,874 - INFO - 📊 تدريب استراتيجية Combined...
2025-09-12 03:18:25,018 - INFO -    ✅ Combined: معدل النجاح 0.00%, الربح 0.0000, النقاط 0.00
2025-09-12 03:18:25,018 - INFO - 🏆 أفضل استراتيجية: BB (النقاط: 32.50)
2025-09-12 03:18:25,647 - INFO - 🧠 بدء تدريب الاستراتيجيات...
2025-09-12 03:18:25,650 - ERROR - خطأ في جلب البيانات: DataCollector.fetch_historical_data() got an unexpected keyword argument 'days'
2025-09-12 03:18:25,654 - ERROR - خطأ في تدريب MovingAverageStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:18:25,658 - ERROR - خطأ في تدريب RSIStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:18:25,662 - ERROR - خطأ في تدريب MACDStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:18:25,666 - ERROR - خطأ في تدريب BollingerBandsStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:18:25,688 - ERROR - خطأ في تدريب CombinedStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 03:18:25,689 - WARNING - لا توجد استراتيجيات مدربة
2025-09-12 03:18:25,689 - INFO - ✅ تم التدريب بنجاح في 2025-09-12 03:18:25.689279
2025-09-12 03:18:31,432 - INFO - جلب البيانات لـ EURUSD من alpha_vantage
2025-09-12 03:18:31,432 - INFO - جلب البيانات من Alpha Vantage لـ EURUSD (EURUSD)
2025-09-12 03:18:33,157 - INFO - جلب البيانات لـ EURUSD من alpha_vantage
2025-09-12 03:18:33,157 - INFO - جلب البيانات من Alpha Vantage لـ EURUSD (EURUSD)
2025-09-12 03:18:35,213 - INFO - تم جلب 100 صف من البيانات من Alpha Vantage
2025-09-12 03:18:35,227 - ERROR - خطأ في حساب المؤشرات التقنية: module 'ta.volume' has no attribute 'volume_sma'
2025-09-12 03:18:35,228 - ERROR - خطأ في إنشاء الميزات: 'RSI'
2025-09-12 03:18:35,232 - INFO - تم حفظ البيانات في data/EURUSD_20250912.csv
2025-09-12 03:18:36,747 - INFO - تم جلب 100 صف من البيانات من Alpha Vantage
2025-09-12 03:18:36,763 - INFO - 🤖 بدء تدريب النموذج الأصلي...
2025-09-12 03:18:36,763 - INFO - 📊 البيانات الأصلية: 100 صف، 5 عمود
2025-09-12 03:18:36,763 - INFO - 📊 أعمدة البيانات: ['Open', 'High', 'Low', 'Close', 'Volume']
2025-09-12 03:18:36,766 - INFO - ✅ تم إنشاء عمود الهدف (Target)
2025-09-12 03:18:36,767 - INFO - ✅ تم تحضير البيانات: 90 صف، 3 ميزة
2025-09-12 03:18:36,767 - INFO - ✅ الميزات المستخدمة: ['SMA_5', 'SMA_10', 'Price_Change']
2025-09-12 03:18:36,768 - INFO - ✅ توزيع الفئات: {1: 51, 0: 39}
2025-09-12 03:18:36,768 - INFO - ✅ تم تحضير البيانات: 90 صف، 3 ميزة
2025-09-12 03:18:36,769 - INFO - بيانات التدريب: 72 صف
2025-09-12 03:18:36,769 - INFO - بيانات الاختبار: 18 صف
2025-09-12 03:18:36,770 - INFO - ✅ تم تقسيم البيانات: تدريب 72، اختبار 18
2025-09-12 03:18:36,770 - INFO - 🤖 بدء تدريب النموذج...
2025-09-12 03:18:36,770 - INFO - 📊 بيانات التدريب: 72 صف، 3 ميزة
2025-09-12 03:18:36,771 - INFO - 📊 توزيع الهدف في التدريب: {1: 41, 0: 31}
2025-09-12 03:18:36,771 - INFO - 📊 عدد الفئات: 2, القيم: [np.int64(0), np.int64(1)]
2025-09-12 03:18:36,771 - INFO - ✅ تم إعداد النموذج للتصنيف الثنائي
2025-09-12 03:18:36,772 - INFO - 🚀 بدء عملية التدريب...
2025-09-12 03:18:36,811 - INFO - ✅ تم تدريب النموذج بنجاح
2025-09-12 03:18:36,811 - INFO - 📊 أفضل تكرار: 0
2025-09-12 03:18:36,812 - INFO - ✅ تم تدريب النموذج
2025-09-12 03:18:36,812 - INFO - 🔍 بدء تقييم النموذج...
2025-09-12 03:18:36,812 - INFO - 📊 بيانات الاختبار: 18 صف، 3 ميزة
2025-09-12 03:18:36,813 - INFO - 📊 توزيع الهدف في الاختبار: {1: 10, 0: 8}
2025-09-12 03:18:36,814 - INFO - ✅ تم التنبؤ بدون best_iteration
2025-09-12 03:18:36,815 - INFO - 📊 شكل التنبؤات: (18,)
2025-09-12 03:18:36,815 - INFO - 📊 التنبؤات النهائية: (array([0]), array([18]))
2025-09-12 03:18:36,818 - INFO - ✅ دقة النموذج: 0.4444
2025-09-12 03:18:36,830 - INFO - ✅ تم إنشاء تقرير التصنيف
2025-09-12 03:18:36,833 - INFO - ✅ تم إنشاء مصفوفة الخلط
2025-09-12 03:18:36,833 - INFO - 🎉 تم تقييم النموذج بنجاح!
2025-09-12 03:18:36,833 - INFO - ✅ تم تقييم النموذج: دقة 0.444
2025-09-12 03:18:36,838 - INFO - تم حفظ النموذج في models/trading_model.joblib
2025-09-12 03:18:36,838 - INFO - ✅ تم حفظ النموذج
2025-09-12 03:18:36,839 - INFO - 🎉 تم تدريب النموذج بنجاح!
2025-09-12 03:18:36,885 - INFO - تم تحميل النموذج بنجاح
2025-09-12 03:18:55,399 - INFO - ⏹️ تم إيقاف التداول
2025-09-12 04:56:30,286 - INFO - تم بدء تشغيل نظام التداول الذكي
2025-09-12 04:56:30,287 - INFO - AI Trading System started
2025-09-12 04:56:31,716 - INFO - تم تشغيل الواجهة الرسومية بنجاح
2025-09-12 04:56:31,716 - INFO - GUI launched successfully
2025-09-12 04:56:33,899 - INFO - تم تهيئة نظام التداول المستمر
2025-09-12 04:56:36,237 - INFO - تم الاتصال بنجاح - الحساب: 96406085
2025-09-12 04:56:36,237 - INFO - الرصيد: 99950.69
2025-09-12 04:56:36,238 - INFO - نوع الحساب: Demo
2025-09-12 04:56:36,244 - INFO - تم قطع الاتصال مع MT5
2025-09-12 04:56:37,245 - INFO - تم تحميل النموذج بنجاح
2025-09-12 04:56:38,984 - INFO - 🤖 بدء تدريب الاستراتيجيات...
2025-09-12 04:56:38,984 - INFO - 📊 تدريب استراتيجية MA...
2025-09-12 04:56:39,103 - INFO -    ✅ MA: معدل النجاح 42.86%, الربح 0.0181, النقاط 30.59
2025-09-12 04:56:39,103 - INFO - 📊 تدريب استراتيجية RSI...
2025-09-12 04:56:39,226 - INFO -    ✅ RSI: معدل النجاح 40.00%, الربح 0.0146, النقاط 27.14
2025-09-12 04:56:39,226 - INFO - 📊 تدريب استراتيجية MACD...
2025-09-12 04:56:39,346 - INFO -    ✅ MACD: معدل النجاح 33.33%, الربح 0.0124, النقاط 25.46
2025-09-12 04:56:39,347 - INFO - 📊 تدريب استراتيجية BB...
2025-09-12 04:56:39,484 - INFO -    ✅ BB: معدل النجاح 56.25%, الربح -0.0187, النقاط 32.50
2025-09-12 04:56:39,484 - INFO - 📊 تدريب استراتيجية Combined...
2025-09-12 04:56:39,636 - INFO -    ✅ Combined: معدل النجاح 0.00%, الربح 0.0000, النقاط 0.00
2025-09-12 04:56:39,636 - INFO - 🏆 أفضل استراتيجية: BB (النقاط: 32.50)
2025-09-12 04:56:41,000 - INFO - تم تهيئة نظام التداول المستمر
2025-09-12 04:56:41,006 - INFO - 🧠 بدء تدريب الاستراتيجيات...
2025-09-12 04:56:41,008 - ERROR - خطأ في جلب البيانات: DataCollector.fetch_historical_data() got an unexpected keyword argument 'days'
2025-09-12 04:56:41,013 - ERROR - خطأ في تدريب MovingAverageStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 04:56:41,017 - ERROR - خطأ في تدريب RSIStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 04:56:41,020 - ERROR - خطأ في تدريب MACDStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 04:56:41,024 - ERROR - خطأ في تدريب BollingerBandsStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 04:56:41,047 - ERROR - خطأ في تدريب CombinedStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 04:56:41,047 - WARNING - لا توجد استراتيجيات مدربة
2025-09-12 04:56:41,047 - INFO - ✅ تم التدريب بنجاح في 2025-09-12 04:56:41.047842
2025-09-12 04:56:42,178 - INFO - تم تهيئة نظام التداول المستمر
2025-09-12 04:56:46,539 - INFO - تم تهيئة نظام التداول المستمر
2025-09-12 04:56:46,540 - INFO - 🚀 بدء نظام التداول المستمر
2025-09-12 04:56:46,541 - INFO - 🧠 بدء حلقة التدريب المستمر
2025-09-12 04:56:46,542 - INFO - 🧠 بدء تدريب الاستراتيجيات...
2025-09-12 04:56:46,542 - INFO - 💰 بدء حلقة التداول المستمر
2025-09-12 04:56:46,542 - INFO - ✅ تم بدء النظام المستمر
2025-09-12 04:56:46,543 - WARNING - لا توجد استراتيجية مدربة، انتظار التدريب...
2025-09-12 04:56:46,545 - ERROR - خطأ في جلب البيانات: DataCollector.fetch_historical_data() got an unexpected keyword argument 'days'
2025-09-12 04:56:46,550 - ERROR - خطأ في تدريب MovingAverageStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 04:56:46,554 - ERROR - خطأ في تدريب RSIStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 04:56:46,557 - ERROR - خطأ في تدريب MACDStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 04:56:46,563 - ERROR - خطأ في تدريب BollingerBandsStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 04:56:46,584 - ERROR - خطأ في تدريب CombinedStrategy: TradingStrategy.calculate_performance() missing 1 required positional argument: 'signals'
2025-09-12 04:56:46,585 - WARNING - لا توجد استراتيجيات مدربة
2025-09-12 04:56:46,585 - INFO - ✅ تم التدريب بنجاح في 2025-09-12 04:56:46.585514
2025-09-12 04:57:55,114 - INFO - ⏹️ تم إيقاف التداول
