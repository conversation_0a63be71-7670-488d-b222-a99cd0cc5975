# اختبار إعدادات الطباعة 🧪

## 🎯 خطوات الاختبار:

### 1. 🚀 تشغيل التطبيق
1. شغل التطبيق
2. سجل دخول كمدير (Administrator)

### 2. 🎨 اختبار صفحة التصميم
1. اذهب لصفحة **تصميم صفحات الطباعة**
2. اختر نوع التقرير: **تقرير المبيعات**
3. غير الإعدادات التالية:
   - **لون العنوان**: اختر لون أحمر
   - **حجم خط العنوان**: 20
   - **نص العنوان**: "تقرير مبيعات الصيدلية المحدث"
   - **لون خلفية الجدول**: اختر لون أزرق فاتح
4. اضغط **حفظ الإعدادات**
5. يجب أن تظهر رسالة "تم حفظ الإعدادات بنجاح!"

### 3. 📊 اختبار تطبيق الإعدادات
1. اذهب لصفحة **تقرير المبيعات**
2. اضغط **🖨️ طباعة التقرير**
3. في نافذة المعاينة، تحقق من:
   - ✅ العنوان باللون الأحمر
   - ✅ حجم الخط 20
   - ✅ النص "تقرير مبيعات الصيدلية المحدث"
   - ✅ خلفية الجدول باللون الأزرق الفاتح

### 4. 🔄 اختبار تقرير آخر
1. ارجع لصفحة **تصميم صفحات الطباعة**
2. اختر نوع التقرير: **جرد الأدوية**
3. غير الإعدادات:
   - **لون العنوان**: اختر لون أخضر
   - **نص العنوان**: "جرد أدوية الصيدلية"
4. احفظ الإعدادات
5. اذهب لصفحة **فحص صلاحية الأدوية**
6. اختر نوع التقرير واضغط **طباعة**
7. تحقق من تطبيق الإعدادات الجديدة

### 5. 🌐 اختبار تطبيق على جميع التقارير
1. ارجع لصفحة **تصميم صفحات الطباعة**
2. اختر أي نوع تقرير
3. عدل الإعدادات حسب رغبتك
4. اضغط **تطبيق على جميع التقارير**
5. يجب أن تظهر رسالة "تم تطبيق الإعدادات على جميع التقارير!"
6. اختبر عدة تقارير للتأكد من تطبيق نفس الإعدادات

## ✅ النتائج المتوقعة:

### إذا عمل كل شيء بشكل صحيح:
- ✅ تحفظ الإعدادات بنجاح
- ✅ تطبق الإعدادات على التقارير المناسبة
- ✅ كل نوع تقرير له إعداداته المستقلة
- ✅ خيار "تطبيق على جميع التقارير" يعمل
- ✅ الألوان والخطوط تظهر بشكل صحيح

### إذا لم تعمل الإعدادات:
- ❌ الإعدادات لا تحفظ
- ❌ التقارير تستخدم الإعدادات الافتراضية
- ❌ ظهور رسائل خطأ

## 🔧 استكشاف الأخطاء:

### إذا ظهرت رسالة خطأ في الحفظ:
1. تحقق من اتصال قاعدة البيانات
2. تأكد من وجود جدول `print_settings`
3. شغل السكريبت `create_print_settings_table.sql`

### إذا لم تطبق الإعدادات:
1. أعد تشغيل التطبيق
2. تحقق من Debug Output للرسائل
3. تأكد من حفظ الإعدادات أولاً

### إذا ظهرت أخطاء SQL:
1. تحقق من اسم الخادم (يجب أن يكون NARUTO)
2. تحقق من وجود قاعدة البيانات pharmacy
3. تحقق من صلاحيات الوصول

## 📝 تقرير الاختبار:

### ✅ الميزات التي تعمل:
- [ ] حفظ إعدادات الطباعة
- [ ] تطبيق الإعدادات على التقارير
- [ ] إعدادات مستقلة لكل نوع تقرير
- [ ] تطبيق على جميع التقارير
- [ ] ألوان العنوان والجدول
- [ ] أحجام الخطوط
- [ ] نصوص العناوين المخصصة

### ❌ المشاكل الموجودة:
- [ ] لا توجد مشاكل
- [ ] مشكلة في الحفظ
- [ ] مشكلة في التطبيق
- [ ] مشكلة في قاعدة البيانات
- [ ] أخرى: _______________

## 🎉 النتيجة النهائية:
إذا نجحت جميع الاختبارات، فإن نظام إعدادات الطباعة يعمل بشكل مثالي! 🎊
