#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة نظام تداول العملات الرقمية على MetaTrader 5
MT5 Cryptocurrency Trading GUI
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from datetime import datetime
import json
from mt5_real_crypto_system import MT5RealCryptoSystem

class MT5CryptoGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 نظام تداول العملات الرقمية - MetaTrader 5")
        self.root.geometry("1400x900")
        self.root.configure(bg='#1a1a1a')
        
        # النظام الأساسي
        self.trading_system = None
        self.is_trading = False
        self.trading_thread = None
        
        # إعداد الواجهة
        self.setup_styles()
        self.create_widgets()
        self.update_display()
        
        # تحديث دوري للواجهة
        self.root.after(2000, self.periodic_update)
        
    def setup_styles(self):
        """إعداد أنماط الواجهة"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # ألوان داكنة متقدمة
        style.configure('Dark.TFrame', background='#2d2d2d', relief='flat')
        style.configure('Dark.TLabel', background='#2d2d2d', foreground='#ffffff', font=('Segoe UI', 10))
        style.configure('Dark.TButton', background='#404040', foreground='white', font=('Segoe UI', 9))
        style.configure('Success.TButton', background='#28a745', foreground='white', font=('Segoe UI', 9, 'bold'))
        style.configure('Danger.TButton', background='#dc3545', foreground='white', font=('Segoe UI', 9, 'bold'))
        style.configure('Warning.TButton', background='#ffc107', foreground='black', font=('Segoe UI', 9, 'bold'))
        style.configure('Info.TButton', background='#17a2b8', foreground='white', font=('Segoe UI', 9, 'bold'))
        
        # إطارات ملونة
        style.configure('Success.TLabelFrame', background='#2d2d2d', foreground='#28a745')
        style.configure('Warning.TLabelFrame', background='#2d2d2d', foreground='#ffc107')
        style.configure('Info.TLabelFrame', background='#2d2d2d', foreground='#17a2b8')
        style.configure('Dark.TLabelFrame', background='#2d2d2d', foreground='#ffffff')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, style='Dark.TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # شريط العنوان
        self.create_header(main_frame)
        
        # الإطار العلوي - معلومات الحساب والتحكم
        self.create_control_panel(main_frame)
        
        # الإطار الأوسط - التحليل والصفقات
        self.create_analysis_panel(main_frame)
        
        # الإطار السفلي - سجل الأحداث
        self.create_log_panel(main_frame)
        
    def create_header(self, parent):
        """إنشاء شريط العنوان"""
        header_frame = ttk.Frame(parent, style='Dark.TFrame')
        header_frame.pack(fill=tk.X, pady=(0, 15))
        
        # العنوان الرئيسي
        title_label = ttk.Label(header_frame, 
                               text="🚀 نظام تداول العملات الرقمية - MetaTrader 5", 
                               font=('Segoe UI', 18, 'bold'), 
                               style='Dark.TLabel')
        title_label.pack(side=tk.LEFT)
        
        # حالة الاتصال
        self.connection_frame = ttk.Frame(header_frame, style='Dark.TFrame')
        self.connection_frame.pack(side=tk.RIGHT)
        
        self.status_label = ttk.Label(self.connection_frame, 
                                     text="🔴 غير متصل", 
                                     font=('Segoe UI', 12, 'bold'), 
                                     style='Dark.TLabel')
        self.status_label.pack(side=tk.TOP)
        
        self.connect_button = ttk.Button(self.connection_frame, 
                                        text="🔌 اتصال", 
                                        command=self.connect_mt5,
                                        style='Info.TButton')
        self.connect_button.pack(side=tk.TOP, pady=(5, 0))
        
    def create_control_panel(self, parent):
        """إنشاء لوحة التحكم"""
        control_frame = ttk.Frame(parent, style='Dark.TFrame')
        control_frame.pack(fill=tk.X, pady=(0, 15))
        
        # معلومات الحساب
        account_frame = ttk.LabelFrame(control_frame, text="📊 معلومات الحساب",
                                      style='Dark.TLabelFrame')
        account_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # شبكة معلومات الحساب
        account_grid = ttk.Frame(account_frame, style='Dark.TFrame')
        account_grid.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)
        
        # الصف الأول
        ttk.Label(account_grid, text="💰 الرصيد:", style='Dark.TLabel').grid(row=0, column=0, sticky=tk.W, pady=2)
        self.balance_label = ttk.Label(account_grid, text="$0.00", font=('Segoe UI', 10, 'bold'), style='Dark.TLabel')
        self.balance_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        ttk.Label(account_grid, text="📈 الأسهم:", style='Dark.TLabel').grid(row=0, column=2, sticky=tk.W, padx=(20, 0), pady=2)
        self.equity_label = ttk.Label(account_grid, text="$0.00", font=('Segoe UI', 10, 'bold'), style='Dark.TLabel')
        self.equity_label.grid(row=0, column=3, sticky=tk.W, padx=(10, 0), pady=2)
        
        # الصف الثاني
        ttk.Label(account_grid, text="📊 الصفقات:", style='Dark.TLabel').grid(row=1, column=0, sticky=tk.W, pady=2)
        self.positions_label = ttk.Label(account_grid, text="0", font=('Segoe UI', 10, 'bold'), style='Dark.TLabel')
        self.positions_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        ttk.Label(account_grid, text="🧠 نقاط التعلم:", style='Dark.TLabel').grid(row=1, column=2, sticky=tk.W, padx=(20, 0), pady=2)
        self.learning_label = ttk.Label(account_grid, text="0", font=('Segoe UI', 10, 'bold'), style='Dark.TLabel')
        self.learning_label.grid(row=1, column=3, sticky=tk.W, padx=(10, 0), pady=2)
        
        # لوحة التحكم
        trading_frame = ttk.LabelFrame(control_frame, text="🎮 لوحة التحكم", 
                                      style='Success.TLabelFrame')
        trading_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        # شبكة التحكم
        control_grid = ttk.Frame(trading_frame, style='Dark.TFrame')
        control_grid.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)
        
        # اختيار العملة
        ttk.Label(control_grid, text="💎 العملة الرقمية:", style='Dark.TLabel').grid(row=0, column=0, sticky=tk.W, pady=5)
        
        self.symbol_var = tk.StringVar(value='BTCUSD')
        self.symbol_combo = ttk.Combobox(control_grid, textvariable=self.symbol_var,
                                        values=['BTCUSD', 'ETHUSD', 'LTCUSD', 'XRPUSD', 'BCHUSD'],
                                        state='readonly', width=12)
        self.symbol_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        self.symbol_combo.bind('<<ComboboxSelected>>', self.on_symbol_change)
        
        # وضع التداول
        ttk.Label(control_grid, text="🔄 الوضع:", style='Dark.TLabel').grid(row=0, column=2, sticky=tk.W, padx=(20, 0), pady=5)
        
        self.mode_var = tk.StringVar(value="تجريبي")
        mode_combo = ttk.Combobox(control_grid, textvariable=self.mode_var,
                                 values=["تجريبي", "حقيقي"], state='readonly', width=10)
        mode_combo.grid(row=0, column=3, sticky=tk.W, padx=(10, 0), pady=5)
        mode_combo.bind('<<ComboboxSelected>>', self.on_mode_change)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(control_grid, style='Dark.TFrame')
        buttons_frame.grid(row=1, column=0, columnspan=4, pady=(10, 0))
        
        self.start_button = ttk.Button(buttons_frame, text="🚀 بدء التداول", 
                                      command=self.start_trading, style='Success.TButton')
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(buttons_frame, text="⏹️ إيقاف التداول", 
                                     command=self.stop_trading, style='Danger.TButton',
                                     state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.train_button = ttk.Button(buttons_frame, text="🧠 تدريب النموذج", 
                                      command=self.train_model, style='Warning.TButton')
        self.train_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.analyze_button = ttk.Button(buttons_frame, text="📊 تحليل فوري", 
                                        command=self.analyze_current, style='Info.TButton')
        self.analyze_button.pack(side=tk.LEFT)
        
    def create_analysis_panel(self, parent):
        """إنشاء لوحة التحليل"""
        analysis_frame = ttk.Frame(parent, style='Dark.TFrame')
        analysis_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # تحليل السوق
        market_frame = ttk.LabelFrame(analysis_frame, text="📈 تحليل السوق", 
                                     style='Warning.TLabelFrame')
        market_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        self.analysis_text = scrolledtext.ScrolledText(market_frame, height=20, width=50,
                                                      bg='#1e1e1e', fg='#00ff41', 
                                                      font=('Consolas', 10),
                                                      insertbackground='white')
        self.analysis_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # الصفقات المفتوحة
        positions_frame = ttk.LabelFrame(analysis_frame, text="💼 الصفقات المفتوحة", 
                                        style='Success.TLabelFrame')
        positions_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        # جدول الصفقات
        columns = ('الرمز', 'النوع', 'الحجم', 'سعر الدخول', 'السعر الحالي', 'الربح/الخسارة')
        self.positions_tree = ttk.Treeview(positions_frame, columns=columns, show='headings', height=20)
        
        for col in columns:
            self.positions_tree.heading(col, text=col)
            if col == 'الرمز':
                self.positions_tree.column(col, width=80)
            elif col == 'النوع':
                self.positions_tree.column(col, width=60)
            elif col == 'الحجم':
                self.positions_tree.column(col, width=80)
            else:
                self.positions_tree.column(col, width=100)
                
        # شريط التمرير للجدول
        positions_scrollbar = ttk.Scrollbar(positions_frame, orient=tk.VERTICAL, 
                                           command=self.positions_tree.yview)
        self.positions_tree.configure(yscrollcommand=positions_scrollbar.set)
        
        self.positions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        positions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
    def create_log_panel(self, parent):
        """إنشاء لوحة السجل"""
        log_frame = ttk.LabelFrame(parent, text="📝 سجل الأحداث", style='Dark.TLabelFrame')
        log_frame.pack(fill=tk.X)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, 
                                                 bg='#0d1117', fg='#58a6ff', 
                                                 font=('Consolas', 9),
                                                 insertbackground='white')
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
    def connect_mt5(self):
        """الاتصال بـ MetaTrader 5"""
        def connect_thread():
            try:
                self.log_message("🔌 محاولة الاتصال بـ MetaTrader 5...")
                success = self.trading_system.connect_mt5()
                
                if success:
                    self.root.after(0, lambda: self.on_connection_success())
                else:
                    self.root.after(0, lambda: self.on_connection_failed())
                    
            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"❌ خطأ في الاتصال: {e}"))
                
        threading.Thread(target=connect_thread, daemon=True).start()
        
    def on_connection_success(self):
        """عند نجاح الاتصال"""
        self.status_label.config(text="🟢 متصل", foreground='#28a745')
        self.connect_button.config(text="✅ متصل", state=tk.DISABLED)
        
        # تحديث قائمة العملات
        if self.trading_system.crypto_symbols:
            self.symbol_combo.config(values=self.trading_system.crypto_symbols)
            if self.trading_system.current_symbol in self.trading_system.crypto_symbols:
                self.symbol_var.set(self.trading_system.current_symbol)
                
        self.log_message("✅ تم الاتصال بـ MetaTrader 5 بنجاح!")
        self.log_message(f"💎 العملات الرقمية المتوفرة: {len(self.trading_system.crypto_symbols)}")
        
    def on_connection_failed(self):
        """عند فشل الاتصال"""
        self.status_label.config(text="🔴 فشل الاتصال", foreground='#dc3545')
        self.log_message("❌ فشل في الاتصال بـ MetaTrader 5")
        messagebox.showerror("خطأ في الاتصال", 
                           "فشل في الاتصال بـ MetaTrader 5\n\nتأكد من:\n• تشغيل MetaTrader 5\n• صحة بيانات الحساب\n• تفعيل التداول الآلي")
        
    def on_symbol_change(self, event):
        """تغيير العملة المختارة"""
        new_symbol = self.symbol_var.get()
        success = self.trading_system.switch_symbol(new_symbol)
        if success:
            self.log_message(f"🔄 تم تغيير العملة إلى: {new_symbol}")
        else:
            self.log_message(f"❌ فشل في تغيير العملة إلى: {new_symbol}")
            
    def on_mode_change(self, event):
        """تغيير وضع التداول"""
        mode = self.mode_var.get()
        demo_mode = (mode == "تجريبي")
        self.trading_system.demo_mode = demo_mode
        self.log_message(f"🔄 تم التبديل إلى وضع: {mode}")
        
    def start_trading(self):
        """بدء التداول"""
        if not self.trading_system.connected:
            messagebox.showwarning("تحذير", "يجب الاتصال بـ MetaTrader 5 أولاً!")
            return
            
        if self.is_trading:
            return
            
        self.is_trading = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        
        # بدء التداول في thread منفصل
        self.trading_thread = threading.Thread(target=self.trading_loop, daemon=True)
        self.trading_thread.start()
        
        self.log_message("🚀 تم بدء تداول العملات الرقمية...")
        
    def stop_trading(self):
        """إيقاف التداول"""
        self.is_trading = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        
        self.log_message("⏹️ تم إيقاف التداول")
        
    def trading_loop(self):
        """حلقة التداول الرئيسية"""
        try:
            while self.is_trading:
                # فحص الصفقات المفتوحة
                self.trading_system.check_open_positions()
                
                # تحليل العملة الحالية
                analysis = self.trading_system.analyze_crypto_market(self.trading_system.current_symbol)
                
                # عرض التحليل
                self.root.after(0, lambda a=analysis: self.display_analysis(a))
                
                # تنفيذ التداول
                if (analysis['decision'] in ['buy', 'sell'] and 
                    analysis['confidence'] >= self.trading_system.min_confidence and
                    len(self.trading_system.positions) < self.trading_system.max_positions):
                    
                    success = self.trading_system.execute_crypto_trade(analysis)
                    if success:
                        self.root.after(0, lambda: self.log_message(f"✅ تم تنفيذ صفقة {analysis['decision']} - {analysis['symbol']}"))
                
                # انتظار قبل التحليل التالي
                time.sleep(120)  # دقيقتان
                
        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"❌ خطأ في التداول: {e}"))
            
    def analyze_current(self):
        """تحليل فوري للعملة الحالية"""
        def analyze_thread():
            try:
                if not self.trading_system.connected:
                    self.root.after(0, lambda: messagebox.showwarning("تحذير", "يجب الاتصال بـ MetaTrader 5 أولاً!"))
                    return
                    
                self.root.after(0, lambda: self.log_message(f"📊 تحليل فوري لـ {self.trading_system.current_symbol}..."))
                
                analysis = self.trading_system.analyze_crypto_market(self.trading_system.current_symbol)
                self.root.after(0, lambda: self.display_analysis(analysis))
                self.root.after(0, lambda: self.log_message("✅ تم التحليل الفوري"))
                
            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"❌ خطأ في التحليل: {e}"))
                
        threading.Thread(target=analyze_thread, daemon=True).start()
        
    def train_model(self):
        """تدريب النموذج"""
        def train_thread():
            try:
                self.log_message("🧠 بدء تدريب نموذج التعلم الآلي...")
                success = self.trading_system.train_ml_model()
                
                if success:
                    self.root.after(0, lambda: self.log_message("✅ تم تدريب النموذج بنجاح"))
                else:
                    self.root.after(0, lambda: self.log_message("❌ فشل في تدريب النموذج - بيانات غير كافية"))
                    
            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"❌ خطأ في التدريب: {e}"))
                
        threading.Thread(target=train_thread, daemon=True).start()

    def display_analysis(self, analysis):
        """عرض تحليل السوق"""
        try:
            self.analysis_text.delete(1.0, tk.END)

            # تحديد لون القرار
            decision_color = "#00ff41"  # أخضر
            if analysis['decision'] == 'sell':
                decision_color = "#ff4757"  # أحمر
            elif analysis['decision'] == 'hold':
                decision_color = "#ffa502"  # برتقالي

            analysis_text = f"""
╔══════════════════════════════════════════════════════════╗
║                    📊 تحليل العملة الرقمية                    ║
╚══════════════════════════════════════════════════════════╝

🎯 العملة: {analysis.get('symbol', 'غير محدد')}
⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

┌─────────────────────────────────────────────────────────┐
│                        🎯 القرار النهائي                        │
└─────────────────────────────────────────────────────────┘
🚀 القرار: {analysis['decision'].upper()}
📈 مستوى الثقة: {analysis['confidence']:.2%}

┌─────────────────────────────────────────────────────────┐
│                      📊 تحليل السوق                       │
└─────────────────────────────────────────────────────────┘
💰 السعر الحالي: ${analysis.get('price', 0):.4f}
📈 نقاط الاتجاه: {analysis.get('trend_score', 0)}/4
⚡ نقاط الزخم: {analysis.get('momentum_score', 0)}
📊 التقلبات: {analysis.get('volatility', 0):.2f}%

┌─────────────────────────────────────────────────────────┐
│                     🔍 المؤشرات الفنية                     │
└─────────────────────────────────────────────────────────┘
📊 RSI: {analysis.get('rsi', 0):.2f}
📈 MACD: {analysis.get('macd', 0):.6f}
📊 نسبة الحجم: {analysis.get('volume_ratio', 1):.2f}x

"""

            if 'ml_prediction' in analysis and analysis['ml_prediction'] is not None:
                ml_pred = analysis['ml_prediction']
                ml_direction = "📈 صاعد" if ml_pred > 0 else "📉 هابط" if ml_pred < 0 else "➡️ مستقر"
                analysis_text += f"""┌─────────────────────────────────────────────────────────┐
│                    🤖 توقع الذكاء الاصطناعي                   │
└─────────────────────────────────────────────────────────┘
🔮 التوقع: {ml_pred:.4f}
🎯 الاتجاه: {ml_direction}

"""

            if 'reason' in analysis:
                analysis_text += f"""┌─────────────────────────────────────────────────────────┐
│                         💡 السبب                         │
└─────────────────────────────────────────────────────────┘
{analysis['reason']}

"""

            # إضافة توصيات
            analysis_text += f"""┌─────────────────────────────────────────────────────────┐
│                        🎯 التوصيات                        │
└─────────────────────────────────────────────────────────┘
"""

            if analysis['decision'] == 'buy':
                analysis_text += """🟢 توصية الشراء:
   • السوق يظهر إشارات إيجابية
   • الزخم في صالح الارتفاع
   • مستوى الثقة مرتفع
"""
            elif analysis['decision'] == 'sell':
                analysis_text += """🔴 توصية البيع:
   • السوق يظهر إشارات سلبية
   • الزخم في صالح الانخفاض
   • مستوى الثقة مرتفع
"""
            else:
                analysis_text += """🟡 توصية الانتظار:
   • السوق غير واضح الاتجاه
   • مستوى الثقة منخفض
   • انتظار إشارات أوضح
"""

            self.analysis_text.insert(tk.END, analysis_text)

        except Exception as e:
            self.log_message(f"❌ خطأ في عرض التحليل: {e}")

    def update_positions_table(self):
        """تحديث جدول الصفقات"""
        try:
            # مسح الجدول
            for item in self.positions_tree.get_children():
                self.positions_tree.delete(item)

            # إضافة الصفقات المفتوحة
            for ticket, position in self.trading_system.positions.items():
                # تحديد لون الصف بناءً على الربح/الخسارة
                profit = position.get('profit', 0)
                if profit > 0:
                    tags = ('profit',)
                elif profit < 0:
                    tags = ('loss',)
                else:
                    tags = ('neutral',)

                # تنسيق البيانات
                symbol = position.get('symbol', '')
                trade_type = position.get('type', '').upper()
                volume = f"{position.get('volume', 0):.2f}"
                price_open = f"${position.get('price_open', 0):.4f}"
                price_current = f"${position.get('price_current', 0):.4f}"

                profit_text = f"${profit:.2f}"
                if profit > 0:
                    profit_text = f"+{profit_text}"

                self.positions_tree.insert('', tk.END,
                                         values=(symbol, trade_type, volume, price_open, price_current, profit_text),
                                         tags=tags)

            # تطبيق ألوان الصفوف
            self.positions_tree.tag_configure('profit', background='#1e3a1e', foreground='#4caf50')
            self.positions_tree.tag_configure('loss', background='#3a1e1e', foreground='#f44336')
            self.positions_tree.tag_configure('neutral', background='#2d2d2d', foreground='#ffffff')

        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث الجدول: {e}")

    def update_display(self):
        """تحديث عرض المعلومات"""
        try:
            # تحديث معلومات الحساب
            summary = self.trading_system.get_account_summary()

            if 'error' not in summary:
                self.balance_label.config(text=f"${summary.get('balance', 0):.2f}")
                self.equity_label.config(text=f"${summary.get('equity', 0):.2f}")
                self.positions_label.config(text=str(summary.get('open_positions', 0)))
                self.learning_label.config(text=str(summary.get('learning_points', 0)))

                # تحديث حالة الاتصال
                if summary.get('connected', False):
                    self.status_label.config(text="🟢 متصل", foreground='#28a745')
                else:
                    self.status_label.config(text="🔴 غير متصل", foreground='#dc3545')

            # تحديث جدول الصفقات
            self.update_positions_table()

        except Exception as e:
            self.log_message(f"❌ خطأ في التحديث: {e}")

    def periodic_update(self):
        """تحديث دوري للواجهة"""
        self.update_display()
        self.root.after(5000, self.periodic_update)  # كل 5 ثوان

    def log_message(self, message):
        """إضافة رسالة للسجل"""
        try:
            timestamp = datetime.now().strftime('%H:%M:%S')
            log_entry = f"[{timestamp}] {message}\n"

            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)

            # الاحتفاظ بآخر 200 سطر فقط
            lines = self.log_text.get(1.0, tk.END).split('\n')
            if len(lines) > 200:
                self.log_text.delete(1.0, f"{len(lines)-200}.0")

        except Exception as e:
            print(f"خطأ في السجل: {e}")

    def on_closing(self):
        """عند إغلاق النافذة"""
        try:
            if self.is_trading:
                if messagebox.askokcancel("تأكيد الإغلاق",
                                        "التداول قيد التشغيل. هل تريد إيقافه والخروج؟"):
                    self.is_trading = False
                    time.sleep(1)  # انتظار قصير لإيقاف التداول
                else:
                    return

            # إغلاق اتصال MT5
            if self.trading_system.connected:
                import MetaTrader5 as mt5
                mt5.shutdown()

            self.root.destroy()

        except Exception as e:
            print(f"خطأ في الإغلاق: {e}")
            self.root.destroy()

    def run(self):
        """تشغيل الواجهة"""
        try:
            self.log_message("🚀 مرحباً بك في نظام تداول العملات الرقمية على MetaTrader 5")
            self.log_message("💡 اضغط 'اتصال' للاتصال بـ MT5، ثم اختر العملة واضغط 'بدء التداول'")
            self.log_message("🎯 العملات المدعومة: Bitcoin, Ethereum, Litecoin, Ripple, وأكثر...")

            # ربط حدث إغلاق النافذة
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

            self.root.mainloop()

        except Exception as e:
            print(f"خطأ في تشغيل الواجهة: {e}")

def main():
    """الدالة الرئيسية"""
    try:
        app = MT5CryptoGUI()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")

if __name__ == "__main__":
    main()
