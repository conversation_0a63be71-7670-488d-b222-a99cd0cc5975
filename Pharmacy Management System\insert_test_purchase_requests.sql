-- إدراج بيانات تجريبية في جدول purchase_requests
USE UnifiedPharmacy;

PRINT '=== إدراج بيانات تجريبية في purchase_requests ===';

-- حذف البيانات الموجودة لتجنب التكرار
DELETE FROM purchase_requests;
PRINT 'تم حذف البيانات الموجودة';

-- إدراج طلبات تجريبية
-- طلب من المستخدم رقم 2 للمستخدم رقم 1 لدواء رقم 1
INSERT INTO purchase_requests 
(buyer_pharmacy_id, seller_pharmacy_id, medicine_id, requested_quantity, offered_price, request_message, status, request_date)
VALUES 
(2, 1, 1, 10, 50.00, 'أحتاج هذا الدواء بسرعة', 'pending', GETDATE());

-- طلب من المستخدم رقم 3 للمستخدم رقم 1 لدواء رقم 2
INSERT INTO purchase_requests 
(buyer_pharmacy_id, seller_pharmacy_id, medicine_id, requested_quantity, offered_price, request_message, status, request_date)
VALUES 
(3, 1, 2, 5, 25.00, 'طلب عاجل', 'pending', GETDATE());

-- طلب من المستخدم رقم 2 للمستخدم رقم 1 لدواء رقم 3
INSERT INTO purchase_requests 
(buyer_pharmacy_id, seller_pharmacy_id, medicine_id, requested_quantity, offered_price, request_message, status, request_date)
VALUES 
(2, 1, 3, 15, 75.00, 'للمرضى المزمنين', 'pending', GETDATE());

-- طلب من المستخدم رقم 4 للمستخدم رقم 1 لدواء رقم 1
INSERT INTO purchase_requests 
(buyer_pharmacy_id, seller_pharmacy_id, medicine_id, requested_quantity, offered_price, request_message, status, request_date)
VALUES 
(4, 1, 1, 8, 40.00, 'طلب شهري', 'pending', GETDATE());

-- طلب من المستخدم رقم 3 للمستخدم رقم 1 لدواء رقم 4
INSERT INTO purchase_requests 
(buyer_pharmacy_id, seller_pharmacy_id, medicine_id, requested_quantity, offered_price, request_message, status, request_date)
VALUES 
(3, 1, 4, 12, 60.00, 'للأطفال', 'pending', GETDATE());

PRINT '✅ تم إدراج 5 طلبات تجريبية';

-- عرض النتائج
PRINT '=== الطلبات المدرجة ===';
SELECT 
    pr.id,
    pr.buyer_pharmacy_id,
    pr.seller_pharmacy_id,
    pr.medicine_id,
    pr.requested_quantity,
    pr.offered_price,
    pr.request_message,
    pr.status,
    pr.request_date
FROM purchase_requests pr
ORDER BY pr.request_date DESC;

-- عرض عدد الطلبات لكل صيدلية بائعة
PRINT '=== عدد الطلبات لكل صيدلية بائعة ===';
SELECT 
    seller_pharmacy_id as 'معرف الصيدلية البائعة',
    COUNT(*) as 'عدد الطلبات'
FROM purchase_requests
GROUP BY seller_pharmacy_id
ORDER BY seller_pharmacy_id;

PRINT '=== انتهى الإدراج ===';
