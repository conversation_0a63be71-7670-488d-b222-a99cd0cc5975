using System;
using System.Drawing;
using System.Windows.Forms;

namespace Pharmacy_Management_System
{
    public partial class AddDosageForm : Form
    {
        public string DosageName { get; private set; }
        public int Quantity { get; private set; }
        public decimal Price { get; private set; }

        public AddDosageForm()
        {
            InitializeComponent();
            InitializeForm();
            ApplyLanguage();
            ApplyModernDesign();
        }

        private void InitializeForm()
        {
            // إعداد القيم الافتراضية
            numQuantity.Minimum = 1;
            numQuantity.Maximum = 10000;
            numQuantity.Value = 1;

            numPrice.DecimalPlaces = 2;
            numPrice.Increment = 0.25m;
            numPrice.Minimum = 0.01m;
            numPrice.Maximum = 10000m;
            numPrice.Value = 1.00m;

            txtDosageName.MaxLength = 100;
            txtDosageName.Focus();
        }

        private void ApplyLanguage()
        {
            this.Text = LanguageManager.GetText("Add New Dosage");
            lblTitle.Text = LanguageManager.GetText("Add New Dosage");
            lblDosageName.Text = LanguageManager.GetText("Dosage Name") + ":";
            lblQuantity.Text = LanguageManager.GetText("Quantity") + ":";
            lblPrice.Text = LanguageManager.GetText("Price Per Unit") + ":";
            btnAdd.Text = LanguageManager.GetText("Add");
            btnCancel.Text = LanguageManager.GetText("Cancel");
        }

        private void ApplyModernDesign()
        {
            this.BackColor = Color.FromArgb(245, 245, 245);
            
            // تصميم اللوحة الرئيسية
            panelMain.BackColor = Color.White;
            panelMain.BorderStyle = BorderStyle.None;
            
            // تصميم الأزرار
            btnAdd.BackColor = Color.FromArgb(76, 175, 80);
            btnAdd.ForeColor = Color.White;
            btnAdd.FlatStyle = FlatStyle.Flat;
            btnAdd.FlatAppearance.BorderSize = 0;
            
            btnCancel.BackColor = Color.FromArgb(158, 158, 158);
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.FlatAppearance.BorderSize = 0;

            // تصميم حقول الإدخال
            txtDosageName.BorderStyle = BorderStyle.FixedSingle;
            txtDosageName.Font = new Font("Segoe UI", 11F);
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(txtDosageName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم الجرعة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtDosageName.Focus();
                    return;
                }

                if (numQuantity.Value <= 0)
                {
                    MessageBox.Show("يجب أن تكون الكمية أكبر من صفر", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    numQuantity.Focus();
                    return;
                }

                if (numPrice.Value <= 0)
                {
                    MessageBox.Show("يجب أن يكون السعر أكبر من صفر", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    numPrice.Focus();
                    return;
                }

                // حفظ القيم
                DosageName = txtDosageName.Text.Trim();
                Quantity = (int)numQuantity.Value;
                Price = numPrice.Value;

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الجرعة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void txtDosageName_KeyPress(object sender, KeyPressEventArgs e)
        {
            // السماح بالأحرف العربية والإنجليزية والأرقام والمسافات
            if (!char.IsControl(e.KeyChar) && 
                !char.IsLetterOrDigit(e.KeyChar) && 
                e.KeyChar != ' ' && 
                e.KeyChar != '-' && 
                e.KeyChar != '.')
            {
                e.Handled = true;
            }
        }

        private void AddDosageForm_Load(object sender, EventArgs e)
        {
            txtDosageName.Focus();
        }
    }
}
