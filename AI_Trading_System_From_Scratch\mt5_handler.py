import MetaTrader5 as mt5
from datetime import datetime

class MT5Handler:
    def __init__(self, config):
        self.config = config
        self.server = config['MT5']['server']
        self.login = int(config['MT5']['login'])
        self.password = config['MT5']['password']
        self.path = config['MT5']['path']
        self.connected = False

    def connect(self):
        if not mt5.initialize(path=self.path):
            print("MT5 initialize failed")
            return False
        if not mt5.login(self.login, self.password, self.server):
            print("MT5 login failed")
            mt5.shutdown()
            return False
        self.connected = True
        print("Connected to MT5")
        return True

    def disconnect(self):
        if self.connected:
            mt5.shutdown()
            self.connected = False

    def open_position(self, symbol, direction, lot_size, sl_pips, tp_pips):
        if not self.connected:
            return None

        # Get current price
        tick = mt5.symbol_info_tick(symbol)
        if tick is None:
            print(f"Failed to get tick for {symbol}")
            return None

        price = tick.ask if direction == 'BUY' else tick.bid
        sl = price - sl_pips * 0.0001 if direction == 'BUY' else price + sl_pips * 0.0001
        tp = price + tp_pips * 0.0001 if direction == 'BUY' else price - tp_pips * 0.0001

        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": lot_size,
            "type": mt5.ORDER_TYPE_BUY if direction == 'BUY' else mt5.ORDER_TYPE_SELL,
            "price": price,
            "sl": sl,
            "tp": tp,
            "deviation": 10,
            "magic": 234000,
            "comment": "AI Trade",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }

        result = mt5.order_send(request)
        if result.retcode != mt5.TRADE_RETCODE_DONE:
            print(f"Order failed: {result.comment}")
            return None
        return result.order

    def close_position(self, ticket):
        if not self.connected:
            return False

        position = mt5.positions_get(ticket=ticket)
        if not position:
            return False
        pos = position[0]

        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": pos.symbol,
            "volume": pos.volume,
            "type": mt5.ORDER_TYPE_SELL if pos.type == 0 else mt5.ORDER_TYPE_BUY,
            "position": ticket,
            "price": mt5.symbol_info_tick(pos.symbol).bid if pos.type == 0 else mt5.symbol_info_tick(pos.symbol).ask,
            "deviation": 10,
            "magic": 234000,
            "comment": "Close AI Trade",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }

        result = mt5.order_send(request)
        return result.retcode == mt5.TRADE_RETCODE_DONE
