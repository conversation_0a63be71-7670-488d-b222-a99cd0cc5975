-- اختبار نظام تسجيل الدخول الجديد
-- Test New Login System

USE UnifiedPharmacy;
GO

PRINT 'اختبار نظام تسجيل الدخول الجديد...';
PRINT '================================';

-- 1. عرض الصيدليات المتاحة
PRINT '';
PRINT '1. الصيدليات المتاحة:';
SELECT 
    id,
    pharmacyCode AS 'كود الصيدلية',
    pharmacyName AS 'اسم الصيدلية',
    isActive AS 'نشطة'
FROM pharmacies
WHERE isActive = 1
ORDER BY id;

-- 2. عرض المستخدمين وصيدلياتهم
PRINT '';
PRINT '2. المستخدمين وصيدلياتهم:';
SELECT 
    u.id,
    u.username AS 'اسم المستخدم',
    u.name AS 'الاسم',
    u.userRole AS 'الدور',
    p.pharmacyCode AS 'كود الصيدلية',
    p.pharmacyName AS 'اسم الصيدلية'
FROM users u
LEFT JOIN pharmacies p ON u.pharmacyId = p.id
WHERE u.isActive = 1
ORDER BY u.id;

-- 3. اختبار تسجيل الدخول
PRINT '';
PRINT '3. اختبار تسجيل الدخول:';
PRINT 'محاولة تسجيل الدخول للمستخدم admin في الصيدلية MAIN001...';

DECLARE @pharmacyId INT;
SELECT @pharmacyId = id FROM pharmacies WHERE pharmacyCode = 'MAIN001';

SELECT
    u.id,
    u.username,
    u.name,
    u.userRole,
    u.pharmacyId,
    p.pharmacyName,
    p.pharmacyCode
FROM users u
INNER JOIN pharmacies p ON u.pharmacyId = p.id
WHERE u.username = 'admin'
  AND u.pass = 'admin123'
  AND u.pharmacyId = @pharmacyId
  AND u.isActive = 1
  AND p.isActive = 1;

-- 4. إحصائيات النظام
PRINT '';
PRINT '4. إحصائيات النظام:';
PRINT 'عدد الصيدليات النشطة: ' + CAST((SELECT COUNT(*) FROM pharmacies WHERE isActive = 1) AS VARCHAR(10));
PRINT 'عدد المستخدمين النشطين: ' + CAST((SELECT COUNT(*) FROM users WHERE isActive = 1) AS VARCHAR(10));

PRINT '';
PRINT '✅ اكتمل اختبار النظام!';
PRINT '';
PRINT '🔑 بيانات الاختبار:';
PRINT '   كود الصيدلية: MAIN001';
PRINT '   اسم المستخدم: admin';
PRINT '   كلمة المرور: admin123';
