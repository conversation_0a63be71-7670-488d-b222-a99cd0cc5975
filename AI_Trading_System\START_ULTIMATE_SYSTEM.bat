@echo off
chcp 65001 >nul
title 🚀 نظام التداول الذكي النهائي - مع النظام التجريبي
color 0A

echo.
echo ================================================
echo    🚀 نظام التداول الذكي النهائي 🚀
echo ================================================
echo.
echo 🆕 الميزات الجديدة:
echo    ✅ نظام تداول تجريبي متكامل
echo    ✅ تعلم ذكي ومتوازن (25-95%% ثقة)
echo    ✅ تنفيذ صفقات حقيقية محسن
echo    ✅ إدارة مخاطر متقدمة
echo    ✅ تبديل تلقائي للنظام التجريبي
echo.
echo 🔧 كيف يعمل النظام:
echo    1️⃣ يحاول الاتصال بـ MT5 أولاً
echo    2️⃣ في حالة الفشل يتبدل للنظام التجريبي
echo    3️⃣ يتعلم من جميع الصفقات ويحسن الأداء
echo    4️⃣ يدخل صفقات حقيقية عند توفر الفرص
echo.
echo 🧠 نظام التعلم المحسن:
echo    📊 تحليل ذكي للأنماط
echo    🎯 تعديل متوازن للثقة
echo    🔄 إعادة تعيين تلقائية
echo    📈 تحسين مستمر للأداء
echo.

cd /d "%~dp0"

echo 📦 فحص المتطلبات...
python -c "import tkinter; print('✅ tkinter متوفر')" 2>nul || (echo ❌ tkinter غير متوفر && pause && exit)
python -c "import pandas; print('✅ pandas متوفر')" 2>nul || (echo ❌ pandas غير متوفر && pause && exit)
python -c "import numpy; print('✅ numpy متوفر')" 2>nul || (echo ❌ numpy غير متوفر && pause && exit)

echo.
echo 🚀 تشغيل النظام النهائي...
echo 💡 سيتم الاتصال بـ MT5 أولاً، وفي حالة الفشل سيتم التبديل للنظام التجريبي
echo.

python advanced_trading_gui.py

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ حدث خطأ في تشغيل النظام
    echo 🔍 تحقق من الأخطاء أعلاه
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق النظام بنجاح
    echo 🎯 شكراً لاستخدام النظام!
)

pause
