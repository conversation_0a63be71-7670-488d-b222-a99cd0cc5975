@echo off
chcp 65001 > nul
title 🚀 نظام تداول العملات الرقمية - MetaTrader 5

echo.
echo ================================================================
echo 🚀 نظام تداول العملات الرقمية على MetaTrader 5
echo MT5 CRYPTOCURRENCY TRADING SYSTEM
echo ================================================================
echo.

echo 💎 العملات الرقمية المدعومة:
echo    ₿ Bitcoin (BTCUSD)           🔸 Binance Coin (BNBUSD)
echo    Ξ Ethereum (ETHUSD)          🔴 Polkadot (DOTUSD)  
echo    💰 Litecoin (LTCUSD)         🦄 Uniswap (UNIUSD)
echo    💧 Ripple (XRPUSD)           🔗 Chainlink (LINKUSD)
echo    💸 Bitcoin Cash (BCHUSD)     ☀️ Solana (SOLUSD)
echo    📊 EOS (EOSUSD)              🔺 Avalanche (AVAXUSD)
echo    ⭐ Stellar (XLMUSD)          🟣 Polygon (MATICUSD)
echo    ♠ Cardano (ADAUSD)           🐕 Dogecoin (DOGEUSD)
echo    🔥 Tron (TRXUSD)             🐕 Shiba Inu (SHIBUSD)
echo    🎮 ApeCoin (APEUSD)          🏖️ Sandbox (SANDUSD)
echo.

echo 🎯 مميزات النظام:
echo    🧠 تحليل فني متقدم مع 15+ مؤشر
echo    🤖 ذكاء اصطناعي وتعلم آلي
echo    📊 تحليل خاص بالعملات الرقمية
echo    🛡️ إدارة مخاطر متقدمة
echo    📈 واجهة رسومية احترافية
echo    🔄 تداول تلقائي 24/7
echo.

echo 🔧 متطلبات التشغيل:
echo    ✅ MetaTrader 5 مثبت ويعمل
echo    ✅ حساب تداول مع دعم العملات الرقمية
echo    ✅ Python 3.8+ مع المكتبات المطلوبة
echo    ✅ اتصال مستقر بالإنترنت
echo.

echo 📋 خطوات التشغيل:
echo    1️⃣ تأكد من تشغيل MetaTrader 5
echo    2️⃣ تسجيل الدخول لحسابك
echo    3️⃣ تفعيل التداول الآلي (AutoTrading)
echo    4️⃣ تشغيل هذا النظام
echo    5️⃣ الاتصال من خلال الواجهة
echo.

echo 🚨 تحذيرات مهمة:
echo    ⚠️ العملات الرقمية عالية التقلب
echo    💰 ابدأ بمبالغ صغيرة للاختبار
echo    📊 راقب النظام في البداية
echo    🛡️ استخدم وقف الخسارة دائماً
echo    📈 لا تستثمر أكثر مما تستطيع خسارته
echo.

pause

echo 🔄 فحص المتطلبات...

REM فحص Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت!
    echo 💡 قم بتثبيت Python 3.8+ من python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM فحص MetaTrader5 library
python -c "import MetaTrader5" 2>nul
if errorlevel 1 (
    echo ❌ مكتبة MetaTrader5 غير مثبتة!
    echo 🔄 محاولة التثبيت...
    pip install MetaTrader5
    if errorlevel 1 (
        echo ❌ فشل في تثبيت MetaTrader5
        echo 💡 جرب: pip install MetaTrader5
        pause
        exit /b 1
    )
)

echo ✅ مكتبة MetaTrader5 متوفرة

REM فحص المكتبات الأخرى
python -c "import pandas, numpy, sklearn, ta" 2>nul
if errorlevel 1 (
    echo ❌ مكتبات مفقودة! محاولة التثبيت...
    pip install pandas numpy scikit-learn ta
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        echo 💡 جرب يدوياً: pip install pandas numpy scikit-learn ta
        pause
        exit /b 1
    )
)

echo ✅ جميع المكتبات متوفرة

REM فحص ملف الإعدادات
if not exist "mt5_crypto_config.ini" (
    echo ⚠️ ملف الإعدادات غير موجود - سيتم إنشاؤه تلقائياً
)

echo.
echo 🚀 بدء نظام تداول العملات الرقمية...
echo.

python mt5_crypto_gui.py

echo.
echo ================================================================
echo ✅ انتهى التشغيل
echo ================================================================
echo.

echo 💡 إذا واجهت مشاكل:
echo    🔧 تأكد من تشغيل MetaTrader 5
echo    🔑 تحقق من بيانات الحساب في mt5_crypto_config.ini
echo    🌐 تأكد من الاتصال بالإنترنت
echo    📊 تأكد من دعم الوسيط للعملات الرقمية
echo    ⚙️ تأكد من تفعيل التداول الآلي في MT5
echo.

echo 🎯 نصائح للنجاح:
echo    📈 ابدأ بوضع التجريبي
echo    💰 استخدم مبالغ صغيرة في البداية
echo    📊 راقب الأداء وتعلم من النتائج
echo    🧠 استخدم ميزة التدريب لتحسين النموذج
echo    🛡️ لا تتجاهل إدارة المخاطر
echo.

pause
