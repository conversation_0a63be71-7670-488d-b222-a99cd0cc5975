# إعداد قاعدة البيانات - نظام إدارة الصيدلية
# Database Setup - Pharmacy Management System

## 📋 **معلومات قاعدة البيانات الحالية**

### **إعدادات الاتصال:**
- **اسم الخادم**: `NARUTO`
- **اسم قاعدة البيانات**: `pharmacy`
- **نوع المصادقة**: Windows Authentication (Integrated Security)
- **سلسلة الاتصال**: `data source = NARUTO; database=pharmacy; integrated security =True`

---

## 🗃️ **هيكل قاعدة البيانات**

### **1. جدول المستخدمين (users)**
```sql
CREATE TABLE users(
    id int identity (1,1) primary key,
    userRole varchar(50) not null,        -- دور المستخدم (Admin/Pharmacist)
    name varchar (250) not null,          -- الاسم الكامل
    dob varchar (250) not null,           -- تاريخ الميلاد
    mobile bigint not null,               -- رقم الهاتف
    email varchar (250) not null,         -- البريد الإلكتروني
    username varchar (250) unique not null, -- اسم المستخدم
    pass varchar(250) not null            -- كلمة المرور
);
```

### **2. جدول الأدوية (medic)**
```sql
CREATE TABLE medic(
    id int identity(1,1) primary key,
    mid varchar(250) not null,            -- معرف الدواء
    mname varchar (250) not null,         -- اسم الدواء
    mnumber varchar (250) not null,       -- رقم الدواء
    mDate varchar (250) not null,         -- تاريخ التصنيع
    eDate varchar(250) not null,          -- تاريخ انتهاء الصلاحية
    quantity bigint not null,             -- الكمية
    perUnit bigint not null,              -- السعر لكل وحدة
    lu varchar(250) not null,             -- آخر تحديث
    br varchar(250) not null,             -- العلامة التجارية
    
    -- أعمدة إضافية
    newEDate VARCHAR(250) NULL,           -- تاريخ انتهاء صلاحية جديد
    newQuantity BIGINT NULL,              -- كمية جديدة
    allqun BIGINT NULL,                   -- إجمالي الكمية
    mnumber_qty INT NULL,                 -- كمية الرقم الأول
    newMDate VARCHAR(250) NULL,           -- تاريخ تصنيع جديد
    
    -- أعمدة الجرعات
    dos2 VARCHAR(100) NULL,               -- الجرعة الثانية
    dos2_qty INT NULL,                    -- كمية الجرعة الثانية
    dos3 VARCHAR(100) NULL,               -- الجرعة الثالثة
    dos3_qty INT NULL,                    -- كمية الجرعة الثالثة
    dos4 VARCHAR(100) NULL,               -- الجرعة الرابعة
    dos4_qty INT NULL,                    -- كمية الجرعة الرابعة
    
    -- أعمدة الكميات الأصلية
    originalQuantity BIGINT DEFAULT 0,    -- الكمية الأصلية
    originalNewQuantity BIGINT DEFAULT 0  -- الكمية الجديدة الأصلية
);
```

### **3. جدول المبيعات (sales)**
```sql
CREATE TABLE sales (
    id INT IDENTITY(1,1) PRIMARY KEY,
    mid VARCHAR(250) NOT NULL,            -- معرف الدواء
    medicineName VARCHAR(250) NOT NULL,   -- اسم الدواء
    dosage VARCHAR(100) NOT NULL,         -- الجرعة
    quantity INT NOT NULL,                -- الكمية المباعة
    pricePerUnit BIGINT NOT NULL,         -- السعر لكل وحدة
    totalPrice BIGINT NOT NULL,           -- إجمالي السعر
    employeeUsername VARCHAR(250) NOT NULL, -- اسم المستخدم للموظف
    employeeName VARCHAR(250) NOT NULL,   -- اسم الموظف
    saleDate DATETIME DEFAULT GETDATE()   -- تاريخ البيع
);
```

### **4. جدول جلسات الموظفين (employee_sessions)**
```sql
CREATE TABLE employee_sessions (
    id INT IDENTITY(1,1) PRIMARY KEY,
    username VARCHAR(250),                -- اسم المستخدم
    employeeName VARCHAR(250),            -- اسم الموظف
    loginTime DATETIME,                   -- وقت تسجيل الدخول
    logoutTime DATETIME NULL,             -- وقت تسجيل الخروج
    sessionDate DATE                      -- تاريخ الجلسة
);
```

---

## 🚀 **خطوات الإعداد**

### **الطريقة الأولى: استخدام الملفات الجاهزة**
```bash
# 1. تشغيل إعداد قاعدة البيانات
setup_database.bat

# 2. اختبار قاعدة البيانات
test_database.bat
```

### **الطريقة الثانية: الإعداد اليدوي**
1. افتح SQL Server Management Studio
2. اتصل بالخادم `NARUTO`
3. شغل السكريبت `update_database_schema.sql`
4. تحقق من النتائج باستخدام `test_database_queries.sql`

---

## 🔍 **استعلامات مهمة للاختبار**

### **تسجيل الدخول:**
```sql
SELECT COUNT(*) FROM users WHERE username = 'your_username' AND pass = 'your_password';
```

### **الأدوية المتاحة:**
```sql
SELECT * FROM medic WHERE eDate >= GETDATE() AND quantity > 0;
```

### **الأدوية منتهية الصلاحية:**
```sql
SELECT * FROM medic WHERE eDate <= GETDATE();
```

### **الأدوية قليلة المخزون:**
```sql
SELECT * FROM medic WHERE quantity <= 10;
```

### **المبيعات اليومية:**
```sql
SELECT 
    CONVERT(DATE, saleDate) AS 'التاريخ',
    COUNT(*) AS 'عدد المبيعات',
    SUM(totalPrice) AS 'إجمالي المبيعات'
FROM sales
WHERE CONVERT(DATE, saleDate) = CONVERT(DATE, GETDATE())
GROUP BY CONVERT(DATE, saleDate);
```

---

## ⚠️ **ملاحظات مهمة**

1. **اسم الخادم**: تأكد من أن اسم الخادم `NARUTO` صحيح
2. **SQL Server**: يجب أن يكون SQL Server يعمل
3. **الصلاحيات**: تأكد من وجود صلاحيات Windows Authentication
4. **النسخ الاحتياطي**: قم بعمل نسخة احتياطية قبل أي تعديل

---

## 🛠️ **استكشاف الأخطاء**

### **خطأ في الاتصال:**
- تحقق من تشغيل SQL Server
- تأكد من صحة اسم الخادم
- تحقق من إعدادات الشبكة

### **خطأ في الجداول:**
- شغل `setup_database.bat`
- تحقق من وجود قاعدة البيانات `pharmacy`

### **خطأ في البيانات:**
- شغل `test_database.bat`
- تحقق من سلامة البيانات

---

## 📞 **الدعم**

إذا واجهت أي مشاكل:
1. شغل `test_database.bat` للتشخيص
2. تحقق من ملفات السجل
3. راجع رسائل الخطأ في SQL Server
