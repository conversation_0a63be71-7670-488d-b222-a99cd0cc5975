using System;
using System.Drawing;
using System.Windows.Forms;

namespace Pharmacy_Admin_System
{
    /// <summary>
    /// واجهة تسجيل الدخول للمدير العام
    /// Super Admin Login Form
    /// </summary>
    public partial class AdminLoginForm : Form
    {
        private DatabaseManager dbManager;
        private Panel mainPanel;
        private Panel loginPanel;
        private Label titleLabel;
        private Label subtitleLabel;
        private TextBox usernameTextBox;
        private TextBox passwordTextBox;
        private Button loginButton;
        private Button exitButton;
        private Label statusLabel;
        private PictureBox logoBox;

        public AdminLoginForm()
        {
            InitializeComponent();
            dbManager = new DatabaseManager();
            SetupForm();
            CheckDatabaseConnection();
        }

        private void SetupForm()
        {
            // Main panel
            mainPanel = new Panel
            {
                Size = new Size(800, 600),
                Location = new Point(0, 0),
                BackColor = Color.FromArgb(45, 45, 48)
            };
            this.Controls.Add(mainPanel);

            // Login panel
            loginPanel = new Panel
            {
                Size = new Size(400, 500),
                Location = new Point(200, 50),
                BackColor = Color.FromArgb(62, 62, 66),
                BorderStyle = BorderStyle.FixedSingle
            };
            mainPanel.Controls.Add(loginPanel);

            // Logo placeholder
            logoBox = new PictureBox
            {
                Size = new Size(80, 80),
                Location = new Point(160, 30),
                BackColor = Color.FromArgb(0, 122, 204),
                SizeMode = PictureBoxSizeMode.CenterImage
            };
            loginPanel.Controls.Add(logoBox);

            // Title
            titleLabel = new Label
            {
                Text = "نظام إدارة الصيدليات المركزي",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(350, 40),
                Location = new Point(25, 130),
                TextAlign = ContentAlignment.MiddleCenter
            };
            loginPanel.Controls.Add(titleLabel);

            // Subtitle
            subtitleLabel = new Label
            {
                Text = "Central Pharmacy Management System",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.LightGray,
                Size = new Size(350, 25),
                Location = new Point(25, 170),
                TextAlign = ContentAlignment.MiddleCenter
            };
            loginPanel.Controls.Add(subtitleLabel);

            // Username label
            var usernameLabel = new Label
            {
                Text = "اسم المستخدم:",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                Size = new Size(100, 25),
                Location = new Point(50, 220),
                TextAlign = ContentAlignment.MiddleLeft
            };
            loginPanel.Controls.Add(usernameLabel);

            // Username textbox
            usernameTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 12),
                Size = new Size(300, 30),
                Location = new Point(50, 245),
                BackColor = Color.FromArgb(85, 85, 85),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            loginPanel.Controls.Add(usernameTextBox);

            // Password label
            var passwordLabel = new Label
            {
                Text = "كلمة المرور:",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                Size = new Size(100, 25),
                Location = new Point(50, 290),
                TextAlign = ContentAlignment.MiddleLeft
            };
            loginPanel.Controls.Add(passwordLabel);

            // Password textbox
            passwordTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 12),
                Size = new Size(300, 30),
                Location = new Point(50, 315),
                BackColor = Color.FromArgb(85, 85, 85),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                UseSystemPasswordChar = true
            };
            loginPanel.Controls.Add(passwordTextBox);

            // Login button
            loginButton = new Button
            {
                Text = "تسجيل الدخول",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Size = new Size(140, 40),
                Location = new Point(50, 370),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            loginButton.FlatAppearance.BorderSize = 0;
            loginButton.Click += LoginButton_Click;
            loginPanel.Controls.Add(loginButton);

            // Exit button
            exitButton = new Button
            {
                Text = "إغلاق",
                Font = new Font("Segoe UI", 12),
                Size = new Size(140, 40),
                Location = new Point(210, 370),
                BackColor = Color.FromArgb(180, 50, 50),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            exitButton.FlatAppearance.BorderSize = 0;
            exitButton.Click += (s, e) => Application.Exit();
            loginPanel.Controls.Add(exitButton);

            // Status label
            statusLabel = new Label
            {
                Text = "أدخل بيانات تسجيل الدخول",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.LightGray,
                Size = new Size(300, 25),
                Location = new Point(50, 430),
                TextAlign = ContentAlignment.MiddleCenter
            };
            loginPanel.Controls.Add(statusLabel);

            // Add Enter key support
            usernameTextBox.KeyDown += (s, e) => { if (e.KeyCode == Keys.Enter) passwordTextBox.Focus(); };
            passwordTextBox.KeyDown += (s, e) => { if (e.KeyCode == Keys.Enter) LoginButton_Click(null, null); };

            // Set default values for testing
            usernameTextBox.Text = "superadmin";
            passwordTextBox.Text = "admin2025";
        }

        private void CheckDatabaseConnection()
        {
            try
            {
                if (!dbManager.EnsureAdminDatabaseExists())
                {
                    statusLabel.Text = "قاعدة البيانات غير موجودة - يرجى تشغيل سكريبت الإعداد";
                    statusLabel.ForeColor = Color.Orange;
                    loginButton.Enabled = false;
                }
                else
                {
                    statusLabel.Text = "النظام جاهز - أدخل بيانات تسجيل الدخول";
                    statusLabel.ForeColor = Color.LightGreen;
                }
            }
            catch (Exception ex)
            {
                statusLabel.Text = $"خطأ في الاتصال بقاعدة البيانات: {ex.Message}";
                statusLabel.ForeColor = Color.Red;
                loginButton.Enabled = false;
            }
        }

        private void LoginButton_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من البيانات المدخلة
                if (string.IsNullOrWhiteSpace(usernameTextBox.Text) || 
                    string.IsNullOrWhiteSpace(passwordTextBox.Text))
                {
                    statusLabel.Text = "يرجى إدخال اسم المستخدم وكلمة المرور";
                    statusLabel.ForeColor = Color.Orange;
                    return;
                }

                statusLabel.Text = "جاري التحقق من البيانات...";
                statusLabel.ForeColor = Color.Yellow;
                Application.DoEvents();

                // التحقق من صحة بيانات تسجيل الدخول
                var adminData = dbManager.ValidateAdminLogin(usernameTextBox.Text, passwordTextBox.Text);

                if (adminData != null)
                {
                    statusLabel.Text = "تم تسجيل الدخول بنجاح";
                    statusLabel.ForeColor = Color.LightGreen;

                    // تسجيل النشاط
                    dbManager.LogActivity(
                        Convert.ToInt32(adminData["id"]), 
                        null, 
                        "Login", 
                        $"تسجيل دخول المدير العام: {adminData["fullName"]}"
                    );

                    // فتح الواجهة الرئيسية
                    var mainForm = new MainAdminForm(adminData);
                    mainForm.Show();
                    this.Hide();
                }
                else
                {
                    statusLabel.Text = "اسم المستخدم أو كلمة المرور غير صحيحة";
                    statusLabel.ForeColor = Color.Red;
                    passwordTextBox.Clear();
                    passwordTextBox.Focus();
                }
            }
            catch (Exception ex)
            {
                statusLabel.Text = $"خطأ في تسجيل الدخول: {ex.Message}";
                statusLabel.ForeColor = Color.Red;
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            Application.Exit();
            base.OnFormClosing(e);
        }
    }
}
