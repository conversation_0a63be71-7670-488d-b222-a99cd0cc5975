using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy_Management_System
{
    /// <summary>
    /// كلاس للتعامل مع قاعدة البيانات الموحدة
    /// </summary>
    internal class UnifiedFunction
    {
        // سلسلة الاتصال بقاعدة البيانات الموحدة
        private string connectionString = "data source = NARUTO; database=UnifiedPharmacy; integrated security =True";

        /// <summary>
        /// الحصول على اتصال بقاعدة البيانات الموحدة
        /// </summary>
        public SqlConnection getConnection()
        {
            SqlConnection con = new SqlConnection();
            con.ConnectionString = connectionString;
            return con;
        }

        /// <summary>
        /// جلب البيانات من قاعدة البيانات الموحدة
        /// </summary>
        public DataSet getData(String query)
        {
            SqlConnection con = null;
            try
            {
                con = getConnection();
                SqlCommand cmd = new SqlCommand();
                cmd.Connection = con;
                cmd.CommandText = query;
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                DataSet ds = new DataSet();
                da.Fill(ds);
                return ds;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب البيانات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"الاستعلام: {query}");
                throw;
            }
            finally
            {
                if (con != null && con.State == ConnectionState.Open)
                {
                    con.Close();
                }
            }
        }

        /// <summary>
        /// تنفيذ استعلام تحديث أو إدراج أو حذف
        /// </summary>
        public void setData(String query, String msg)
        {
            SqlConnection con = null;
            try
            {
                con = getConnection();
                SqlCommand cmd = new SqlCommand();
                cmd.Connection = con;
                cmd.CommandText = query;

                con.Open();
                cmd.ExecuteNonQuery();

                // عرض الرسالة فقط إذا لم تكن فارغة
                if (!string.IsNullOrEmpty(msg) && !string.IsNullOrWhiteSpace(msg))
                {
                    MessageBox.Show(msg, "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنفيذ الاستعلام: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"الاستعلام: {query}");
                throw;
            }
            finally
            {
                if (con != null && con.State == ConnectionState.Open)
                {
                    con.Close();
                }
            }
        }

        /// <summary>
        /// تنفيذ استعلام مع معاملات لتجنب SQL Injection
        /// </summary>
        public DataSet getDataWithParameters(string query, Dictionary<string, object> parameters)
        {
            SqlConnection con = null;
            try
            {
                con = getConnection();
                SqlCommand cmd = new SqlCommand(query, con);

                if (parameters != null)
                {
                    foreach (var param in parameters)
                    {
                        cmd.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                    }
                }

                SqlDataAdapter da = new SqlDataAdapter(cmd);
                DataSet ds = new DataSet();
                da.Fill(ds);
                return ds;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب البيانات مع المعاملات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"الاستعلام: {query}");
                throw;
            }
            finally
            {
                if (con != null && con.State == ConnectionState.Open)
                {
                    con.Close();
                }
            }
        }

        /// <summary>
        /// تنفيذ استعلام مع معاملات - دالة موحدة للاستعلامات
        /// </summary>
        public DataSet ExecuteQuery(string query, Dictionary<string, object> parameters)
        {
            SqlConnection con = null;
            try
            {
                con = getConnection();
                SqlCommand cmd = new SqlCommand(query, con);

                if (parameters != null)
                {
                    foreach (var param in parameters)
                    {
                        cmd.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                    }
                }

                SqlDataAdapter da = new SqlDataAdapter(cmd);
                DataSet ds = new DataSet();
                da.Fill(ds);
                return ds;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنفيذ الاستعلام: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"الاستعلام: {query}");
                if (parameters != null)
                {
                    foreach (var param in parameters)
                    {
                        System.Diagnostics.Debug.WriteLine($"المعامل {param.Key}: {param.Value}");
                    }
                }
                throw;
            }
            finally
            {
                if (con != null && con.State == ConnectionState.Open)
                {
                    con.Close();
                }
            }
        }

        /// <summary>
        /// تنفيذ استعلام تحديث مع معاملات
        /// </summary>
        public int setDataWithParameters(string query, Dictionary<string, object> parameters, string msg = "")
        {
            SqlConnection con = null;
            try
            {
                con = getConnection();
                SqlCommand cmd = new SqlCommand(query, con);
                
                if (parameters != null)
                {
                    foreach (var param in parameters)
                    {
                        cmd.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                    }
                }

                con.Open();
                int result = cmd.ExecuteNonQuery();

                // عرض الرسالة فقط إذا لم تكن فارغة
                if (!string.IsNullOrEmpty(msg) && !string.IsNullOrWhiteSpace(msg))
                {
                    MessageBox.Show(msg, "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنفيذ الاستعلام مع المعاملات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"الاستعلام: {query}");
                throw;
            }
            finally
            {
                if (con != null && con.State == ConnectionState.Open)
                {
                    con.Close();
                }
            }
        }

        /// <summary>
        /// الحصول على قائمة الصيدليات النشطة
        /// </summary>
        public DataSet getActivePharmacies()
        {
            try
            {
                string query = "SELECT id, pharmacyCode, pharmacyName, ownerName, city, region FROM pharmacies WHERE isActive = 1 ORDER BY pharmacyName";
                return getData(query);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب الصيدليات: {ex.Message}");

                // إنشاء DataSet فارغ مع صيدلية افتراضية
                DataSet ds = new DataSet();
                DataTable dt = new DataTable();
                dt.Columns.Add("id", typeof(int));
                dt.Columns.Add("pharmacyCode", typeof(string));
                dt.Columns.Add("pharmacyName", typeof(string));
                dt.Columns.Add("ownerName", typeof(string));
                dt.Columns.Add("city", typeof(string));
                dt.Columns.Add("region", typeof(string));

                // إضافة صيدلية افتراضية
                DataRow row = dt.NewRow();
                row["id"] = 1;
                row["pharmacyCode"] = "MAIN001";
                row["pharmacyName"] = "الصيدلية الرئيسية";
                row["ownerName"] = "مدير النظام";
                row["city"] = "المدينة";
                row["region"] = "المنطقة";
                dt.Rows.Add(row);

                ds.Tables.Add(dt);
                return ds;
            }
        }

        /// <summary>
        /// التحقق من صحة بيانات تسجيل الدخول
        /// </summary>
        public DataSet validateLogin(string username, string password, int pharmacyId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"محاولة تسجيل الدخول: {username}, pharmacyId: {pharmacyId}");

                DataSet result;

                // إذا كان pharmacyId صفر أو غير صحيح، ابحث بدون pharmacyId
                if (pharmacyId <= 0)
                {
                    System.Diagnostics.Debug.WriteLine("pharmacyId غير صحيح، البحث بدون pharmacyId...");

                    var simpleParameters = new Dictionary<string, object>
                    {
                        {"@username", username},
                        {"@password", password}
                    };

                    string simpleQuery = @"
                        SELECT u.*, COALESCE(p.pharmacyName, 'صيدلية افتراضية') as pharmacyName,
                               COALESCE(p.pharmacyCode, 'DEFAULT') as pharmacyCode
                        FROM users u
                        LEFT JOIN pharmacies p ON u.pharmacyId = p.id
                        WHERE u.username = @username AND u.pass = @password AND u.isActive = 1";

                    result = getDataWithParameters(simpleQuery, simpleParameters);
                    System.Diagnostics.Debug.WriteLine($"نتيجة البحث البسيط: {result.Tables[0].Rows.Count} صف");
                }
                else
                {
                    // البحث مع pharmacyId محدد
                    var parameters = new Dictionary<string, object>
                    {
                        {"@username", username},
                        {"@password", password},
                        {"@pharmacyId", pharmacyId}
                    };

                    string query = @"
                        SELECT u.*, p.pharmacyName, p.pharmacyCode
                        FROM users u
                        INNER JOIN pharmacies p ON u.pharmacyId = p.id
                        WHERE u.username = @username AND u.pass = @password AND u.pharmacyId = @pharmacyId AND u.isActive = 1";

                    result = getDataWithParameters(query, parameters);
                    System.Diagnostics.Debug.WriteLine($"نتيجة البحث مع pharmacyId: {result.Tables[0].Rows.Count} صف");

                    // إذا لم نجد نتائج، جرب بدون pharmacyId
                    if (result.Tables[0].Rows.Count == 0)
                    {
                        System.Diagnostics.Debug.WriteLine("لم توجد نتائج، محاولة البحث بدون pharmacyId...");

                        var simpleParameters = new Dictionary<string, object>
                        {
                            {"@username", username},
                            {"@password", password}
                        };

                        string simpleQuery = @"
                            SELECT u.*, COALESCE(p.pharmacyName, 'صيدلية افتراضية') as pharmacyName,
                                   COALESCE(p.pharmacyCode, 'DEFAULT') as pharmacyCode
                            FROM users u
                            LEFT JOIN pharmacies p ON u.pharmacyId = p.id
                            WHERE u.username = @username AND u.pass = @password AND u.isActive = 1";

                        result = getDataWithParameters(simpleQuery, simpleParameters);
                        System.Diagnostics.Debug.WriteLine($"نتيجة البحث البديل: {result.Tables[0].Rows.Count} صف");
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من تسجيل الدخول: {ex.Message}");

                // في حالة الخطأ، استخدم قاعدة البيانات القديمة
                try
                {
                    Function oldFunction = new Function();
                    string oldQuery = $"SELECT * FROM users WHERE username = '{username}' AND pass = '{password}'";
                    return oldFunction.getData(oldQuery);
                }
                catch
                {
                    // إنشاء DataSet فارغ
                    return new DataSet();
                }
            }
        }

        /// <summary>
        /// تسجيل جلسة دخول المستخدم
        /// </summary>
        public void recordLoginSession(int pharmacyId, string username, string employeeName)
        {
            var parameters = new Dictionary<string, object>
            {
                {"@pharmacyId", pharmacyId},
                {"@username", username},
                {"@employeeName", employeeName},
                {"@loginTime", DateTime.Now},
                {"@sessionDate", DateTime.Now.Date}
            };

            string query = @"
                INSERT INTO employee_sessions (pharmacyId, username, employeeName, loginTime, sessionDate)
                VALUES (@pharmacyId, @username, @employeeName, @loginTime, @sessionDate)";

            setDataWithParameters(query, parameters);
        }

        /// <summary>
        /// تسجيل خروج المستخدم
        /// </summary>
        public void recordLogoutSession(int pharmacyId, string username)
        {
            var parameters = new Dictionary<string, object>
            {
                {"@pharmacyId", pharmacyId},
                {"@username", username},
                {"@logoutTime", DateTime.Now}
            };

            string query = @"
                UPDATE employee_sessions 
                SET logoutTime = @logoutTime 
                WHERE pharmacyId = @pharmacyId AND username = @username AND logoutTime IS NULL";

            setDataWithParameters(query, parameters);
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        public bool testConnection()
        {
            try
            {
                using (var con = getConnection())
                {
                    con.Open();
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود اسم المستخدم
        /// </summary>
        public bool isUsernameExists(string username)
        {
            try
            {
                var parameters = new Dictionary<string, object>
                {
                    {"@username", username}
                };

                string query = "SELECT COUNT(*) FROM users WHERE username = @username";
                DataSet ds = getDataWithParameters(query, parameters);

                if (ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    int count = Convert.ToInt32(ds.Tables[0].Rows[0][0]);
                    return count > 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من اسم المستخدم: {ex.Message}");
                return true; // في حالة الخطأ، نفترض أن الاسم موجود لتجنب التكرار
            }
        }

        /// <summary>
        /// إنشاء مستخدم جديد
        /// </summary>
        public bool createUser(Dictionary<string, object> userInfo)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"إنشاء مستخدم جديد: {userInfo["username"]}, pharmacyId: {userInfo["pharmacyId"]}");

                string query = @"
                    INSERT INTO users (pharmacyId, userRole, name, dob, mobile, email, username, pass, isActive)
                    VALUES (@pharmacyId, @userRole, @name, @dob, @mobile, @email, @username, @pass, 1)";

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", userInfo["pharmacyId"]},
                    {"@userRole", userInfo["userRole"]},
                    {"@name", userInfo["name"]},
                    {"@dob", userInfo["dob"]},
                    {"@mobile", userInfo["mobile"]},
                    {"@email", userInfo["email"]},
                    {"@username", userInfo["username"]},
                    {"@pass", userInfo["pass"]}
                };

                int result = setDataWithParameters(query, parameters);
                System.Diagnostics.Debug.WriteLine($"نتيجة إنشاء المستخدم: {result}");

                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء المستخدم: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود المستخدم في قاعدة البيانات (للتشخيص)
        /// </summary>
        public DataSet getUserInfo(string username)
        {
            try
            {
                var parameters = new Dictionary<string, object>
                {
                    {"@username", username}
                };

                string query = @"
                    SELECT u.*, p.pharmacyName, p.pharmacyCode
                    FROM users u
                    LEFT JOIN pharmacies p ON u.pharmacyId = p.id
                    WHERE u.username = @username";

                return getDataWithParameters(query, parameters);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب معلومات المستخدم: {ex.Message}");
                return new DataSet();
            }
        }
    }
}
