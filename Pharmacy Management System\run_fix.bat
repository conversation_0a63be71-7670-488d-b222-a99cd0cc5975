@echo off
echo تشغيل إصلاح قاعدة البيانات...

cd /d "%~dp0"
sqlcmd -S NARUTO -E -i create_sp_RegisterPharmacy.sql

if %ERRORLEVEL% EQU 0 (
    echo تم إنشاء الإجراء المخزن بنجاح!
    
    echo إنشاء باقي الإجراءات...
    sqlcmd -S NARUTO -E -Q "USE PharmacyNetworkOnline; IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_SearchMedicines') DROP PROCEDURE sp_SearchMedicines; CREATE PROCEDURE sp_SearchMedicines @searchTerm NVARCHAR(250) = '', @pharmacyId INT = NULL, @category NVARCHAR(100) = '', @minPrice DECIMAL(10,2) = 0, @maxPrice DECIMAL(10,2) = 999999, @pageNumber INT = 1, @pageSize INT = 20 AS BEGIN SET NOCOUNT ON; DECLARE @offset INT = (@pageNumber - 1) * @pageSize; SELECT m.id, m.pharmacyId, p.pharmacyName, p.pharmacyCode, p.city, p.region, p.phone, m.medicineName, m.genericName, m.brandName, m.manufacturer, m.category, m.dosageForm, m.strength, m.availableQuantity, m.unitPrice, m.wholesalePrice, m.expiryDate, DATEDIFF(day, GETDATE(), m.expiryDate) as daysToExpiry FROM networkmedicines m INNER JOIN pharmacies p ON m.pharmacyId = p.id WHERE m.isAvailableForSale = 1 AND m.availableQuantity > 0 AND m.expiryDate > GETDATE() AND p.isActive = 1 AND (@pharmacyId IS NULL OR m.pharmacyId != @pharmacyId) AND (@searchTerm = '' OR m.medicineName LIKE '%' + @searchTerm + '%' OR m.genericName LIKE '%' + @searchTerm + '%' OR m.brandName LIKE '%' + @searchTerm + '%' OR m.manufacturer LIKE '%' + @searchTerm + '%') AND (@category = '' OR m.category = @category) AND m.unitPrice BETWEEN @minPrice AND @maxPrice ORDER BY m.medicineName OFFSET @offset ROWS FETCH NEXT @pageSize ROWS ONLY; END;"
    
    sqlcmd -S NARUTO -E -Q "USE PharmacyNetworkOnline; IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetActivePharmacies') DROP PROCEDURE sp_GetActivePharmacies; CREATE PROCEDURE sp_GetActivePharmacies AS BEGIN SET NOCOUNT ON; SELECT p.id, p.pharmacyCode, p.pharmacyName, p.ownerName, p.city, p.region, p.phone, p.email, p.lastOnline, p.subscriptionType, COUNT(DISTINCT u.id) as totalUsers, COUNT(DISTINCT m.id) as totalMedicines, 0 as totalOrdersSent, 0 as totalOrdersReceived, 0 as averageRating FROM pharmacies p LEFT JOIN network_users u ON p.id = u.pharmacyId AND u.isActive = 1 LEFT JOIN networkmedicines m ON p.id = m.pharmacyId AND m.isAvailableForSale = 1 WHERE p.isActive = 1 GROUP BY p.id, p.pharmacyCode, p.pharmacyName, p.ownerName, p.city, p.region, p.phone, p.email, p.lastOnline, p.subscriptionType ORDER BY p.pharmacyName; END;"
    
    echo ✅ تم إصلاح قاعدة البيانات بنجاح!
    echo يمكنك الآن تسجيل صيدلية جديدة بدون أخطاء.
) else (
    echo ❌ حدث خطأ أثناء الإصلاح!
)

pause
