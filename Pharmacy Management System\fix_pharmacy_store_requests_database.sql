-- إصلا<PERSON> قاعدة البيانات لمتجر الصيدلية - طلبات الأدوية
USE UnifiedPharmacy;

PRINT '=== بدء إصلاح قاعدة البيانات لطلبات الأدوية ===';

-- ===================================
-- 1. إنشاء جدول الصيدليات إذا لم يكن موجوداً
-- ===================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacies')
BEGIN
    PRINT 'إنشاء جدول الصيدليات...';
    CREATE TABLE pharmacies (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacy_name NVARCHAR(255) NOT NULL,
        pharmacy_code NVARCHAR(50) UNIQUE NOT NULL,
        phone NVARCHAR(20),
        address NVARCHAR(500),
        city NVARCHAR(100),
        region NVARCHAR(100),
        email NVARCHAR(255),
        created_date DATETIME DEFAULT GETDATE(),
        is_active BIT DEFAULT 1
    );
    PRINT '✅ تم إنشاء جدول pharmacies';
    
    -- إدراج بيانات تجريبية
    INSERT INTO pharmacies (pharmacy_name, pharmacy_code, phone, city, region) VALUES
    ('الصيدلية المركزية', 'PHARM001', '0123456789', 'الرياض', 'الرياض'),
    ('صيدلية النهضة', 'PHARM002', '0123456790', 'جدة', 'مكة المكرمة'),
    ('صيدلية الشفاء', 'PHARM003', '0123456791', 'الدمام', 'الشرقية');
    PRINT '✅ تم إدراج بيانات تجريبية للصيدليات';
END
ELSE
    PRINT '✅ جدول pharmacies موجود';

-- ===================================
-- 2. إنشاء جدول الأدوية المنشورة
-- ===================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'published_medicines')
BEGIN
    PRINT 'إنشاء جدول الأدوية المنشورة...';
    CREATE TABLE published_medicines (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacy_id INT NOT NULL,
        medicine_name NVARCHAR(255) NOT NULL,
        medicine_number NVARCHAR(100),
        quantity INT NOT NULL DEFAULT 0,
        expiry_date DATE,
        price_per_unit DECIMAL(10,2) NOT NULL DEFAULT 0,
        description NVARCHAR(1000),
        publish_date DATETIME DEFAULT GETDATE(),
        is_active BIT DEFAULT 1,
        dosage_info NVARCHAR(MAX), -- JSON للجرعات المتعددة
        FOREIGN KEY (pharmacy_id) REFERENCES pharmacies(id)
    );
    PRINT '✅ تم إنشاء جدول published_medicines';
END
ELSE
    PRINT '✅ جدول published_medicines موجود';

-- ===================================
-- 3. إنشاء جدول طلبات الشراء
-- ===================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'purchase_requests')
BEGIN
    PRINT 'إنشاء جدول طلبات الشراء...';
    CREATE TABLE purchase_requests (
        id INT IDENTITY(1,1) PRIMARY KEY,
        buyer_pharmacy_id INT NOT NULL,
        seller_pharmacy_id INT NOT NULL,
        published_medicine_id INT NOT NULL,
        requested_quantity INT NOT NULL,
        offered_price DECIMAL(10,2),
        status NVARCHAR(50) DEFAULT 'pending',
        request_date DATETIME DEFAULT GETDATE(),
        response_date DATETIME,
        response_message NVARCHAR(1000),
        FOREIGN KEY (buyer_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (seller_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (published_medicine_id) REFERENCES published_medicines(id)
    );
    PRINT '✅ تم إنشاء جدول purchase_requests';
END
ELSE
    PRINT '✅ جدول purchase_requests موجود';

-- ===================================
-- 4. إنشاء جدول الرسائل
-- ===================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacy_messages')
BEGIN
    PRINT 'إنشاء جدول الرسائل...';
    CREATE TABLE pharmacy_messages (
        id INT IDENTITY(1,1) PRIMARY KEY,
        sender_pharmacy_id INT NOT NULL,
        receiver_pharmacy_id INT NOT NULL,
        message_text NVARCHAR(MAX) NOT NULL,
        message_date DATETIME DEFAULT GETDATE(),
        is_read BIT DEFAULT 0,
        related_request_id INT NULL,
        FOREIGN KEY (sender_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (receiver_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (related_request_id) REFERENCES purchase_requests(id)
    );
    PRINT '✅ تم إنشاء جدول pharmacy_messages';
END
ELSE
    PRINT '✅ جدول pharmacy_messages موجود';

PRINT '=== تم إكمال إصلاح قاعدة البيانات ===';

-- عرض إحصائيات سريعة
SELECT 'pharmacies' as TableName, COUNT(*) as RecordCount FROM pharmacies
UNION ALL
SELECT 'published_medicines', COUNT(*) FROM published_medicines
UNION ALL
SELECT 'purchase_requests', COUNT(*) FROM purchase_requests
UNION ALL
SELECT 'pharmacy_messages', COUNT(*) FROM pharmacy_messages;
