#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import messagebox, scrolledtext
import threading
import time
from datetime import datetime
import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from mt5_real_crypto_system import MT5RealCryptoSystem
except ImportError as e:
    print(f"خطأ في استيراد النظام: {e}")
    sys.exit(1)

class SimpleMT5GUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام تداول العملات الرقمية على MetaTrader 5")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2b2b2b')
        
        # النظام
        self.trading_system = None
        self.is_connected = False
        self.is_trading = False
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # بدء تحديث البيانات
        self.update_data()
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#2b2b2b')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = tk.Label(main_frame, text="🚀 نظام تداول العملات الرقمية على MetaTrader 5", 
                              font=('Arial', 18, 'bold'), fg='#00ff88', bg='#2b2b2b')
        title_label.pack(pady=(0, 20))
        
        # الإطار العلوي - التحكم
        control_frame = tk.Frame(main_frame, bg='#2b2b2b', relief=tk.RAISED, bd=2)
        control_frame.pack(fill=tk.X, pady=(0, 15))

        # عنوان الإطار
        tk.Label(control_frame, text="التحكم الأساسي",
                font=('Arial', 12, 'bold'), fg='#ffffff', bg='#2b2b2b').pack(pady=5)
        
        # صف الاتصال
        conn_frame = tk.Frame(control_frame, bg='#2b2b2b')
        conn_frame.pack(fill=tk.X, padx=15, pady=15)
        
        self.connect_btn = tk.Button(conn_frame, text="🔌 اتصال بـ MT5", command=self.connect_system,
                                    bg='#28a745', fg='white', font=('Arial', 10, 'bold'), width=15)
        self.connect_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.disconnect_btn = tk.Button(conn_frame, text="🔌 قطع الاتصال", command=self.disconnect_system,
                                       bg='#dc3545', fg='white', font=('Arial', 10, 'bold'), 
                                       width=15, state='disabled')
        self.disconnect_btn.pack(side=tk.LEFT, padx=(0, 20))
        
        self.status_label = tk.Label(conn_frame, text="❌ غير متصل بـ MT5", 
                                    fg='#dc3545', bg='#2b2b2b', font=('Arial', 11, 'bold'))
        self.status_label.pack(side=tk.LEFT)
        
        # صف الرمز
        symbol_frame = tk.Frame(control_frame, bg='#2b2b2b')
        symbol_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        tk.Label(symbol_frame, text="💰 رمز العملة:", fg='white', bg='#2b2b2b', 
                font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        
        self.symbol_var = tk.StringVar(value="BTC")

        # قائمة منسدلة بسيطة
        symbol_values = ['BTC', 'ETH', 'BTCL', 'BTCO', 'BTCUSD', 'ETHUSD']
        self.symbol_menu = tk.OptionMenu(symbol_frame, self.symbol_var, *symbol_values)
        self.symbol_menu.config(bg='#343a40', fg='white', font=('Arial', 9), width=10)
        self.symbol_menu.pack(side=tk.LEFT, padx=(10, 20))
        
        self.refresh_btn = tk.Button(symbol_frame, text="🔄 تحديث", command=self.refresh_price,
                                    bg='#007bff', fg='white', font=('Arial', 9), width=10)
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 20))
        
        self.price_label = tk.Label(symbol_frame, text="السعر: غير متوفر", 
                                   fg='#ffc107', bg='#2b2b2b', font=('Arial', 11, 'bold'))
        self.price_label.pack(side=tk.LEFT)
        
        # صف الإعدادات
        settings_frame = tk.Frame(control_frame, bg='#2b2b2b')
        settings_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        tk.Label(settings_frame, text="🎯 نسبة الثقة:", fg='white', bg='#2b2b2b', 
                font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        
        self.confidence_var = tk.DoubleVar(value=70.0)
        self.confidence_scale = tk.Scale(settings_frame, from_=30, to=100, orient=tk.HORIZONTAL,
                                        variable=self.confidence_var, bg='#2b2b2b', fg='white',
                                        highlightbackground='#2b2b2b', length=200)
        self.confidence_scale.pack(side=tk.LEFT, padx=(10, 10))
        
        self.confidence_label = tk.Label(settings_frame, text="70%", 
                                        fg='#ffc107', bg='#2b2b2b', font=('Arial', 12, 'bold'))
        self.confidence_label.pack(side=tk.LEFT, padx=(0, 20))
        
        # أزرار سريعة
        tk.Button(settings_frame, text="50%", command=lambda: self.set_confidence(50),
                 bg='#ffc107', fg='black', font=('Arial', 8), width=5).pack(side=tk.LEFT, padx=2)
        tk.Button(settings_frame, text="70%", command=lambda: self.set_confidence(70),
                 bg='#007bff', fg='white', font=('Arial', 8), width=5).pack(side=tk.LEFT, padx=2)
        tk.Button(settings_frame, text="90%", command=lambda: self.set_confidence(90),
                 bg='#28a745', fg='white', font=('Arial', 8), width=5).pack(side=tk.LEFT, padx=2)
        tk.Button(settings_frame, text="100%", command=lambda: self.set_confidence(100),
                 bg='#6f42c1', fg='white', font=('Arial', 8), width=5).pack(side=tk.LEFT, padx=2)
        
        # صف التداول
        trading_frame = tk.Frame(control_frame, bg='#2b2b2b')
        trading_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        self.demo_var = tk.BooleanVar(value=True)
        self.demo_check = tk.Checkbutton(trading_frame, text="🛡️ وضع تجريبي آمن", 
                                        variable=self.demo_var, fg='white', bg='#2b2b2b',
                                        selectcolor='#2b2b2b', font=('Arial', 10))
        self.demo_check.pack(side=tk.LEFT, padx=(0, 30))
        
        self.start_btn = tk.Button(trading_frame, text="🚀 بدء التداول", command=self.start_trading,
                                  bg='#28a745', fg='white', font=('Arial', 10, 'bold'), 
                                  width=15, state='disabled')
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_btn = tk.Button(trading_frame, text="⏹️ إيقاف التداول", command=self.stop_trading,
                                 bg='#dc3545', fg='white', font=('Arial', 10, 'bold'), 
                                 width=15, state='disabled')
        self.stop_btn.pack(side=tk.LEFT)
        
        # الإطار الأوسط - معلومات الحساب
        account_frame = tk.Frame(main_frame, bg='#2b2b2b', relief=tk.RAISED, bd=2)
        account_frame.pack(fill=tk.X, pady=(0, 15))

        # عنوان الإطار
        tk.Label(account_frame, text="معلومات حساب MetaTrader 5",
                font=('Arial', 12, 'bold'), fg='#ffffff', bg='#2b2b2b').pack(pady=5)
        
        # شبكة المعلومات
        info_frame = tk.Frame(account_frame, bg='#2b2b2b')
        info_frame.pack(fill=tk.X, padx=15, pady=15)
        
        # الصف الأول
        row1 = tk.Frame(info_frame, bg='#2b2b2b')
        row1.pack(fill=tk.X, pady=5)
        
        tk.Label(row1, text="🏢 الشركة:", fg='white', bg='#2b2b2b', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.company_label = tk.Label(row1, text="غير متصل", fg='#17a2b8', bg='#2b2b2b', font=('Arial', 10))
        self.company_label.pack(side=tk.LEFT, padx=(10, 50))
        
        tk.Label(row1, text="💰 الرصيد:", fg='white', bg='#2b2b2b', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.balance_label = tk.Label(row1, text="$0.00", fg='#28a745', bg='#2b2b2b', font=('Arial', 12, 'bold'))
        self.balance_label.pack(side=tk.LEFT, padx=(10, 50))
        
        tk.Label(row1, text="📊 الربح/الخسارة:", fg='white', bg='#2b2b2b', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.profit_label = tk.Label(row1, text="$0.00", fg='white', bg='#2b2b2b', font=('Arial', 12, 'bold'))
        self.profit_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # الصف الثاني
        row2 = tk.Frame(info_frame, bg='#2b2b2b')
        row2.pack(fill=tk.X, pady=5)
        
        tk.Label(row2, text="🖥️ الخادم:", fg='white', bg='#2b2b2b', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.server_label = tk.Label(row2, text="غير متصل", fg='#17a2b8', bg='#2b2b2b', font=('Arial', 10))
        self.server_label.pack(side=tk.LEFT, padx=(10, 50))
        
        tk.Label(row2, text="📋 الصفقات:", fg='white', bg='#2b2b2b', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.positions_label = tk.Label(row2, text="0", fg='#ffc107', bg='#2b2b2b', font=('Arial', 12, 'bold'))
        self.positions_label.pack(side=tk.LEFT, padx=(10, 50))
        
        tk.Label(row2, text="🔄 الحالة:", fg='white', bg='#2b2b2b', font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.system_status_label = tk.Label(row2, text="متوقف", fg='#ffc107', bg='#2b2b2b', font=('Arial', 11, 'bold'))
        self.system_status_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # السجل
        log_frame = tk.Frame(main_frame, bg='#2b2b2b', relief=tk.RAISED, bd=2)
        log_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان الإطار
        tk.Label(log_frame, text="سجل الأحداث والتحليلات",
                font=('Arial', 12, 'bold'), fg='#ffffff', bg='#2b2b2b').pack(pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, 
                                                 bg='#1e1e1e', fg='#00ff88', 
                                                 font=('Consolas', 9),
                                                 insertbackground='#00ff88')
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # رسالة ترحيب
        welcome_msg = """🚀 مرحباً بك في نظام تداول العملات الرقمية على MetaTrader 5!

✨ المميزات:
• الاتصال المباشر بـ MetaTrader 5
• عرض معلومات الحساب الحقيقية
• تداول العملات الرقمية (BTC, ETH, إلخ)
• إعدادات نسبة الثقة قابلة للتخصيص (30% - 100%)
• تحليل فني متقدم مع الذكاء الاصطناعي

📋 خطوات البدء:
1. تأكد من تشغيل MetaTrader 5 وتسجيل الدخول
2. اضغط "اتصال بـ MT5" للاتصال
3. اختر رمز العملة (BTC, ETH, إلخ)
4. حدد نسبة الثقة المطلوبة للدخول
5. اضغط "بدء التداول" لبدء التداول الذكي

⚠️ تنبيه: ابدأ دائماً بالوضع التجريبي!
"""
        self.log_text.insert(tk.END, welcome_msg)
        
        # ربط تحديث نسبة الثقة
        self.confidence_scale.configure(command=self.update_confidence_display)
        
    def update_confidence_display(self, value=None):
        """تحديث عرض نسبة الثقة"""
        confidence = self.confidence_var.get()
        self.confidence_label.config(text=f"{confidence:.0f}%")
        
        # تغيير اللون حسب النسبة
        if confidence >= 90:
            color = '#28a745'  # أخضر
        elif confidence >= 70:
            color = '#007bff'  # أزرق
        elif confidence >= 50:
            color = '#ffc107'  # أصفر
        else:
            color = '#dc3545'  # أحمر
            
        self.confidence_label.config(fg=color)
        
    def set_confidence(self, value):
        """تحديد نسبة الثقة بسرعة"""
        self.confidence_var.set(value)
        self.update_confidence_display()
        
    def log_message(self, message):
        """إضافة رسالة للسجل"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, full_message)
        self.log_text.see(tk.END)
        
    def connect_system(self):
        """الاتصال بالنظام"""
        try:
            self.log_message("🔌 محاولة الاتصال بـ MetaTrader 5...")
            
            demo_mode = self.demo_var.get()
            self.trading_system = MT5RealCryptoSystem(demo_mode=demo_mode)
            
            if self.trading_system.connect():
                self.is_connected = True
                self.status_label.config(text="✅ متصل بـ MT5", fg='#28a745')
                self.connect_btn.config(state='disabled')
                self.disconnect_btn.config(state='normal')
                self.start_btn.config(state='normal')
                self.refresh_btn.config(state='normal')
                
                # تحديث معلومات الحساب
                self.update_account_info()
                
                # تحديث الرموز المتوفرة
                self.refresh_symbols()
                
                self.log_message("✅ تم الاتصال بـ MT5 بنجاح!")
                
            else:
                self.log_message("❌ فشل في الاتصال بـ MT5!")
                messagebox.showerror("خطأ", "فشل في الاتصال بـ MetaTrader 5\nتأكد من:\n• تشغيل MT5\n• تسجيل الدخول\n• تفعيل التداول الآلي")
                
        except Exception as e:
            self.log_message(f"❌ خطأ في الاتصال: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في الاتصال: {str(e)}")
            
    def disconnect_system(self):
        """قطع الاتصال"""
        if self.is_trading:
            self.stop_trading()
            
        if self.trading_system:
            self.trading_system.disconnect()
            
        self.is_connected = False
        self.status_label.config(text="❌ غير متصل بـ MT5", fg='#dc3545')
        self.connect_btn.config(state='normal')
        self.disconnect_btn.config(state='disabled')
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='disabled')
        self.refresh_btn.config(state='disabled')
        
        # مسح المعلومات
        self.company_label.config(text="غير متصل")
        self.server_label.config(text="غير متصل")
        self.balance_label.config(text="$0.00")
        self.profit_label.config(text="$0.00", fg='white')
        self.positions_label.config(text="0")
        self.system_status_label.config(text="متوقف", fg='#ffc107')
        self.price_label.config(text="السعر: غير متوفر")
        
        self.log_message("🔌 تم قطع الاتصال")
        
    def refresh_symbols(self):
        """تحديث الرموز المتوفرة"""
        if not self.is_connected:
            return
            
        try:
            symbols = self.trading_system.get_mt5_symbols()
            if symbols:
                # إضافة الرموز الخارجية
                all_symbols = symbols + ['BTCUSD', 'ETHUSD', 'LTCUSD', 'XRPUSD']

                # تحديث القائمة المنسدلة
                menu = self.symbol_menu['menu']
                menu.delete(0, 'end')
                for symbol in all_symbols:
                    menu.add_command(label=symbol, command=tk._setit(self.symbol_var, symbol))

                self.log_message(f"🔄 تم تحديث الرموز: {len(symbols)} رمز من MT5")
            else:
                self.log_message("⚠️ لم يتم العثور على رموز عملات رقمية في MT5")
                
        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث الرموز: {str(e)}")
            
    def refresh_price(self):
        """تحديث السعر الحالي"""
        if not self.is_connected:
            return
            
        try:
            symbol = self.symbol_var.get()
            price_info = self.trading_system.get_best_price(symbol)
            
            if price_info['final_price']:
                price = price_info['final_price']
                source = price_info['source']
                self.price_label.config(text=f"السعر: ${price:,.2f} ({source})")
                self.log_message(f"💰 {symbol}: ${price:,.2f} (المصدر: {source})")
            else:
                self.price_label.config(text="السعر: غير متوفر")
                self.log_message(f"❌ فشل في الحصول على سعر {symbol}")
                
        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث السعر: {str(e)}")
            
    def update_account_info(self):
        """تحديث معلومات الحساب"""
        if not self.is_connected or not self.trading_system:
            return
            
        try:
            account_info = self.trading_system.get_account_info()
            
            if account_info:
                self.company_label.config(text=account_info.get('company', 'غير متوفر'))
                self.server_label.config(text=account_info.get('server', 'غير متوفر'))
                
                balance = account_info.get('balance', 0)
                self.balance_label.config(text=f"${balance:,.2f}")
                
                profit = account_info.get('profit', 0)
                if profit > 0:
                    self.profit_label.config(text=f"+${profit:,.2f}", fg='#28a745')
                elif profit < 0:
                    self.profit_label.config(text=f"${profit:,.2f}", fg='#dc3545')
                else:
                    self.profit_label.config(text="$0.00", fg='white')
                
                positions = self.trading_system.get_open_positions()
                self.positions_label.config(text=str(len(positions)))
                
        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث معلومات الحساب: {str(e)}")
            
    def start_trading(self):
        """بدء التداول"""
        if not self.is_connected:
            messagebox.showwarning("تحذير", "يجب الاتصال بـ MT5 أولاً!")
            return
            
        symbol = self.symbol_var.get()
        confidence_threshold = self.confidence_var.get()
        
        # تأكيد البدء
        mode_text = "تجريبي آمن" if self.demo_var.get() else "حقيقي"
        confirm_msg = f"""هل تريد بدء التداول الذكي؟

الإعدادات:
• الرمز: {symbol}
• الوضع: {mode_text}
• نسبة الثقة المطلوبة: {confidence_threshold:.0f}%

⚠️ تأكد من الإعدادات قبل المتابعة!"""
        
        if not messagebox.askyesno("تأكيد بدء التداول", confirm_msg):
            return
            
        self.is_trading = True
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.system_status_label.config(text="🚀 يعمل", fg='#28a745')
        
        # تطبيق الإعدادات
        self.trading_system.demo_mode = self.demo_var.get()
        self.trading_system.set_current_pair(symbol)
        self.trading_system.set_confidence_threshold(confidence_threshold)
        
        self.log_message(f"🚀 بدء التداول الذكي:")
        self.log_message(f"   💰 الرمز: {symbol}")
        self.log_message(f"   🛡️ الوضع: {mode_text}")
        self.log_message(f"   🎯 نسبة الثقة: {confidence_threshold:.0f}%")
        
        # بدء التداول في خيط منفصل
        threading.Thread(target=self.trading_loop, daemon=True).start()
        
    def stop_trading(self):
        """إيقاف التداول"""
        self.is_trading = False
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.system_status_label.config(text="⏹️ متوقف", fg='#ffc107')
        self.log_message("⏹️ تم إيقاف التداول الذكي")
        
    def trading_loop(self):
        """حلقة التداول الذكية"""
        while self.is_trading and self.is_connected:
            try:
                symbol = self.symbol_var.get()
                self.log_message(f"🔍 تحليل {symbol} بالذكاء الاصطناعي...")
                
                # تحليل السوق
                analysis = self.trading_system.analyze_market(symbol)
                
                if 'error' not in analysis:
                    decision = analysis['decision']
                    confidence = analysis['confidence']
                    price = analysis['price']
                    
                    self.log_message(f"📊 نتيجة التحليل:")
                    self.log_message(f"   🎯 القرار: {decision}")
                    self.log_message(f"   📈 الثقة: {confidence:.1f}%")
                    self.log_message(f"   💰 السعر: ${price:,.2f}")
                    
                    # محاولة تنفيذ التداول
                    if self.trading_system.execute_trade_mt5(analysis):
                        self.log_message("✅ تم تنفيذ الصفقة بنجاح!")
                        self.log_message("🧠 النظام يتعلم من هذه الصفقة...")
                    else:
                        threshold = self.trading_system.confidence_threshold
                        self.log_message(f"⏳ لا توجد فرصة دخول (الثقة: {confidence:.1f}% < المطلوب: {threshold:.0f}%)")
                else:
                    self.log_message(f"❌ خطأ في التحليل: {analysis['error']}")
                
                # تحديث معلومات الحساب
                self.root.after(0, self.update_account_info)
                
                # انتظار قبل التحليل التالي (30 ثانية)
                for i in range(30):
                    if not self.is_trading:
                        break
                    time.sleep(1)
                
            except Exception as e:
                self.log_message(f"❌ خطأ في التداول: {str(e)}")
                time.sleep(10)
                
    def update_data(self):
        """تحديث البيانات دورياً"""
        if self.is_connected:
            self.update_account_info()
            
        # جدولة التحديث التالي (كل 5 ثوانٍ)
        self.root.after(5000, self.update_data)
        
    def run(self):
        """تشغيل التطبيق"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log_message("⏹️ تم إيقاف التطبيق")
        finally:
            if self.trading_system:
                self.trading_system.disconnect()
                
    def on_closing(self):
        """عند إغلاق التطبيق"""
        if self.is_trading:
            if messagebox.askokcancel("تأكيد الإغلاق", "النظام يعمل حالياً. هل تريد إيقافه والخروج؟"):
                self.stop_trading()
                time.sleep(1)
                self.disconnect_system()
                self.root.destroy()
        else:
            self.disconnect_system()
            self.root.destroy()

if __name__ == "__main__":
    try:
        app = SimpleMT5GUI()
        app.run()
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")
