@echo off
echo ========================================
echo   إضافة أدوية تجريبية لاختبار المتجر
echo   Add Test Medicines for Store Testing
echo ========================================
echo.

echo 🧪 إضافة أدوية تجريبية لاختبار متجر الأدوية...
echo.

echo الأدوية التي سيتم إضافتها:
echo ✅ باراسيتامول 500 مجم
echo ✅ أموكسيسيلين 250 مجم  
echo ✅ إيبوبروفين 400 مجم
echo ✅ أسبرين 100 مجم
echo ✅ أوميبرازول 20 مجم
echo ✅ سيتريزين 10 مجم
echo ✅ ديكلوفيناك 50 مجم
echo ✅ لوراتادين 10 مجم
echo ✅ سيمفاستاتين 20 مجم
echo ✅ ميتفورمين 500 مجم
echo.

echo 🚀 بدء الإضافة...
echo.

sqlcmd -S NARUTO -E -i add_sample_medicines.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo   ✅ تم إضافة الأدوية التجريبية بنجاح!
    echo ========================================
    echo.
    echo 🎯 الآن يمكنك اختبار متجر الأدوية:
    echo.
    echo 1. شغل برنامج إدارة الصيدلية
    echo 2. سجل دخول كموظف
    echo 3. اضغط على "متجر الصيدلية"
    echo 4. يجب أن ترى 10 أدوية في تبويب "الأدوية المحلية"
    echo 5. اختر أي دواء واضغط "نشر الدواء"
    echo 6. املأ التفاصيل واضغط "نشر"
    echo 7. انتقل لتبويب "أدويتي المنشورة" لرؤية الدواء المنشور
    echo.
    echo 💡 نصائح:
    echo • جميع الأدوية لها تواريخ صلاحية صالحة
    echo • الكميات متنوعة من 25 إلى 200 قطعة
    echo • الأسعار متنوعة من 1.25 إلى 35 جنيه
    echo • يمكنك تعديل البيانات من صفحة "تعديل دواء"
    echo.
) else (
    echo.
    echo ========================================
    echo   ❌ حدث خطأ أثناء إضافة الأدوية!
    echo ========================================
    echo.
    echo تأكد من:
    echo 1. تشغيل SQL Server
    echo 2. وجود قاعدة البيانات UnifiedPharmacy
    echo 3. وجود جدول medic في قاعدة البيانات
    echo 4. صحة اسم الخادم (NARUTO)
    echo 5. وجود صلاحيات الإدارة
    echo.
    echo إذا كان جدول medic غير موجود:
    echo 1. افتح SQL Server Management Studio
    echo 2. اتصل بالخادم NARUTO
    echo 3. افتح قاعدة البيانات UnifiedPharmacy
    echo 4. شغل سكريبت إنشاء الجداول أولاً
    echo.
)

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
