#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار سريع للاتصال والرموز المتوفرة
Quick Connection and Symbols Test
"""

print("🔌 اختبار سريع للاتصال...")

try:
    from mt5_crypto_trading_system import MT5CryptoTradingSystem
    
    # إنشاء النظام
    system = MT5CryptoTradingSystem(demo_mode=True)
    
    print("✅ تم إنشاء النظام")
    
    # محاولة الاتصال
    if system.connect_mt5():
        print("✅ تم الاتصال بنجاح!")
        
        print(f"💰 الرصيد: ${system.account_info.balance:.2f}")
        print(f"📊 الرموز المتوفرة: {len(system.crypto_symbols)}")
        
        if system.crypto_symbols:
            print("📋 الرموز المتوفرة:")
            for i, symbol in enumerate(system.crypto_symbols[:10], 1):
                print(f"   {i}. {symbol}")
                
            # اختبار تحليل
            print(f"\n📈 اختبار تحليل {system.current_symbol}...")
            analysis = system.analyze_crypto_market(system.current_symbol)
            
            if analysis:
                print(f"✅ التحليل نجح!")
                print(f"   🎯 القرار: {analysis['decision']}")
                print(f"   📊 الثقة: {analysis['confidence']:.2%}")
                print(f"   💰 السعر: ${analysis.get('price', 0):.4f}")
            else:
                print("❌ فشل التحليل")
        else:
            print("❌ لا توجد رموز متوفرة")
    else:
        print("❌ فشل الاتصال")
        
except Exception as e:
    print(f"❌ خطأ: {e}")

input("\nاضغط Enter للخروج...")
