-- ========================================
-- إنشاء صيدلية افتراضية لاختبار متجر الأدوية
-- Create Default Pharmacy for Testing Pharmacy Store
-- ========================================

USE UnifiedPharmacy;
GO

PRINT '🏪 إنشاء صيدلية افتراضية لاختبار متجر الأدوية...';
PRINT '';

-- التحقق من وجود جدول pharmacies
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacies')
BEGIN
    PRINT '❌ جدول pharmacies غير موجود! يرجى تشغيل install_pharmacy_store.bat أولاً.';
    RETURN;
END

-- التحقق من وجود صيدلية افتراضية
IF NOT EXISTS (SELECT * FROM pharmacies WHERE id = 1)
BEGIN
    -- إنشاء صيدلية افتراضية
    INSERT INTO pharmacies (pharmacy_name, owner_name, phone, email, address, city, license_number, is_active, subscription_end_date)
    VALUES 
    (N'الصيدلية الافتراضية', N'مدير النظام', '01234567890', '<EMAIL>', 
     N'شارع الرئيسي، المدينة', N'القاهرة', 'LIC-DEFAULT-001', 1, DATEADD(YEAR, 1, GETDATE()));
    
    PRINT '✅ تم إنشاء الصيدلية الافتراضية بنجاح!';
END
ELSE
BEGIN
    PRINT '⚠️ الصيدلية الافتراضية موجودة مسبقاً';
    
    -- تحديث بيانات الصيدلية الافتراضية
    UPDATE pharmacies 
    SET pharmacy_name = N'الصيدلية الافتراضية',
        owner_name = N'مدير النظام',
        phone = '01234567890',
        email = '<EMAIL>',
        address = N'شارع الرئيسي، المدينة',
        city = N'القاهرة',
        license_number = 'LIC-DEFAULT-001',
        is_active = 1,
        subscription_end_date = DATEADD(YEAR, 1, GETDATE())
    WHERE id = 1;
    
    PRINT '✅ تم تحديث بيانات الصيدلية الافتراضية';
END

-- إنشاء صيدلية ثانية للاختبار
IF NOT EXISTS (SELECT * FROM pharmacies WHERE id = 2)
BEGIN
    INSERT INTO pharmacies (pharmacy_name, owner_name, phone, email, address, city, license_number, is_active, subscription_end_date)
    VALUES 
    (N'صيدلية النور', N'أحمد محمد', '01111111111', '<EMAIL>', 
     N'شارع النيل، الجيزة', N'الجيزة', 'LIC-NOUR-002', 1, DATEADD(YEAR, 1, GETDATE()));
    
    PRINT '✅ تم إنشاء صيدلية النور للاختبار';
END

-- إنشاء صيدلية ثالثة للاختبار
IF NOT EXISTS (SELECT * FROM pharmacies WHERE id = 3)
BEGIN
    INSERT INTO pharmacies (pharmacy_name, owner_name, phone, email, address, city, license_number, is_active, subscription_end_date)
    VALUES 
    (N'صيدلية الشفاء', N'فاطمة علي', '01222222222', '<EMAIL>', 
     N'شارع الجامعة، الإسكندرية', N'الإسكندرية', 'LIC-SHIFA-003', 1, DATEADD(YEAR, 1, GETDATE()));
    
    PRINT '✅ تم إنشاء صيدلية الشفاء للاختبار';
END

PRINT '';
PRINT '📋 الصيدليات المسجلة:';
SELECT 
    id,
    pharmacy_name as [اسم الصيدلية],
    owner_name as [اسم المالك],
    phone as [الهاتف],
    city as [المدينة],
    is_active as [نشطة]
FROM pharmacies
ORDER BY id;

PRINT '';
PRINT '🎯 الآن يمكنك اختبار متجر الأدوية:';
PRINT '1. افتح برنامج إدارة الصيدلية';
PRINT '2. اذهب لمتجر الأدوية';
PRINT '3. انشر بعض الأدوية من تبويب "الأدوية المحلية"';
PRINT '4. ستظهر في تبويب "أدويتي المعروضة"';
PRINT '5. يمكن للصيدليات الأخرى رؤيتها في "الأدوية المنشورة"';
PRINT '';
