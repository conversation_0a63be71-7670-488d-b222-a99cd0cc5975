-- ========================================
-- اختبار نظام الإشعارات والمحادثات
-- Testing Notification and Chat System
-- ========================================

USE UnifiedPharmacy;
GO

PRINT 'بدء اختبار نظام الإشعارات والمحادثات...';
PRINT '';

-- ========================================
-- 1. التحقق من وجود الجداول المطلوبة
-- ========================================

PRINT '1. التحقق من وجود الجداول المطلوبة:';
PRINT '==========================================';

-- التحقق من جدول notifications
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'notifications')
    PRINT '✅ جدول notifications موجود'
ELSE
    PRINT '❌ جدول notifications غير موجود'

-- التحقق من جدول pharmacy_messages
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacy_messages')
    PRINT '✅ جدول pharmacy_messages موجود'
ELSE
    PRINT '❌ جدول pharmacy_messages غير موجود'

-- التحقق من جدول pharmacies
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacies')
    PRINT '✅ جدول pharmacies موجود'
ELSE
    PRINT '❌ جدول pharmacies غير موجود'

-- التحقق من جدول published_medicines
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'published_medicines')
    PRINT '✅ جدول published_medicines موجود'
ELSE
    PRINT '❌ جدول published_medicines غير موجود'

PRINT '';

-- ========================================
-- 2. إضافة بيانات تجريبية للاختبار
-- ========================================

PRINT '2. إضافة بيانات تجريبية للاختبار:';
PRINT '=====================================';

-- إضافة صيدليات تجريبية إضافية
IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacyCode = 'TEST001')
BEGIN
    INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, licenseNumber, address, city, region, phone, email, isActive)
    VALUES ('TEST001', 'صيدلية الاختبار الأولى', 'أحمد محمد', 'LIC001', 'شارع الملك فهد', 'الرياض', 'الرياض', '**********', '<EMAIL>', 1);
    PRINT '✅ تم إضافة صيدلية الاختبار الأولى';
END
ELSE
    PRINT '⚠️ صيدلية الاختبار الأولى موجودة مسبقاً';

IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacyCode = 'TEST002')
BEGIN
    INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, licenseNumber, address, city, region, phone, email, isActive)
    VALUES ('TEST002', 'صيدلية الاختبار الثانية', 'فاطمة علي', 'LIC002', 'شارع العليا', 'جدة', 'مكة المكرمة', '**********', '<EMAIL>', 1);
    PRINT '✅ تم إضافة صيدلية الاختبار الثانية';
END
ELSE
    PRINT '⚠️ صيدلية الاختبار الثانية موجودة مسبقاً';

-- إضافة أدوية منشورة تجريبية
DECLARE @pharmacy1Id INT, @pharmacy2Id INT;
SELECT @pharmacy1Id = id FROM pharmacies WHERE pharmacyCode = 'TEST001';
SELECT @pharmacy2Id = id FROM pharmacies WHERE pharmacyCode = 'TEST002';

IF @pharmacy1Id IS NOT NULL AND NOT EXISTS (SELECT * FROM published_medicines WHERE medicine_name = 'باراسيتامول 500 مجم' AND pharmacy_id = @pharmacy1Id)
BEGIN
    INSERT INTO published_medicines (pharmacy_id, medicine_name, quantity_available, expiry_date, price_per_unit, description, published_date, is_available)
    VALUES (@pharmacy1Id, 'باراسيتامول 500 مجم', 100, DATEADD(MONTH, 6, GETDATE()), 5.50, 'مسكن للألم وخافض للحرارة', GETDATE(), 1);
    PRINT '✅ تم إضافة دواء باراسيتامول للصيدلية الأولى';
END

IF @pharmacy2Id IS NOT NULL AND NOT EXISTS (SELECT * FROM published_medicines WHERE medicine_name = 'أموكسيسيلين 250 مجم' AND pharmacy_id = @pharmacy2Id)
BEGIN
    INSERT INTO published_medicines (pharmacy_id, medicine_name, quantity_available, expiry_date, price_per_unit, description, published_date, is_available)
    VALUES (@pharmacy2Id, 'أموكسيسيلين 250 مجم', 50, DATEADD(MONTH, 8, GETDATE()), 12.75, 'مضاد حيوي واسع المجال', GETDATE(), 1);
    PRINT '✅ تم إضافة دواء أموكسيسيلين للصيدلية الثانية';
END

PRINT '';

-- ========================================
-- 3. إنشاء إشعارات تجريبية
-- ========================================

PRINT '3. إنشاء إشعارات تجريبية:';
PRINT '==========================';

-- إشعار طلب شراء
IF @pharmacy1Id IS NOT NULL
BEGIN
    INSERT INTO notifications (pharmacy_id, notification_type, title, content, is_read, created_date)
    VALUES (@pharmacy1Id, 'purchase_request', 'طلب شراء جديد', 'تلقيت طلب شراء من صيدلية الاختبار الثانية للدواء: باراسيتامول 500 مجم', 0, GETDATE());
    PRINT '✅ تم إنشاء إشعار طلب شراء للصيدلية الأولى';
END

-- إشعار رسالة
IF @pharmacy2Id IS NOT NULL
BEGIN
    INSERT INTO notifications (pharmacy_id, notification_type, title, content, is_read, created_date)
    VALUES (@pharmacy2Id, 'message', 'رسالة جديدة من صيدلية الاختبار الأولى', 'مرحباً، هل يمكنني الحصول على معلومات إضافية عن الأموكسيسيلين؟', 0, GETDATE());
    PRINT '✅ تم إنشاء إشعار رسالة للصيدلية الثانية';
END

-- إشعار نظام
IF @pharmacy1Id IS NOT NULL
BEGIN
    INSERT INTO notifications (pharmacy_id, notification_type, title, content, is_read, created_date)
    VALUES (@pharmacy1Id, 'system', 'تحديث النظام', 'تم تحديث نظام إدارة الصيدلية إلى الإصدار الجديد', 0, GETDATE());
    PRINT '✅ تم إنشاء إشعار نظام للصيدلية الأولى';
END

PRINT '';

-- ========================================
-- 4. إنشاء رسائل تجريبية
-- ========================================

PRINT '4. إنشاء رسائل تجريبية:';
PRINT '=======================';

IF @pharmacy1Id IS NOT NULL AND @pharmacy2Id IS NOT NULL
BEGIN
    -- رسالة من الصيدلية الأولى للثانية
    INSERT INTO pharmacy_messages (senderPharmacyId, receiverPharmacyId, senderUserId, messageContent, messageType, sentAt, isRead)
    VALUES (@pharmacy1Id, @pharmacy2Id, 1, 'مرحباً، أود الاستفسار عن توفر أدوية المضادات الحيوية لديكم', 'General', GETDATE(), 0);

    -- رد من الصيدلية الثانية
    INSERT INTO pharmacy_messages (senderPharmacyId, receiverPharmacyId, senderUserId, messageContent, messageType, sentAt, isRead)
    VALUES (@pharmacy2Id, @pharmacy1Id, 1, 'أهلاً وسهلاً، نعم لدينا مجموعة متنوعة من المضادات الحيوية. يمكنك مراجعة قائمة الأدوية المنشورة', 'General', GETDATE(), 0);

    PRINT '✅ تم إنشاء رسائل تجريبية بين الصيدليات';
END

PRINT '';

-- ========================================
-- 5. عرض النتائج للاختبار
-- ========================================

PRINT '5. عرض النتائج للاختبار:';
PRINT '=========================';

PRINT 'الصيدليات المسجلة:';
PRINT '==================';
SELECT 
    id,
    pharmacyCode as 'كود الصيدلية',
    pharmacyName as 'اسم الصيدلية',
    ownerName as 'اسم المالك',
    city as 'المدينة',
    isActive as 'نشطة'
FROM pharmacies
WHERE pharmacyCode LIKE 'TEST%' OR pharmacyCode = 'MAIN001';

PRINT '';
PRINT 'الإشعارات غير المقروءة:';
PRINT '========================';
SELECT 
    n.id,
    p.pharmacyName as 'الصيدلية',
    n.notification_type as 'نوع الإشعار',
    n.title as 'العنوان',
    n.content as 'المحتوى',
    n.created_date as 'تاريخ الإنشاء'
FROM notifications n
JOIN pharmacies p ON n.pharmacy_id = p.id
WHERE n.is_read = 0
ORDER BY n.created_date DESC;

PRINT '';
PRINT 'الرسائل بين الصيدليات:';
PRINT '=====================';
SELECT
    pm.id,
    p1.pharmacyName as 'المرسل',
    p2.pharmacyName as 'المستقبل',
    pm.messageType as 'نوع الرسالة',
    pm.messageContent as 'المحتوى',
    pm.sentAt as 'تاريخ الإرسال',
    CASE WHEN pm.isRead = 1 THEN 'مقروءة' ELSE 'غير مقروءة' END as 'الحالة'
FROM pharmacy_messages pm
JOIN pharmacies p1 ON pm.senderPharmacyId = p1.id
JOIN pharmacies p2 ON pm.receiverPharmacyId = p2.id
ORDER BY pm.sentAt DESC;

PRINT '';
PRINT 'الأدوية المنشورة:';
PRINT '================';
SELECT 
    pm.id,
    p.pharmacyName as 'الصيدلية',
    pm.medicine_name as 'اسم الدواء',
    pm.quantity_available as 'الكمية المتاحة',
    pm.price_per_unit as 'السعر',
    pm.expiry_date as 'تاريخ الانتهاء',
    pm.published_date as 'تاريخ النشر'
FROM published_medicines pm
JOIN pharmacies p ON pm.pharmacy_id = p.id
WHERE pm.is_available = 1
ORDER BY pm.published_date DESC;

PRINT '';
PRINT '✅ تم إكمال اختبار نظام الإشعارات والمحادثات بنجاح!';
PRINT '';
PRINT 'يمكنك الآن اختبار النظام من خلال:';
PRINT '1. تسجيل الدخول بكود الصيدلية TEST001 أو TEST002';
PRINT '2. فتح صفحة متجر الأدوية';
PRINT '3. النقر على زر الإشعارات لرؤية الإشعارات الجديدة';
PRINT '4. النقر على زر "محادثة الصيدلية" بجانب أي دواء منشور';
PRINT '5. اختبار إرسال واستقبال الرسائل';
