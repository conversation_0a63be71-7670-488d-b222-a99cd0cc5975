#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any
import numpy as np

class AdvancedLearningSystem:
    """نظام التعلم المتقدم للتداول"""
    
    def __init__(self, data_file="trading_memory.json"):
        self.data_file = data_file
        self.memory = self.load_memory()
        self.current_session_trades = []
        self.learned_patterns = {}
        self.mistake_patterns = {}
        self.success_patterns = {}
        
    def load_memory(self) -> Dict:
        """تحميل ذاكرة التعلم من الملف"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    print(f"🧠 تم تحميل {len(data.get('trades', []))} صفقة من الذاكرة")
                    return data
        except Exception as e:
            print(f"⚠️ خطأ في تحميل الذاكرة: {e}")
        
        return {
            'trades': [],
            'patterns': {},
            'mistakes': {},
            'successes': {},
            'strategies': {},
            'confidence_adjustments': {},
            'last_updated': datetime.now().isoformat()
        }
    
    def save_memory(self):
        """حفظ ذاكرة التعلم في الملف"""
        try:
            # فحص ما إذا كانت الذاكرة سلبية جداً
            self.check_and_reset_negative_memory()

            self.memory['last_updated'] = datetime.now().isoformat()
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.memory, f, ensure_ascii=False, indent=2)
            print(f"💾 تم حفظ الذاكرة: {len(self.memory['trades'])} صفقة")
        except Exception as e:
            print(f"❌ خطأ في حفظ الذاكرة: {e}")

    def check_and_reset_negative_memory(self):
        """فحص وإعادة تعيين الذاكرة السلبية جداً"""
        if len(self.memory['trades']) >= 50:
            recent_trades = self.memory['trades'][-50:]
            profitable_trades = len([t for t in recent_trades if t.get('profit', 0) > 0])
            success_rate = (profitable_trades / len(recent_trades)) * 100

            if success_rate < 20:  # أقل من 20% نجاح في آخر 50 صفقة
                print(f"🔄 إعادة تعيين الذاكرة السلبية - معدل النجاح: {success_rate:.1f}%")

                # الاحتفاظ بالصفقات الناجحة فقط
                successful_trades = [t for t in recent_trades if t.get('profit', 0) > 0]

                # إعادة تعيين الذاكرة مع الاحتفاظ بالدروس الإيجابية
                self.memory = {
                    'trades': successful_trades[-20:] if len(successful_trades) > 20 else successful_trades,
                    'patterns': {},
                    'mistakes': {},  # مسح الأخطاء
                    'successes': {},  # إعادة بناء النجاحات
                    'strategies': {},
                    'confidence_adjustments': {},
                    'last_updated': datetime.now().isoformat()
                }

                print("✅ تم إعادة تعيين الذاكرة مع الاحتفاظ بالدروس الإيجابية")
    
    def record_trade(self, trade_data: Dict):
        """تسجيل صفقة جديدة في الذاكرة"""
        trade_record = {
            'timestamp': datetime.now().isoformat(),
            'symbol': trade_data.get('symbol'),
            'decision': trade_data.get('decision'),
            'confidence': trade_data.get('confidence'),
            'entry_price': trade_data.get('entry_price'),
            'exit_price': trade_data.get('exit_price'),
            'profit': trade_data.get('profit', 0),
            'profit_pct': trade_data.get('profit_pct', 0),
            'exit_reason': trade_data.get('exit_reason'),
            'market_conditions': trade_data.get('market_conditions', {}),
            'indicators': trade_data.get('indicators', {}),
            'session_id': trade_data.get('session_id', 'unknown')
        }
        
        self.memory['trades'].append(trade_record)
        self.current_session_trades.append(trade_record)
        
        # تحليل الصفقة فوراً
        self.analyze_trade(trade_record)
        
        # حفظ كل 5 صفقات
        if len(self.current_session_trades) % 5 == 0:
            self.save_memory()
    
    def analyze_trade(self, trade: Dict):
        """تحليل الصفقة وتعلم الأنماط"""
        try:
            # تحديد نوع النتيجة
            is_success = trade['profit'] > 0
            confidence_level = self.get_confidence_category(trade['confidence'])
            
            # إنشاء مفتاح النمط
            pattern_key = f"{trade['symbol']}_{trade['decision']}_{confidence_level}"
            
            if is_success:
                self.record_success_pattern(pattern_key, trade)
            else:
                self.record_mistake_pattern(pattern_key, trade)
                
            # تحليل ظروف السوق
            self.analyze_market_conditions(trade, is_success)
            
        except Exception as e:
            print(f"❌ خطأ في تحليل الصفقة: {e}")
    
    def record_success_pattern(self, pattern_key: str, trade: Dict):
        """تسجيل نمط ناجح"""
        if pattern_key not in self.memory['successes']:
            self.memory['successes'][pattern_key] = {
                'count': 0,
                'total_profit': 0,
                'avg_profit': 0,
                'conditions': [],
                'indicators': []
            }
        
        success = self.memory['successes'][pattern_key]
        success['count'] += 1
        success['total_profit'] += trade['profit']
        success['avg_profit'] = success['total_profit'] / success['count']
        
        # حفظ الظروف الناجحة
        if trade['market_conditions']:
            success['conditions'].append(trade['market_conditions'])
        
        if trade['indicators']:
            success['indicators'].append(trade['indicators'])
    
    def record_mistake_pattern(self, pattern_key: str, trade: Dict):
        """تسجيل نمط خاطئ"""
        if pattern_key not in self.memory['mistakes']:
            self.memory['mistakes'][pattern_key] = {
                'count': 0,
                'total_loss': 0,
                'avg_loss': 0,
                'conditions': [],
                'indicators': [],
                'warnings': []
            }
        
        mistake = self.memory['mistakes'][pattern_key]
        mistake['count'] += 1
        mistake['total_loss'] += abs(trade['profit'])
        mistake['avg_loss'] = mistake['total_loss'] / mistake['count']
        
        # حفظ الظروف الخاطئة
        if trade['market_conditions']:
            mistake['conditions'].append(trade['market_conditions'])
        
        if trade['indicators']:
            mistake['indicators'].append(trade['indicators'])
        
        # إضافة تحذير
        warning = f"تجنب {trade['decision']} على {trade['symbol']} بثقة {trade['confidence']:.1f}%"
        if warning not in mistake['warnings']:
            mistake['warnings'].append(warning)
    
    def should_avoid_trade(self, symbol: str, decision: str, confidence: float) -> tuple:
        """فحص ما إذا كان يجب تجنب الصفقة بناءً على معايير صارمة ومتوازنة"""
        confidence_level = self.get_confidence_category(confidence)
        pattern_key = f"{symbol}_{decision}_{confidence_level}"

        total_trades = 0
        successful_trades = 0
        total_loss = 0

        # حساب الإحصائيات الشاملة
        if pattern_key in self.memory['successes']:
            successful_trades = self.memory['successes'][pattern_key]['count']
            total_trades += successful_trades

        if pattern_key in self.memory['mistakes']:
            mistake_data = self.memory['mistakes'][pattern_key]
            total_trades += mistake_data['count']
            total_loss = mistake_data['avg_loss']

        # نحتاج عينة كبيرة قبل اتخاذ قرار التجنب
        if total_trades >= 10:  # عينة كبيرة
            success_rate = (successful_trades / total_trades) * 100

            # شروط التجنب الصارمة جداً
            if success_rate < 15 and total_loss > 150:  # فشل شديد مع خسائر كبيرة
                return True, f"⚠️ نمط فاشل جداً: {success_rate:.1f}% نجاح، خسارة ${total_loss:.2f}"
            elif success_rate < 10:  # فشل شديد جداً
                return True, f"⚠️ نمط فاشل: {success_rate:.1f}% نجاح من {total_trades} صفقات"

        return False, ""
    
    def get_confidence_adjustment(self, symbol: str, decision: str, base_confidence: float) -> float:
        """تعديل مستوى الثقة بناء على التعلم المتوازن"""
        confidence_level = self.get_confidence_category(base_confidence)
        pattern_key = f"{symbol}_{decision}_{confidence_level}"

        adjustment = 0
        total_trades = 0
        successful_trades = 0

        # حساب معدل النجاح الإجمالي للنمط
        if pattern_key in self.memory['successes']:
            successful_trades = self.memory['successes'][pattern_key]['count']
            total_trades += successful_trades

        if pattern_key in self.memory['mistakes']:
            total_trades += self.memory['mistakes'][pattern_key]['count']

        # إذا لم يكن لدينا بيانات كافية، لا نعدل الثقة
        if total_trades < 5:
            return base_confidence

        # حساب معدل النجاح
        success_rate = (successful_trades / total_trades) * 100 if total_trades > 0 else 0

        # تعديل الثقة بناءً على معدل النجاح
        if success_rate >= 70:
            # نمط ناجح جداً - زيادة الثقة
            adjustment = min(10, (success_rate - 70) / 3)  # حد أقصى +10
        elif success_rate >= 50:
            # نمط متوسط - تعديل طفيف
            adjustment = (success_rate - 50) / 10  # من 0 إلى +2
        elif success_rate >= 30:
            # نمط ضعيف - تقليل طفيف
            adjustment = -((50 - success_rate) / 10)  # من 0 إلى -2
        else:
            # نمط فاشل - تقليل أكبر ولكن ليس مدمر
            adjustment = -min(8, (30 - success_rate) / 5)  # حد أقصى -8

        # تطبيق التعديل مع حدود آمنة
        adjusted_confidence = max(25, min(95, base_confidence + adjustment))  # حد أدنى 25%

        if abs(adjustment) > 0.5:
            print(f"🧠 تعديل الثقة: {base_confidence:.1f}% → {adjusted_confidence:.1f}% ({adjustment:+.1f}) | معدل النجاح: {success_rate:.1f}%")

        return adjusted_confidence

    def reset_confidence_if_too_low(self, current_confidence: float) -> float:
        """إعادة تعيين الثقة إذا انخفضت كثيراً"""
        if current_confidence < 25:
            print(f"🔄 إعادة تعيين الثقة: {current_confidence:.1f}% → 40.0% (الثقة منخفضة جداً)")
            return 40.0  # إعادة تعيين لمستوى معقول
        return current_confidence

    def should_enter_learning_mode(self) -> bool:
        """تحديد ما إذا كان النظام يحتاج لوضع التعلم المكثف"""
        total_recent_trades = len([t for t in self.memory['trades'][-20:] if t.get('profit', 0) < 0])
        if total_recent_trades >= 15:  # 15 خسارة من آخر 20 صفقة
            print("🧠 دخول وضع التعلم المكثف - تحليل أعمق للأنماط")
            return True
        return False

    def get_adaptive_confidence_boost(self, symbol: str) -> float:
        """زيادة تكيفية للثقة بناءً على أداء الرمز"""
        symbol_trades = [t for t in self.memory['trades'][-50:] if t['symbol'] == symbol]
        if len(symbol_trades) >= 10:
            profitable_trades = len([t for t in symbol_trades if t.get('profit', 0) > 0])
            success_rate = (profitable_trades / len(symbol_trades)) * 100

            if success_rate >= 60:
                boost = min(15, (success_rate - 60) / 2)  # حد أقصى +15
                print(f"🚀 زيادة ثقة تكيفية لـ {symbol}: +{boost:.1f}% (معدل نجاح {success_rate:.1f}%)")
                return boost
            elif success_rate <= 30:
                penalty = -min(10, (30 - success_rate) / 3)  # حد أقصى -10
                print(f"⚠️ تقليل ثقة تكيفي لـ {symbol}: {penalty:.1f}% (معدل نجاح {success_rate:.1f}%)")
                return penalty

        return 0

    def get_confidence_category(self, confidence: float) -> str:
        """تصنيف مستوى الثقة"""
        if confidence >= 80:
            return "very_high"
        elif confidence >= 70:
            return "high"
        elif confidence >= 50:
            return "medium"
        else:
            return "low"
    
    def analyze_market_conditions(self, trade: Dict, is_success: bool):
        """تحليل ظروف السوق"""
        try:
            conditions = trade.get('market_conditions', {})
            if not conditions:
                return
            
            volatility = conditions.get('volatility', 0)
            price_change = conditions.get('price_change', 0)
            
            # تصنيف ظروف السوق
            if volatility > 0.02:
                market_type = "volatile"
            else:
                market_type = "stable"
            
            # تسجيل الأداء حسب نوع السوق
            market_key = f"{trade['symbol']}_{market_type}"
            
            if market_key not in self.memory['patterns']:
                self.memory['patterns'][market_key] = {
                    'total_trades': 0,
                    'successful_trades': 0,
                    'success_rate': 0
                }
            
            pattern = self.memory['patterns'][market_key]
            pattern['total_trades'] += 1
            
            if is_success:
                pattern['successful_trades'] += 1
            
            pattern['success_rate'] = (pattern['successful_trades'] / pattern['total_trades']) * 100
            
        except Exception as e:
            print(f"❌ خطأ في تحليل ظروف السوق: {e}")
    
    def get_learning_insights(self) -> List[str]:
        """الحصول على رؤى التعلم"""
        insights = []
        
        try:
            # تحليل الأخطاء الشائعة
            if self.memory['mistakes']:
                most_common_mistakes = sorted(
                    self.memory['mistakes'].items(),
                    key=lambda x: x[1]['count'],
                    reverse=True
                )[:3]
                
                for pattern, data in most_common_mistakes:
                    if data['count'] >= 2:
                        insights.append(f"⚠️ تجنب النمط: {pattern} ({data['count']} أخطاء)")
            
            # تحليل النجاحات
            if self.memory['successes']:
                best_patterns = sorted(
                    self.memory['successes'].items(),
                    key=lambda x: x[1]['avg_profit'],
                    reverse=True
                )[:3]
                
                for pattern, data in best_patterns:
                    if data['count'] >= 2:
                        insights.append(f"✅ نمط ناجح: {pattern} (متوسط ربح ${data['avg_profit']:.2f})")
            
            # تحليل ظروف السوق
            if self.memory['patterns']:
                for market_key, data in self.memory['patterns'].items():
                    if data['total_trades'] >= 5:
                        if data['success_rate'] > 70:
                            insights.append(f"📈 أداء ممتاز في {market_key}: {data['success_rate']:.1f}%")
                        elif data['success_rate'] < 40:
                            insights.append(f"📉 أداء ضعيف في {market_key}: {data['success_rate']:.1f}%")
            
        except Exception as e:
            print(f"❌ خطأ في تحليل الرؤى: {e}")
        
        return insights
    
    def generate_strategy_recommendations(self) -> List[str]:
        """توليد توصيات استراتيجية"""
        recommendations = []
        
        try:
            total_trades = len(self.memory['trades'])
            if total_trades < 10:
                return ["📊 نحتاج المزيد من البيانات لتوليد توصيات (أقل من 10 صفقات)"]
            
            # حساب معدل النجاح الإجمالي
            successful_trades = len([t for t in self.memory['trades'] if t['profit'] > 0])
            overall_success_rate = (successful_trades / total_trades) * 100
            
            if overall_success_rate > 60:
                recommendations.append(f"🎯 الاستراتيجية الحالية ناجحة: {overall_success_rate:.1f}% نجاح")
            elif overall_success_rate < 40:
                recommendations.append(f"⚠️ الاستراتيجية تحتاج تحسين: {overall_success_rate:.1f}% نجاح فقط")
            
            # تحليل مستويات الثقة
            confidence_analysis = {}
            for trade in self.memory['trades']:
                conf_cat = self.get_confidence_category(trade['confidence'])
                if conf_cat not in confidence_analysis:
                    confidence_analysis[conf_cat] = {'total': 0, 'successful': 0}
                
                confidence_analysis[conf_cat]['total'] += 1
                if trade['profit'] > 0:
                    confidence_analysis[conf_cat]['successful'] += 1
            
            # توصيات مستوى الثقة
            for conf_level, data in confidence_analysis.items():
                if data['total'] >= 5:
                    success_rate = (data['successful'] / data['total']) * 100
                    if success_rate > 70:
                        recommendations.append(f"✅ ركز على صفقات {conf_level}: {success_rate:.1f}% نجاح")
                    elif success_rate < 30:
                        recommendations.append(f"❌ تجنب صفقات {conf_level}: {success_rate:.1f}% نجاح فقط")
            
        except Exception as e:
            print(f"❌ خطأ في توليد التوصيات: {e}")
        
        return recommendations
    
    def reset_session(self):
        """إعادة تعيين جلسة التداول الحالية"""
        self.current_session_trades = []
        print("🔄 تم إعادة تعيين جلسة التداول")
    
    def get_memory_stats(self) -> Dict:
        """إحصائيات الذاكرة"""
        return {
            'total_trades': len(self.memory['trades']),
            'success_patterns': len(self.memory['successes']),
            'mistake_patterns': len(self.memory['mistakes']),
            'market_patterns': len(self.memory['patterns']),
            'current_session_trades': len(self.current_session_trades)
        }
