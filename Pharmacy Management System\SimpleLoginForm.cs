using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace Pharmacy_Management_System
{
    public partial class SimpleLoginForm : Form
    {
        private UnifiedPharmacyFunction unifiedDb;
        private Panel mainPanel;
        private Panel loginPanel;
        private Label titleLabel;
        private TextBox usernameTextBox;
        private TextBox passwordTextBox;
        private Button loginButton;
        private Button exitButton;
        private Label statusLabel;
        private Label usernameLabel;
        private Label passwordLabel;

        public SimpleLoginForm()
        {
            InitializeComponent();
            unifiedDb = new UnifiedPharmacyFunction();
            SetupForm();
        }



        private void SetupForm()
        {
            // Main panel
            mainPanel = new Panel
            {
                Size = new Size(800, 600),
                Location = new Point(0, 0),
                BackColor = Color.FromArgb(45, 45, 48)
            };
            this.Controls.Add(mainPanel);

            // Login panel
            loginPanel = new Panel
            {
                Size = new Size(400, 450),
                Location = new Point(200, 75),
                BackColor = Color.FromArgb(62, 62, 66),
                BorderStyle = BorderStyle.FixedSingle
            };
            mainPanel.Controls.Add(loginPanel);

            // Title
            titleLabel = new Label
            {
                Text = "نظام إدارة الصيدلية",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(300, 40),
                Location = new Point(50, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };
            loginPanel.Controls.Add(titleLabel);

            // Username label
            usernameLabel = new Label
            {
                Text = "اسم المستخدم:",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                Size = new Size(100, 25),
                Location = new Point(50, 100),
                TextAlign = ContentAlignment.MiddleLeft
            };
            loginPanel.Controls.Add(usernameLabel);

            // Username textbox
            usernameTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 12),
                Size = new Size(300, 30),
                Location = new Point(50, 130),
                BackColor = Color.FromArgb(69, 73, 74),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Text = "admin" // Default for testing
            };
            loginPanel.Controls.Add(usernameTextBox);

            // Password label
            passwordLabel = new Label
            {
                Text = "كلمة المرور:",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                Size = new Size(100, 25),
                Location = new Point(50, 180),
                TextAlign = ContentAlignment.MiddleLeft
            };
            loginPanel.Controls.Add(passwordLabel);

            // Password textbox
            passwordTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 12),
                Size = new Size(300, 30),
                Location = new Point(50, 210),
                BackColor = Color.FromArgb(69, 73, 74),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                UseSystemPasswordChar = true,
                Text = "admin123" // Default for testing
            };
            loginPanel.Controls.Add(passwordTextBox);

            // Login button
            loginButton = new Button
            {
                Text = "تسجيل الدخول",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Size = new Size(300, 40),
                Location = new Point(50, 270),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            loginButton.FlatAppearance.BorderSize = 0;
            loginButton.Click += LoginButton_Click;
            loginPanel.Controls.Add(loginButton);

            // Exit button
            exitButton = new Button
            {
                Text = "خروج",
                Font = new Font("Segoe UI", 10),
                Size = new Size(100, 30),
                Location = new Point(250, 330),
                BackColor = Color.FromArgb(192, 57, 43),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            exitButton.FlatAppearance.BorderSize = 0;
            exitButton.Click += (s, e) => Application.Exit();
            loginPanel.Controls.Add(exitButton);

            // Status label
            statusLabel = new Label
            {
                Text = "أدخل بيانات تسجيل الدخول",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.LightGray,
                Size = new Size(300, 25),
                Location = new Point(50, 380),
                TextAlign = ContentAlignment.MiddleCenter
            };
            loginPanel.Controls.Add(statusLabel);

            // Add Enter key support
            usernameTextBox.KeyDown += (s, e) => { if (e.KeyCode == Keys.Enter) passwordTextBox.Focus(); };
            passwordTextBox.KeyDown += (s, e) => { if (e.KeyCode == Keys.Enter) LoginButton_Click(null, null); };
        }

        private void LoginButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(usernameTextBox.Text) || string.IsNullOrWhiteSpace(passwordTextBox.Text))
                {
                    statusLabel.Text = "يرجى إدخال اسم المستخدم وكلمة المرور";
                    statusLabel.ForeColor = Color.Red;
                    return;
                }

                statusLabel.Text = "جاري التحقق من البيانات...";
                statusLabel.ForeColor = Color.Yellow;
                Application.DoEvents();

                // محاولة تسجيل الدخول
                DataSet loginResult = unifiedDb.ValidateLogin(usernameTextBox.Text, passwordTextBox.Text);

                if (loginResult.Tables.Count > 0 && loginResult.Tables[0].Rows.Count > 0)
                {
                    DataRow userRow = loginResult.Tables[0].Rows[0];

                    statusLabel.Text = "تم تسجيل الدخول بنجاح!";
                    statusLabel.ForeColor = Color.Green;

                    // تحديث SessionManager مع معلومات الجلسة
                    SessionManager.Login(
                        Convert.ToInt32(userRow["id"]),
                        userRow["username"].ToString(),
                        userRow["name"].ToString(),
                        userRow["userRole"].ToString(),
                        Convert.ToInt32(userRow["pharmacyId"]),
                        userRow["pharmacyName"].ToString(),
                        userRow["pharmacyCode"].ToString()
                    );

                    // تسجيل جلسة في قاعدة البيانات
                    unifiedDb.RecordLoginSession(
                        Convert.ToInt32(userRow["pharmacyId"]),
                        Convert.ToInt32(userRow["id"]),
                        userRow["username"].ToString(),
                        userRow["name"].ToString()
                    );

                    // فتح الواجهة المناسبة
                    string role = userRow["userRole"].ToString();
                    if (role == "Administrator")
                    {
                        Adminstrator adminForm = new Adminstrator(userRow["name"].ToString());
                        adminForm.Show();
                    }
                    else
                    {
                        Pharmacist pharmacistForm = new Pharmacist(userRow["name"].ToString(), userRow["userRole"].ToString());
                        pharmacistForm.Show();
                    }

                    this.Hide();
                }
                else
                {
                    statusLabel.Text = "اسم المستخدم أو كلمة المرور غير صحيحة";
                    statusLabel.ForeColor = Color.Red;
                    passwordTextBox.Clear();
                    passwordTextBox.Focus();
                }
            }
            catch (Exception ex)
            {
                statusLabel.Text = $"خطأ في تسجيل الدخول: {ex.Message}";
                statusLabel.ForeColor = Color.Red;
            }
        }

        private void SimpleLoginForm_Load(object sender, EventArgs e)
        {

        }
    }
}
