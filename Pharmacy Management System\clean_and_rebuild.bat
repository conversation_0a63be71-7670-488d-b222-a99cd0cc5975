@echo off
echo تنظيف وإعادة بناء المشروع...

REM تنظيف المجلدات القديمة
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"

REM تنظيف ملفات Visual Studio المؤقتة
if exist "*.suo" del /q "*.suo"
if exist ".vs" rmdir /s /q ".vs"

echo تم تنظيف المجلدات القديمة.

REM محاولة البناء باستخدام MSBuild
echo البحث عن MSBuild...

if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    echo تم العثور على MSBuild 2022 Community
    "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" "Pharmacy Management System.csproj" /p:Configuration=Debug /p:Platform="Any CPU" /t:Rebuild /verbosity:minimal
    goto :check_result
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo تم العثور على MSBuild 2022 Professional
    "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" "Pharmacy Management System.csproj" /p:Configuration=Debug /p:Platform="Any CPU" /t:Rebuild /verbosity:minimal
    goto :check_result
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    echo تم العثور على MSBuild 2019 Community
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" "Pharmacy Management System.csproj" /p:Configuration=Debug /p:Platform="Any CPU" /t:Rebuild /verbosity:minimal
    goto :check_result
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo تم العثور على MSBuild 2019 Professional
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" "Pharmacy Management System.csproj" /p:Configuration=Debug /p:Platform="Any CPU" /t:Rebuild /verbosity:minimal
    goto :check_result
)

echo لم يتم العثور على MSBuild. يرجى إعادة بناء المشروع من Visual Studio.
goto :end

:check_result
if %ERRORLEVEL% EQU 0 (
    echo تم البناء بنجاح!
    echo يمكنك الآن تشغيل البرنامج من Visual Studio.
) else (
    echo فشل في البناء. يرجى التحقق من الأخطاء أعلاه.
)

:end
echo انتهى.
pause
