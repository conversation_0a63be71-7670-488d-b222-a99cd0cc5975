# تعليمات إنشاء جدول إعدادات الطباعة 🗄️

## 📋 الخطوات المطلوبة:

### 1. 🔧 فتح SQL Server Management Studio
- افتح برنامج **SQL Server Management Studio**
- اتصل بالخادم **NARUTO**

### 2. 📂 اختيار قاعدة البيانات
- اختر قاعدة البيانات **pharmacy**
- انقر بالزر الأيمن واختر **New Query**

### 3. 📝 تشغيل السكريبت
- انسخ محتوى الملف `create_print_settings_table.sql`
- الصق المحتوى في نافذة الاستعلام الجديدة
- اضغط **F5** أو **Execute** لتشغيل السكريبت

### 4. ✅ التحقق من النجاح
يجب أن ترى الرسائل التالية:
```
تم إنشاء جدول print_settings بنجاح
عدد السجلات المدرجة: 6
```

### 5. 🔍 التحقق من الجدول
```sql
SELECT * FROM print_settings;
```

## 📊 البيانات المتوقعة:
سيتم إنشاء 6 سجلات افتراضية:
- عام
- مبيعات الأدوية  
- تقرير المبيعات
- جلسات الموظفين
- جرد الأدوية
- صلاحية الأدوية

## 🚨 ملاحظات مهمة:
1. **تأكد من اسم الخادم**: يجب أن يكون **NARUTO**
2. **تأكد من اسم قاعدة البيانات**: يجب أن تكون **pharmacy**
3. **إذا كان الجدول موجوداً**: سيتم حذفه وإعادة إنشاؤه
4. **بعد تشغيل السكريبت**: أعد تشغيل التطبيق لتفعيل الإعدادات

## 🎯 النتيجة المتوقعة:
بعد تشغيل السكريبت بنجاح، ستعمل صفحة تصميم الطباعة بشكل صحيح وستطبق الإعدادات على جميع التقارير.
