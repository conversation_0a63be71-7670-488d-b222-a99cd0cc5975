-- ========================================
-- الإجراءات المخزنة لمتجر الأدوية
-- Stored Procedures for Pharmacy Store
-- ========================================

USE PharmacyNetworkOnline;
GO

-- ========================================
-- 1. إجراء تسجيل صيدلية جديدة
-- Register New Pharmacy Procedure
-- ========================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_RegisterPharmacy')
    DROP PROCEDURE sp_RegisterPharmacy;
GO

CREATE PROCEDURE sp_RegisterPharmacy
    @pharmacy_name NVARCHAR(255),
    @owner_name NVARCHAR(255),
    @phone NVARCHAR(50),
    @email NVARCHAR(255),
    @address NVARCHAR(500),
    @city NVARCHAR(100),
    @license_number NVARCHAR(100)
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- التحقق من عدم وجود رقم ترخيص مكرر
        IF EXISTS (SELECT 1 FROM pharmacies WHERE license_number = @license_number)
        BEGIN
            SELECT 0 AS Success, N'رقم الترخيص موجود مسبقاً' AS Message;
            RETURN;
        END
        
        -- إدراج الصيدلية الجديدة
        INSERT INTO pharmacies (pharmacy_name, owner_name, phone, email, address, city, license_number, subscription_end_date)
        VALUES (@pharmacy_name, @owner_name, @phone, @email, @address, @city, @license_number, DATEADD(YEAR, 1, GETDATE()));
        
        DECLARE @pharmacy_id INT = SCOPE_IDENTITY();
        
        -- إنشاء إشعار ترحيب
        INSERT INTO notifications (pharmacy_id, notification_type, title, content)
        VALUES (@pharmacy_id, 'system', N'مرحباً بك في شبكة الصيدليات', 
                N'تم تسجيل صيدليتك بنجاح. يمكنك الآن نشر الأدوية وإرسال طلبات الشراء.');
        
        SELECT 1 AS Success, N'تم تسجيل الصيدلية بنجاح' AS Message, @pharmacy_id AS PharmacyId;
    END TRY
    BEGIN CATCH
        SELECT 0 AS Success, ERROR_MESSAGE() AS Message;
    END CATCH
END
GO

-- ========================================
-- 2. إجراء نشر دواء للبيع
-- Publish Medicine for Sale Procedure
-- ========================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_PublishMedicine')
    DROP PROCEDURE sp_PublishMedicine;
GO

CREATE PROCEDURE sp_PublishMedicine
    @pharmacy_id INT,
    @medicine_name NVARCHAR(255),
    @medicine_number NVARCHAR(100),
    @dosage NVARCHAR(100),
    @quantity_available INT,
    @price_per_unit DECIMAL(10,2),
    @manufacturing_date DATE,
    @expiry_date DATE,
    @description NVARCHAR(1000)
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- التحقق من صحة البيانات
        IF @quantity_available <= 0
        BEGIN
            SELECT 0 AS Success, N'الكمية يجب أن تكون أكبر من صفر' AS Message;
            RETURN;
        END
        
        IF @price_per_unit <= 0
        BEGIN
            SELECT 0 AS Success, N'السعر يجب أن يكون أكبر من صفر' AS Message;
            RETURN;
        END
        
        IF @expiry_date <= GETDATE()
        BEGIN
            SELECT 0 AS Success, N'تاريخ الانتهاء يجب أن يكون في المستقبل' AS Message;
            RETURN;
        END
        
        -- نشر الدواء
        INSERT INTO published_medicines (pharmacy_id, medicine_name, medicine_number, dosage, 
                                       quantity_available, price_per_unit, manufacturing_date, 
                                       expiry_date, description)
        VALUES (@pharmacy_id, @medicine_name, @medicine_number, @dosage, 
                @quantity_available, @price_per_unit, @manufacturing_date, 
                @expiry_date, @description);
        
        DECLARE @medicine_id INT = SCOPE_IDENTITY();
        
        SELECT 1 AS Success, N'تم نشر الدواء بنجاح' AS Message, @medicine_id AS MedicineId;
    END TRY
    BEGIN CATCH
        SELECT 0 AS Success, ERROR_MESSAGE() AS Message;
    END CATCH
END
GO

-- ========================================
-- 3. إجراء البحث عن الأدوية
-- Search Medicines Procedure
-- ========================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_SearchMedicines')
    DROP PROCEDURE sp_SearchMedicines;
GO

CREATE PROCEDURE sp_SearchMedicines
    @search_term NVARCHAR(255) = '',
    @city NVARCHAR(100) = '',
    @min_quantity INT = 0,
    @max_price DECIMAL(10,2) = 999999,
    @exclude_expired BIT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        pm.id,
        pm.medicine_name,
        pm.medicine_number,
        pm.dosage,
        pm.quantity_available,
        pm.price_per_unit,
        pm.manufacturing_date,
        pm.expiry_date,
        pm.description,
        pm.published_date,
        p.pharmacy_name,
        p.owner_name,
        p.phone,
        p.city,
        p.address,
        -- حساب متوسط التقييم
        ISNULL(AVG(CAST(pr.rating AS FLOAT)), 0) AS average_rating,
        COUNT(pr.id) AS total_ratings
    FROM published_medicines pm
    INNER JOIN pharmacies p ON pm.pharmacy_id = p.id
    LEFT JOIN pharmacy_ratings pr ON p.id = pr.rated_pharmacy_id
    WHERE pm.is_available = 1
        AND p.is_active = 1
        AND (@search_term = '' OR pm.medicine_name LIKE '%' + @search_term + '%')
        AND (@city = '' OR p.city LIKE '%' + @city + '%')
        AND pm.quantity_available >= @min_quantity
        AND pm.price_per_unit <= @max_price
        AND (@exclude_expired = 0 OR pm.expiry_date > GETDATE())
    GROUP BY pm.id, pm.medicine_name, pm.medicine_number, pm.dosage, 
             pm.quantity_available, pm.price_per_unit, pm.manufacturing_date,
             pm.expiry_date, pm.description, pm.published_date,
             p.pharmacy_name, p.owner_name, p.phone, p.city, p.address
    ORDER BY pm.published_date DESC;
END
GO

-- ========================================
-- 4. إجراء إرسال طلب شراء
-- Send Purchase Request Procedure
-- ========================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_SendPurchaseRequest')
    DROP PROCEDURE sp_SendPurchaseRequest;
GO

CREATE PROCEDURE sp_SendPurchaseRequest
    @buyer_pharmacy_id INT,
    @seller_pharmacy_id INT,
    @medicine_id INT,
    @requested_quantity INT,
    @offered_price DECIMAL(10,2),
    @request_message NVARCHAR(1000)
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- التحقق من توفر الكمية المطلوبة
        DECLARE @available_quantity INT;
        SELECT @available_quantity = quantity_available 
        FROM published_medicines 
        WHERE id = @medicine_id AND is_available = 1;
        
        IF @available_quantity IS NULL
        BEGIN
            SELECT 0 AS Success, N'الدواء غير متوفر' AS Message;
            RETURN;
        END
        
        IF @requested_quantity > @available_quantity
        BEGIN
            SELECT 0 AS Success, N'الكمية المطلوبة أكبر من المتوفر' AS Message;
            RETURN;
        END
        
        -- إرسال طلب الشراء
        INSERT INTO purchase_requests (buyer_pharmacy_id, seller_pharmacy_id, medicine_id, 
                                     requested_quantity, offered_price, request_message)
        VALUES (@buyer_pharmacy_id, @seller_pharmacy_id, @medicine_id, 
                @requested_quantity, @offered_price, @request_message);
        
        DECLARE @request_id INT = SCOPE_IDENTITY();
        
        -- إنشاء إشعار للبائع
        DECLARE @buyer_name NVARCHAR(255), @medicine_name NVARCHAR(255);
        SELECT @buyer_name = pharmacy_name FROM pharmacies WHERE id = @buyer_pharmacy_id;
        SELECT @medicine_name = medicine_name FROM published_medicines WHERE id = @medicine_id;
        
        INSERT INTO notifications (pharmacy_id, notification_type, title, content, related_id)
        VALUES (@seller_pharmacy_id, 'purchase_request', 
                N'طلب شراء جديد', 
                N'تلقيت طلب شراء من ' + @buyer_name + N' للدواء: ' + @medicine_name,
                @request_id);
        
        SELECT 1 AS Success, N'تم إرسال طلب الشراء بنجاح' AS Message, @request_id AS RequestId;
    END TRY
    BEGIN CATCH
        SELECT 0 AS Success, ERROR_MESSAGE() AS Message;
    END CATCH
END
GO

-- ========================================
-- 5. إجراء الرد على طلب الشراء
-- Respond to Purchase Request Procedure
-- ========================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_RespondToPurchaseRequest')
    DROP PROCEDURE sp_RespondToPurchaseRequest;
GO

CREATE PROCEDURE sp_RespondToPurchaseRequest
    @request_id INT,
    @response_status NVARCHAR(50), -- 'accepted' or 'rejected'
    @response_message NVARCHAR(1000)
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- التحقق من وجود الطلب
        DECLARE @buyer_pharmacy_id INT, @seller_pharmacy_id INT, @medicine_id INT, @requested_quantity INT;
        
        SELECT @buyer_pharmacy_id = buyer_pharmacy_id, 
               @seller_pharmacy_id = seller_pharmacy_id,
               @medicine_id = medicine_id,
               @requested_quantity = requested_quantity
        FROM purchase_requests 
        WHERE id = @request_id AND status = 'pending';
        
        IF @buyer_pharmacy_id IS NULL
        BEGIN
            SELECT 0 AS Success, N'الطلب غير موجود أو تم الرد عليه مسبقاً' AS Message;
            RETURN;
        END
        
        -- تحديث حالة الطلب
        UPDATE purchase_requests 
        SET status = @response_status, 
            response_date = GETDATE(), 
            response_message = @response_message
        WHERE id = @request_id;
        
        -- إنشاء إشعار للمشتري
        DECLARE @seller_name NVARCHAR(255), @medicine_name NVARCHAR(255);
        SELECT @seller_name = pharmacy_name FROM pharmacies WHERE id = @seller_pharmacy_id;
        SELECT @medicine_name = medicine_name FROM published_medicines WHERE id = @medicine_id;
        
        DECLARE @notification_title NVARCHAR(255);
        DECLARE @notification_content NVARCHAR(1000);
        
        IF @response_status = 'accepted'
        BEGIN
            SET @notification_title = N'تم قبول طلب الشراء';
            SET @notification_content = N'تم قبول طلبك لشراء ' + @medicine_name + N' من ' + @seller_name;
        END
        ELSE
        BEGIN
            SET @notification_title = N'تم رفض طلب الشراء';
            SET @notification_content = N'تم رفض طلبك لشراء ' + @medicine_name + N' من ' + @seller_name;
        END
        
        INSERT INTO notifications (pharmacy_id, notification_type, title, content, related_id)
        VALUES (@buyer_pharmacy_id, 'purchase_request', @notification_title, @notification_content, @request_id);
        
        SELECT 1 AS Success, N'تم الرد على الطلب بنجاح' AS Message;
    END TRY
    BEGIN CATCH
        SELECT 0 AS Success, ERROR_MESSAGE() AS Message;
    END CATCH
END
GO

-- ========================================
-- 6. إجراء إرسال رسالة بين الصيدليات
-- Send Message Between Pharmacies Procedure
-- ========================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_SendMessage')
    DROP PROCEDURE sp_SendMessage;
GO

CREATE PROCEDURE sp_SendMessage
    @sender_pharmacy_id INT,
    @receiver_pharmacy_id INT,
    @subject NVARCHAR(255),
    @message_content NVARCHAR(2000),
    @related_request_id INT = NULL
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        -- إرسال الرسالة
        INSERT INTO pharmacy_messages (sender_pharmacy_id, receiver_pharmacy_id, subject,
                                     message_content, related_request_id)
        VALUES (@sender_pharmacy_id, @receiver_pharmacy_id, @subject,
                @message_content, @related_request_id);

        DECLARE @message_id INT = SCOPE_IDENTITY();

        -- إنشاء إشعار للمستقبل
        DECLARE @sender_name NVARCHAR(255);
        SELECT @sender_name = pharmacy_name FROM pharmacies WHERE id = @sender_pharmacy_id;

        INSERT INTO notifications (pharmacy_id, notification_type, title, content, related_id)
        VALUES (@receiver_pharmacy_id, 'message',
                N'رسالة جديدة من ' + @sender_name,
                @subject,
                @message_id);

        SELECT 1 AS Success, N'تم إرسال الرسالة بنجاح' AS Message, @message_id AS MessageId;
    END TRY
    BEGIN CATCH
        SELECT 0 AS Success, ERROR_MESSAGE() AS Message;
    END CATCH
END
GO

-- ========================================
-- 7. إجراء الحصول على الإشعارات
-- Get Notifications Procedure
-- ========================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetNotifications')
    DROP PROCEDURE sp_GetNotifications;
GO

CREATE PROCEDURE sp_GetNotifications
    @pharmacy_id INT,
    @unread_only BIT = 0
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        id,
        notification_type,
        title,
        content,
        is_read,
        created_date,
        read_date,
        related_id
    FROM notifications
    WHERE pharmacy_id = @pharmacy_id
        AND (@unread_only = 0 OR is_read = 0)
    ORDER BY created_date DESC;
END
GO

-- ========================================
-- 8. إجراء تحديث حالة الإشعار
-- Mark Notification as Read Procedure
-- ========================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_MarkNotificationRead')
    DROP PROCEDURE sp_MarkNotificationRead;
GO

CREATE PROCEDURE sp_MarkNotificationRead
    @notification_id INT
AS
BEGIN
    SET NOCOUNT ON;

    UPDATE notifications
    SET is_read = 1, read_date = GETDATE()
    WHERE id = @notification_id;

    SELECT 1 AS Success, N'تم تحديث الإشعار' AS Message;
END
GO

-- ========================================
-- 9. إجراء الحصول على الأدوية المنشورة لصيدلية معينة
-- Get Published Medicines for Specific Pharmacy
-- ========================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetMyPublishedMedicines')
    DROP PROCEDURE sp_GetMyPublishedMedicines;
GO

CREATE PROCEDURE sp_GetMyPublishedMedicines
    @pharmacy_id INT
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        id,
        medicine_name,
        medicine_number,
        dosage,
        quantity_available,
        price_per_unit,
        manufacturing_date,
        expiry_date,
        description,
        is_available,
        published_date,
        updated_date,
        -- عدد الطلبات المرسلة لهذا الدواء
        (SELECT COUNT(*) FROM purchase_requests WHERE medicine_id = pm.id) AS total_requests,
        -- عدد الطلبات المعلقة
        (SELECT COUNT(*) FROM purchase_requests WHERE medicine_id = pm.id AND status = 'pending') AS pending_requests
    FROM published_medicines pm
    WHERE pharmacy_id = @pharmacy_id
    ORDER BY published_date DESC;
END
GO

-- ========================================
-- 10. إجراء الحصول على طلبات الشراء
-- Get Purchase Requests Procedure
-- ========================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetPurchaseRequests')
    DROP PROCEDURE sp_GetPurchaseRequests;
GO

CREATE PROCEDURE sp_GetPurchaseRequests
    @pharmacy_id INT,
    @request_type NVARCHAR(20) -- 'sent' or 'received'
AS
BEGIN
    SET NOCOUNT ON;

    IF @request_type = 'sent'
    BEGIN
        -- الطلبات المرسلة من هذه الصيدلية
        SELECT
            pr.id,
            pr.requested_quantity,
            pr.offered_price,
            pr.request_message,
            pr.status,
            pr.request_date,
            pr.response_date,
            pr.response_message,
            pm.medicine_name,
            pm.dosage,
            pm.price_per_unit,
            p.pharmacy_name AS seller_pharmacy_name,
            p.phone AS seller_phone
        FROM purchase_requests pr
        INNER JOIN published_medicines pm ON pr.medicine_id = pm.id
        INNER JOIN pharmacies p ON pr.seller_pharmacy_id = p.id
        WHERE pr.buyer_pharmacy_id = @pharmacy_id
        ORDER BY pr.request_date DESC;
    END
    ELSE
    BEGIN
        -- الطلبات المستلمة لهذه الصيدلية
        SELECT
            pr.id,
            pr.requested_quantity,
            pr.offered_price,
            pr.request_message,
            pr.status,
            pr.request_date,
            pr.response_date,
            pr.response_message,
            pm.medicine_name,
            pm.dosage,
            pm.price_per_unit,
            p.pharmacy_name AS buyer_pharmacy_name,
            p.phone AS buyer_phone
        FROM purchase_requests pr
        INNER JOIN published_medicines pm ON pr.medicine_id = pm.id
        INNER JOIN pharmacies p ON pr.buyer_pharmacy_id = p.id
        WHERE pr.seller_pharmacy_id = @pharmacy_id
        ORDER BY pr.request_date DESC;
    END
END
GO

PRINT '';
PRINT '========================================';
PRINT '✅ تم إنشاء جميع الإجراءات المخزنة بنجاح!';
PRINT '========================================';
PRINT '';
PRINT 'الإجراءات المنشأة:';
PRINT '1. sp_RegisterPharmacy - تسجيل صيدلية جديدة';
PRINT '2. sp_PublishMedicine - نشر دواء للبيع';
PRINT '3. sp_SearchMedicines - البحث عن الأدوية';
PRINT '4. sp_SendPurchaseRequest - إرسال طلب شراء';
PRINT '5. sp_RespondToPurchaseRequest - الرد على طلب الشراء';
PRINT '6. sp_SendMessage - إرسال رسالة بين الصيدليات';
PRINT '7. sp_GetNotifications - الحصول على الإشعارات';
PRINT '8. sp_MarkNotificationRead - تحديث حالة الإشعار';
PRINT '9. sp_GetMyPublishedMedicines - الحصول على الأدوية المنشورة';
PRINT '10. sp_GetPurchaseRequests - الحصول على طلبات الشراء';
PRINT '';
