-- إصلاح عرض طلبات الأدوية في متجر الأدوية
USE UnifiedPharmacy;

PRINT '=== إصلاح عرض طلبات الأدوية ===';

-- ===================================
-- 1. التحقق من البيانات الحالية
-- ===================================
PRINT '1. فحص البيانات الحالية...';

-- فحص الصيدليات
PRINT 'عدد الصيدليات:';
SELECT COUNT(*) as 'عدد الصيدليات' FROM pharmacies;

-- فحص الأدوية
PRINT 'عدد الأدوية:';
SELECT COUNT(*) as 'عدد الأدوية' FROM medic;

-- فحص طلبات الشراء
PRINT 'عدد طلبات الشراء:';
SELECT COUNT(*) as 'عدد الطلبات' FROM purchase_requests;

-- ===================================
-- 2. إضافة صيدليات إضافية إذا لزم الأمر
-- ===================================
PRINT '2. إضافة صيدليات إضافية...';

-- إضافة صيدليات أخرى للاختبار
IF (SELECT COUNT(*) FROM pharmacies) < 3
BEGIN
    INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, address, city, phone, isActive)
    VALUES 
    ('PH002', N'صيدلية النور', N'فاطمة أحمد', N'شارع الأمير محمد', N'جدة', '0123456789', 1),
    ('PH003', N'صيدلية الصحة', N'محمد علي', N'شارع الملك عبدالعزيز', N'الدمام', '0134567890', 1);
    PRINT '✅ تم إضافة صيدليات إضافية';
END

-- ===================================
-- 3. إضافة أدوية للصيدلية الأولى
-- ===================================
PRINT '3. إضافة أدوية للصيدلية الأولى...';

-- التأكد من وجود أدوية للصيدلية الأولى
IF (SELECT COUNT(*) FROM medic WHERE pharmacyId = 1) < 3
BEGIN
    INSERT INTO medic (pharmacyId, mname, mnumber, quantity, eDate, perUnit, mDate, originalQuantity)
    VALUES 
    (1, N'باراسيتامول 500mg', 'PAR500', 100, '2025-12-31', 15.50, GETDATE(), 100),
    (1, N'أموكسيسيلين 250mg', 'AMX250', 50, '2025-11-30', 25.00, GETDATE(), 50),
    (1, N'أسبرين 100mg', 'ASP100', 75, '2025-10-31', 12.00, GETDATE(), 75),
    (1, N'فيتامين د 1000 وحدة', 'VITD1000', 200, '2026-01-31', 30.00, GETDATE(), 200);
    PRINT '✅ تم إضافة أدوية للصيدلية الأولى';
END

-- ===================================
-- 4. إضافة مستخدمين للصيدليات الأخرى
-- ===================================
PRINT '4. إضافة مستخدمين للصيدليات الأخرى...';

-- إضافة مستخدمين للصيدليات الأخرى
IF NOT EXISTS (SELECT * FROM users WHERE pharmacyId = 2)
BEGIN
    INSERT INTO users (pharmacyId, userRole, name, username, pass, isActive)
    VALUES 
    (2, 'Pharmacist', N'فاطمة أحمد', 'fatima', 'fatima', 1),
    (3, 'Pharmacist', N'محمد علي', 'mohamed', 'mohamed', 1);
    PRINT '✅ تم إضافة مستخدمين للصيدليات الأخرى';
END

-- ===================================
-- 5. حذف الطلبات القديمة وإضافة طلبات جديدة
-- ===================================
PRINT '5. إضافة طلبات أدوية جديدة...';

-- حذف الطلبات القديمة
DELETE FROM purchase_requests;
PRINT 'تم حذف الطلبات القديمة';

-- إضافة طلبات جديدة
DECLARE @medicine1 INT, @medicine2 INT, @medicine3 INT;

-- الحصول على معرفات الأدوية
SELECT TOP 1 @medicine1 = id FROM medic WHERE pharmacyId = 1 AND mname LIKE N'%باراسيتامول%';
SELECT TOP 1 @medicine2 = id FROM medic WHERE pharmacyId = 1 AND mname LIKE N'%أموكسيسيلين%';
SELECT TOP 1 @medicine3 = id FROM medic WHERE pharmacyId = 1 AND mname LIKE N'%أسبرين%';

IF @medicine1 IS NOT NULL AND @medicine2 IS NOT NULL AND @medicine3 IS NOT NULL
BEGIN
    INSERT INTO purchase_requests (medicine_id, buyer_pharmacy_id, seller_pharmacy_id, requested_quantity, offered_price, request_message, status, request_date)
    VALUES 
    -- طلبات من الصيدلية الثانية للأولى
    (@medicine1, 2, 1, 20, 15.00, N'نحتاج باراسيتامول بشكل عاجل للمرضى', 'pending', GETDATE()),
    (@medicine2, 2, 1, 10, 24.00, N'طلب أموكسيسيلين للأطفال', 'pending', DATEADD(HOUR, -1, GETDATE())),
    (@medicine3, 2, 1, 15, 11.50, N'أسبرين للمرضى كبار السن', 'pending', DATEADD(HOUR, -2, GETDATE())),
    
    -- طلبات من الصيدلية الثالثة للأولى
    (@medicine1, 3, 1, 30, 15.50, N'طلب كمية كبيرة من باراسيتامول', 'pending', DATEADD(HOUR, -3, GETDATE())),
    (@medicine2, 3, 1, 5, 25.00, N'طلب صغير من أموكسيسيلين', 'pending', DATEADD(HOUR, -4, GETDATE())),
    
    -- طلب مقبول (للاختبار)
    (@medicine3, 2, 1, 25, 12.00, N'طلب تم قبوله مسبقاً', 'accepted', DATEADD(DAY, -1, GETDATE())),
    
    -- طلب مرفوض (للاختبار)
    (@medicine1, 3, 1, 50, 14.00, N'طلب كمية كبيرة جداً', 'rejected', DATEADD(DAY, -2, GETDATE()));
    
    PRINT '✅ تم إضافة 7 طلبات أدوية جديدة';
END
ELSE
BEGIN
    PRINT '❌ لم يتم العثور على الأدوية المطلوبة';
END

-- ===================================
-- 6. التحقق من النتائج
-- ===================================
PRINT '6. التحقق من النتائج النهائية...';

-- عرض الطلبات الجديدة
PRINT 'الطلبات المضافة:';
SELECT 
    pr.id,
    m.mname as 'اسم الدواء',
    pr.requested_quantity as 'الكمية المطلوبة',
    pr.offered_price as 'السعر المعروض',
    p_buyer.pharmacyName as 'الصيدلية الطالبة',
    pr.status as 'الحالة',
    pr.request_date as 'تاريخ الطلب'
FROM purchase_requests pr
INNER JOIN medic m ON pr.medicine_id = m.id
INNER JOIN pharmacies p_buyer ON pr.buyer_pharmacy_id = p_buyer.id
WHERE pr.seller_pharmacy_id = 1
ORDER BY pr.request_date DESC;

-- إحصائيات
PRINT '';
PRINT 'إحصائيات النظام:';
SELECT 
    (SELECT COUNT(*) FROM pharmacies) as 'عدد الصيدليات',
    (SELECT COUNT(*) FROM users) as 'عدد المستخدمين',
    (SELECT COUNT(*) FROM medic WHERE pharmacyId = 1) as 'أدوية الصيدلية الأولى',
    (SELECT COUNT(*) FROM purchase_requests WHERE seller_pharmacy_id = 1) as 'طلبات للصيدلية الأولى';

-- ===================================
-- 7. اختبار الاستعلام المستخدم في الكود
-- ===================================
PRINT '7. اختبار الاستعلام المستخدم في الكود...';

DECLARE @pharmacyId INT = 1;

SELECT
    pr.id,
    pr.requested_quantity as requestedQuantity,
    pr.offered_price as offeredPrice,
    pr.request_date as requestDate,
    pr.status,
    ISNULL(pr.response_message, '') as responseMessage,
    ISNULL(pr.request_message, '') as requestMessage,
    m.mname as medicineName,
    ISNULL(m.mnumber, '') as medicineNumber,
    m.perUnit as originalPrice,
    p_buyer.pharmacyName as buyerPharmacyName,
    ISNULL(p_buyer.phone, '') as buyerPhone,
    ISNULL(p_buyer.city, '') as buyerCity
FROM purchase_requests pr
INNER JOIN medic m ON pr.medicine_id = m.id AND m.pharmacyId = pr.seller_pharmacy_id
INNER JOIN pharmacies p_buyer ON pr.buyer_pharmacy_id = p_buyer.id
WHERE pr.seller_pharmacy_id = @pharmacyId
ORDER BY pr.request_date DESC;

PRINT '✅ تم إصلاح عرض طلبات الأدوية بنجاح!';
PRINT '';
PRINT '📋 للاختبار:';
PRINT '1. سجل دخول بـ admin/admin';
PRINT '2. اذهب إلى متجر الأدوية → طلبات الأدوية';
PRINT '3. يجب أن ترى 7 طلبات أدوية';
