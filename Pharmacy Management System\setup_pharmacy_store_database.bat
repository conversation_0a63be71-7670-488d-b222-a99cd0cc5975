@echo off
echo إعداد قاعدة بيانات متجر الصيدلية...
echo Setting up Pharmacy Store Database...
echo.

REM تحقق من وجود SQL Server
echo التحقق من SQL Server...
echo Checking SQL Server...
sc query MSSQLSERVER >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo تحقق من SQLEXPRESS...
    echo Checking SQLEXPRESS...
    sc query MSSQL$SQLEXPRESS >nul 2>&1
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ SQL Server غير مثبت أو غير مشغل
        echo ❌ SQL Server is not installed or not running
        pause
        exit /b 1
    )
)

REM تنفيذ إعداد قاعدة البيانات الكاملة
echo تنفيذ إعداد قاعدة البيانات...
echo Executing database setup...

REM محاولة الاتصال بـ SQL Server الافتراضي أولاً
echo محاولة الاتصال بـ SQL Server...
echo Trying to connect to SQL Server...
sqlcmd -S . -E -i "complete_pharmacy_store_database.sql" >nul 2>&1

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم الاتصال بـ SQL Server الافتراضي
    echo ✅ Connected to default SQL Server
    sqlcmd -S . -E -i "complete_pharmacy_store_database.sql"
) else (
    echo محاولة الاتصال بـ SQLEXPRESS...
    echo Trying to connect to SQLEXPRESS...
    sqlcmd -S .\SQLEXPRESS -E -i "complete_pharmacy_store_database.sql"
)

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم إعداد قاعدة البيانات بنجاح!
    echo ✅ Database setup completed successfully!
    echo.
    echo تم إعداد التالي:
    echo The following has been set up:
    echo - قاعدة بيانات UnifiedPharmacy
    echo - UnifiedPharmacy database
    echo - جميع الجداول المطلوبة
    echo - All required tables
    echo - الإجراءات المخزنة
    echo - Stored procedures
    echo - بيانات تجريبية
    echo - Sample data
    echo.
    echo يمكنك الآن تشغيل نظام إدارة الصيدلية
    echo You can now run the Pharmacy Management System.
) else (
    echo.
    echo ❌ فشل في إعداد قاعدة البيانات!
    echo ❌ Database setup failed!
    echo يرجى التحقق من رسائل الخطأ أعلاه
    echo Please check the error messages above.
    echo.
    echo تأكد من:
    echo Make sure:
    echo 1. SQL Server يعمل
    echo 1. SQL Server is running
    echo 2. لديك الصلاحيات المناسبة
    echo 2. You have appropriate permissions
    echo 3. ملفات SQL موجودة في المجلد الحالي
    echo 3. The SQL files exist in the current directory
)

echo.
pause
