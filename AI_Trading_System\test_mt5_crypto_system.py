#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام تداول العملات الرقمية على MetaTrader 5
MT5 Cryptocurrency Trading System Test
"""

import sys
import os
import time
from datetime import datetime
import pandas as pd
import numpy as np

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from mt5_crypto_trading_system import MT5CryptoTradingSystem

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    print("🧪 اختبار الوظائف الأساسية...")
    
    try:
        # إنشاء النظام
        system = MT5CryptoTradingSystem(demo_mode=True)
        print("✅ تم إنشاء النظام بنجاح")
        
        # اختبار تحميل الإعدادات
        if hasattr(system, 'login') and system.login:
            print("✅ تم تحميل الإعدادات بنجاح")
        else:
            print("❌ فشل في تحميل الإعدادات")
            return False
            
        # اختبار قائمة العملات
        if system.crypto_symbols and len(system.crypto_symbols) > 0:
            print(f"✅ العملات الرقمية المدعومة: {len(system.crypto_symbols)}")
            print(f"   📋 العملات: {', '.join(system.crypto_symbols[:5])}...")
        else:
            print("❌ لا توجد عملات رقمية مدعومة")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار الأساسي: {e}")
        return False

def test_mt5_connection():
    """اختبار الاتصال بـ MetaTrader 5"""
    print("\n🔌 اختبار الاتصال بـ MetaTrader 5...")
    
    try:
        system = MT5CryptoTradingSystem(demo_mode=True)
        
        # محاولة الاتصال
        connected = system.connect_mt5()
        
        if connected:
            print("✅ تم الاتصال بـ MT5 بنجاح!")
            
            # اختبار معلومات الحساب
            if system.account_info:
                print(f"   📊 رقم الحساب: {system.account_info.login}")
                print(f"   💰 الرصيد: ${system.account_info.balance:.2f}")
                print(f"   🏢 الشركة: {system.account_info.company}")
                print(f"   🔄 التداول مسموح: {'نعم' if system.account_info.trade_allowed else 'لا'}")
            
            # اختبار العملات المتوفرة
            available_cryptos = [symbol for symbol in system.crypto_symbols 
                               if system.trading_system.mt5.symbol_info(symbol) is not None]
            print(f"   💎 العملات الرقمية المتوفرة: {len(available_cryptos)}")
            
            return True
        else:
            print("❌ فشل في الاتصال بـ MT5")
            print("💡 تأكد من:")
            print("   • تشغيل MetaTrader 5")
            print("   • صحة بيانات الحساب في mt5_crypto_config.ini")
            print("   • تفعيل التداول الآلي")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الاتصال: {e}")
        return False

def test_data_retrieval():
    """اختبار استرجاع البيانات"""
    print("\n📊 اختبار استرجاع بيانات العملات الرقمية...")
    
    try:
        system = MT5CryptoTradingSystem(demo_mode=True)
        
        if not system.connect_mt5():
            print("❌ لا يمكن الاتصال بـ MT5 للاختبار")
            return False
            
        # اختبار استرجاع البيانات لعملات مختلفة
        test_symbols = ['BTCUSD', 'ETHUSD', 'LTCUSD']
        successful_retrievals = 0
        
        for symbol in test_symbols:
            if symbol in system.crypto_symbols:
                print(f"   🔍 اختبار {symbol}...")
                
                # استرجاع البيانات
                df = system.get_crypto_data(symbol, count=50)
                
                if df is not None and not df.empty:
                    print(f"   ✅ {symbol}: {len(df)} شمعة")
                    print(f"      💰 آخر سعر: ${df['close'].iloc[-1]:.4f}")
                    print(f"      📊 أعلى سعر: ${df['high'].max():.4f}")
                    print(f"      📉 أقل سعر: ${df['low'].min():.4f}")
                    successful_retrievals += 1
                else:
                    print(f"   ❌ {symbol}: فشل في استرجاع البيانات")
            else:
                print(f"   ⚠️ {symbol}: غير متوفر")
                
        if successful_retrievals > 0:
            print(f"✅ تم استرجاع البيانات بنجاح لـ {successful_retrievals} عملة")
            return True
        else:
            print("❌ فشل في استرجاع البيانات لأي عملة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار استرجاع البيانات: {e}")
        return False

def test_technical_analysis():
    """اختبار التحليل الفني"""
    print("\n📈 اختبار التحليل الفني...")
    
    try:
        system = MT5CryptoTradingSystem(demo_mode=True)
        
        if not system.connect_mt5():
            print("❌ لا يمكن الاتصال بـ MT5 للاختبار")
            return False
            
        # اختبار التحليل لعملة واحدة
        test_symbol = 'BTCUSD'
        if test_symbol not in system.crypto_symbols:
            test_symbol = system.crypto_symbols[0] if system.crypto_symbols else None
            
        if not test_symbol:
            print("❌ لا توجد عملات متوفرة للاختبار")
            return False
            
        print(f"   🔍 تحليل {test_symbol}...")
        
        # تشغيل التحليل
        analysis = system.analyze_crypto_market(test_symbol)
        
        if analysis and 'decision' in analysis:
            print("✅ تم التحليل بنجاح!")
            print(f"   🎯 القرار: {analysis['decision']}")
            print(f"   📊 مستوى الثقة: {analysis['confidence']:.2%}")
            print(f"   💰 السعر: ${analysis.get('price', 0):.4f}")
            print(f"   📈 نقاط الاتجاه: {analysis.get('trend_score', 0)}")
            print(f"   ⚡ نقاط الزخم: {analysis.get('momentum_score', 0)}")
            print(f"   📊 RSI: {analysis.get('rsi', 0):.2f}")
            
            if 'ml_prediction' in analysis and analysis['ml_prediction'] is not None:
                print(f"   🤖 توقع ML: {analysis['ml_prediction']:.4f}")
            else:
                print("   🤖 نموذج ML غير مدرب بعد")
                
            return True
        else:
            print("❌ فشل في التحليل")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التحليل: {e}")
        return False

def test_demo_trading():
    """اختبار التداول التجريبي"""
    print("\n🛡️ اختبار التداول التجريبي...")
    
    try:
        system = MT5CryptoTradingSystem(demo_mode=True)
        
        if not system.connect_mt5():
            print("❌ لا يمكن الاتصال بـ MT5 للاختبار")
            return False
            
        # اختبار تحليل وتداول
        test_symbol = 'BTCUSD'
        if test_symbol not in system.crypto_symbols:
            test_symbol = system.crypto_symbols[0] if system.crypto_symbols else None
            
        if not test_symbol:
            print("❌ لا توجد عملات متوفرة للاختبار")
            return False
            
        print(f"   🔍 اختبار تداول {test_symbol}...")
        
        # تحليل السوق
        analysis = system.analyze_crypto_market(test_symbol)
        
        if not analysis or 'decision' in analysis:
            print("❌ فشل في التحليل")
            return False
            
        print(f"   📊 نتيجة التحليل: {analysis['decision']} - ثقة: {analysis['confidence']:.2%}")
        
        # محاولة تنفيذ صفقة تجريبية
        if analysis['decision'] in ['buy', 'sell'] and analysis['confidence'] >= 0.5:
            print("   🚀 محاولة تنفيذ صفقة تجريبية...")
            
            # تخفيض حد الثقة مؤقتاً للاختبار
            original_confidence = system.min_confidence
            system.min_confidence = 0.5
            
            success = system.execute_crypto_trade(analysis)
            
            # إعادة حد الثقة الأصلي
            system.min_confidence = original_confidence
            
            if success:
                print("✅ تم تنفيذ الصفقة التجريبية بنجاح!")
                return True
            else:
                print("❌ فشل في تنفيذ الصفقة التجريبية")
                return False
        else:
            print("   ⚠️ لا توجد إشارة تداول قوية للاختبار")
            print("   💡 هذا طبيعي - النظام محافظ في قراراته")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التداول التجريبي: {e}")
        return False

def test_machine_learning():
    """اختبار نظام التعلم الآلي"""
    print("\n🧠 اختبار نظام التعلم الآلي...")
    
    try:
        system = MT5CryptoTradingSystem(demo_mode=True)
        
        # اختبار تحميل بيانات التعلم
        system.load_learning_data()
        print(f"   📚 نقاط التعلم المحملة: {len(system.learning_data)}")
        
        # إضافة بيانات تجريبية إذا لم تكن موجودة
        if len(system.learning_data) < 10:
            print("   🔄 إضافة بيانات تجريبية للاختبار...")
            
            # إنشاء بيانات تجريبية
            for i in range(20):
                fake_features = np.random.rand(17)  # 17 ميزة
                fake_target = np.random.uniform(-0.05, 0.05)  # تغيير سعر بين -5% و +5%
                
                system.learning_data.append({
                    'features': fake_features,
                    'target': fake_target,
                    'timestamp': datetime.now(),
                    'symbol': 'BTCUSD'
                })
                
            print(f"   ✅ تم إضافة {len(system.learning_data)} نقطة تجريبية")
        
        # اختبار تدريب النموذج
        print("   🏋️ اختبار تدريب النموذج...")
        success = system.train_ml_model()
        
        if success:
            print("✅ تم تدريب النموذج بنجاح!")
            print(f"   🎯 النموذج مدرب: {system.model_trained}")
            
            # اختبار التنبؤ
            if system.model_trained:
                test_features = np.random.rand(1, 17)
                prediction = system.predict_price_movement(test_features)
                
                if prediction is not None:
                    print(f"   🔮 اختبار التنبؤ: {prediction:.4f}")
                    print("✅ نظام التعلم الآلي يعمل بشكل صحيح!")
                    return True
                else:
                    print("❌ فشل في اختبار التنبؤ")
                    return False
            else:
                print("❌ النموذج غير مدرب")
                return False
        else:
            print("❌ فشل في تدريب النموذج")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التعلم الآلي: {e}")
        return False

def run_comprehensive_test():
    """تشغيل اختبار شامل"""
    print("="*70)
    print("🧪 اختبار شامل لنظام تداول العملات الرقمية على MetaTrader 5")
    print("="*70)
    
    tests = [
        ("الوظائف الأساسية", test_basic_functionality),
        ("الاتصال بـ MT5", test_mt5_connection),
        ("استرجاع البيانات", test_data_retrieval),
        ("التحليل الفني", test_technical_analysis),
        ("التداول التجريبي", test_demo_trading),
        ("التعلم الآلي", test_machine_learning)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 اختبار: {test_name}")
        print(f"{'='*50}")
        
        try:
            start_time = time.time()
            result = test_func()
            end_time = time.time()
            
            if result:
                print(f"✅ نجح الاختبار في {end_time - start_time:.2f} ثانية")
                passed_tests += 1
            else:
                print(f"❌ فشل الاختبار في {end_time - start_time:.2f} ثانية")
                
        except Exception as e:
            print(f"💥 خطأ في الاختبار: {e}")
            
        time.sleep(1)  # انتظار قصير بين الاختبارات
    
    # النتيجة النهائية
    print("\n" + "="*70)
    print("📊 نتائج الاختبار الشامل")
    print("="*70)
    
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"📊 معدل النجاح: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 النظام يعمل بشكل ممتاز!")
        print("🚀 جاهز للاستخدام الفعلي")
    elif success_rate >= 60:
        print("⚠️ النظام يعمل بشكل جيد مع بعض المشاكل")
        print("🔧 قد تحتاج لإصلاحات طفيفة")
    else:
        print("❌ النظام يحتاج إصلاحات كبيرة")
        print("🛠️ راجع الأخطاء وأصلحها قبل الاستخدام")
    
    print("\n💡 نصائح:")
    print("   • تأكد من تشغيل MetaTrader 5")
    print("   • تحقق من بيانات الحساب في mt5_crypto_config.ini")
    print("   • تأكد من دعم الوسيط للعملات الرقمية")
    print("   • ابدأ بالوضع التجريبي دائماً")
    
    return success_rate >= 60

if __name__ == "__main__":
    try:
        success = run_comprehensive_test()
        
        print("\n" + "="*70)
        if success:
            print("🎯 الاختبار مكتمل - النظام جاهز!")
        else:
            print("⚠️ الاختبار مكتمل - يحتاج إصلاحات")
        print("="*70)
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n💥 خطأ عام في الاختبار: {e}")
    
    input("\n📋 اضغط Enter للخروج...")
