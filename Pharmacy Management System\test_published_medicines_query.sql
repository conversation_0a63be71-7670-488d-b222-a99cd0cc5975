-- اختبار استعلام الأدوية المنشورة
-- Test Published Medicines Query

USE UnifiedPharmacy;
GO

PRINT '🧪 اختبار استعلام الأدوية المنشورة...';
PRINT '🧪 Testing Published Medicines Query...';
PRINT '';

-- 1. عرض البيانات الأساسية
PRINT '1. البيانات الأساسية:';
PRINT '1. Basic Data:';
PRINT '───────────────────────────────────────';

PRINT 'عدد الصيدليات المسجلة:';
PRINT 'Number of registered pharmacies:';
SELECT COUNT(*) as pharmacy_count FROM pharmacies;

PRINT '';
PRINT 'عدد الأدوية المنشورة:';
PRINT 'Number of published medicines:';
SELECT COUNT(*) as published_count FROM published_medicines;

PRINT '';

-- 2. اختبار الاستعلام الأساسي
PRINT '2. اختبار الاستعلام الأساسي:';
PRINT '2. Testing Basic Query:';
PRINT '───────────────────────────────────────';

SELECT
    pm.id,
    pm.medicine_name as medicineName,
    pm.quantity_available as quantity,
    pm.expiry_date as expiryDate,
    pm.price_per_unit as pricePerUnit,
    pm.description,
    pm.published_date as publishDate,
    p.pharmacyName as pharmacyName,
    p.phone as pharmacyPhone,
    p.address as pharmacyAddress,
    p.city as pharmacyCity,
    DATEDIFF(day, GETDATE(), pm.expiry_date) as daysToExpiry
FROM published_medicines pm
INNER JOIN pharmacies p ON pm.pharmacy_id = p.id
WHERE pm.is_available = 1
AND pm.expiry_date > GETDATE()
ORDER BY pm.published_date DESC;

PRINT '';

-- 3. اختبار استعلام "أدويتي المعروضة"
PRINT '3. اختبار استعلام "أدويتي المعروضة":';
PRINT '3. Testing "My Published Medicines" Query:';
PRINT '───────────────────────────────────────';

DECLARE @currentPharmacyId INT = 1;

SELECT
    id,
    medicine_name as medicineName,
    quantity_available as quantity,
    expiry_date as expiryDate,
    price_per_unit as pricePerUnit,
    description,
    published_date as publishDate,
    DATEDIFF(day, GETDATE(), expiry_date) as daysToExpiry
FROM published_medicines
WHERE pharmacy_id = @currentPharmacyId
AND is_available = 1
ORDER BY published_date DESC;

PRINT '';

-- 4. اختبار استعلام الأدوية من الصيدليات الأخرى
PRINT '4. اختبار استعلام الأدوية من الصيدليات الأخرى:';
PRINT '4. Testing Other Pharmacies Medicines Query:';
PRINT '───────────────────────────────────────';

SELECT
    pm.id,
    pm.medicine_name as medicineName,
    pm.quantity_available as quantity,
    pm.expiry_date as expiryDate,
    pm.price_per_unit as pricePerUnit,
    pm.description,
    pm.published_date as publishDate,
    p.pharmacyName as pharmacyName,
    p.phone as pharmacyPhone,
    p.address as pharmacyAddress,
    p.city as pharmacyCity,
    DATEDIFF(day, GETDATE(), pm.expiry_date) as daysToExpiry
FROM published_medicines pm
INNER JOIN pharmacies p ON pm.pharmacy_id = p.id
WHERE pm.is_available = 1
AND pm.expiry_date > GETDATE()
AND pm.pharmacy_id != @currentPharmacyId
ORDER BY pm.published_date DESC;

PRINT '';

-- 5. اختبار فلتر الصيدليات
PRINT '5. اختبار فلتر الصيدليات:';
PRINT '5. Testing Pharmacy Filter:';
PRINT '───────────────────────────────────────';

SELECT DISTINCT p.pharmacyName
FROM published_medicines pm
INNER JOIN pharmacies p ON pm.pharmacy_id = p.id
WHERE pm.is_available = 1
AND pm.pharmacy_id != @currentPharmacyId
ORDER BY p.pharmacyName;

PRINT '';

-- 6. إضافة أدوية تجريبية إضافية إذا لزم الأمر
PRINT '6. إضافة أدوية تجريبية إضافية:';
PRINT '6. Adding Additional Test Medicines:';
PRINT '───────────────────────────────────────';

-- إضافة دواء من الصيدلية الثانية
IF NOT EXISTS (SELECT * FROM published_medicines WHERE pharmacy_id = 2)
BEGIN
    INSERT INTO published_medicines
    (pharmacy_id, medicine_name, medicine_number, quantity_available, expiry_date, price_per_unit, description, published_date, is_available)
    VALUES
    (2, N'أسبرين 500 مجم', 'ASP500', 10, '2026-12-31', 15.50, N'مسكن للألم وخافض للحرارة', GETDATE(), 1);
    
    PRINT '✅ تم إضافة دواء تجريبي من الصيدلية الثانية';
    PRINT '✅ Added test medicine from second pharmacy';
END
ELSE
BEGIN
    PRINT '✅ توجد أدوية من الصيدلية الثانية بالفعل';
    PRINT '✅ Medicines from second pharmacy already exist';
END

PRINT '';

-- 7. النتائج النهائية
PRINT '7. النتائج النهائية:';
PRINT '7. Final Results:';
PRINT '───────────────────────────────────────';

SELECT 
    'الإجمالي' as النوع,
    COUNT(*) as العدد
FROM published_medicines
WHERE is_available = 1

UNION ALL

SELECT 
    'صيدلية 1' as النوع,
    COUNT(*) as العدد
FROM published_medicines
WHERE is_available = 1 AND pharmacy_id = 1

UNION ALL

SELECT 
    'صيدلية 2' as النوع,
    COUNT(*) as العدد
FROM published_medicines
WHERE is_available = 1 AND pharmacy_id = 2;

PRINT '';
PRINT '🎉 انتهى اختبار الاستعلامات!';
PRINT '🎉 Query testing completed!';
PRINT '';
PRINT 'يمكنك الآن اختبار النظام مرة أخرى';
PRINT 'You can now test the system again';
