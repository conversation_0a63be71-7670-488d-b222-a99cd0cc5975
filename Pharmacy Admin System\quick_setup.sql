-- إعداد سريع لقاعدة بيانات الإدارة المركزية
USE master;
GO

-- إنشاء قاعدة البيانات
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'PharmacyAdminSystem')
BEGIN
    CREATE DATABASE PharmacyAdminSystem;
    PRINT 'تم إنشاء قاعدة بيانات PharmacyAdminSystem';
END
GO

USE PharmacyAdminSystem;
GO

-- جدول المديرين العامين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='admin_users' AND xtype='U')
BEGIN
    CREATE TABLE admin_users (
        id int IDENTITY(1,1) PRIMARY KEY,
        username nvarchar(100) UNIQUE NOT NULL,
        password nvarchar(255) NOT NULL,
        fullName nvarchar(255) NOT NULL,
        email nvarchar(255) NULL,
        phone nvarchar(20) NULL,
        role nvarchar(50) DEFAULT 'SuperAdmin',
        isActive bit DEFAULT 1,
        lastLogin datetime NULL,
        createdDate datetime DEFAULT GETDATE()
    );
    PRINT 'تم إنشاء جدول admin_users';
END

-- إضافة مدير عام افتراضي
IF NOT EXISTS (SELECT * FROM admin_users WHERE username = 'superadmin')
BEGIN
    INSERT INTO admin_users (username, password, fullName, email, phone, role, isActive)
    VALUES ('superadmin', 'admin2025', N'المدير العام للنظام', '<EMAIL>', '**********', 'SuperAdmin', 1);
    PRINT 'تم إضافة المدير العام الافتراضي';
END

-- جدول الصيدليات المسجلة
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='registered_pharmacies' AND xtype='U')
BEGIN
    CREATE TABLE registered_pharmacies (
        id int IDENTITY(1,1) PRIMARY KEY,
        pharmacyName nvarchar(255) NOT NULL,
        pharmacyCode nvarchar(50) UNIQUE NOT NULL,
        ownerName nvarchar(255) NOT NULL,
        ownerPhone nvarchar(20) NOT NULL,
        ownerEmail nvarchar(255) NULL,
        address nvarchar(500) NOT NULL,
        city nvarchar(100) NOT NULL,
        region nvarchar(100) NULL,
        licenseNumber nvarchar(100) NOT NULL,
        subscriptionType nvarchar(50) DEFAULT 'Basic',
        subscriptionStartDate datetime DEFAULT GETDATE(),
        subscriptionEndDate datetime NULL,
        monthlyFee decimal(10,2) DEFAULT 0.00,
        status nvarchar(50) DEFAULT 'Pending',
        isActive bit DEFAULT 0,
        registrationDate datetime DEFAULT GETDATE(),
        approvedDate datetime NULL,
        approvedBy int NULL,
        lastBackupDate datetime NULL
    );
    PRINT 'تم إنشاء جدول registered_pharmacies';
END

-- جدول خطط الاشتراك
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='subscription_plans' AND xtype='U')
BEGIN
    CREATE TABLE subscription_plans (
        id int IDENTITY(1,1) PRIMARY KEY,
        planName nvarchar(100) NOT NULL,
        planNameAr nvarchar(100) NOT NULL,
        description nvarchar(500) NULL,
        monthlyPrice decimal(10,2) NOT NULL,
        maxUsers int DEFAULT 5,
        maxMedicines int DEFAULT 1000,
        hasNetworkAccess bit DEFAULT 1,
        hasReports bit DEFAULT 1,
        hasBackup bit DEFAULT 1,
        supportLevel nvarchar(50) DEFAULT 'Basic',
        isActive bit DEFAULT 1,
        createdDate datetime DEFAULT GETDATE()
    );
    
    INSERT INTO subscription_plans (planName, planNameAr, description, monthlyPrice, maxUsers, maxMedicines, hasNetworkAccess, hasReports, hasBackup, supportLevel)
    VALUES 
    ('Basic', N'أساسي', N'خطة أساسية للصيدليات الصغيرة', 99.00, 3, 500, 1, 1, 0, 'Basic'),
    ('Standard', N'قياسي', N'خطة قياسية للصيدليات المتوسطة', 199.00, 5, 1000, 1, 1, 1, 'Standard'),
    ('Premium', N'مميز', N'خطة مميزة للصيدليات الكبيرة', 299.00, 10, 5000, 1, 1, 1, 'Premium'),
    ('Enterprise', N'مؤسسي', N'خطة مؤسسية للسلاسل الكبيرة', 499.00, 50, 50000, 1, 1, 1, 'Enterprise');
    
    PRINT 'تم إنشاء جدول subscription_plans وإضافة الخطط';
END

-- جدول مدفوعات الاشتراكات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='subscription_payments' AND xtype='U')
BEGIN
    CREATE TABLE subscription_payments (
        id int IDENTITY(1,1) PRIMARY KEY,
        pharmacyId int NOT NULL,
        planId int NOT NULL,
        amount decimal(10,2) NOT NULL,
        paymentDate datetime DEFAULT GETDATE(),
        paymentMethod nvarchar(50) NULL,
        transactionId nvarchar(100) NULL,
        status nvarchar(50) DEFAULT 'Completed',
        validFrom datetime NOT NULL,
        validTo datetime NOT NULL,
        notes nvarchar(500) NULL,
        createdBy int NULL
    );
    PRINT 'تم إنشاء جدول subscription_payments';
END

-- جدول النسخ الاحتياطية
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='backup_history' AND xtype='U')
BEGIN
    CREATE TABLE backup_history (
        id int IDENTITY(1,1) PRIMARY KEY,
        pharmacyId int NOT NULL,
        backupType nvarchar(50) NOT NULL,
        backupPath nvarchar(500) NOT NULL,
        backupSize bigint NULL,
        backupDate datetime DEFAULT GETDATE(),
        status nvarchar(50) DEFAULT 'Completed',
        errorMessage nvarchar(1000) NULL,
        createdBy int NULL
    );
    PRINT 'تم إنشاء جدول backup_history';
END

-- جدول سجل النشاطات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='activity_log' AND xtype='U')
BEGIN
    CREATE TABLE activity_log (
        id int IDENTITY(1,1) PRIMARY KEY,
        adminUserId int NULL,
        pharmacyId int NULL,
        activityType nvarchar(100) NOT NULL,
        description nvarchar(500) NOT NULL,
        details nvarchar(2000) NULL,
        activityDate datetime DEFAULT GETDATE()
    );
    PRINT 'تم إنشاء جدول activity_log';
END

-- جدول إعدادات النظام
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='system_settings' AND xtype='U')
BEGIN
    CREATE TABLE system_settings (
        id int IDENTITY(1,1) PRIMARY KEY,
        settingKey nvarchar(100) UNIQUE NOT NULL,
        settingValue nvarchar(1000) NULL,
        description nvarchar(500) NULL,
        category nvarchar(100) NULL,
        isEditable bit DEFAULT 1,
        lastModified datetime DEFAULT GETDATE()
    );
    
    INSERT INTO system_settings (settingKey, settingValue, description, category, isEditable)
    VALUES 
    ('SystemName', N'نظام إدارة الصيدليات المركزي', N'اسم النظام', 'General', 1),
    ('SystemVersion', '1.0.0', N'إصدار النظام', 'General', 0),
    ('MaxPharmacies', '1000', N'الحد الأقصى للصيدليات المسجلة', 'Limits', 1),
    ('SupportEmail', '<EMAIL>', N'بريد الدعم الفني', 'Contact', 1),
    ('SupportPhone', '**********', N'هاتف الدعم الفني', 'Contact', 1);
    
    PRINT 'تم إنشاء جدول system_settings وإضافة الإعدادات';
END

-- إضافة بيانات تجريبية للصيدليات
IF NOT EXISTS (SELECT * FROM registered_pharmacies WHERE pharmacyCode = 'DEMO001')
BEGIN
    INSERT INTO registered_pharmacies (pharmacyName, pharmacyCode, ownerName, ownerPhone, ownerEmail, address, city, region, licenseNumber, subscriptionType, status, isActive, subscriptionEndDate, monthlyFee)
    VALUES 
    (N'صيدلية النهضة', 'DEMO001', N'أحمد محمد', '**********', '<EMAIL>', N'شارع الملك فهد', N'الرياض', N'الرياض', 'LIC001', 'Premium', 'Approved', 1, DATEADD(MONTH, 6, GETDATE()), 299.00),
    (N'صيدلية الشفاء', 'DEMO002', N'فاطمة علي', '**********', '<EMAIL>', N'شارع الأمير سلطان', N'جدة', N'مكة المكرمة', 'LIC002', 'Standard', 'Approved', 1, DATEADD(MONTH, 3, GETDATE()), 199.00),
    (N'صيدلية الأمل', 'DEMO003', N'محمد سعد', '**********', '<EMAIL>', N'شارع الملك عبدالعزيز', N'الدمام', N'الشرقية', 'LIC003', 'Basic', 'Pending', 0, NULL, 99.00);
    
    PRINT 'تم إضافة صيدليات تجريبية';
END

PRINT '';
PRINT '========================================';
PRINT 'تم إعداد قاعدة البيانات بنجاح!';
PRINT 'بيانات تسجيل الدخول:';
PRINT 'اسم المستخدم: superadmin';
PRINT 'كلمة المرور: admin2025';
PRINT '========================================';
