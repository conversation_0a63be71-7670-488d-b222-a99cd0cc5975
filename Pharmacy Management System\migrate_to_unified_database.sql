-- نقل جميع البيانات من قواعد البيانات القديمة إلى UnifiedPharmacy
-- Migrate all data from old databases to UnifiedPharmacy

USE master;
GO

PRINT '========================================';
PRINT '   نقل البيانات إلى قاعدة البيانات الموحدة';
PRINT '   Migrating to Unified Database';
PRINT '========================================';

-- إنشاء قاعدة البيانات الموحدة إذا لم تكن موجودة
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'UnifiedPharmacy')
BEGIN
    CREATE DATABASE UnifiedPharmacy;
    PRINT '✅ تم إنشاء قاعدة البيانات UnifiedPharmacy';
END
ELSE
BEGIN
    PRINT '✅ قاعدة البيانات UnifiedPharmacy موجودة بالفعل';
END
GO

USE UnifiedPharmacy;
GO

-- 1. إنشاء جدول الصيدليات
PRINT '';
PRINT '1. إنشاء جدول الصيدليات...';
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies' AND xtype='U')
BEGIN
    CREATE TABLE pharmacies (
        id int IDENTITY(1,1) PRIMARY KEY,
        pharmacyName nvarchar(255) NOT NULL,
        pharmacyCode nvarchar(50) UNIQUE NOT NULL,
        ownerName nvarchar(255) NULL,
        address nvarchar(500) NULL,
        city nvarchar(100) NULL,
        phone nvarchar(20) NULL,
        email nvarchar(255) NULL,
        isActive bit DEFAULT 1,
        subscriptionType nvarchar(50) DEFAULT 'Basic',
        registrationDate datetime DEFAULT GETDATE()
    );
    PRINT '✅ تم إنشاء جدول pharmacies';
END

-- إضافة صيدلية افتراضية
IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacyCode = 'MAIN001')
BEGIN
    INSERT INTO pharmacies (pharmacyName, pharmacyCode, ownerName, address, city, phone, email, isActive, subscriptionType)
    VALUES (N'الصيدلية الرئيسية', 'MAIN001', N'مدير النظام', N'الرياض', N'الرياض', '0112345678', '<EMAIL>', 1, 'Premium');
    PRINT '✅ تم إضافة الصيدلية الافتراضية';
END

-- 2. إنشاء جدول المستخدمين
PRINT '';
PRINT '2. إنشاء جدول المستخدمين...';
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
BEGIN
    CREATE TABLE users (
        id int IDENTITY(1,1) PRIMARY KEY,
        userRole nvarchar(50) NOT NULL,
        name nvarchar(255) NOT NULL,
        dob date NULL,
        mobile nvarchar(20) NULL,
        email nvarchar(255) NULL,
        username nvarchar(100) UNIQUE NOT NULL,
        pass nvarchar(255) NOT NULL,
        pharmacyId int NULL,
        isActive bit DEFAULT 1,
        createdDate datetime DEFAULT GETDATE(),
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
    );
    PRINT '✅ تم إنشاء جدول users';
END

-- نقل المستخدمين من قاعدة البيانات القديمة
PRINT '';
PRINT '3. نقل المستخدمين من قاعدة البيانات القديمة...';
DECLARE @defaultPharmacyId INT;
SELECT @defaultPharmacyId = id FROM pharmacies WHERE pharmacyCode = 'MAIN001';

-- التحقق من وجود قاعدة البيانات القديمة
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'pharmacy')
BEGIN
    -- نقل المستخدمين
    INSERT INTO users (userRole, name, dob, mobile, email, username, pass, pharmacyId, isActive)
    SELECT 
        userRole, 
        name, 
        dob, 
        mobile, 
        email, 
        username, 
        pass, 
        @defaultPharmacyId,
        CASE WHEN ISNULL(isActive, 1) = 1 THEN 1 ELSE 0 END
    FROM pharmacy.dbo.users
    WHERE username NOT IN (SELECT username FROM users);
    
    PRINT '✅ تم نقل المستخدمين من قاعدة pharmacy';
END

-- إضافة مستخدمين افتراضيين
IF NOT EXISTS (SELECT * FROM users WHERE username = 'admin')
BEGIN
    INSERT INTO users (userRole, name, dob, mobile, email, username, pass, pharmacyId, isActive)
    VALUES ('Administrator', N'مدير النظام', '1990-01-01', '0501234567', '<EMAIL>', 'admin', 'admin123', @defaultPharmacyId, 1);
    PRINT '✅ تم إضافة المدير الافتراضي';
END

IF NOT EXISTS (SELECT * FROM users WHERE username = 'pharmacist')
BEGIN
    INSERT INTO users (userRole, name, dob, mobile, email, username, pass, pharmacyId, isActive)
    VALUES ('Pharmacist', N'صيدلي النظام', '1992-01-01', '0507654321', '<EMAIL>', 'pharmacist', 'pharm123', @defaultPharmacyId, 1);
    PRINT '✅ تم إضافة الصيدلي الافتراضي';
END

-- 4. إنشاء جدول الأدوية
PRINT '';
PRINT '4. إنشاء جدول الأدوية...';
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='medic' AND xtype='U')
BEGIN
    CREATE TABLE medic (
        id int IDENTITY(1,1) PRIMARY KEY,
        mid nvarchar(50) NULL,
        mname nvarchar(255) NOT NULL,
        mnumber nvarchar(100) NULL,
        mDate date NULL,
        eDate date NULL,
        quantity int DEFAULT 0,
        perUnit decimal(10,2) DEFAULT 0,
        lu nvarchar(100) NULL,
        br nvarchar(100) NULL,
        originalQuantity int DEFAULT 0,
        originalNewQuantity int DEFAULT 0,
        newEDate date NULL,
        newQuantity int DEFAULT 0,
        allqun int DEFAULT 0,
        mnumber_qty int DEFAULT 0,
        newMDate date NULL,
        dos2 nvarchar(100) NULL,
        dos2_qty int DEFAULT 0,
        dos3 nvarchar(100) NULL,
        dos3_qty int DEFAULT 0,
        dos4 nvarchar(100) NULL,
        dos4_qty int DEFAULT 0,
        pharmacyId int NULL,
        isActive bit DEFAULT 1,
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
    );
    PRINT '✅ تم إنشاء جدول medic';
END

-- نقل الأدوية من قاعدة البيانات القديمة
PRINT '';
PRINT '5. نقل الأدوية من قاعدة البيانات القديمة...';
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'pharmacy')
BEGIN
    -- نقل الأدوية
    INSERT INTO medic (mid, mname, mnumber, mDate, eDate, quantity, perUnit, lu, br, originalQuantity, originalNewQuantity, 
                       newEDate, newQuantity, allqun, mnumber_qty, newMDate, dos2, dos2_qty, dos3, dos3_qty, dos4, dos4_qty, pharmacyId, isActive)
    SELECT 
        mid, mname, mnumber, mDate, eDate, quantity, perUnit, lu, br, 
        ISNULL(originalQuantity, quantity),
        ISNULL(originalNewQuantity, quantity),
        newEDate, newQuantity, allqun, mnumber_qty, newMDate,
        dos2, dos2_qty, dos3, dos3_qty, dos4, dos4_qty,
        @defaultPharmacyId,
        1
    FROM pharmacy.dbo.medic
    WHERE mname NOT IN (SELECT mname FROM medic WHERE pharmacyId = @defaultPharmacyId);
    
    PRINT '✅ تم نقل الأدوية من قاعدة pharmacy';
END

-- 6. إنشاء جدول المبيعات
PRINT '';
PRINT '6. إنشاء جدول المبيعات...';
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='sales' AND xtype='U')
BEGIN
    CREATE TABLE sales (
        id int IDENTITY(1,1) PRIMARY KEY,
        employeeName nvarchar(255) NULL,
        medicineName nvarchar(255) NULL,
        quantity int NULL,
        unitPrice decimal(10,2) NULL,
        totalPrice decimal(10,2) NULL,
        saleDate datetime DEFAULT GETDATE(),
        dosage nvarchar(100) NULL,
        pharmacyId int NULL,
        userId int NULL,
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id),
        FOREIGN KEY (userId) REFERENCES users(id)
    );
    PRINT '✅ تم إنشاء جدول sales';
END

-- نقل المبيعات من قاعدة البيانات القديمة
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'pharmacy')
BEGIN
    INSERT INTO sales (employeeName, medicineName, quantity, unitPrice, totalPrice, saleDate, dosage, pharmacyId)
    SELECT 
        employeeName, medicineName, quantity, unitPrice, totalPrice, saleDate, dosage, @defaultPharmacyId
    FROM pharmacy.dbo.sales;
    
    PRINT '✅ تم نقل المبيعات من قاعدة pharmacy';
END

-- 7. إنشاء جدول جلسات الموظفين
PRINT '';
PRINT '7. إنشاء جدول جلسات الموظفين...';
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='employee_sessions' AND xtype='U')
BEGIN
    CREATE TABLE employee_sessions (
        id int IDENTITY(1,1) PRIMARY KEY,
        username nvarchar(255) NULL,
        employeeName nvarchar(255) NULL,
        loginTime datetime NULL,
        logoutTime datetime NULL,
        sessionDate date NULL,
        pharmacyId int NULL,
        userId int NULL,
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id),
        FOREIGN KEY (userId) REFERENCES users(id)
    );
    PRINT '✅ تم إنشاء جدول employee_sessions';
END

-- 8. إنشاء جدول أدوية الشبكة
PRINT '';
PRINT '8. إنشاء جدول أدوية الشبكة...';
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='networkmedicines' AND xtype='U')
BEGIN
    CREATE TABLE networkmedicines (
        id int IDENTITY(1,1) PRIMARY KEY,
        pharmacyId int NOT NULL,
        medicineName nvarchar(255) NOT NULL,
        manufacturer nvarchar(255) NULL,
        category nvarchar(100) NULL,
        pricePerUnit decimal(10,2) NOT NULL DEFAULT 0.0,
        availableQuantity int NOT NULL,
        expiryDate datetime NOT NULL,
        description nvarchar(500) NULL DEFAULT '',
        dateAdded datetime DEFAULT GETDATE(),
        isActive bit DEFAULT 1,
        isAvailableForSale bit DEFAULT 1,
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
    );
    PRINT '✅ تم إنشاء جدول networkmedicines';
END

-- إضافة أدوية تجريبية للشبكة
IF NOT EXISTS (SELECT * FROM networkmedicines)
BEGIN
    INSERT INTO networkmedicines (pharmacyId, medicineName, manufacturer, category, pricePerUnit, availableQuantity, expiryDate, description, isActive, isAvailableForSale)
    VALUES
    (@defaultPharmacyId, N'باراسيتامول 500 مجم', N'شركة الدواء السعودية', N'مسكنات', 15.50, 100, DATEADD(YEAR, 2, GETDATE()), N'مسكن للألم وخافض للحرارة', 1, 1),
    (@defaultPharmacyId, N'أموكسيسيلين 250 مجم', N'شركة المضادات الحيوية', N'مضادات حيوية', 45.00, 50, DATEADD(YEAR, 1, GETDATE()), N'مضاد حيوي واسع المجال', 1, 1),
    (@defaultPharmacyId, N'إيبوبروفين 400 مجم', N'شركة الأدوية المتقدمة', N'مضادات الالتهاب', 25.75, 75, DATEADD(MONTH, 18, GETDATE()), N'مضاد للالتهاب ومسكن', 1, 1),
    (@defaultPharmacyId, N'أوميبرازول 20 مجم', N'شركة الجهاز الهضمي', N'أدوية الجهاز الهضمي', 35.25, 60, DATEADD(YEAR, 2, GETDATE()), N'لعلاج قرحة المعدة', 1, 1),
    (@defaultPharmacyId, N'لوراتادين 10 مجم', N'شركة الحساسية', N'مضادات الحساسية', 18.90, 80, DATEADD(MONTH, 15, GETDATE()), N'لعلاج الحساسية', 1, 1);
    
    PRINT '✅ تم إضافة أدوية تجريبية للشبكة';
END

-- 9. إنشاء جدول إعدادات الطباعة
PRINT '';
PRINT '9. إنشاء جدول إعدادات الطباعة...';
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='print_settings' AND xtype='U')
BEGIN
    CREATE TABLE print_settings (
        id int IDENTITY(1,1) PRIMARY KEY,
        reportType nvarchar(100) NOT NULL,
        paperSize nvarchar(50) DEFAULT 'A4',
        orientation nvarchar(20) DEFAULT 'Portrait',
        marginTop int DEFAULT 20,
        marginBottom int DEFAULT 20,
        marginLeft int DEFAULT 20,
        marginRight int DEFAULT 20,
        fontSize int DEFAULT 12,
        fontFamily nvarchar(100) DEFAULT 'Arial',
        headerText nvarchar(500) NULL,
        footerText nvarchar(500) NULL,
        showLogo bit DEFAULT 1,
        showDate bit DEFAULT 1,
        pharmacyId int NULL,
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
    );
    PRINT '✅ تم إنشاء جدول print_settings';
END

PRINT '';
PRINT '========================================';
PRINT '✅ تم الانتهاء من نقل جميع البيانات';
PRINT '✅ Migration completed successfully';
PRINT '========================================';
PRINT '';
PRINT 'قاعدة البيانات الموحدة UnifiedPharmacy جاهزة للاستخدام';
PRINT 'Unified database UnifiedPharmacy is ready to use';
PRINT '';
PRINT 'الجداول المنشأة:';
PRINT '- pharmacies: الصيدليات';
PRINT '- users: المستخدمين';
PRINT '- medic: الأدوية';
PRINT '- sales: المبيعات';
PRINT '- employee_sessions: جلسات الموظفين';
PRINT '- networkmedicines: أدوية الشبكة';
PRINT '- print_settings: إعدادات الطباعة';
PRINT '';
PRINT 'البيانات الافتراضية:';
PRINT '- صيدلية رئيسية (MAIN001)';
PRINT '- مدير (admin/admin123)';
PRINT '- صيدلي (pharmacist/pharm123)';
PRINT '- 5 أدوية تجريبية للشبكة';
