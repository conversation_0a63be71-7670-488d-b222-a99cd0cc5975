-- إضافة بيانات تجريبية للشبكة الأونلاين
-- Add Sample Data for Online Network

USE PharmacyNetworkOnline;
GO

PRINT 'إضافة بيانات تجريبية للشبكة الأونلاين...';

-- إضافة صيدليات إضافية
INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, licenseNumber, address, city, region, phone, email, isActive, registrationDate, lastOnline, subscriptionType)
VALUES 
('PH002', 'صيدلية الشفاء', 'فاطمة علي', 'LIC002', 'شارع المعز، الأزهر', 'القاهرة', 'القاهرة الكبرى', '01987654321', '<EMAIL>', 1, GETDATE(), GETDATE(), 'Basic'),
('PH003', 'صيدلية الصحة', 'محمد حسن', 'LIC003', 'شارع النيل، الدقي', 'الجيزة', 'الجيزة', '01122334455', '<EMAIL>', 1, GETDATE(), GETDATE(), 'Premium'),
('PH004', 'صيدلية الأمل', 'سارة أحمد', 'LIC004', 'شارع الكورنيش، سيدي جابر', 'الإسكندرية', 'الإسكندرية', '01555666777', '<EMAIL>', 1, GETDATE(), GETDATE(), 'Basic');

-- إضافة مستخدمين للصيدليات الجديدة
INSERT INTO network_users (pharmacyId, userRole, name, username, passwordHash, email, isActive, createdAt, updatedAt)
VALUES 
(2, 'Admin', 'فاطمة علي', 'admin_shifa', 'hashed_password_456', '<EMAIL>', 1, GETDATE(), GETDATE()),
(3, 'Admin', 'محمد حسن', 'admin_seha', 'hashed_password_789', '<EMAIL>', 1, GETDATE(), GETDATE()),
(4, 'Admin', 'سارة أحمد', 'admin_amal', 'hashed_password_101', '<EMAIL>', 1, GETDATE(), GETDATE());

-- إضافة أدوية من صيدليات مختلفة
INSERT INTO networkmedicines (pharmacyId, localMedicineId, medicineName, manufacturer, category, dosageForm, strength, availableQuantity, unitPrice, wholesalePrice, expiryDate, isAvailableForSale, requiresPrescription)
VALUES 
-- أدوية من صيدلية الشفاء (pharmacyId = 2)
(2, 'MED006', 'أسبرين', 'شركة باير', 'مسكنات', 'أقراص', '100mg', 120, 3.25, 2.75, '2025-09-15', 1, 0),
(2, 'MED007', 'أوميجا 3', 'شركة فايزر', 'مكملات غذائية', 'كبسولات', '1000mg', 60, 25.50, 22.00, '2026-01-10', 1, 0),
(2, 'MED008', 'لوراتادين', 'شركة كلاريتين', 'مضادات الحساسية', 'أقراص', '10mg', 90, 12.75, 11.25, '2025-07-20', 1, 0),
(2, 'MED009', 'ديكلوفيناك', 'شركة نوفارتيس', 'مضادات الالتهاب', 'جل', '1%', 45, 18.00, 16.50, '2025-12-05', 1, 0),

-- أدوية من صيدلية الصحة (pharmacyId = 3)
(3, 'MED010', 'سيتريزين', 'شركة يوسي بي', 'مضادات الحساسية', 'شراب', '5mg/5ml', 35, 14.25, 12.75, '2025-10-30', 1, 0),
(3, 'MED011', 'كالسيوم + فيتامين د', 'شركة سانوفي', 'فيتامينات', 'أقراص', '600mg+400IU', 80, 22.00, 19.50, '2026-02-15', 1, 0),
(3, 'MED012', 'مضاد حيوي أزيثروميسين', 'شركة فايزر', 'مضادات حيوية', 'كبسولات', '250mg', 40, 35.75, 32.00, '2025-11-10', 1, 1),
(3, 'MED013', 'شامبو طبي', 'شركة جونسون', 'منتجات العناية', 'شامبو', '200ml', 25, 28.50, 25.75, '2026-04-20', 1, 0),

-- أدوية من صيدلية الأمل (pharmacyId = 4)
(4, 'MED014', 'فيتامين ب المركب', 'شركة إيفا فارما', 'فيتامينات', 'أقراص', 'مركب', 100, 16.00, 14.25, '2025-08-25', 1, 0),
(4, 'MED015', 'كريم مرطب', 'شركة نيفيا', 'منتجات العناية', 'كريم', '100ml', 50, 19.75, 17.50, '2026-06-30', 1, 0),
(4, 'MED016', 'دواء الضغط', 'شركة سيرفير', 'أدوية القلب', 'أقراص', '5mg', 70, 42.50, 38.75, '2025-09-05', 1, 1),
(4, 'MED017', 'شراب الحديد', 'شركة سبيماكو', 'مكملات غذائية', 'شراب', '15mg/5ml', 30, 21.25, 19.00, '2025-12-15', 1, 0),

-- أدوية إضافية من صيدلية النور (pharmacyId = 1)
(1, 'MED018', 'مضاد للفطريات', 'شركة جانسن', 'مضادات الفطريات', 'كريم', '1%', 40, 24.75, 22.25, '2025-10-10', 1, 0),
(1, 'MED019', 'فيتامين سي فوار', 'شركة يوروفيتال', 'فيتامينات', 'أقراص فوارة', '1000mg', 65, 13.50, 12.00, '2026-03-05', 1, 0),
(1, 'MED020', 'مسكن للأطفال', 'شركة جونسون', 'مسكنات', 'شراب', '120mg/5ml', 55, 16.25, 14.75, '2025-11-20', 1, 0);

PRINT 'تم إضافة الأدوية التجريبية بنجاح!';

-- عرض إحصائيات
SELECT 
    'الصيدليات' as النوع,
    COUNT(*) as العدد
FROM pharmacies
WHERE isActive = 1

UNION ALL

SELECT 
    'المستخدمين' as النوع,
    COUNT(*) as العدد
FROM network_users
WHERE isActive = 1

UNION ALL

SELECT 
    'الأدوية المتاحة' as النوع,
    COUNT(*) as العدد
FROM networkmedicines
WHERE isAvailableForSale = 1;

-- عرض الأدوية حسب الصيدلية
SELECT 
    p.pharmacyName as 'اسم الصيدلية',
    COUNT(m.id) as 'عدد الأدوية',
    SUM(m.availableQuantity) as 'إجمالي الكمية'
FROM pharmacies p
LEFT JOIN networkmedicines m ON p.id = m.pharmacyId AND m.isAvailableForSale = 1
WHERE p.isActive = 1
GROUP BY p.id, p.pharmacyName
ORDER BY p.pharmacyName;

PRINT 'تم إعداد البيانات التجريبية بنجاح!';
PRINT 'يمكنك الآن رؤية الأدوية في المتجر الأونلاين.';
