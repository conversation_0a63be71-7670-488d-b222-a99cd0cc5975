using System;
using System.Collections.Generic;
using System.Data;

namespace Pharmacy_Admin_System
{
    /// <summary>
    /// مدير الصيدليات - إدارة الصيدليات المسجلة في النظام
    /// Pharmacy Manager - Manages registered pharmacies in the system
    /// </summary>
    public class PharmacyManager
    {
        private DatabaseManager dbManager;

        public PharmacyManager()
        {
            dbManager = new DatabaseManager();
        }

        #region Pharmacy Registration

        /// <summary>
        /// تسجيل صيدلية جديدة
        /// </summary>
        public int RegisterPharmacy(PharmacyRegistrationData data)
        {
            try
            {
                string command = @"
                    INSERT INTO registered_pharmacies 
                    (pharmacyName, pharmacyCode, ownerName, ownerPhone, ownerEmail, address, city, region, 
                     licenseNumber, taxNumber, subscriptionType, status, isActive, registrationDate)
                    VALUES 
                    (@pharmacyName, @pharmacyCode, @ownerName, @ownerPhone, @ownerEmail, @address, @city, @region,
                     @licenseNumber, @taxNumber, @subscriptionType, 'Pending', 0, GETDATE());
                    SELECT SCOPE_IDENTITY();";

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyName", data.PharmacyName},
                    {"@pharmacyCode", data.PharmacyCode},
                    {"@ownerName", data.OwnerName},
                    {"@ownerPhone", data.OwnerPhone},
                    {"@ownerEmail", data.OwnerEmail},
                    {"@address", data.Address},
                    {"@city", data.City},
                    {"@region", data.Region},
                    {"@licenseNumber", data.LicenseNumber},
                    {"@taxNumber", data.TaxNumber},
                    {"@subscriptionType", data.SubscriptionType}
                };

                var result = dbManager.ExecuteAdminScalar(command, parameters);
                return Convert.ToInt32(result);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تسجيل الصيدلية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الموافقة على تسجيل صيدلية
        /// </summary>
        public void ApprovePharmacy(int pharmacyId, int approvedBy, string notes = null)
        {
            try
            {
                string command = @"
                    UPDATE registered_pharmacies 
                    SET status = 'Approved', isActive = 1, approvedDate = GETDATE(), 
                        approvedBy = @approvedBy, notes = @notes
                    WHERE id = @pharmacyId";

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", pharmacyId},
                    {"@approvedBy", approvedBy},
                    {"@notes", notes}
                };

                dbManager.ExecuteAdminCommand(command, parameters);

                // تسجيل النشاط
                dbManager.LogActivity(approvedBy, pharmacyId, "Pharmacy Approved", 
                    $"تم الموافقة على تسجيل الصيدلية", notes);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الموافقة على الصيدلية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// رفض تسجيل صيدلية
        /// </summary>
        public void RejectPharmacy(int pharmacyId, int rejectedBy, string rejectionReason)
        {
            try
            {
                string command = @"
                    UPDATE registered_pharmacies 
                    SET status = 'Rejected', rejectionReason = @rejectionReason, approvedBy = @rejectedBy
                    WHERE id = @pharmacyId";

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", pharmacyId},
                    {"@rejectedBy", rejectedBy},
                    {"@rejectionReason", rejectionReason}
                };

                dbManager.ExecuteAdminCommand(command, parameters);

                // تسجيل النشاط
                dbManager.LogActivity(rejectedBy, pharmacyId, "Pharmacy Rejected", 
                    $"تم رفض تسجيل الصيدلية", rejectionReason);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في رفض الصيدلية: {ex.Message}", ex);
            }
        }

        #endregion

        #region Pharmacy Management

        /// <summary>
        /// الحصول على جميع الصيدليات
        /// </summary>
        public DataTable GetAllPharmacies()
        {
            try
            {
                string query = @"
                    SELECT 
                        rp.*,
                        au.fullName as approvedByName,
                        sp.planNameAr as subscriptionPlanName,
                        sp.monthlyPrice
                    FROM registered_pharmacies rp
                    LEFT JOIN admin_users au ON rp.approvedBy = au.id
                    LEFT JOIN subscription_plans sp ON rp.subscriptionType = sp.planName
                    ORDER BY rp.registrationDate DESC";

                return dbManager.ExecuteAdminQuery(query);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب بيانات الصيدليات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على الصيدليات المعلقة للموافقة
        /// </summary>
        public DataTable GetPendingPharmacies()
        {
            try
            {
                string query = @"
                    SELECT 
                        id, pharmacyName, pharmacyCode, ownerName, ownerPhone, 
                        city, licenseNumber, subscriptionType, registrationDate
                    FROM registered_pharmacies 
                    WHERE status = 'Pending'
                    ORDER BY registrationDate ASC";

                return dbManager.ExecuteAdminQuery(query);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب الصيدليات المعلقة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تعليق صيدلية
        /// </summary>
        public void SuspendPharmacy(int pharmacyId, int suspendedBy, string reason)
        {
            try
            {
                string command = @"
                    UPDATE registered_pharmacies 
                    SET status = 'Suspended', isActive = 0, notes = @reason
                    WHERE id = @pharmacyId";

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", pharmacyId},
                    {"@reason", reason}
                };

                dbManager.ExecuteAdminCommand(command, parameters);

                // تسجيل النشاط
                dbManager.LogActivity(suspendedBy, pharmacyId, "Pharmacy Suspended", 
                    $"تم تعليق الصيدلية", reason);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تعليق الصيدلية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إعادة تفعيل صيدلية
        /// </summary>
        public void ReactivatePharmacy(int pharmacyId, int reactivatedBy)
        {
            try
            {
                string command = @"
                    UPDATE registered_pharmacies 
                    SET status = 'Approved', isActive = 1
                    WHERE id = @pharmacyId";

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", pharmacyId}
                };

                dbManager.ExecuteAdminCommand(command, parameters);

                // تسجيل النشاط
                dbManager.LogActivity(reactivatedBy, pharmacyId, "Pharmacy Reactivated", 
                    $"تم إعادة تفعيل الصيدلية");
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إعادة تفعيل الصيدلية: {ex.Message}", ex);
            }
        }

        #endregion

        #region Statistics

        /// <summary>
        /// الحصول على إحصائيات الصيدليات
        /// </summary>
        public PharmacyStatistics GetPharmacyStatistics()
        {
            try
            {
                var stats = new PharmacyStatistics();

                // إجمالي الصيدليات
                var totalQuery = "SELECT COUNT(*) FROM registered_pharmacies";
                stats.TotalPharmacies = Convert.ToInt32(dbManager.ExecuteAdminScalar(totalQuery));

                // الصيدليات النشطة
                var activeQuery = "SELECT COUNT(*) FROM registered_pharmacies WHERE status = 'Approved' AND isActive = 1";
                stats.ActivePharmacies = Convert.ToInt32(dbManager.ExecuteAdminScalar(activeQuery));

                // الصيدليات المعلقة
                var pendingQuery = "SELECT COUNT(*) FROM registered_pharmacies WHERE status = 'Pending'";
                stats.PendingPharmacies = Convert.ToInt32(dbManager.ExecuteAdminScalar(pendingQuery));

                // الصيدليات المعلقة
                var suspendedQuery = "SELECT COUNT(*) FROM registered_pharmacies WHERE status = 'Suspended'";
                stats.SuspendedPharmacies = Convert.ToInt32(dbManager.ExecuteAdminScalar(suspendedQuery));

                // الصيدليات المرفوضة
                var rejectedQuery = "SELECT COUNT(*) FROM registered_pharmacies WHERE status = 'Rejected'";
                stats.RejectedPharmacies = Convert.ToInt32(dbManager.ExecuteAdminScalar(rejectedQuery));

                return stats;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب إحصائيات الصيدليات: {ex.Message}", ex);
            }
        }

        #endregion

        #region Search and Filter

        /// <summary>
        /// البحث في الصيدليات
        /// </summary>
        public DataTable SearchPharmacies(string searchTerm, string status = null, string city = null)
        {
            try
            {
                string query = @"
                    SELECT 
                        rp.*,
                        au.fullName as approvedByName,
                        sp.planNameAr as subscriptionPlanName
                    FROM registered_pharmacies rp
                    LEFT JOIN admin_users au ON rp.approvedBy = au.id
                    LEFT JOIN subscription_plans sp ON rp.subscriptionType = sp.planName
                    WHERE 1=1";

                var parameters = new Dictionary<string, object>();

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query += " AND (rp.pharmacyName LIKE @searchTerm OR rp.ownerName LIKE @searchTerm OR rp.pharmacyCode LIKE @searchTerm)";
                    parameters.Add("@searchTerm", $"%{searchTerm}%");
                }

                if (!string.IsNullOrEmpty(status))
                {
                    query += " AND rp.status = @status";
                    parameters.Add("@status", status);
                }

                if (!string.IsNullOrEmpty(city))
                {
                    query += " AND rp.city = @city";
                    parameters.Add("@city", city);
                }

                query += " ORDER BY rp.registrationDate DESC";

                return dbManager.ExecuteAdminQuery(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث في الصيدليات: {ex.Message}", ex);
            }
        }

        #endregion
    }

    #region Data Classes

    /// <summary>
    /// بيانات تسجيل صيدلية جديدة
    /// </summary>
    public class PharmacyRegistrationData
    {
        public string PharmacyName { get; set; }
        public string PharmacyCode { get; set; }
        public string OwnerName { get; set; }
        public string OwnerPhone { get; set; }
        public string OwnerEmail { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string Region { get; set; }
        public string LicenseNumber { get; set; }
        public string TaxNumber { get; set; }
        public string SubscriptionType { get; set; }
    }

    /// <summary>
    /// إحصائيات الصيدليات
    /// </summary>
    public class PharmacyStatistics
    {
        public int TotalPharmacies { get; set; }
        public int ActivePharmacies { get; set; }
        public int PendingPharmacies { get; set; }
        public int SuspendedPharmacies { get; set; }
        public int RejectedPharmacies { get; set; }
    }

    #endregion
}
