-- إعداد قاعدة البيانات الأونلاين الكاملة
-- Complete Online Database Setup

-- إنشاء قاعدة البيانات إذا لم تكن موجودة
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'PharmacyNetworkOnline')
BEGIN
    CREATE DATABASE PharmacyNetworkOnline;
    PRINT 'تم إنشاء قاعدة البيانات PharmacyNetworkOnline';
END
GO

USE PharmacyNetworkOnline;
GO

PRINT '========================================';
PRINT 'إعداد قاعدة البيانات الأونلاين الكاملة';
PRINT '========================================';

-- ===================================
-- 1. جدول الصيدليات
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies' AND xtype='U')
BEGIN
    CREATE TABLE pharmacies (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyCode VARCHAR(20) UNIQUE NOT NULL,
        pharmacyName NVARCHAR(250) NOT NULL,
        ownerName NVARCHAR(250) NOT NULL,
        licenseNumber VARCHAR(100) UNIQUE NOT NULL,
        address NVARCHAR(500) NOT NULL,
        city NVARCHAR(100) NOT NULL,
        region NVARCHAR(100) NOT NULL,
        phone VARCHAR(20) NOT NULL,
        email VARCHAR(250) NOT NULL,
        latitude DECIMAL(10, 8) NULL,
        longitude DECIMAL(11, 8) NULL,
        isActive BIT DEFAULT 1,
        registrationDate DATETIME DEFAULT GETDATE(),
        lastOnline DATETIME DEFAULT GETDATE(),
        subscriptionType VARCHAR(50) DEFAULT 'Basic',
        subscriptionExpiry DATETIME NULL,
        connectionString NVARCHAR(500) NULL,
        apiKey VARCHAR(100) UNIQUE NULL,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE()
    );
    PRINT 'تم إنشاء جدول pharmacies';
END

-- ===================================
-- 2. جدول المستخدمين
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='network_users' AND xtype='U')
BEGIN
    CREATE TABLE network_users (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyId INT NOT NULL,
        userRole VARCHAR(50) NOT NULL,
        name NVARCHAR(250) NOT NULL,
        username VARCHAR(250) UNIQUE NOT NULL,
        passwordHash VARCHAR(500) NOT NULL,
        email VARCHAR(250) NOT NULL,
        phone VARCHAR(20) NULL,
        isActive BIT DEFAULT 1,
        lastLogin DATETIME NULL,
        permissions TEXT NULL,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
    );
    PRINT 'تم إنشاء جدول network_users';
END

-- ===================================
-- 3. جدول الأدوية
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='networkmedicines' AND xtype='U')
BEGIN
    CREATE TABLE networkmedicines (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyId INT NOT NULL,
        localMedicineId VARCHAR(250) NOT NULL,
        medicineName NVARCHAR(250) NOT NULL,
        genericName NVARCHAR(250) NULL,
        brandName NVARCHAR(250) NULL,
        manufacturer NVARCHAR(250) NOT NULL,
        category NVARCHAR(100) NULL,
        dosageForm VARCHAR(100) NULL,
        strength VARCHAR(100) NULL,
        availableQuantity INT NOT NULL,
        unitPrice DECIMAL(10, 2) NOT NULL,
        wholesalePrice DECIMAL(10, 2) NULL,
        manufacturingDate DATE NULL,
        expiryDate DATE NOT NULL,
        batchNumber VARCHAR(100) NULL,
        location NVARCHAR(100) NULL,
        isAvailableForSale BIT DEFAULT 1,
        minOrderQuantity INT DEFAULT 1,
        maxOrderQuantity INT NULL,
        description NVARCHAR(1000) NULL,
        sideEffects NVARCHAR(1000) NULL,
        contraindications NVARCHAR(1000) NULL,
        storageConditions NVARCHAR(500) NULL,
        imageUrl VARCHAR(500) NULL,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
    );
    PRINT 'تم إنشاء جدول networkmedicines';
END

-- ===================================
-- 4. جدول الطلبات بين الصيدليات
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='inter_pharmacy_orders' AND xtype='U')
BEGIN
    CREATE TABLE inter_pharmacy_orders (
        id INT IDENTITY(1,1) PRIMARY KEY,
        orderNumber VARCHAR(50) UNIQUE NOT NULL,
        buyerPharmacyId INT NOT NULL,
        sellerPharmacyId INT NOT NULL,
        orderStatus VARCHAR(50) DEFAULT 'Pending',
        totalAmount DECIMAL(12, 2) NOT NULL,
        shippingCost DECIMAL(10, 2) DEFAULT 0,
        taxAmount DECIMAL(10, 2) DEFAULT 0,
        orderDate DATETIME DEFAULT GETDATE(),
        responseDate DATETIME NULL,
        deliveryDate DATETIME NULL,
        notes NVARCHAR(1000) NULL,
        FOREIGN KEY (buyerPharmacyId) REFERENCES pharmacies(id),
        FOREIGN KEY (sellerPharmacyId) REFERENCES pharmacies(id)
    );
    PRINT 'تم إنشاء جدول inter_pharmacy_orders';
END

-- ===================================
-- 5. جدول تفاصيل الطلبات
-- ===================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='order_details' AND xtype='U')
BEGIN
    CREATE TABLE order_details (
        id INT IDENTITY(1,1) PRIMARY KEY,
        orderId INT NOT NULL,
        medicineId INT NOT NULL,
        requestedQuantity INT NOT NULL,
        approvedQuantity INT NULL,
        unitPrice DECIMAL(10, 2) NOT NULL,
        totalPrice DECIMAL(12, 2) NOT NULL,
        notes NVARCHAR(500) NULL,
        FOREIGN KEY (orderId) REFERENCES inter_pharmacy_orders(id),
        FOREIGN KEY (medicineId) REFERENCES networkmedicines(id)
    );
    PRINT 'تم إنشاء جدول order_details';
END

-- ===================================
-- إنشاء الفهارس
-- ===================================
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_networkmedicines_pharmacyId')
    CREATE INDEX IX_networkmedicines_pharmacyId ON networkmedicines(pharmacyId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_networkmedicines_medicineName')
    CREATE INDEX IX_networkmedicines_medicineName ON networkmedicines(medicineName);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_networkmedicines_isAvailableForSale')
    CREATE INDEX IX_networkmedicines_isAvailableForSale ON networkmedicines(isAvailableForSale);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_networkmedicines_expiryDate')
    CREATE INDEX IX_networkmedicines_expiryDate ON networkmedicines(expiryDate);

PRINT 'تم إنشاء الفهارس';

PRINT '========================================';
PRINT 'تم إعداد قاعدة البيانات الأونلاين بنجاح';
PRINT '========================================';

-- عرض معلومات الجداول
SELECT 
    'pharmacies' as TableName, COUNT(*) as RecordCount FROM pharmacies
UNION ALL
SELECT 
    'network_users' as TableName, COUNT(*) as RecordCount FROM network_users
UNION ALL
SELECT 
    'networkmedicines' as TableName, COUNT(*) as RecordCount FROM networkmedicines
UNION ALL
SELECT 
    'inter_pharmacy_orders' as TableName, COUNT(*) as RecordCount FROM inter_pharmacy_orders
UNION ALL
SELECT 
    'order_details' as TableName, COUNT(*) as RecordCount FROM order_details;
