-- إصلاح قاعدة البيانات المحلية
-- Fix Local Database Issues

USE pharmacy;
GO

PRINT '========================================';
PRINT 'إصلاح قاعدة البيانات المحلية';
PRINT '========================================';

-- ===================================
-- 1. التحقق من جداول قاعدة البيانات
-- ===================================

-- التحقق من جدول المستخدمين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
BEGIN
    CREATE TABLE users(
        id int identity (1,1) primary key,
        userRole varchar(50) not null,
        name varchar (250) not null,
        dob varchar (250) not null,
        mobile bigint not null,
        email varchar (250) not null,
        username varchar (250) unique not null,
        pass varchar(250) not null
    );
    PRINT 'تم إنشاء جدول users';
END
ELSE
BEGIN
    PRINT 'جدول users موجود';
END

-- التحقق من جدول الأدوية
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='medic' AND xtype='U')
BEGIN
    CREATE TABLE medic(
        id int identity(1,1) primary key,
        mid varchar(250) not null,
        mname varchar (250) not null,
        mnumber varchar (250) not null,
        mDate varchar (250) not null,
        eDate varchar(250) not null,
        quantity bigint not null,
        perUnit bigint not null,
        lu varchar(250) not null,
        br varchar(250) not null
    );
    PRINT 'تم إنشاء جدول medic';
END
ELSE
BEGIN
    PRINT 'جدول medic موجود';
END

-- إضافة الأعمدة المفقودة لجدول medic إذا لم تكن موجودة
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'newEDate')
    ALTER TABLE medic ADD newEDate VARCHAR(250) NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'newQuantity')
    ALTER TABLE medic ADD newQuantity BIGINT NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'allqun')
    ALTER TABLE medic ADD allqun BIGINT NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'mnumber_qty')
    ALTER TABLE medic ADD mnumber_qty INT NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'newMDate')
    ALTER TABLE medic ADD newMDate VARCHAR(250) NULL;

-- إضافة أعمدة الجرعات
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'dos2')
    ALTER TABLE medic ADD dos2 VARCHAR(100) NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'dos2_qty')
    ALTER TABLE medic ADD dos2_qty INT NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'dos3')
    ALTER TABLE medic ADD dos3 VARCHAR(100) NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'dos3_qty')
    ALTER TABLE medic ADD dos3_qty INT NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'dos4')
    ALTER TABLE medic ADD dos4 VARCHAR(100) NULL;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'dos4_qty')
    ALTER TABLE medic ADD dos4_qty INT NULL;

-- إضافة أعمدة الكميات الأصلية
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'originalQuantity')
    ALTER TABLE medic ADD originalQuantity BIGINT DEFAULT 0;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'originalNewQuantity')
    ALTER TABLE medic ADD originalNewQuantity BIGINT DEFAULT 0;

PRINT 'تم التحقق من أعمدة جدول medic';

-- ===================================
-- 2. إنشاء جدول المبيعات إذا لم يكن موجوداً
-- ===================================

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='sales' AND xtype='U')
BEGIN
    CREATE TABLE sales (
        id INT IDENTITY(1,1) PRIMARY KEY,
        mid VARCHAR(250) NOT NULL,
        medicineName VARCHAR(250) NOT NULL,
        dosage VARCHAR(100) NOT NULL,
        quantity INT NOT NULL,
        pricePerUnit BIGINT NOT NULL,
        totalPrice BIGINT NOT NULL,
        employeeUsername VARCHAR(250) NOT NULL,
        employeeName VARCHAR(250) NOT NULL,
        saleDate DATETIME DEFAULT GETDATE()
    );
    PRINT 'تم إنشاء جدول sales';
END
ELSE
BEGIN
    PRINT 'جدول sales موجود';
END

-- ===================================
-- 3. إنشاء جدول جلسات الموظفين إذا لم يكن موجوداً
-- ===================================

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='employee_sessions' AND xtype='U')
BEGIN
    CREATE TABLE employee_sessions (
        id INT IDENTITY(1,1) PRIMARY KEY,
        username VARCHAR(250),
        employeeName VARCHAR(250),
        loginTime DATETIME,
        logoutTime DATETIME NULL,
        sessionDate DATE
    );
    PRINT 'تم إنشاء جدول employee_sessions';
END
ELSE
BEGIN
    PRINT 'جدول employee_sessions موجود';
END

-- ===================================
-- 4. إضافة بيانات تجريبية للأدوية إذا كانت فارغة
-- ===================================

DECLARE @medicCount INT;
SELECT @medicCount = COUNT(*) FROM medic WHERE quantity > 0;

IF @medicCount < 5
BEGIN
    PRINT 'إضافة أدوية تجريبية...';
    
    -- حذف الأدوية ذات الكمية صفر أولاً
    DELETE FROM medic WHERE quantity = 0;
    
    -- إضافة أدوية تجريبية
    INSERT INTO medic (mid, mname, mnumber, mDate, eDate, quantity, perUnit, lu, br, newEDate, newQuantity, allqun, mnumber_qty, newMDate, originalQuantity, originalNewQuantity)
    VALUES 
    ('MED001', 'باراسيتامول 500 مجم', '500mg', '2024-01-01', '2025-12-31', 100, 5, 'صندوق', 'فايزر', '2025-12-31', 50, 150, 100, '2024-01-01', 100, 50),
    ('MED002', 'أموكسيسيلين 250 مجم', '250mg', '2024-02-01', '2025-10-15', 75, 15, 'علبة', 'جلاكسو', '2025-10-15', 25, 100, 75, '2024-02-01', 75, 25),
    ('MED003', 'فيتامين سي 1000 مجم', '1000mg', '2024-03-01', '2026-03-20', 60, 12, 'زجاجة', 'باير', '2026-03-20', 40, 100, 60, '2024-03-01', 60, 40),
    ('MED004', 'إيبوبروفين 400 مجم', '400mg', '2024-04-01', '2025-08-10', 80, 8, 'صندوق', 'نوفارتيس', '2025-08-10', 20, 100, 80, '2024-04-01', 80, 20),
    ('MED005', 'شراب السعال للأطفال', '120ml', '2024-05-01', '2025-11-25', 30, 18, 'زجاجة', 'سانوفي', '2025-11-25', 15, 45, 30, '2024-05-01', 30, 15),
    ('MED006', 'أسبرين 100 مجم', '100mg', '2024-06-01', '2025-09-15', 120, 3, 'صندوق', 'باير', '2025-09-15', 80, 200, 120, '2024-06-01', 120, 80),
    ('MED007', 'أوميجا 3', '1000mg', '2024-07-01', '2026-01-10', 50, 25, 'علبة', 'فايزر', '2026-01-10', 30, 80, 50, '2024-07-01', 50, 30),
    ('MED008', 'سيتريزين شراب', '5mg/5ml', '2024-08-01', '2025-10-30', 40, 14, 'زجاجة', 'يوسي بي', '2025-10-30', 20, 60, 40, '2024-08-01', 40, 20),
    ('MED009', 'كالسيوم + فيتامين د', '600mg+400IU', '2024-09-01', '2026-02-15', 70, 22, 'علبة', 'سانوفي', '2026-02-15', 30, 100, 70, '2024-09-01', 70, 30),
    ('MED010', 'مضاد للفطريات كريم', '1%', '2024-10-01', '2025-10-10', 35, 24, 'أنبوب', 'جانسن', '2025-10-10', 15, 50, 35, '2024-10-01', 35, 15);
    
    PRINT 'تم إضافة 10 أدوية تجريبية';
END
ELSE
BEGIN
    PRINT 'الأدوية موجودة في قاعدة البيانات';
END

-- ===================================
-- 5. إضافة مستخدم تجريبي إذا لم يكن موجوداً
-- ===================================

IF NOT EXISTS (SELECT * FROM users WHERE username = 'admin')
BEGIN
    INSERT INTO users (userRole, name, dob, mobile, email, username, pass)
    VALUES ('Administrator', 'مدير النظام', '1980-01-01', 1234567890, '<EMAIL>', 'admin', 'admin123');
    PRINT 'تم إضافة مستخدم مدير تجريبي';
END

IF NOT EXISTS (SELECT * FROM users WHERE username = 'employee1')
BEGIN
    INSERT INTO users (userRole, name, dob, mobile, email, username, pass)
    VALUES ('Employee', 'موظف الصيدلية', '1990-05-15', 9876543210, '<EMAIL>', 'employee1', 'emp123');
    PRINT 'تم إضافة موظف تجريبي';
END

-- ===================================
-- 6. عرض الإحصائيات النهائية
-- ===================================

PRINT '========================================';
PRINT 'إحصائيات قاعدة البيانات المحلية:';
PRINT '========================================';

SELECT 
    'المستخدمين' as النوع,
    COUNT(*) as العدد
FROM users

UNION ALL

SELECT 
    'الأدوية المتاحة' as النوع,
    COUNT(*) as العدد
FROM medic
WHERE quantity > 0 OR newQuantity > 0

UNION ALL

SELECT 
    'المبيعات' as النوع,
    COUNT(*) as العدد
FROM sales

UNION ALL

SELECT 
    'جلسات الموظفين' as النوع,
    COUNT(*) as العدد
FROM employee_sessions;

-- عرض عينة من الأدوية المتاحة
PRINT 'عينة من الأدوية المتاحة:';
SELECT TOP 5 
    mname as 'اسم الدواء',
    quantity as 'الكمية',
    perUnit as 'السعر',
    eDate as 'تاريخ الانتهاء'
FROM medic 
WHERE quantity > 0
ORDER BY mname;

PRINT '========================================';
PRINT 'تم إصلاح قاعدة البيانات المحلية بنجاح!';
PRINT '========================================';
