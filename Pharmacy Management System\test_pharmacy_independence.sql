-- اختبار استقلالية بيانات الصيدليات
USE UnifiedPharmacy;

PRINT '=== اختبار استقلالية بيانات الصيدليات ===';

-- 1. عرض الصيدليات المتاحة
PRINT '1. الصيدليات المتاحة:';
SELECT 
    id,
    pharmacyCode,
    pharmacyName,
    ownerName
FROM pharmacies
ORDER BY id;

-- 2. عرض المستخدمين وصيدلياتهم
PRINT '2. المستخدمين وصيدلياتهم:';
SELECT 
    u.id,
    u.name,
    u.username,
    u.userRole,
    u.pharmacyId,
    p.pharmacyName
FROM users u
LEFT JOIN pharmacies p ON u.pharmacyId = p.id
ORDER BY u.pharmacyId, u.id;

-- 3. عرض الأدوية لكل صيدلية
PRINT '3. الأدوية لكل صيدلية:';
SELECT 
    p.pharmacyName,
    COUNT(m.id) as medicine_count,
    STRING_AGG(m.mname, ', ') as medicines
FROM pharmacies p
LEFT JOIN medic m ON p.id = m.pharmacy_id
GROUP BY p.id, p.pharmacyName
ORDER BY p.id;

-- 4. عرض المبيعات لكل صيدلية
PRINT '4. المبيعات لكل صيدلية:';
SELECT 
    p.pharmacyName,
    COUNT(s.id) as sales_count,
    ISNULL(SUM(s.totalPrice), 0) as total_revenue
FROM pharmacies p
LEFT JOIN sales s ON p.id = s.pharmacy_id
GROUP BY p.id, p.pharmacyName
ORDER BY p.id;

-- 5. عرض جلسات الموظفين لكل صيدلية
PRINT '5. جلسات الموظفين لكل صيدلية:';
SELECT 
    p.pharmacyName,
    COUNT(es.id) as session_count,
    COUNT(DISTINCT es.username) as unique_employees
FROM pharmacies p
LEFT JOIN employee_sessions es ON p.id = es.pharmacy_id
GROUP BY p.id, p.pharmacyName
ORDER BY p.id;

-- 6. اختبار الفلترة - الأدوية للصيدلية الأولى فقط
PRINT '6. أدوية الصيدلية الأولى فقط:';
SELECT 
    m.id,
    m.mname,
    m.quantity,
    m.pharmacy_id
FROM medic m
WHERE m.pharmacy_id = 1;

-- 7. اختبار الفلترة - الأدوية للصيدلية الثانية فقط
PRINT '7. أدوية الصيدلية الثانية فقط:';
SELECT 
    m.id,
    m.mname,
    m.quantity,
    m.pharmacy_id
FROM medic m
WHERE m.pharmacy_id = 2;

PRINT '=== انتهى الاختبار ===';
