#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
فحص حالة السوق - Market Status Checker
"""

import MetaTrader5 as mt5
from datetime import datetime, timezone
import configparser

class MarketStatusChecker:
    def __init__(self):
        self.connected = False
        self.load_config()
        
    def load_config(self):
        """تحميل إعدادات الحساب"""
        config = configparser.ConfigParser()
        config.read('config.ini')
        
        self.login = int(config.get('MT5_CONNECTION', 'login', fallback='96406085'))
        self.password = config.get('MT5_CONNECTION', 'password', fallback='D!2qKdJy')
        self.server = config.get('MT5_CONNECTION', 'server', fallback='MetaQuotes-Demo')
        self.symbol = config.get('TRADING_SETTINGS', 'symbol', fallback='EURUSD')
        
    def connect(self):
        """الاتصال بـ MetaTrader 5"""
        print("🔌 الاتصال بـ MetaTrader 5...")
        
        if not mt5.initialize():
            print(f"❌ فشل في تهيئة MT5: {mt5.last_error()}")
            return False
            
        if not mt5.login(self.login, self.password, self.server):
            print(f"❌ فشل في تسجيل الدخول: {mt5.last_error()}")
            return False
            
        self.connected = True
        print("✅ تم الاتصال بنجاح")
        return True
        
    def check_market_status(self):
        """فحص حالة السوق"""
        if not self.connected:
            if not self.connect():
                return False
                
        print(f"\n📊 فحص حالة السوق للرمز: {self.symbol}")
        print("=" * 50)
        
        # معلومات الرمز
        symbol_info = mt5.symbol_info(self.symbol)
        if not symbol_info:
            print(f"❌ الرمز {self.symbol} غير متوفر")
            return False
            
        # الوقت الحالي
        current_time = datetime.now()
        server_time = datetime.fromtimestamp(mt5.symbol_info_tick(self.symbol).time)
        
        print(f"🕐 الوقت المحلي: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🌐 وقت الخادم: {server_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # حالة التداول
        if symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_DISABLED:
            print("❌ التداول معطل لهذا الرمز")
            return False
        elif symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_LONGONLY:
            print("📈 التداول مسموح - شراء فقط")
        elif symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_SHORTONLY:
            print("📉 التداول مسموح - بيع فقط")
        elif symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_CLOSEONLY:
            print("🔒 إغلاق الصفقات فقط")
        elif symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_FULL:
            print("✅ التداول مسموح - شراء وبيع")
        else:
            print(f"⚠️ وضع تداول غير معروف: {symbol_info.trade_mode}")
            
        # السعر الحالي
        tick = mt5.symbol_info_tick(self.symbol)
        if tick:
            print(f"💰 السعر الحالي:")
            print(f"   📈 Ask: {tick.ask}")
            print(f"   📉 Bid: {tick.bid}")
            print(f"   📊 Spread: {tick.ask - tick.bid:.5f}")
            print(f"   🕐 آخر تحديث: {datetime.fromtimestamp(tick.time).strftime('%H:%M:%S')}")
        else:
            print("❌ لا يمكن الحصول على السعر الحالي")
            
        # معلومات الجلسة
        print(f"\n📅 معلومات الجلسة:")
        print(f"   🌅 بداية الجلسة: {symbol_info.session_open}")
        print(f"   🌇 نهاية الجلسة: {symbol_info.session_close}")
        
        # التحقق من إمكانية التداول
        can_trade = self.can_trade_now()
        if can_trade:
            print("\n🎉 السوق مفتوح - يمكن التداول الآن!")
        else:
            print("\n⏰ السوق مغلق - انتظر حتى فتح السوق")
            
        return can_trade
        
    def can_trade_now(self):
        """التحقق من إمكانية التداول الآن"""
        if not self.connected:
            return False
            
        # محاولة الحصول على السعر الحالي
        tick = mt5.symbol_info_tick(self.symbol)
        if not tick:
            return False
            
        # التحقق من أن السعر محدث (خلال آخر دقيقة)
        current_time = datetime.now().timestamp()
        price_age = current_time - tick.time
        
        if price_age > 60:  # أكثر من دقيقة
            print(f"⚠️ السعر قديم ({price_age:.0f} ثانية)")
            return False
            
        # التحقق من وجود spread معقول
        spread = tick.ask - tick.bid
        if spread <= 0 or spread > 0.01:  # spread غير طبيعي
            print(f"⚠️ Spread غير طبيعي: {spread:.5f}")
            return False
            
        return True
        
    def get_trading_hours(self):
        """الحصول على أوقات التداول"""
        print("\n🕐 أوقات التداول لسوق الفوركس:")
        print("   🌏 سيدني: 22:00 - 07:00 GMT")
        print("   🌏 طوكيو: 00:00 - 09:00 GMT")
        print("   🌍 لندن: 08:00 - 17:00 GMT")
        print("   🌎 نيويورك: 13:00 - 22:00 GMT")
        print("\n💡 أفضل أوقات التداول:")
        print("   🔥 تداخل لندن-نيويورك: 13:00 - 17:00 GMT")
        print("   🔥 تداخل سيدني-طوكيو: 00:00 - 07:00 GMT")

def main():
    """الدالة الرئيسية"""
    print("📊 فحص حالة السوق")
    print("=" * 30)
    
    checker = MarketStatusChecker()
    
    # فحص حالة السوق
    market_open = checker.check_market_status()
    
    # عرض أوقات التداول
    checker.get_trading_hours()
    
    print("\n" + "=" * 50)
    if market_open:
        print("🎉 النتيجة: السوق مفتوح - يمكن التداول!")
        print("💡 يمكنك تشغيل نظام التداول الآن")
    else:
        print("⏰ النتيجة: السوق مغلق - انتظر حتى فتح السوق")
        print("💡 جرب مرة أخرى خلال أوقات التداول")

if __name__ == "__main__":
    main()
