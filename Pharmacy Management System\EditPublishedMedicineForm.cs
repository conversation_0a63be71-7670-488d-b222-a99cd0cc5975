using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Pharmacy_Management_System.PharmacistUC;
using static Pharmacy_Management_System.PharmacistUC.UC_P_PharmacyStore;

namespace Pharmacy_Management_System
{
    public partial class EditPublishedMedicineForm : Form
    {
        public int UpdatedQuantity { get; private set; }
        public decimal UpdatedPrice { get; private set; }
        public string UpdatedDescription { get; private set; }
        public List<DosageInfo> UpdatedDosages { get; private set; }

        private int publishedMedicineId;
        private string medicineName;
        private int currentQuantity;
        private decimal currentPrice;
        private string currentDescription;
        private int maxAvailableQuantity;
        private List<DosageInfo> currentDosages;

        public EditPublishedMedicineForm(int publishedMedicineId, string medicineName,
            int currentQuantity, decimal currentPrice, string currentDescription,
            int maxAvailableQuantity, List<DosageInfo> dosages = null)
        {
            InitializeComponent();
            
            this.publishedMedicineId = publishedMedicineId;
            this.medicineName = medicineName;
            this.currentQuantity = currentQuantity;
            this.currentPrice = currentPrice;
            this.currentDescription = currentDescription ?? "";
            this.maxAvailableQuantity = maxAvailableQuantity;
            this.currentDosages = dosages ?? new List<DosageInfo>();
            this.UpdatedDosages = new List<DosageInfo>();
            
            InitializeForm();
            ApplyLanguage();
            ApplyModernDesign();
        }

        private void InitializeForm()
        {
            // تعيين القيم الحالية
            lblMedicineName.Text = $"الدواء: {medicineName}";
            lblCurrentQuantity.Text = $"الكمية المنشورة حالياً: {currentQuantity}";
            lblMaxQuantity.Text = $"الكمية المتاحة في المخزون: {maxAvailableQuantity}";
            
            numQuantity.Value = currentQuantity;
            numQuantity.Maximum = maxAvailableQuantity;
            numQuantity.Minimum = 1;
            
            numPrice.Value = currentPrice;
            numPrice.DecimalPlaces = 2;
            numPrice.Increment = 0.25m;
            numPrice.Minimum = 0.01m;
            numPrice.Maximum = 10000m;
            
            txtDescription.Text = currentDescription;
            txtDescription.MaxLength = 500;
            
            // إعداد الجرعات
            InitializeDosages();
        }

        private void InitializeDosages()
        {
            panelDosages.Controls.Clear();
            
            if (currentDosages.Count > 0)
            {
                lblDosagesTitle.Visible = true;
                panelDosages.Visible = true;
                
                int yPosition = 10;
                foreach (var dosage in currentDosages)
                {
                    AddDosageControl(dosage, yPosition);
                    yPosition += 80;
                }
                
                // زر إضافة جرعة جديدة
                Button btnAddDosage = new Button
                {
                    Text = "إضافة جرعة",
                    Size = new Size(120, 35),
                    Location = new Point(10, yPosition),
                    BackColor = Color.FromArgb(33, 150, 243),
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold)
                };
                btnAddDosage.Click += BtnAddDosage_Click;
                panelDosages.Controls.Add(btnAddDosage);
            }
            else
            {
                lblDosagesTitle.Visible = false;
                panelDosages.Visible = false;
            }
        }

        private void AddDosageControl(DosageInfo dosage, int yPosition)
        {
            // لوحة الجرعة
            Panel dosagePanel = new Panel
            {
                Size = new Size(panelDosages.Width - 20, 70),
                Location = new Point(10, yPosition),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(250, 250, 250)
            };

            // اسم الجرعة
            Label lblDosageName = new Label
            {
                Text = $"الجرعة: {dosage.DosageName}",
                Location = new Point(10, 10),
                Size = new Size(150, 20),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            // كمية الجرعة
            Label lblQuantityLabel = new Label
            {
                Text = "الكمية:",
                Location = new Point(10, 35),
                Size = new Size(50, 20)
            };

            NumericUpDown numDosageQuantity = new NumericUpDown
            {
                Value = dosage.Quantity,
                Location = new Point(70, 33),
                Size = new Size(80, 25),
                Minimum = 0,
                Maximum = maxAvailableQuantity,
                Tag = dosage.DosageName
            };

            // سعر الجرعة
            Label lblPriceLabel = new Label
            {
                Text = "السعر:",
                Location = new Point(170, 35),
                Size = new Size(40, 20)
            };

            NumericUpDown numDosagePrice = new NumericUpDown
            {
                Value = dosage.Price,
                Location = new Point(220, 33),
                Size = new Size(80, 25),
                DecimalPlaces = 2,
                Increment = 0.25m,
                Minimum = 0.01m,
                Maximum = 10000m,
                Tag = dosage.DosageName
            };

            // زر حذف الجرعة
            Button btnRemoveDosage = new Button
            {
                Text = "حذف",
                Size = new Size(50, 25),
                Location = new Point(320, 33),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Tag = dosage.DosageName
            };
            btnRemoveDosage.Click += BtnRemoveDosage_Click;

            dosagePanel.Controls.AddRange(new Control[] 
            { 
                lblDosageName, lblQuantityLabel, numDosageQuantity, 
                lblPriceLabel, numDosagePrice, btnRemoveDosage 
            });
            
            panelDosages.Controls.Add(dosagePanel);
        }

        private void BtnAddDosage_Click(object sender, EventArgs e)
        {
            using (var addDosageForm = new AddDosageForm())
            {
                if (addDosageForm.ShowDialog() == DialogResult.OK)
                {
                    var newDosage = new DosageInfo
                    {
                        DosageName = addDosageForm.DosageName,
                        Quantity = addDosageForm.Quantity,
                        Price = addDosageForm.Price
                    };
                    
                    currentDosages.Add(newDosage);
                    InitializeDosages();
                }
            }
        }

        private void BtnRemoveDosage_Click(object sender, EventArgs e)
        {
            Button btn = sender as Button;
            string dosageName = btn.Tag.ToString();
            
            var dosageToRemove = currentDosages.FirstOrDefault(d => d.DosageName == dosageName);
            if (dosageToRemove != null)
            {
                if (MessageBox.Show($"هل تريد حذف الجرعة '{dosageName}'؟", "تأكيد الحذف", 
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    currentDosages.Remove(dosageToRemove);
                    InitializeDosages();
                }
            }
        }

        private void ApplyLanguage()
        {
            this.Text = LanguageManager.GetText("Edit Published Medicine");
            lblTitle.Text = LanguageManager.GetText("Edit Published Medicine");
            lblQuantityLabel.Text = LanguageManager.GetText("Quantity") + ":";
            lblPriceLabel.Text = LanguageManager.GetText("Price Per Unit") + ":";
            lblDescriptionLabel.Text = LanguageManager.GetText("Description") + ":";
            lblDosagesTitle.Text = LanguageManager.GetText("Dosages") + ":";
            btnSave.Text = LanguageManager.GetText("Save Changes");
            btnCancel.Text = LanguageManager.GetText("Cancel");
        }

        private void ApplyModernDesign()
        {
            this.BackColor = Color.FromArgb(245, 245, 245);
            
            // تصميم اللوحة الرئيسية
            panelMain.BackColor = Color.White;
            panelMain.BorderStyle = BorderStyle.None;
            
            // تصميم الأزرار
            btnSave.BackColor = Color.FromArgb(76, 175, 80);
            btnSave.ForeColor = Color.White;
            btnSave.FlatStyle = FlatStyle.Flat;
            btnSave.FlatAppearance.BorderSize = 0;
            
            btnCancel.BackColor = Color.FromArgb(158, 158, 158);
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.FlatAppearance.BorderSize = 0;
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (numQuantity.Value <= 0)
                {
                    MessageBox.Show("يجب أن تكون الكمية أكبر من صفر", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (numPrice.Value <= 0)
                {
                    MessageBox.Show("يجب أن يكون السعر أكبر من صفر", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // جمع بيانات الجرعات المحدثة
                UpdatedDosages.Clear();
                foreach (Control control in panelDosages.Controls)
                {
                    if (control is Panel dosagePanel)
                    {
                        var numQuantity = dosagePanel.Controls.OfType<NumericUpDown>().FirstOrDefault(n => n.Tag != null);
                        var numPrice = dosagePanel.Controls.OfType<NumericUpDown>().LastOrDefault(n => n.Tag != null);
                        
                        if (numQuantity != null && numPrice != null)
                        {
                            UpdatedDosages.Add(new DosageInfo
                            {
                                DosageName = numQuantity.Tag.ToString(),
                                Quantity = (int)numQuantity.Value,
                                Price = numPrice.Value
                            });
                        }
                    }
                }

                // حفظ القيم المحدثة
                UpdatedQuantity = (int)numQuantity.Value;
                UpdatedPrice = numPrice.Value;
                UpdatedDescription = txtDescription.Text.Trim();

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التعديلات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
