using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy_Management_System.PharmacistUC
{
    public class DosageInfo
    {
        public string DosageName { get; set; }
        public int Quantity { get; set; }
        public decimal Price { get; set; }
    }

    public partial class UC_P_PharmacyStore : UserControl
    {
        private UnifiedPharmacyFunction unifiedDb;
        private Timer refreshTimer;
        private bool isLoading = false;
        private NotificationManager notificationManager;
        private Timer notificationTimer;
        private int lastLoggedPharmacyId = -1; // لتقليل رسائل التشخيص المتكررة

        public UC_P_PharmacyStore()
        {
            InitializeComponent();
            unifiedDb = new UnifiedPharmacyFunction();
            notificationManager = new NotificationManager();

            // تطبيق التصميم العصري
            ApplyModernDesign();

            // إعداد الأحداث
            SetupEventHandlers();

            // الاشتراك في أحداث التغيير
            // LanguageManager.LanguageChanged += (s, e) => UpdateTexts();
            // ModernTheme.ThemeChanged += (s, e) => {
            //     ApplyModernDataGridStyle(dataGridViewLocalMedicines);
            //     ApplyModernDataGridStyle(dataGridViewPublishedMedicines);
            //     ApplyModernDataGridStyle(dataGridViewMyPublished);
            // };

            // إعداد مؤقت التحديث
            refreshTimer = new Timer();
            refreshTimer.Interval = 30000; // 30 ثانية
            refreshTimer.Tick += RefreshTimer_Tick;

            // إعداد مؤقت الإشعارات
            notificationTimer = new Timer();
            notificationTimer.Interval = 10000; // 10 ثوانِ
            notificationTimer.Tick += NotificationTimer_Tick;
        }

        private async void UC_P_PharmacyStore_Load(object sender, EventArgs e)
        {
            // تطبيق اللغة الحالية
            ApplyLanguage();

            // تحميل البيانات الأولية
            await LoadInitialData();

            // بدء مؤقت التحديث
            refreshTimer.Start();

            // بدء مؤقت الإشعارات وتحديث العرض
            notificationTimer.Start();
            UpdateNotificationDisplay();
        }

        private async Task LoadInitialData()
        {
            try
            {
                isLoading = true;

                // عرض رسالة تحميل
                System.Diagnostics.Debug.WriteLine("🔄 بدء تحميل البيانات الأولية...");

                // تحميل الأدوية المحلية للنشر
                await LoadLocalMedicines();

                // تحميل الأدوية المنشورة في المتجر (تحديث تلقائي)
                await LoadPublishedMedicines();

                // تحميل أدويتي المعروضة (تحديث تلقائي)
                await LoadMyPublishedMedicines();

                // تحميل طلبات الأدوية
                try
                {
                    await LoadMedicineRequests();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل طلبات الأدوية (السطر 92): {ex.Message}");
                    // لا نعرض رسالة خطأ للمستخدم هنا لتجنب الإزعاج
                }

                // تحميل فلاتر الصيدليات
                LoadPharmacyFilter();

                System.Diagnostics.Debug.WriteLine("✅ تم تحميل جميع البيانات بنجاح");
                isLoading = false;
            }
            catch (Exception ex)
            {
                isLoading = false;
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل البيانات: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetupEventHandlers()
        {
            try
            {
                // إعداد أحداث البحث والفلترة
                if (txtSearchPublished != null)
                {
                    txtSearchPublished.TextChanged += async (s, e) => await ApplyFilters();
                }

                if (cmbFilterExpiry != null)
                {
                    cmbFilterExpiry.SelectedIndexChanged += async (s, e) => await ApplyFilters();
                }

                if (cmbFilterPharmacy != null)
                {
                    cmbFilterPharmacy.SelectedIndexChanged += async (s, e) => await ApplyFilters();
                }

                // إعداد فلتر التاريخ
                if (dtpFilterDate != null)
                {
                    dtpFilterDate.Enabled = false;
                }

                if (chkFilterByDate != null)
                {
                    chkFilterByDate.Checked = false;
                }

                // إعداد أحداث تبويب طلبات الأدوية
                if (btnRefreshRequests != null)
                {
                    btnRefreshRequests.Click += BtnRefreshRequests_Click;
                }

                if (btnAcceptRequest != null)
                {
                    btnAcceptRequest.Click += BtnAcceptRequest_Click;
                }

                if (btnRejectRequest != null)
                {
                    btnRejectRequest.Click += BtnRejectRequest_Click;
                }

                if (btnMessagesRequests != null)
                {
                    btnMessagesRequests.Click += BtnMessagesRequests_Click;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إعداد الأحداث: {ex.Message}");
            }
        }

        private Task LoadLocalMedicines()
        {
            try
            {
                // تحميل الأدوية المحلية المتاحة للنشر
                string query = @"
                    SELECT
                        id,
                        mname as medicineName,
                        mnumber as medicineNumber,
                        quantity,
                        eDate as expiryDate,
                        perUnit as pricePerUnit,
                        DATEDIFF(day, GETDATE(), eDate) as daysToExpiry
                    FROM medic
                    WHERE quantity > 0
                    AND eDate > GETDATE()
                    ORDER BY mname";

                System.Diagnostics.Debug.WriteLine("🔍 تحميل الأدوية المحلية...");
                System.Diagnostics.Debug.WriteLine($"الاستعلام: {query}");

                // أولاً تحقق من وجود أي أدوية في الجدول
                string countQuery = "SELECT COUNT(*) FROM medic";
                DataSet countResult = unifiedDb.ExecuteQuery(countQuery, null);
                int totalMedicines = Convert.ToInt32(countResult.Tables[0].Rows[0][0]);
                System.Diagnostics.Debug.WriteLine($"إجمالي الأدوية في الجدول: {totalMedicines}");

                DataSet result = unifiedDb.ExecuteQuery(query, null);

                System.Diagnostics.Debug.WriteLine($"عدد الأدوية المحملة (متاحة للنشر): {result.Tables[0].Rows.Count}");

                if (dataGridViewLocalMedicines != null)
                {
                    dataGridViewLocalMedicines.DataSource = result.Tables[0];
                    FormatLocalMedicinesGrid();

                    // إظهار رسالة إذا لم توجد أدوية
                    if (result.Tables[0].Rows.Count == 0)
                    {
                        if (totalMedicines == 0)
                        {
                            MessageBox.Show("لا توجد أدوية في قاعدة البيانات.\n\nيرجى إضافة أدوية أولاً من صفحة 'إضافة دواء'.",
                                          "لا توجد أدوية", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show($"يوجد {totalMedicines} دواء في قاعدة البيانات، لكن لا توجد أدوية متاحة للنشر.\n\nالأسباب المحتملة:\n• الكمية = 0\n• تاريخ الصلاحية منتهي\n\nيرجى التحقق من المخزون وتواريخ الصلاحية.",
                                          "لا توجد أدوية متاحة للنشر", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ dataGridViewLocalMedicines is null!");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل الأدوية المحلية: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل الأدوية المحلية:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            return Task.CompletedTask;
        }

        private async Task LoadPublishedMedicines()
        {
            try
            {
                if (IsDisposed) return;
                System.Diagnostics.Debug.WriteLine("🔍 تحميل الأدوية المنشورة...");

                // أولاً تحقق من وجود الجداول المطلوبة
                string checkTablesQuery = @"
                    SELECT
                        (SELECT COUNT(*) FROM sys.tables WHERE name = 'published_medicines') as published_medicines_exists,
                        (SELECT COUNT(*) FROM sys.tables WHERE name = 'pharmacies') as pharmacies_exists";

                DataSet checkResult = unifiedDb.ExecuteQuery(checkTablesQuery, null);
                bool publishedMedicinesExists = Convert.ToInt32(checkResult.Tables[0].Rows[0]["published_medicines_exists"]) > 0;
                bool pharmaciesExists = Convert.ToInt32(checkResult.Tables[0].Rows[0]["pharmacies_exists"]) > 0;

                System.Diagnostics.Debug.WriteLine($"جدول published_medicines موجود: {publishedMedicinesExists}");
                System.Diagnostics.Debug.WriteLine($"جدول pharmacies موجود: {pharmaciesExists}");

                if (!publishedMedicinesExists || !pharmaciesExists)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ جداول متجر الأدوية غير موجودة!");

                    if (dataGridViewPublishedMedicines != null)
                    {
                        dataGridViewPublishedMedicines.DataSource = null;
                        MessageBox.Show("جداول متجر الأدوية غير موجودة في قاعدة البيانات.\n\nيرجى تشغيل ملف 'install_pharmacy_store.bat' لإعداد متجر الأدوية أولاً.",
                                      "جداول غير موجودة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                    return;
                }

                // تحميل الأدوية المنشورة من جميع الصيدليات مع معلومات الصيدلية بالعربي
                string query = @"
                    SELECT
                        pm.id,
                        pm.medicine_name as medicineName,
                        pm.quantity_available as quantity,
                        pm.expiry_date as expiryDate,
                        pm.price_per_unit as pricePerUnit,
                        pm.description,
                        pm.published_date as publishDate,
                        p.pharmacyName as pharmacyName,
                        p.phone as pharmacyPhone,
                        p.address as pharmacyAddress,
                        p.city as pharmacyCity,
                        ISNULL(p.region, p.city) as pharmacyRegion,
                        DATEDIFF(day, GETDATE(), pm.expiry_date) as daysToExpiry,
                        -- معلومات الصيدلية مجمعة بالعربي
                        (p.pharmacyName + ' - هاتف: ' + ISNULL(p.phone, 'غير محدد') + ' - المنطقة: ' + ISNULL(p.region, ISNULL(p.city, 'غير محدد'))) as pharmacyFullInfo
                    FROM published_medicines pm
                    INNER JOIN pharmacies p ON pm.pharmacy_id = p.id
                    WHERE pm.is_available = 1
                    AND pm.expiry_date > GETDATE()
                    ORDER BY pm.published_date DESC";

                System.Diagnostics.Debug.WriteLine($"الاستعلام: {query}");

                DataSet result = unifiedDb.ExecuteQuery(query, null);

                System.Diagnostics.Debug.WriteLine($"عدد الأدوية المنشورة المحملة: {result.Tables[0].Rows.Count}");

                if (dataGridViewPublishedMedicines != null)
                {
                    dataGridViewPublishedMedicines.DataSource = result.Tables[0];
                    FormatPublishedMedicinesGrid();

                    if (result.Tables[0].Rows.Count == 0)
                    {
                        MessageBox.Show("لا توجد أدوية منشورة من صيدليات أخرى حالياً.\n\nلاختبار هذه الميزة:\n1. انشر بعض الأدوية من تبويب 'الأدوية المحلية'\n2. أو اطلب من صيدليات أخرى نشر أدوية",
                                      "لا توجد أدوية منشورة", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (ObjectDisposedException)
            {
                // تم التخلص من الكائن، لا حاجة لعرض رسالة خطأ
                System.Diagnostics.Debug.WriteLine("⚠️ تم التخلص من UC_P_PharmacyStore أثناء تحميل الأدوية المنشورة");
            }
            catch (Exception ex)
            {
                if (!IsDisposed)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل الأدوية المنشورة: {ex.Message}");
                    MessageBox.Show($"خطأ في تحميل الأدوية المنشورة:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private async Task LoadMyPublishedMedicines()
        {
            try
            {
                if (IsDisposed) return;
                System.Diagnostics.Debug.WriteLine("🔍 تحميل أدويتي المعروضة...");

                // التحقق من وجود جدول published_medicines
                string checkTableQuery = "SELECT COUNT(*) FROM sys.tables WHERE name = 'published_medicines'";
                DataSet checkResult = unifiedDb.ExecuteQuery(checkTableQuery, null);
                bool tableExists = Convert.ToInt32(checkResult.Tables[0].Rows[0][0]) > 0;

                if (!tableExists)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ جدول published_medicines غير موجود!");

                    if (dataGridViewMyPublished != null)
                    {
                        dataGridViewMyPublished.DataSource = null;
                        MessageBox.Show("جدول الأدوية المنشورة غير موجود.\n\nيرجى تشغيل ملف 'install_pharmacy_store.bat' لإعداد متجر الأدوية أولاً.",
                                      "جدول غير موجود", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                    return;
                }

                // تحميل أدويتي المعروضة
                string query = @"
                    SELECT
                        id,
                        medicine_name as medicineName,
                        quantity_available as quantity,
                        expiry_date as expiryDate,
                        price_per_unit as pricePerUnit,
                        description,
                        published_date as publishDate,
                        DATEDIFF(day, GETDATE(), expiry_date) as daysToExpiry
                    FROM published_medicines
                    WHERE pharmacy_id = @currentPharmacyId
                    AND is_available = 1
                    ORDER BY published_date DESC";

                // استخدام pharmacy ID افتراضي إذا لم يكن متوفر
                int currentPharmacyId = 1; // ID افتراضي
                try
                {
                    if (SessionManager.CurrentPharmacyId > 0)
                        currentPharmacyId = SessionManager.CurrentPharmacyId;
                }
                catch
                {
                    System.Diagnostics.Debug.WriteLine("استخدام pharmacy ID افتراضي: 1");
                }

                var parameters = new System.Collections.Generic.Dictionary<string, object>
                {
                    {"@currentPharmacyId", currentPharmacyId}
                };

                System.Diagnostics.Debug.WriteLine($"البحث عن أدوية الصيدلية ID: {currentPharmacyId}");

                DataSet result = unifiedDb.ExecuteQuery(query, parameters);

                System.Diagnostics.Debug.WriteLine($"عدد أدويتي المعروضة: {result.Tables[0].Rows.Count}");

                if (dataGridViewMyPublished != null)
                {
                    dataGridViewMyPublished.DataSource = result.Tables[0];
                    FormatMyPublishedGrid();

                    if (result.Tables[0].Rows.Count == 0)
                    {
                        MessageBox.Show("لم تقم بنشر أي أدوية بعد.\n\nلنشر دواء:\n1. اذهب لتبويب 'الأدوية المحلية'\n2. اختر دواء\n3. اضغط 'نشر الدواء'",
                                      "لا توجد أدوية منشورة", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل أدويتي المعروضة: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل أدويتي المعروضة:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void FormatLocalMedicinesGrid()
        {
            try
            {
                if (dataGridViewLocalMedicines.Columns.Count > 0)
                {
                    // تحسين الخط العربي وإعدادات العرض
                    dataGridViewLocalMedicines.Font = new Font("Segoe UI", 10F, FontStyle.Regular);
                    dataGridViewLocalMedicines.DefaultCellStyle.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                    dataGridViewLocalMedicines.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                    dataGridViewLocalMedicines.RightToLeft = RightToLeft.Yes;
                    dataGridViewLocalMedicines.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dataGridViewLocalMedicines.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dataGridViewLocalMedicines.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
                    dataGridViewLocalMedicines.RowTemplate.Height = 35;

                    // إخفاء وتنسيق الأعمدة
                    dataGridViewLocalMedicines.Columns["id"].Visible = false;
                    dataGridViewLocalMedicines.Columns["medicineName"].HeaderText = "اسم الدواء";
                    dataGridViewLocalMedicines.Columns["medicineName"].Width = 200;
                    if (dataGridViewLocalMedicines.Columns.Contains("medicineNumber"))
                    {
                        dataGridViewLocalMedicines.Columns["medicineNumber"].HeaderText = "رقم الدواء";
                        dataGridViewLocalMedicines.Columns["medicineNumber"].Width = 100;
                    }
                    dataGridViewLocalMedicines.Columns["quantity"].HeaderText = "الكمية المتاحة";
                    dataGridViewLocalMedicines.Columns["quantity"].Width = 100;
                    dataGridViewLocalMedicines.Columns["expiryDate"].HeaderText = "تاريخ الانتهاء";
                    dataGridViewLocalMedicines.Columns["expiryDate"].Width = 120;
                    dataGridViewLocalMedicines.Columns["expiryDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
                    dataGridViewLocalMedicines.Columns["pricePerUnit"].HeaderText = "السعر للوحدة";
                    dataGridViewLocalMedicines.Columns["pricePerUnit"].Width = 100;
                    dataGridViewLocalMedicines.Columns["daysToExpiry"].HeaderText = "أيام للانتهاء";
                    dataGridViewLocalMedicines.Columns["daysToExpiry"].Width = 100;

                    // تلوين الصفوف حسب تاريخ الانتهاء مع تحسين الألوان
                    foreach (DataGridViewRow row in dataGridViewLocalMedicines.Rows)
                    {
                        if (row.Cells["daysToExpiry"].Value != null)
                        {
                            int daysToExpiry = Convert.ToInt32(row.Cells["daysToExpiry"].Value);
                            if (daysToExpiry <= 30)
                            {
                                row.DefaultCellStyle.BackColor = Color.LightCoral;
                                row.DefaultCellStyle.ForeColor = Color.DarkRed;
                            }
                            else if (daysToExpiry <= 90)
                            {
                                row.DefaultCellStyle.BackColor = Color.LightYellow;
                                row.DefaultCellStyle.ForeColor = Color.DarkOrange;
                            }
                            else
                            {
                                row.DefaultCellStyle.BackColor = Color.LightGreen;
                                row.DefaultCellStyle.ForeColor = Color.DarkGreen;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنسيق جدول الأدوية المحلية: {ex.Message}");
            }
        }

        private void FormatPublishedMedicinesGrid()
        {
            try
            {
                if (dataGridViewPublishedMedicines.Columns.Count > 0)
                {
                    // تحسين الخط العربي وإعدادات العرض
                    dataGridViewPublishedMedicines.Font = new Font("Segoe UI", 10F, FontStyle.Regular);
                    dataGridViewPublishedMedicines.DefaultCellStyle.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                    dataGridViewPublishedMedicines.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                    dataGridViewPublishedMedicines.RightToLeft = RightToLeft.Yes;
                    dataGridViewPublishedMedicines.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dataGridViewPublishedMedicines.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dataGridViewPublishedMedicines.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
                    dataGridViewPublishedMedicines.RowTemplate.Height = 35;

                    // إخفاء الأعمدة غير المطلوبة
                    dataGridViewPublishedMedicines.Columns["id"].Visible = false;
                    if (dataGridViewPublishedMedicines.Columns.Contains("pharmacyAddress"))
                        dataGridViewPublishedMedicines.Columns["pharmacyAddress"].Visible = false;
                    if (dataGridViewPublishedMedicines.Columns.Contains("pharmacyCity"))
                        dataGridViewPublishedMedicines.Columns["pharmacyCity"].Visible = false;
                    if (dataGridViewPublishedMedicines.Columns.Contains("pharmacyRegion"))
                        dataGridViewPublishedMedicines.Columns["pharmacyRegion"].Visible = false;
                    if (dataGridViewPublishedMedicines.Columns.Contains("pharmacyPhone"))
                        dataGridViewPublishedMedicines.Columns["pharmacyPhone"].Visible = false;

                    // تعيين عناوين الأعمدة بالعربي مع تحسين العرض
                    dataGridViewPublishedMedicines.Columns["medicineName"].HeaderText = "اسم الدواء";
                    dataGridViewPublishedMedicines.Columns["medicineName"].Width = 150;
                    dataGridViewPublishedMedicines.Columns["quantity"].HeaderText = "الكمية المتاحة";
                    dataGridViewPublishedMedicines.Columns["quantity"].Width = 80;
                    dataGridViewPublishedMedicines.Columns["expiryDate"].HeaderText = "تاريخ الانتهاء";
                    dataGridViewPublishedMedicines.Columns["expiryDate"].Width = 100;
                    dataGridViewPublishedMedicines.Columns["pricePerUnit"].HeaderText = "السعر للوحدة";
                    dataGridViewPublishedMedicines.Columns["pricePerUnit"].Width = 80;
                    dataGridViewPublishedMedicines.Columns["description"].HeaderText = "الوصف";
                    dataGridViewPublishedMedicines.Columns["description"].Width = 120;
                    dataGridViewPublishedMedicines.Columns["pharmacyName"].HeaderText = "اسم الصيدلية";
                    dataGridViewPublishedMedicines.Columns["pharmacyName"].Width = 120;
                    dataGridViewPublishedMedicines.Columns["publishDate"].HeaderText = "تاريخ النشر";
                    dataGridViewPublishedMedicines.Columns["publishDate"].Width = 100;
                    dataGridViewPublishedMedicines.Columns["daysToExpiry"].HeaderText = "أيام للانتهاء";
                    dataGridViewPublishedMedicines.Columns["daysToExpiry"].Width = 80;

                    // عرض معلومات الصيدلية الكاملة بالعربي
                    if (dataGridViewPublishedMedicines.Columns.Contains("pharmacyFullInfo"))
                    {
                        dataGridViewPublishedMedicines.Columns["pharmacyFullInfo"].HeaderText = "معلومات الصيدلية";
                        dataGridViewPublishedMedicines.Columns["pharmacyFullInfo"].Width = 300;
                        dataGridViewPublishedMedicines.Columns["pharmacyFullInfo"].DefaultCellStyle.WrapMode = DataGridViewTriState.True;
                        dataGridViewPublishedMedicines.Columns["pharmacyFullInfo"].DefaultCellStyle.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                    }

                    // تنسيق عرض التواريخ
                    if (dataGridViewPublishedMedicines.Columns.Contains("expiryDate"))
                    {
                        dataGridViewPublishedMedicines.Columns["expiryDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
                    }
                    if (dataGridViewPublishedMedicines.Columns.Contains("publishDate"))
                    {
                        dataGridViewPublishedMedicines.Columns["publishDate"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm";
                    }

                    // تلوين الصفوف حسب تاريخ الانتهاء
                    foreach (DataGridViewRow row in dataGridViewPublishedMedicines.Rows)
                    {
                        if (row.Cells["daysToExpiry"].Value != null)
                        {
                            int daysToExpiry = Convert.ToInt32(row.Cells["daysToExpiry"].Value);
                            if (daysToExpiry <= 30)
                            {
                                row.DefaultCellStyle.BackColor = Color.LightCoral;
                                row.DefaultCellStyle.ForeColor = Color.DarkRed;
                            }
                            else if (daysToExpiry <= 90)
                            {
                                row.DefaultCellStyle.BackColor = Color.LightYellow;
                                row.DefaultCellStyle.ForeColor = Color.DarkOrange;
                            }
                            else
                            {
                                row.DefaultCellStyle.BackColor = Color.LightGreen;
                                row.DefaultCellStyle.ForeColor = Color.DarkGreen;
                            }
                        }
                    }
                }

                // إضافة عمود زر المحادثة
                AddChatButtonColumn();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنسيق جدول الأدوية المنشورة: {ex.Message}");
            }
        }

        /// <summary>
        /// إضافة عمود زر المحادثة للجدول
        /// </summary>
        private void AddChatButtonColumn()
        {
            try
            {
                if (dataGridViewPublishedMedicines == null) return;

                // التحقق من وجود عمود الزر مسبقاً
                if (dataGridViewPublishedMedicines.Columns.Contains("ChatButton"))
                    return;

                // إنشاء عمود الزر
                DataGridViewButtonColumn chatButtonColumn = new DataGridViewButtonColumn();
                chatButtonColumn.Name = "ChatButton";
                chatButtonColumn.HeaderText = "💬 محادثة";
                chatButtonColumn.Text = "محادثة الصيدلية";
                chatButtonColumn.UseColumnTextForButtonValue = true;
                chatButtonColumn.Width = 120;
                chatButtonColumn.FlatStyle = FlatStyle.Flat;

                // إضافة العمود للجدول
                dataGridViewPublishedMedicines.Columns.Add(chatButtonColumn);

                // ربط حدث النقر على الزر
                dataGridViewPublishedMedicines.CellClick -= DataGridViewPublishedMedicines_CellClick;
                dataGridViewPublishedMedicines.CellClick += DataGridViewPublishedMedicines_CellClick;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة عمود زر المحادثة: {ex.Message}");
            }
        }

        /// <summary>
        /// معالجة النقر على خلايا جدول الأدوية المنشورة
        /// </summary>
        private void DataGridViewPublishedMedicines_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex < 0 || e.ColumnIndex < 0) return;

                // التحقق من النقر على عمود زر المحادثة
                if (dataGridViewPublishedMedicines.Columns[e.ColumnIndex].Name == "ChatButton")
                {
                    var row = dataGridViewPublishedMedicines.Rows[e.RowIndex];

                    // الحصول على معلومات الصيدلية من الصف
                    string pharmacyName = row.Cells["pharmacyName"].Value?.ToString() ?? "صيدلية غير معروفة";
                    string medicineName = row.Cells["medicineName"].Value?.ToString() ?? "دواء غير معروف";

                    // الحصول على ID الصيدلية من قاعدة البيانات
                    int pharmacyId = GetPharmacyIdByName(pharmacyName);

                    if (pharmacyId > 0)
                    {
                        // فتح نافذة المحادثة
                        OpenChatWithPharmacy(pharmacyId, pharmacyName);
                    }
                    else
                    {
                        MessageBox.Show($"لا يمكن العثور على معلومات الصيدلية: {pharmacyName}",
                                      "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معالجة النقر: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// الحصول على ID الصيدلية بالاسم
        /// </summary>
        private int GetPharmacyIdByName(string pharmacyName)
        {
            try
            {
                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyName", pharmacyName}
                };

                string query = "SELECT id FROM pharmacies WHERE pharmacyName = @pharmacyName";
                DataSet result = unifiedDb.ExecuteQuery(query, parameters);

                if (result.Tables.Count > 0 && result.Tables[0].Rows.Count > 0)
                {
                    return Convert.ToInt32(result.Tables[0].Rows[0]["id"]);
                }

                return 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على ID الصيدلية: {ex.Message}");
                return 0;
            }
        }

        private void FormatMyPublishedGrid()
        {
            try
            {
                if (dataGridViewMyPublished.Columns.Count > 0)
                {
                    dataGridViewMyPublished.Columns["id"].Visible = false;
                    dataGridViewMyPublished.Columns["medicineName"].HeaderText = LanguageManager.GetText("Medicine Name");
                    dataGridViewMyPublished.Columns["quantity"].HeaderText = LanguageManager.GetText("Quantity");
                    dataGridViewMyPublished.Columns["expiryDate"].HeaderText = LanguageManager.GetText("Expiry Date");
                    dataGridViewMyPublished.Columns["pricePerUnit"].HeaderText = LanguageManager.GetText("Price Per Unit");
                    dataGridViewMyPublished.Columns["description"].HeaderText = LanguageManager.GetText("Description");
                    dataGridViewMyPublished.Columns["publishDate"].HeaderText = LanguageManager.GetText("Publish Date");
                    dataGridViewMyPublished.Columns["daysToExpiry"].HeaderText = LanguageManager.GetText("Days to Expiry");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنسيق جدول أدويتي المعروضة: {ex.Message}");
            }
        }

        // أحداث الأزرار والتفاعل
        private async void btnPublishMedicine_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridViewLocalMedicines.SelectedRows.Count == 0)
                {
                    MessageBox.Show(LanguageManager.GetText("Please select a medicine to publish"),
                        LanguageManager.GetText("Warning"), MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedRow = dataGridViewLocalMedicines.SelectedRows[0];
                int medicineId = Convert.ToInt32(selectedRow.Cells["id"].Value);
                string medicineName = selectedRow.Cells["medicineName"].Value.ToString();
                int quantity = Convert.ToInt32(selectedRow.Cells["quantity"].Value);
                DateTime expiryDate = Convert.ToDateTime(selectedRow.Cells["expiryDate"].Value);
                decimal pricePerUnit = Convert.ToDecimal(selectedRow.Cells["pricePerUnit"].Value);

                // فتح نافذة إدخال الوصف والكمية
                using (var publishForm = new PublishMedicineForm(medicineName, quantity, expiryDate, pricePerUnit))
                {
                    if (publishForm.ShowDialog() == DialogResult.OK)
                    {
                        await PublishMedicine(medicineId, publishForm.PublishQuantity, publishForm.Description);
                        await LoadInitialData();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في نشر الدواء: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task PublishMedicine(int medicineId, int publishQuantity, string description)
        {
            try
            {
                // الحصول على معلومات الدواء
                string getMedicineQuery = @"
                    SELECT mname, mnumber, eDate, perUnit
                    FROM medic
                    WHERE id = @medicineId";

                var getMedicineParams = new System.Collections.Generic.Dictionary<string, object>
                {
                    {"@medicineId", medicineId}
                };

                DataSet medicineResult = unifiedDb.ExecuteQuery(getMedicineQuery, getMedicineParams);

                if (medicineResult.Tables[0].Rows.Count == 0)
                {
                    MessageBox.Show("الدواء غير موجود", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                var medicineRow = medicineResult.Tables[0].Rows[0];

                // التحقق من وجود جدول published_medicines
                string checkTableQuery = "SELECT COUNT(*) FROM sys.tables WHERE name = 'published_medicines'";
                DataSet checkResult = unifiedDb.ExecuteQuery(checkTableQuery, null);
                bool tableExists = Convert.ToInt32(checkResult.Tables[0].Rows[0][0]) > 0;

                if (!tableExists)
                {
                    MessageBox.Show("جدول الأدوية المنشورة غير موجود.\n\nيرجى تشغيل ملف 'install_pharmacy_store.bat' لإعداد متجر الأدوية أولاً.",
                                  "جدول غير موجود", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // نشر الدواء مع أسماء الأعمدة الصحيحة
                string publishQuery = @"
                    INSERT INTO published_medicines
                    (pharmacy_id, medicine_name, medicine_number, quantity_available, expiry_date, price_per_unit, description, published_date, is_available)
                    VALUES
                    (@pharmacy_id, @medicine_name, @medicine_number, @quantity, @expiry_date, @price_per_unit, @description, GETDATE(), 1)";

                // استخدام pharmacy ID افتراضي إذا لم يكن متوفر
                int currentPharmacyId = 1;
                try
                {
                    if (SessionManager.CurrentPharmacyId > 0)
                        currentPharmacyId = SessionManager.CurrentPharmacyId;
                }
                catch
                {
                    System.Diagnostics.Debug.WriteLine("استخدام pharmacy ID افتراضي: 1");
                }

                var publishParams = new System.Collections.Generic.Dictionary<string, object>
                {
                    {"@pharmacy_id", currentPharmacyId},
                    {"@medicine_name", medicineRow["mname"].ToString()},
                    {"@medicine_number", medicineRow["mnumber"]?.ToString() ?? ""},
                    {"@quantity", publishQuantity},
                    {"@expiry_date", medicineRow["eDate"]},
                    {"@price_per_unit", medicineRow["perUnit"]},
                    {"@description", description ?? ""}
                };

                unifiedDb.ExecuteQuery(publishQuery, publishParams);

                MessageBox.Show("تم نشر الدواء بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في نشر الدواء: {ex.Message}");
            }
        }

        private async void btnRequestMedicine_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridViewPublishedMedicines.SelectedRows.Count == 0)
                {
                    MessageBox.Show(LanguageManager.GetText("Please select a medicine to request"),
                        LanguageManager.GetText("Warning"), MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedRow = dataGridViewPublishedMedicines.SelectedRows[0];
                int publishedMedicineId = Convert.ToInt32(selectedRow.Cells["id"].Value);
                string medicineName = selectedRow.Cells["medicineName"].Value.ToString();
                int availableQuantity = Convert.ToInt32(selectedRow.Cells["quantity"].Value);
                string pharmacyName = selectedRow.Cells["pharmacyName"].Value.ToString();
                string pharmacyPhone = selectedRow.Cells["pharmacyPhone"].Value.ToString();

                // فتح نافذة طلب الدواء
                using (var requestForm = new RequestMedicineForm(medicineName, availableQuantity, pharmacyName, pharmacyPhone))
                {
                    if (requestForm.ShowDialog() == DialogResult.OK)
                    {
                        await CreateMedicineRequest(publishedMedicineId, requestForm.RequestQuantity, requestForm.RequestMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طلب الدواء: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task CreateMedicineRequest(int publishedMedicineId, int requestQuantity, string requestMessage)
        {
            try
            {
                // إنشاء طلب الدواء
                string requestQuery = @"
                    INSERT INTO purchase_requests
                    (medicine_id, buyer_pharmacy_id, seller_pharmacy_id, requested_quantity, offered_price, request_message, request_date, status)
                    VALUES
                    (@medicine_id, @buyer_pharmacy_id, @seller_pharmacy_id, @requested_quantity, @offered_price, @request_message, GETDATE(), 'pending')";

                var requestParams = new System.Collections.Generic.Dictionary<string, object>
                {
                    {"@medicine_id", publishedMedicineId},
                    {"@buyer_pharmacy_id", SessionManager.CurrentPharmacyId},
                    {"@seller_pharmacy_id", 1}, // معرف الصيدلية البائعة - يجب تحديده حسب السياق
                    {"@requested_quantity", requestQuantity},
                    {"@offered_price", 0}, // سعر افتراضي، يمكن تحديثه لاحقاً
                    {"@request_message", requestMessage ?? ""}
                };

                unifiedDb.ExecuteQuery(requestQuery, requestParams);

                MessageBox.Show("تم إرسال طلب الدواء بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء طلب الدواء: {ex.Message}");
            }
        }

        private async void btnEditMyPublished_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridViewMyPublished.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار دواء منشور للتعديل", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedRow = dataGridViewMyPublished.SelectedRows[0];
                int publishedMedicineId = Convert.ToInt32(selectedRow.Cells["id"].Value);
                string medicineName = selectedRow.Cells["medicineName"].Value?.ToString() ?? "";
                int currentQuantity = Convert.ToInt32(selectedRow.Cells["quantity"].Value);
                decimal currentPrice = Convert.ToDecimal(selectedRow.Cells["pricePerUnit"].Value);
                string currentDescription = selectedRow.Cells["description"].Value?.ToString() ?? "";

                // الحصول على الكمية المتاحة في المخزون
                int maxAvailableQuantity = await GetMaxAvailableQuantity(medicineName);

                // الحصول على معلومات الجرعات إذا وجدت
                var dosages = await GetMedicineDosages(medicineName);

                using (var editForm = new EditPublishedMedicineForm(
                    publishedMedicineId, medicineName, currentQuantity, currentPrice,
                    currentDescription, maxAvailableQuantity, dosages))
                {
                    if (editForm.ShowDialog() == DialogResult.OK)
                    {
                        await UpdatePublishedMedicine(publishedMedicineId, editForm.UpdatedQuantity,
                            editForm.UpdatedPrice, editForm.UpdatedDescription, editForm.UpdatedDosages);

                        await LoadMyPublishedMedicines();
                        MessageBox.Show("تم تحديث الدواء المنشور بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل الدواء المنشور: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnDeleteMyPublished_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridViewMyPublished.SelectedRows.Count == 0)
                {
                    MessageBox.Show(LanguageManager.GetText("Please select a published medicine to delete"),
                        LanguageManager.GetText("Warning"), MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show(LanguageManager.GetText("Are you sure you want to delete this published medicine?"),
                    LanguageManager.GetText("Confirm Delete"), MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var selectedRow = dataGridViewMyPublished.SelectedRows[0];
                    int publishedMedicineId = Convert.ToInt32(selectedRow.Cells["id"].Value);

                    await DeletePublishedMedicine(publishedMedicineId);
                    await LoadMyPublishedMedicines();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الدواء المنشور: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnRefreshMyPublished_Click(object sender, EventArgs e)
        {
            await LoadMyPublishedMedicines();
        }

        private void txtSearchLocal_TextChanged(object sender, EventArgs e)
        {
            try
            {
                string searchText = txtSearchLocal.Text.Trim();

                if (dataGridViewLocalMedicines.DataSource is DataTable dataTable)
                {
                    if (string.IsNullOrEmpty(searchText))
                    {
                        // إظهار جميع البيانات
                        dataTable.DefaultView.RowFilter = "";
                    }
                    else
                    {
                        // البحث في اسم الدواء ورقم الدواء
                        string filter = $"medicineName LIKE '%{searchText}%' OR medicineNumber LIKE '%{searchText}%'";
                        dataTable.DefaultView.RowFilter = filter;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث: {ex.Message}");
            }
        }

        private async Task DeletePublishedMedicine(int publishedMedicineId)
        {
            try
            {
                string deleteQuery = @"
                    UPDATE published_medicines
                    SET is_available = 0
                    WHERE id = @publishedMedicineId AND pharmacy_id = @pharmacyId";

                var deleteParams = new System.Collections.Generic.Dictionary<string, object>
                {
                    {"@publishedMedicineId", publishedMedicineId},
                    {"@pharmacyId", SessionManager.CurrentPharmacyId}
                };

                unifiedDb.ExecuteQuery(deleteQuery, deleteParams);

                MessageBox.Show("تم حذف الدواء المنشور بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف الدواء المنشور: {ex.Message}");
            }
        }

        private async Task<int> GetMaxAvailableQuantity(string medicineName)
        {
            try
            {
                string query = "SELECT ISNULL(quantity, 0) FROM medic WHERE mname = @medicineName";
                var parameters = new System.Collections.Generic.Dictionary<string, object>
                {
                    {"@medicineName", medicineName}
                };

                DataSet result = unifiedDb.ExecuteQuery(query, parameters);
                if (result.Tables[0].Rows.Count > 0)
                {
                    return Convert.ToInt32(result.Tables[0].Rows[0][0]);
                }
                return 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على الكمية المتاحة: {ex.Message}");
                return 0;
            }
        }

        private async Task<List<DosageInfo>> GetMedicineDosages(string medicineName)
        {
            var dosages = new List<DosageInfo>();

            try
            {
                string query = @"
                    SELECT dos2, dos2_qty, dos3, dos3_qty, dos4, dos4_qty, mnumber_qty
                    FROM medic
                    WHERE mname = @medicineName";

                var parameters = new System.Collections.Generic.Dictionary<string, object>
                {
                    {"@medicineName", medicineName}
                };

                DataSet result = unifiedDb.ExecuteQuery(query, parameters);
                if (result.Tables[0].Rows.Count > 0)
                {
                    var row = result.Tables[0].Rows[0];

                    // الجرعة الأساسية
                    int mainQty = Convert.ToInt32(row["mnumber_qty"] ?? 0);
                    if (mainQty > 0)
                    {
                        dosages.Add(new DosageInfo
                        {
                            DosageName = "الجرعة الأساسية",
                            Quantity = mainQty,
                            Price = 0 // سيتم تحديده من السعر الأساسي
                        });
                    }

                    // الجرعة الثانية
                    if (row["dos2"] != DBNull.Value && Convert.ToInt64(row["dos2"]) > 0)
                    {
                        dosages.Add(new DosageInfo
                        {
                            DosageName = $"جرعة {row["dos2"]}",
                            Quantity = Convert.ToInt32(row["dos2_qty"] ?? 0),
                            Price = 0
                        });
                    }

                    // الجرعة الثالثة
                    if (row["dos3"] != DBNull.Value && Convert.ToInt64(row["dos3"]) > 0)
                    {
                        dosages.Add(new DosageInfo
                        {
                            DosageName = $"جرعة {row["dos3"]}",
                            Quantity = Convert.ToInt32(row["dos3_qty"] ?? 0),
                            Price = 0
                        });
                    }

                    // الجرعة الرابعة
                    if (row["dos4"] != DBNull.Value && Convert.ToInt64(row["dos4"]) > 0)
                    {
                        dosages.Add(new DosageInfo
                        {
                            DosageName = $"جرعة {row["dos4"]}",
                            Quantity = Convert.ToInt32(row["dos4_qty"] ?? 0),
                            Price = 0
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على معلومات الجرعات: {ex.Message}");
            }

            return dosages;
        }

        private async Task UpdatePublishedMedicine(int publishedMedicineId, int newQuantity,
            decimal newPrice, string newDescription, List<DosageInfo> dosages)
        {
            try
            {
                // تحديث البيانات الأساسية
                string updateQuery = @"
                    UPDATE published_medicines
                    SET quantity_available = @quantity,
                        price_per_unit = @price,
                        description = @description
                    WHERE id = @publishedMedicineId AND pharmacy_id = @pharmacyId";

                int currentPharmacyId = 1;
                try
                {
                    if (SessionManager.CurrentPharmacyId > 0)
                        currentPharmacyId = SessionManager.CurrentPharmacyId;
                }
                catch
                {
                    System.Diagnostics.Debug.WriteLine("استخدام pharmacy ID افتراضي: 1");
                }

                var updateParams = new System.Collections.Generic.Dictionary<string, object>
                {
                    {"@quantity", newQuantity},
                    {"@price", newPrice},
                    {"@description", newDescription ?? ""},
                    {"@publishedMedicineId", publishedMedicineId},
                    {"@pharmacyId", currentPharmacyId}
                };

                unifiedDb.ExecuteQuery(updateQuery, updateParams);

                // TODO: تحديث معلومات الجرعات إذا لزم الأمر
                // يمكن إضافة جدول منفصل للجرعات المنشورة في المستقبل
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث الدواء المنشور: {ex.Message}");
            }
        }

        // البحث والفلترة
        private async void txtSearchPublished_TextChanged(object sender, EventArgs e)
        {
            if (!isLoading)
            {
                await ApplyFilters();
            }
        }

        private async void cmbFilterExpiry_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (!isLoading)
            {
                await ApplyFilters();
            }
        }

        private async void cmbFilterPharmacy_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (!isLoading)
            {
                await ApplyFilters();
            }
        }

        private async Task ApplyFilters()
        {
            try
            {
                string searchText = txtSearchPublished?.Text?.Trim() ?? "";
                string expiryFilter = cmbFilterExpiry?.SelectedItem?.ToString() ?? "";
                string pharmacyFilter = cmbFilterPharmacy?.SelectedItem?.ToString() ?? "";

                string query = @"
                    SELECT
                        pm.id,
                        pm.medicine_name as medicineName,
                        pm.quantity_available as quantity,
                        pm.expiry_date as expiryDate,
                        pm.price_per_unit as pricePerUnit,
                        pm.description,
                        pm.published_date as publishDate,
                        p.pharmacyName as pharmacyName,
                        p.phone as pharmacyPhone,
                        p.address as pharmacyAddress,
                        p.city as pharmacyCity,
                        DATEDIFF(day, GETDATE(), pm.expiry_date) as daysToExpiry
                    FROM published_medicines pm
                    INNER JOIN pharmacies p ON pm.pharmacy_id = p.id
                    WHERE pm.is_available = 1
                    AND pm.expiry_date > GETDATE()
                    AND pm.pharmacy_id != @currentPharmacyId";

                var parameters = new System.Collections.Generic.Dictionary<string, object>
                {
                    {"@currentPharmacyId", SessionManager.CurrentPharmacyId}
                };

                // فلتر البحث بالاسم
                if (!string.IsNullOrEmpty(searchText))
                {
                    query += " AND pm.medicine_name LIKE @searchText";
                    parameters.Add("@searchText", $"%{searchText}%");
                }

                // فلتر التاريخ
                if (chkFilterByDate != null && chkFilterByDate.Checked && dtpFilterDate != null)
                {
                    query += " AND CAST(pm.published_date AS DATE) = @filterDate";
                    parameters.Add("@filterDate", dtpFilterDate.Value.Date);
                }

                // فلتر تاريخ الانتهاء
                if (!string.IsNullOrEmpty(expiryFilter))
                {
                    switch (expiryFilter)
                    {
                        case "خلال 30 يوم":
                            query += " AND DATEDIFF(day, GETDATE(), pm.expiry_date) <= 30";
                            break;
                        case "خلال 90 يوم":
                            query += " AND DATEDIFF(day, GETDATE(), pm.expiry_date) <= 90";
                            break;
                        case "خلال 180 يوم":
                            query += " AND DATEDIFF(day, GETDATE(), pm.expiry_date) <= 180";
                            break;
                    }
                }

                // فلتر الصيدلية
                if (!string.IsNullOrEmpty(pharmacyFilter) && pharmacyFilter != "جميع الصيدليات")
                {
                    query += " AND p.pharmacyName = @pharmacyFilter";
                    parameters.Add("@pharmacyFilter", pharmacyFilter);
                }

                query += " ORDER BY pm.published_date DESC";

                DataSet result = unifiedDb.ExecuteQuery(query, parameters);

                if (dataGridViewPublishedMedicines != null)
                {
                    dataGridViewPublishedMedicines.DataSource = result.Tables[0];
                    FormatPublishedMedicinesGrid();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الفلاتر: {ex.Message}");
            }
        }

        private void LoadPharmacyFilter()
        {
            try
            {
                string query = @"
                    SELECT DISTINCT p.pharmacyName
                    FROM published_medicines pm
                    INNER JOIN pharmacies p ON pm.pharmacy_id = p.id
                    WHERE pm.is_available = 1
                    AND pm.pharmacy_id != @currentPharmacyId
                    ORDER BY p.pharmacyName";

                var parameters = new System.Collections.Generic.Dictionary<string, object>
                {
                    {"@currentPharmacyId", SessionManager.CurrentPharmacyId}
                };

                DataSet result = unifiedDb.ExecuteQuery(query, parameters);

                if (cmbFilterPharmacy != null)
                {
                    cmbFilterPharmacy.Items.Clear();
                    cmbFilterPharmacy.Items.Add("جميع الصيدليات");

                    foreach (DataRow row in result.Tables[0].Rows)
                    {
                        cmbFilterPharmacy.Items.Add(row["pharmacyName"].ToString());
                    }

                    cmbFilterPharmacy.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل فلتر الصيدليات: {ex.Message}");
            }
        }

        // أحداث التحديث والتطبيق
        private async void RefreshTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                if (!isLoading && !IsDisposed)
                {
                    System.Diagnostics.Debug.WriteLine("🔄 تحديث تلقائي للبيانات...");
                    await LoadPublishedMedicines();
                    await LoadMyPublishedMedicines();
                    await LoadMedicineRequests();
                    System.Diagnostics.Debug.WriteLine("✅ تم التحديث التلقائي بنجاح");
                }
            }
            catch (ObjectDisposedException)
            {
                // تم التخلص من الكائن، إيقاف المؤقت
                if (refreshTimer != null)
                {
                    refreshTimer.Stop();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التحديث التلقائي: {ex.Message}");
            }
        }

        private void UpdateTexts()
        {
            ApplyLanguage();
        }

        private void ApplyLanguage()
        {
            try
            {
                // تطبيق النصوص العربية
                tabPageLocalMedicines.Text = "الأدوية المحلية";
                tabPagePublishedMedicines.Text = "الأدوية المنشورة";
                tabPageMyPublished.Text = "منشوراتي";
                btnPublishMedicine.Text = "نشر دواء";
                btnRequestMedicine.Text = "طلب شراء";

                // أعمدة الجداول
                if (dataGridViewLocalMedicines.Columns.Count > 0)
                {
                    dataGridViewLocalMedicines.Columns["MedicineName"].HeaderText = "اسم الدواء";
                    dataGridViewLocalMedicines.Columns["Quantity"].HeaderText = "الكمية";
                    dataGridViewLocalMedicines.Columns["ExpiryDate"].HeaderText = "تاريخ الانتهاء";
                    dataGridViewLocalMedicines.Columns["Price"].HeaderText = "السعر";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق اللغة: {ex.Message}");
            }
        }



        private void ApplyModernDesign()
        {
            try
            {
                // تطبيق التصميم العصري
                this.BackColor = Color.White;

                // تطبيق التصميم على الجداول
                ApplyModernDataGridStyle(dataGridViewLocalMedicines);
                ApplyModernDataGridStyle(dataGridViewPublishedMedicines);
                ApplyModernDataGridStyle(dataGridViewMyPublished);
                ApplyModernDataGridStyle(dataGridViewMedicineRequests);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق التصميم العصري: {ex.Message}");
            }
        }

        private void ApplyModernDataGridStyle(DataGridView dgv)
        {
            if (dgv == null) return;

            try
            {
                dgv.BackgroundColor = ModernTheme.BackgroundColor;
                dgv.ForeColor = ModernTheme.IsDarkMode ? Color.White : Color.Black;
                dgv.GridColor = ModernTheme.BorderColor;
                dgv.DefaultCellStyle.BackColor = ModernTheme.BackgroundColor;
                dgv.DefaultCellStyle.ForeColor = ModernTheme.IsDarkMode ? Color.White : Color.Black;
                dgv.DefaultCellStyle.SelectionBackColor = ModernTheme.AccentColor;
                dgv.DefaultCellStyle.SelectionForeColor = Color.White;
                dgv.ColumnHeadersDefaultCellStyle.BackColor = ModernTheme.PrimaryColor;
                dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
                dgv.EnableHeadersVisualStyles = false;
                dgv.BorderStyle = BorderStyle.None;
                dgv.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
                dgv.RowHeadersVisible = false;
                dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
                dgv.MultiSelect = false;
                dgv.ReadOnly = true;
                dgv.AllowUserToAddRows = false;
                dgv.AllowUserToDeleteRows = false;
                dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق تصميم الجدول: {ex.Message}");
            }
        }



        private string ShowInputDialog(string prompt, string title, string defaultValue = "")
        {
            Form inputForm = new Form()
            {
                Width = 400,
                Height = 200,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                Text = title,
                StartPosition = FormStartPosition.CenterParent,
                MaximizeBox = false,
                MinimizeBox = false
            };

            Label lblPrompt = new Label() { Left = 20, Top = 20, Width = 350, Text = prompt };
            TextBox txtInput = new TextBox() { Left = 20, Top = 50, Width = 350, Text = defaultValue };
            Button btnOK = new Button() { Text = "موافق", Left = 200, Width = 80, Top = 90, DialogResult = DialogResult.OK };
            Button btnCancel = new Button() { Text = "إلغاء", Left = 290, Width = 80, Top = 90, DialogResult = DialogResult.Cancel };

            inputForm.Controls.Add(lblPrompt);
            inputForm.Controls.Add(txtInput);
            inputForm.Controls.Add(btnOK);
            inputForm.Controls.Add(btnCancel);
            inputForm.AcceptButton = btnOK;
            inputForm.CancelButton = btnCancel;

            return inputForm.ShowDialog() == DialogResult.OK ? txtInput.Text : "";
        }



        private void tabPageLocalMedicines_Click(object sender, EventArgs e)
        {

        }

        private async void btnRefreshLocal_Click(object sender, EventArgs e)
        {
            try
            {
                await LoadLocalMedicines();
                MessageBox.Show("تم تحديث قائمة الأدوية المحلية بنجاح!", "تحديث", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث القائمة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnRefreshPublished_Click(object sender, EventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 تحديث يدوي للأدوية المنشورة...");
                await LoadPublishedMedicines();
                System.Diagnostics.Debug.WriteLine("✅ تم التحديث اليدوي بنجاح");
                // إزالة رسالة التأكيد المزعجة - البيانات تتحدث تلقائياً الآن
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث القائمة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void chkFilterByDate_CheckedChanged(object sender, EventArgs e)
        {
            if (dtpFilterDate != null)
            {
                dtpFilterDate.Enabled = chkFilterByDate.Checked;
                _ = ApplyFilters();
            }
        }

        private void dtpFilterDate_ValueChanged(object sender, EventArgs e)
        {
            if (chkFilterByDate != null && chkFilterByDate.Checked)
            {
                _ = ApplyFilters();
            }
        }

        #region Notification System

        /// <summary>
        /// تحديث عرض الإشعارات
        /// </summary>
        private void UpdateNotificationDisplay()
        {
            try
            {
                if (notificationManager == null || IsDisposed) return;

                int unreadCount = notificationManager.GetUnreadNotificationCount();

                if (lblNotificationCount != null)
                {
                    lblNotificationCount.Text = unreadCount.ToString();
                    lblNotificationCount.Visible = unreadCount > 0;
                }

                // تحديث لون الزر حسب وجود إشعارات
                if (btnNotifications != null)
                {
                    btnNotifications.BackColor = unreadCount > 0 ?
                        Color.FromArgb(220, 53, 69) : // أحمر للإشعارات الجديدة
                        Color.FromArgb(0, 122, 204);   // أزرق عادي
                }
            }
            catch (ObjectDisposedException)
            {
                // تم التخلص من الكائن، لا حاجة لعرض رسالة خطأ
                System.Diagnostics.Debug.WriteLine("⚠️ تم التخلص من UC_P_PharmacyStore أثناء تحديث الإشعارات");
            }
            catch (Exception ex)
            {
                if (!IsDisposed)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث عرض الإشعارات: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// مؤقت تحديث الإشعارات
        /// </summary>
        private void NotificationTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                if (!IsDisposed)
                {
                    UpdateNotificationDisplay();
                }
            }
            catch (ObjectDisposedException)
            {
                // تم التخلص من الكائن، إيقاف المؤقت
                if (notificationTimer != null)
                {
                    notificationTimer.Stop();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث الإشعارات: {ex.Message}");
            }
        }

        /// <summary>
        /// النقر على زر الإشعارات
        /// </summary>
        private void btnNotifications_Click(object sender, EventArgs e)
        {
            try
            {
                if (panelNotificationDropdown != null)
                {
                    // إظهار/إخفاء قائمة الإشعارات
                    panelNotificationDropdown.Visible = !panelNotificationDropdown.Visible;

                    if (panelNotificationDropdown.Visible)
                    {
                        LoadNotificationsList();
                        panelNotificationDropdown.BringToFront();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض الإشعارات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل قائمة الإشعارات
        /// </summary>
        private void LoadNotificationsList()
        {
            try
            {
                if (listViewNotifications == null || notificationManager == null) return;

                listViewNotifications.Items.Clear();
                listViewNotifications.Columns.Clear();

                // إعداد أعمدة القائمة
                listViewNotifications.Columns.Add("النوع", 80);
                listViewNotifications.Columns.Add("العنوان", 150);
                listViewNotifications.Columns.Add("التاريخ", 100);

                // تحميل الإشعارات
                DataSet notifications = notificationManager.GetNotifications(true); // الإشعارات غير المقروءة فقط

                if (notifications.Tables.Count > 0)
                {
                    foreach (DataRow row in notifications.Tables[0].Rows)
                    {
                        var item = new ListViewItem(row["typeDisplay"].ToString());
                        item.SubItems.Add(row["title"].ToString());
                        item.SubItems.Add(Convert.ToDateTime(row["createdDate"]).ToString("MM/dd HH:mm"));
                        item.Tag = row["id"]; // حفظ ID الإشعار

                        // تلوين الإشعارات غير المقروءة
                        if (!Convert.ToBoolean(row["isRead"]))
                        {
                            item.BackColor = Color.LightYellow;
                            item.Font = new Font(item.Font, FontStyle.Bold);
                        }

                        listViewNotifications.Items.Add(item);
                    }
                }

                // إضافة عنصر لعرض جميع الإشعارات
                if (notifications.Tables[0].Rows.Count == 0)
                {
                    var noNotificationsItem = new ListViewItem("لا توجد إشعارات جديدة");
                    noNotificationsItem.SubItems.Add("");
                    noNotificationsItem.SubItems.Add("");
                    noNotificationsItem.ForeColor = Color.Gray;
                    listViewNotifications.Items.Add(noNotificationsItem);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل قائمة الإشعارات: {ex.Message}");
            }
        }

        /// <summary>
        /// النقر المزدوج على إشعار
        /// </summary>
        private void listViewNotifications_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                if (listViewNotifications.SelectedItems.Count > 0)
                {
                    var selectedItem = listViewNotifications.SelectedItems[0];
                    if (selectedItem.Tag != null)
                    {
                        int notificationId = Convert.ToInt32(selectedItem.Tag);
                        HandleNotificationClick(notificationId);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معالجة الإشعار: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// معالجة النقر على إشعار
        /// </summary>
        private void HandleNotificationClick(int notificationId)
        {
            try
            {
                // تحديد الإشعار كمقروء
                notificationManager.MarkNotificationAsRead(notificationId);

                // الحصول على تفاصيل الإشعار
                DataRow notificationDetails = notificationManager.GetNotificationDetails(notificationId);
                if (notificationDetails == null) return;

                string notificationType = notificationDetails["notification_type"].ToString();

                // معالجة حسب نوع الإشعار
                switch (notificationType)
                {
                    case "purchase_request":
                        // فتح نافذة طلبات الشراء أو المحادثة
                        OpenChatForNotification(notificationId);
                        break;
                    case "message":
                        // فتح المحادثة
                        OpenChatForNotification(notificationId);
                        break;
                    default:
                        // عرض تفاصيل الإشعار
                        MessageBox.Show(notificationDetails["content"].ToString(),
                                      notificationDetails["title"].ToString(),
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                        break;
                }

                // تحديث عرض الإشعارات
                UpdateNotificationDisplay();
                LoadNotificationsList();

                // إخفاء قائمة الإشعارات
                if (panelNotificationDropdown != null)
                {
                    panelNotificationDropdown.Visible = false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معالجة الإشعار: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// فتح المحادثة من الإشعار
        /// </summary>
        private void OpenChatForNotification(int notificationId)
        {
            try
            {
                // الحصول على معلومات الصيدلية من الإشعار
                DataRow pharmacyInfo = notificationManager.GetPharmacyFromNotification(notificationId);
                if (pharmacyInfo != null)
                {
                    int pharmacyId = Convert.ToInt32(pharmacyInfo["pharmacyId"]);
                    string pharmacyName = pharmacyInfo["pharmacyName"].ToString();

                    // فتح نافذة المحادثة
                    OpenChatWithPharmacy(pharmacyId, pharmacyName);
                }
                else
                {
                    MessageBox.Show("لا يمكن العثور على معلومات الصيدلية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح المحادثة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// فتح محادثة مع صيدلية
        /// </summary>
        private void OpenChatWithPharmacy(int pharmacyId, string pharmacyName)
        {
            try
            {
                // التحقق من معرف الصيدلية الحالية
                if (SessionManager.CurrentPharmacyId <= 0)
                {
                    MessageBox.Show("خطأ: لم يتم تحديد صيدلية. يرجى تسجيل الدخول مرة أخرى.", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // التحقق من صحة معرف الصيدلية المستهدفة
                if (pharmacyId <= 0)
                {
                    MessageBox.Show("خطأ: معرف الصيدلية المستهدفة غير صحيح.", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // فتح نافذة المحادثة
                var chatForm = new PharmacyChatForm(pharmacyId, pharmacyName);
                chatForm.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة المحادثة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Medicine Requests Functions

        /// <summary>
        /// تحميل طلبات الأدوية من قاعدة البيانات
        /// </summary>
        private async Task LoadMedicineRequests()
        {
            try
            {
                if (isLoading || IsDisposed) return;
                isLoading = true;

                int currentPharmacyId = SessionManager.CurrentPharmacyId;

                // التحقق من معرف الصيدلية
                if (currentPharmacyId <= 0)
                {
                    System.Diagnostics.Debug.WriteLine("❌ معرف الصيدلية غير صحيح");
                    if (dataGridViewMedicineRequests != null)
                    {
                        dataGridViewMedicineRequests.DataSource = null;
                    }
                    return;
                }

                // تقليل رسائل التشخيص المتكررة
                if (currentPharmacyId != lastLoggedPharmacyId)
                {
                    System.Diagnostics.Debug.WriteLine($"🔍 تحميل طلبات الأدوية للصيدلية ID: {currentPharmacyId}");
                    lastLoggedPharmacyId = currentPharmacyId;
                }

                if (currentPharmacyId <= 0)
                {
                    System.Diagnostics.Debug.WriteLine("❌ خطأ في معرف الصيدلية - استخدام ID افتراضي");
                    MessageBox.Show("تحذير: معرف الصيدلية غير صحيح. سيتم استخدام الصيدلية الافتراضية للاختبار.",
                                  "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    currentPharmacyId = 1; // استخدام ID افتراضي للاختبار
                }

                string query = @"
                    SELECT
                        pr.id,
                        pr.requested_quantity as requestedQuantity,
                        pr.offered_price as offeredPrice,
                        pr.request_date as requestDate,
                        pr.status,
                        ISNULL(pr.response_message, '') as responseMessage,
                        ISNULL(pr.request_message, '') as requestMessage,
                        m.mname as medicineName,
                        ISNULL(m.mnumber, '') as medicineNumber,
                        m.perUnit as originalPrice,
                        p_buyer.pharmacyName as buyerPharmacyName,
                        ISNULL(p_buyer.phone, '') as buyerPhone,
                        ISNULL(p_buyer.city, '') as buyerCity
                    FROM purchase_requests pr
                    INNER JOIN medic m ON pr.medicine_id = m.id AND m.pharmacy_id = pr.seller_pharmacy_id
                    INNER JOIN pharmacies p_buyer ON pr.buyer_pharmacy_id = p_buyer.id
                    WHERE pr.seller_pharmacy_id = @pharmacyId
                    ORDER BY pr.request_date DESC";

                var parameters = new Dictionary<string, object>
                {
                    { "@pharmacyId", currentPharmacyId }
                };

                System.Diagnostics.Debug.WriteLine($"🔍 تنفيذ الاستعلام: {query}");
                System.Diagnostics.Debug.WriteLine($"🔍 معرف الصيدلية: {currentPharmacyId}");

                // اختبار الاتصال بقاعدة البيانات أولاً
                try
                {
                    string testQuery = "SELECT COUNT(*) as total FROM purchase_requests";
                    DataSet testResult = unifiedDb.ExecuteQuery(testQuery, null);
                    if (testResult != null && testResult.Tables.Count > 0)
                    {
                        int totalRequests = Convert.ToInt32(testResult.Tables[0].Rows[0]["total"]);
                        System.Diagnostics.Debug.WriteLine($"📊 إجمالي الطلبات في قاعدة البيانات: {totalRequests}");
                    }
                }
                catch (Exception testEx)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في اختبار قاعدة البيانات: {testEx.Message}");
                    MessageBox.Show($"خطأ في الاتصال بقاعدة البيانات: {testEx.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                DataSet dataSet = unifiedDb.ExecuteQuery(query, parameters);
                System.Diagnostics.Debug.WriteLine($"🔍 نتيجة الاستعلام - DataSet: {(dataSet != null ? "موجود" : "null")}");

                if (dataSet != null)
                {
                    System.Diagnostics.Debug.WriteLine($"🔍 عدد الجداول: {dataSet.Tables.Count}");

                    if (dataSet.Tables.Count > 0)
                    {
                        DataTable dataTable = dataSet.Tables[0];
                        System.Diagnostics.Debug.WriteLine($"📊 عدد الطلبات المسترجعة: {dataTable.Rows.Count}");
                        System.Diagnostics.Debug.WriteLine($"📊 عدد الأعمدة: {dataTable.Columns.Count}");

                        // عرض أسماء الأعمدة
                        string columnNames = string.Join(", ", dataTable.Columns.Cast<DataColumn>().Select(c => c.ColumnName));
                        System.Diagnostics.Debug.WriteLine($"📋 أسماء الأعمدة: {columnNames}");

                        if (dataTable.Rows.Count > 0)
                        {
                            // عرض تفاصيل أول طلب للتشخيص
                            var firstRow = dataTable.Rows[0];
                            System.Diagnostics.Debug.WriteLine($"📋 أول طلب - الدواء: {firstRow["medicineName"]}, الحالة: {firstRow["status"]}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("⚠️ لا توجد صفوف في النتيجة");

                            // اختبار إضافي - البحث عن طلبات بدون شروط
                            try
                            {
                                string simpleQuery = "SELECT COUNT(*) as total FROM purchase_requests WHERE seller_pharmacy_id = @pharmacyId";
                                DataSet simpleResult = unifiedDb.ExecuteQuery(simpleQuery, parameters);
                                if (simpleResult != null && simpleResult.Tables.Count > 0)
                                {
                                    int count = Convert.ToInt32(simpleResult.Tables[0].Rows[0]["total"]);
                                    System.Diagnostics.Debug.WriteLine($"🔍 عدد الطلبات للصيدلية {currentPharmacyId}: {count}");
                                }
                            }
                            catch (Exception simpleEx)
                            {
                                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الاستعلام البسيط: {simpleEx.Message}");
                            }
                        }

                        if (dataGridViewMedicineRequests != null)
                        {
                            dataGridViewMedicineRequests.DataSource = dataTable;
                            FormatMedicineRequestsGrid();
                            System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {dataTable.Rows.Count} طلب دواء");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("❌ dataGridViewMedicineRequests is null!");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("⚠️ لا توجد جداول في النتيجة");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ DataSet فارغ");
                    if (dataGridViewMedicineRequests != null)
                    {
                        dataGridViewMedicineRequests.DataSource = null;
                    }
                }
            }
            catch (ObjectDisposedException)
            {
                // تم التخلص من الكائن، لا حاجة لعرض رسالة خطأ
                System.Diagnostics.Debug.WriteLine("⚠️ تم التخلص من UC_P_PharmacyStore أثناء تحميل طلبات الأدوية");
            }
            catch (Exception ex)
            {
                if (!IsDisposed)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في LoadMedicineRequests: {ex.Message}");
                    // عرض رسالة خطأ فقط إذا لم يتم التخلص من الكائن
                    MessageBox.Show($"خطأ في تحميل طلبات الأدوية: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            finally
            {
                isLoading = false;
            }
        }

        /// <summary>
        /// تنسيق جدول طلبات الأدوية
        /// </summary>
        private void FormatMedicineRequestsGrid()
        {
            try
            {
                if (dataGridViewMedicineRequests == null || dataGridViewMedicineRequests.Columns.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ dataGridViewMedicineRequests is null or has no columns");
                    return;
                }

                // إخفاء الأعمدة غير المرغوب فيها
                if (dataGridViewMedicineRequests.Columns.Contains("id"))
                    dataGridViewMedicineRequests.Columns["id"].Visible = false;

                // تعيين عناوين الأعمدة
                var columnHeaders = new Dictionary<string, string>
                {
                    { "medicineName", "اسم الدواء" },
                    { "medicineNumber", "رقم الدواء" },
                    { "requestedQuantity", "الكمية المطلوبة" },
                    { "originalPrice", "السعر الأصلي" },
                    { "offeredPrice", "السعر المعروض" },
                    { "buyerPharmacyName", "الصيدلية الطالبة" },
                    { "buyerPhone", "الهاتف" },
                    { "buyerCity", "المدينة" },
                    { "requestDate", "تاريخ الطلب" },
                    { "status", "الحالة" },
                    { "responseMessage", "رسالة الرد" },
                    { "description", "الوصف" }
                };

                foreach (var header in columnHeaders)
                {
                    if (dataGridViewMedicineRequests.Columns.Contains(header.Key))
                    {
                        dataGridViewMedicineRequests.Columns[header.Key].HeaderText = header.Value;
                    }
                }

                // تطبيق التصميم العصري
                ApplyModernDataGridStyle(dataGridViewMedicineRequests);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في FormatMedicineRequestsGrid: {ex.Message}");
            }
        }

        /// <summary>
        /// التعامل مع رد على طلب الدواء
        /// </summary>
        private async Task HandleRequestResponse(int requestId, string status, string responseMessage)
        {
            try
            {
                string query = @"
                    UPDATE purchase_requests
                    SET status = @status, response_message = @responseMessage, response_date = GETDATE()
                    WHERE id = @requestId";

                var parameters = new Dictionary<string, object>
                {
                    { "@status", status },
                    { "@responseMessage", responseMessage },
                    { "@requestId", requestId }
                };

                unifiedDb.ExecuteQuery(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث حالة الطلب: {ex.Message}");
            }
        }

        #endregion

        #region Dispose and Cleanup

       
        
        #endregion

        #region Medicine Requests Tab Events

        /// <summary>
        /// تحديث طلبات الأدوية
        /// </summary>
        private async void BtnRefreshRequests_Click(object sender, EventArgs e)
        {
            await LoadMedicineRequests();
        }

        /// <summary>
        /// قبول طلب الدواء
        /// </summary>
        private async void BtnAcceptRequest_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridViewMedicineRequests.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار طلب للقبول", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedRow = dataGridViewMedicineRequests.SelectedRows[0];
                int requestId = Convert.ToInt32(selectedRow.Cells["id"].Value);
                string medicineName = selectedRow.Cells["medicineName"].Value.ToString();
                string buyerPharmacyName = selectedRow.Cells["buyerPharmacyName"].Value.ToString();

                var result = MessageBox.Show(
                    $"هل تريد قبول طلب {medicineName} من {buyerPharmacyName}؟",
                    "تأكيد القبول",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    await HandleRequestResponse(requestId, "accepted", "تم قبول الطلب");
                    MessageBox.Show("تم قبول الطلب بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    await LoadMedicineRequests();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في قبول الطلب: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// رفض طلب الدواء
        /// </summary>
        private async void BtnRejectRequest_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridViewMedicineRequests.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار طلب للرفض", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedRow = dataGridViewMedicineRequests.SelectedRows[0];
                int requestId = Convert.ToInt32(selectedRow.Cells["id"].Value);
                string medicineName = selectedRow.Cells["medicineName"].Value.ToString();
                string buyerPharmacyName = selectedRow.Cells["buyerPharmacyName"].Value.ToString();

                var result = MessageBox.Show(
                    $"هل تريد رفض طلب {medicineName} من {buyerPharmacyName}؟",
                    "تأكيد الرفض",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    await HandleRequestResponse(requestId, "rejected", "تم رفض الطلب");
                    MessageBox.Show("تم رفض الطلب", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    await LoadMedicineRequests();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في رفض الطلب: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// فتح نافذة الرسائل
        /// </summary>
        private void BtnMessagesRequests_Click(object sender, EventArgs e)
        {
            try
            {
                var messagesForm = new PharmacyMessagesForm();
                messagesForm.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة الرسائل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}
