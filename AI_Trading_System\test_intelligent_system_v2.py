#!/usr/bin/env python3
"""
Test Intelligent Trading System V2
اختبار النظام الذكي المتطور
"""

import sys
import os
import time
from datetime import datetime

# إضافة المسار للبحث عن الوحدات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_system_components():
    """
    اختبار مكونات النظام
    """
    print("🧪 اختبار مكونات النظام الذكي المتطور")
    print("="*50)
    
    # اختبار استيراد المكتبات الأساسية
    print("1️⃣ اختبار المكتبات الأساسية...")
    
    try:
        import MetaTrader5 as mt5
        print("   ✅ MetaTrader5: متوفر")
    except ImportError:
        print("   ❌ MetaTrader5: غير متوفر")
        print("   💡 تثبيت: pip install MetaTrader5")
    
    try:
        import pandas as pd
        import numpy as np
        print("   ✅ Pandas & Numpy: متوفر")
    except ImportError:
        print("   ❌ Pandas/Numpy: غير متوفر")
        print("   💡 تثبيت: pip install pandas numpy")
    
    try:
        import talib
        print("   ✅ TA-Lib: متوفر")
    except ImportError:
        print("   ⚠️ TA-Lib: غير متوفر (سيتم استخدام مؤشرات مبسطة)")
    
    try:
        from sklearn.ensemble import RandomForestClassifier
        print("   ✅ Scikit-learn: متوفر")
    except ImportError:
        print("   ⚠️ Scikit-learn: غير متوفر (سيتم استخدام نموذج مبسط)")
    
    # اختبار الوحدات المطورة
    print("\n2️⃣ اختبار الوحدات المطورة...")
    
    try:
        from advanced_analysis_engine import AdvancedAnalysisEngine
        print("   ✅ محرك التحليل المتقدم: متوفر")
        
        # اختبار إنشاء المحرك
        engine = AdvancedAnalysisEngine("EURUSD", 10.0)
        print("   ✅ تم إنشاء محرك التحليل بنجاح")
        
    except ImportError as e:
        print(f"   ❌ محرك التحليل المتقدم: غير متوفر ({e})")
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء محرك التحليل: {e}")
    
    try:
        from smart_money_manager import SmartMoneyManager
        print("   ✅ مدير رأس المال الذكي: متوفر")
        
        # اختبار إنشاء المدير
        manager = SmartMoneyManager("EURUSD", 10.0)
        print("   ✅ تم إنشاء مدير رأس المال بنجاح")
        
        # اختبار حساب حجم الصفقة
        result = manager.calculate_optimal_position_size(100.0, 0.7, 50.0, 2.0)
        print(f"   ✅ اختبار حساب حجم الصفقة: {result['volume']}")
        
    except ImportError as e:
        print(f"   ❌ مدير رأس المال الذكي: غير متوفر ({e})")
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء مدير رأس المال: {e}")
    
    try:
        from intelligent_trading_system_v2 import IntelligentTradingSystemV2
        print("   ✅ النظام الذكي المتطور: متوفر")
        
        # اختبار إنشاء النظام
        system = IntelligentTradingSystemV2()
        print("   ✅ تم إنشاء النظام الذكي بنجاح")
        
        # اختبار حالة النظام
        status = system.get_system_status()
        print(f"   ✅ حالة النظام: {status['symbol']}")
        
    except ImportError as e:
        print(f"   ❌ النظام الذكي المتطور: غير متوفر ({e})")
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء النظام الذكي: {e}")

def test_mt5_connection():
    """
    اختبار الاتصال بـ MT5
    """
    print("\n3️⃣ اختبار الاتصال بـ MetaTrader 5...")
    
    try:
        import MetaTrader5 as mt5
        
        # تهيئة MT5
        if not mt5.initialize():
            print("   ❌ فشل في تهيئة MT5")
            print("   💡 تأكد من فتح MetaTrader 5")
            return False
        
        print("   ✅ تم تهيئة MT5 بنجاح")
        
        # معلومات الطرفية
        terminal_info = mt5.terminal_info()
        if terminal_info:
            print(f"   📊 إصدار الطرفية: {terminal_info.build}")
            print(f"   📊 التداول مسموح: {'✅' if terminal_info.trade_allowed else '❌'}")
        
        # اختبار الرمز
        symbol_info = mt5.symbol_info("EURUSD")
        if symbol_info:
            print("   ✅ رمز EURUSD متوفر")
            print(f"   📊 الحد الأدنى للحجم: {symbol_info.volume_min}")
            print(f"   📊 خطوة الحجم: {symbol_info.volume_step}")
        else:
            print("   ❌ رمز EURUSD غير متوفر")
        
        # اختبار البيانات
        rates = mt5.copy_rates_from_pos("EURUSD", mt5.TIMEFRAME_M15, 0, 10)
        if rates is not None and len(rates) > 0:
            print(f"   ✅ تم الحصول على {len(rates)} شمعة من البيانات")
        else:
            print("   ❌ لا يمكن الحصول على البيانات")
        
        mt5.shutdown()
        return True
        
    except ImportError:
        print("   ❌ مكتبة MetaTrader5 غير مثبتة")
        return False
    except Exception as e:
        print(f"   ❌ خطأ في اختبار MT5: {e}")
        return False

def test_money_management():
    """
    اختبار نظام إدارة رأس المال
    """
    print("\n4️⃣ اختبار نظام إدارة رأس المال...")
    
    try:
        from smart_money_manager import SmartMoneyManager
        
        manager = SmartMoneyManager("EURUSD", 10.0)
        
        # اختبار حسابات مختلفة
        test_balances = [10.0, 50.0, 100.0, 500.0, 1000.0, 5000.0]
        
        for balance in test_balances:
            account_type = manager.get_account_type(balance)
            result = manager.calculate_optimal_position_size(balance, 0.7, 50.0, 2.0)
            
            print(f"   💰 رصيد ${balance}: نوع {account_type}, حجم {result['volume']}, مخاطر {result['risk_percentage']:.1f}%")
        
        # اختبار تقرير المخاطر
        risk_report = manager.get_risk_report(1000.0)
        print(f"   📊 تقرير المخاطر: {risk_report['risk_status']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار إدارة رأس المال: {e}")
        return False

def test_analysis_engine():
    """
    اختبار محرك التحليل
    """
    print("\n5️⃣ اختبار محرك التحليل المتقدم...")
    
    try:
        from advanced_analysis_engine import AdvancedAnalysisEngine
        import pandas as pd
        import numpy as np
        
        engine = AdvancedAnalysisEngine("EURUSD", 10.0)
        
        # إنشاء بيانات تجريبية
        dates = pd.date_range(start='2024-01-01', periods=100, freq='H')
        np.random.seed(42)
        
        # محاكاة أسعار EURUSD
        base_price = 1.1000
        price_changes = np.random.normal(0, 0.001, 100)
        prices = base_price + np.cumsum(price_changes)
        
        test_data = pd.DataFrame({
            'Open': prices + np.random.normal(0, 0.0005, 100),
            'High': prices + np.abs(np.random.normal(0, 0.001, 100)),
            'Low': prices - np.abs(np.random.normal(0, 0.001, 100)),
            'Close': prices,
            'Volume': np.random.randint(1000, 10000, 100)
        }, index=dates)
        
        # اختبار حساب المؤشرات
        df_with_indicators = engine.calculate_advanced_indicators(test_data.copy())
        
        indicators_count = len([col for col in df_with_indicators.columns if col not in test_data.columns])
        print(f"   ✅ تم حساب {indicators_count} مؤشر فني")
        
        # اختبار تقييم ظروف السوق
        multi_data = {'H1': df_with_indicators}
        market_conditions = engine.assess_market_conditions(multi_data)
        print(f"   📊 حالة السوق: {market_conditions['condition']}")
        print(f"   📊 التوصية: {market_conditions['recommendation']}")
        print(f"   📊 الثقة: {market_conditions['confidence']:.2%}")
        
        # اختبار اتخاذ القرار
        decision = engine.make_intelligent_decision(multi_data, 1000.0)
        print(f"   🧠 القرار: {decision['decision']}")
        print(f"   🧠 الثقة: {decision['confidence']:.2%}")
        print(f"   🧠 السبب: {decision['reason']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار محرك التحليل: {e}")
        return False

def test_full_system():
    """
    اختبار النظام الكامل
    """
    print("\n6️⃣ اختبار النظام الكامل...")
    
    try:
        from intelligent_trading_system_v2 import IntelligentTradingSystemV2
        
        # إنشاء النظام
        system = IntelligentTradingSystemV2()
        print("   ✅ تم إنشاء النظام بنجاح")
        
        # اختبار حالة النظام
        status = system.get_system_status()
        print(f"   📊 الرمز: {status['symbol']}")
        print(f"   📊 الحد الأدنى للرصيد: ${status['min_balance']}")
        print(f"   📊 المكونات المتقدمة: {'✅' if status['advanced_modules_available'] else '❌'}")
        print(f"   📊 MT5 متوفر: {'✅' if status['mt5_available'] else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار النظام الكامل: {e}")
        return False

def main():
    """
    الدالة الرئيسية للاختبار
    """
    print("🚀 اختبار النظام الذكي المتطور - الإصدار الثاني")
    print("="*60)
    print(f"⏰ وقت الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # تشغيل الاختبارات
    tests = [
        ("اختبار المكونات", test_system_components),
        ("اختبار MT5", test_mt5_connection),
        ("اختبار إدارة رأس المال", test_money_management),
        ("اختبار محرك التحليل", test_analysis_engine),
        ("اختبار النظام الكامل", test_full_system)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ خطأ في {test_name}: {e}")
            results.append((test_name, False))
    
    # عرض النتائج النهائية
    print("\n" + "="*60)
    print("📋 ملخص نتائج الاختبار:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 النتيجة النهائية: {passed}/{total} اختبار نجح ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
    elif passed >= total * 0.8:
        print("⚠️ معظم الاختبارات نجحت. النظام يعمل مع بعض القيود.")
    else:
        print("❌ عدة اختبارات فشلت. راجع الأخطاء أعلاه.")
    
    print("\n💡 للحصول على أفضل النتائج:")
    print("   1. تأكد من تثبيت جميع المكتبات المطلوبة")
    print("   2. افتح MetaTrader 5 وفعّل التداول الآلي")
    print("   3. تأكد من صحة بيانات الدخول في config.ini")
    print("   4. جرب النظام على حساب Demo أولاً")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
