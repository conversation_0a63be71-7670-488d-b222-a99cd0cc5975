#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import MetaTrader5 as mt5
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
from typing import Dict, List, Optional
import time

# إعداد المكتبات للتحليل الفني
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    print("⚠️ تحذير: مكتبة TA-Lib غير متوفرة، سيتم استخدام مؤشرات بديلة")

from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler

class MT5RealCryptoSystem:
    def __init__(self, demo_mode=True):
        self.demo_mode = demo_mode
        self.connected = False
        self.mt5_connected = False
        
        # إعدادات التداول
        self.confidence_threshold = 70.0  # نسبة الثقة المطلوبة للدخول
        self.risk_per_trade = 0.02  # 2% من رأس المال لكل صفقة
        self.stop_loss_pips = 500   # وقف الخسارة
        self.take_profit_pips = 1000  # جني الربح
        
        # جميع أنواع الرموز المدعومة
        self.supported_symbols = {
            # العملات الرقمية
            'crypto': [
                'BTCUSD', 'ETHUSD', 'LTCUSD', 'XRPUSD', 'ADAUSD', 'DOTUSD',
                'LINKUSD', 'UNIUSD', 'BTC', 'ETH', 'BTCL', 'BTCO'
            ],
            # العملات الرئيسية
            'major_forex': [
                'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD'
            ],
            # الأزواج المتقاطعة
            'cross_pairs': [
                'AUDCAD', 'AUDCHF', 'AUDJPY', 'AUDNZD', 'CADCHF', 'CADJPY', 'CHFJPY',
                'EURAUD', 'EURCAD', 'EURCHF', 'EURGBP', 'EURJPY', 'EURNZD',
                'GBPAUD', 'GBPCAD', 'GBPCHF', 'GBPJPY', 'GBPNZD',
                'NZDCAD', 'NZDCHF', 'NZDJPY'
            ],
            # المعادن الثمينة
            'metals': [
                'XAUUSD', 'XAGUSD', 'XAUEUR', 'XAGEUR'
            ],
            # المؤشرات
            'indices': [
                'US30', 'US500', 'NAS100', 'GER30', 'UK100', 'FRA40', 'JPN225'
            ]
        }

        # قائمة شاملة لجميع الرموز
        self.all_symbols = []
        for category in self.supported_symbols.values():
            self.all_symbols.extend(category)

        # للتوافق مع الكود القديم
        self.crypto_symbols_mt5 = self.supported_symbols['crypto']
        
        # رموز العملات للأسعار الخارجية
        self.crypto_pairs = {
            'BTCUSD': {'name': 'Bitcoin', 'symbol': 'BTC', 'coingecko_id': 'bitcoin'},
            'ETHUSD': {'name': 'Ethereum', 'symbol': 'ETH', 'coingecko_id': 'ethereum'},
            'LTCUSD': {'name': 'Litecoin', 'symbol': 'LTC', 'coingecko_id': 'litecoin'},
            'XRPUSD': {'name': 'Ripple', 'symbol': 'XRP', 'coingecko_id': 'ripple'},
            'ADAUSD': {'name': 'Cardano', 'symbol': 'ADA', 'coingecko_id': 'cardano'},
            'DOTUSD': {'name': 'Polkadot', 'symbol': 'DOT', 'coingecko_id': 'polkadot'},
            'LINKUSD': {'name': 'Chainlink', 'symbol': 'LINK', 'coingecko_id': 'chainlink'},
            'UNIUSD': {'name': 'Uniswap', 'symbol': 'UNI', 'coingecko_id': 'uniswap'}
        }
        
        self.current_pair = 'BTCUSD'
        
        # نماذج التعلم الآلي
        self.price_model = None
        self.scaler = StandardScaler()
        self.model_trained = False
        
        # إعداد السجلات
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        """إعداد نظام السجلات"""
        logger = logging.getLogger('MT5RealCrypto')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            os.makedirs('logs', exist_ok=True)
            handler = logging.FileHandler(f'logs/mt5_real_crypto_{datetime.now().strftime("%Y%m%d")}.log', 
                                        encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger

    def is_crypto_symbol(self, symbol: str) -> bool:
        """التحقق من كون الرمز عملة رقمية"""
        return symbol in self.supported_symbols['crypto']

    def is_forex_symbol(self, symbol: str) -> bool:
        """التحقق من كون الرمز زوج عملات"""
        return (symbol in self.supported_symbols['major_forex'] or
                symbol in self.supported_symbols['cross_pairs'])

    def is_metal_symbol(self, symbol: str) -> bool:
        """التحقق من كون الرمز معدن ثمين"""
        return symbol in self.supported_symbols['metals']

    def is_index_symbol(self, symbol: str) -> bool:
        """التحقق من كون الرمز مؤشر"""
        return symbol in self.supported_symbols['indices']

    def get_symbol_category(self, symbol: str) -> str:
        """الحصول على فئة الرمز"""
        for category, symbols in self.supported_symbols.items():
            if symbol in symbols:
                return category
        return 'unknown'

    def connect_mt5(self) -> bool:
        """الاتصال بـ MetaTrader 5"""
        try:
            self.logger.info("محاولة الاتصال بـ MetaTrader 5...")
            
            # إنهاء أي اتصال سابق
            mt5.shutdown()
            
            # محاولة الاتصال
            if not mt5.initialize():
                error_code = mt5.last_error()
                self.logger.error(f"فشل في الاتصال بـ MT5: {error_code}")
                return False
            
            # التحقق من معلومات الحساب
            account_info = mt5.account_info()
            if account_info is None:
                self.logger.error("فشل في الحصول على معلومات الحساب")
                return False
            
            self.mt5_connected = True
            self.logger.info(f"تم الاتصال بـ MT5 بنجاح!")
            self.logger.info(f"الحساب: {account_info.login}")
            self.logger.info(f"الشركة: {account_info.company}")
            self.logger.info(f"الخادم: {account_info.server}")
            self.logger.info(f"الرصيد: ${account_info.balance:.2f}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في الاتصال بـ MT5: {str(e)}")
            return False
    
    def get_mt5_symbols(self) -> List[str]:
        """الحصول على الرموز المتوفرة في MT5"""
        try:
            if not self.mt5_connected:
                return []
            
            symbols = mt5.symbols_get()
            if symbols is None:
                return []
            
            # البحث عن رموز العملات الرقمية
            crypto_symbols = []
            for symbol in symbols:
                symbol_name = symbol.name
                # البحث عن رموز العملات الرقمية المعروفة
                for crypto_symbol in self.crypto_symbols_mt5:
                    if crypto_symbol in symbol_name:
                        crypto_symbols.append(symbol_name)
                        break
            
            self.logger.info(f"تم العثور على {len(crypto_symbols)} رمز عملة رقمية في MT5")
            return crypto_symbols
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على رموز MT5: {str(e)}")
            return []
    
    def get_mt5_price(self, symbol: str) -> Optional[float]:
        """الحصول على السعر من MT5"""
        try:
            if not self.mt5_connected:
                return None
            
            # الحصول على معلومات الرمز
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                return None
            
            # الحصول على السعر الحالي
            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                return None
            
            # استخدام متوسط البيد والأسك
            price = (tick.bid + tick.ask) / 2
            return float(price)
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على سعر {symbol} من MT5: {str(e)}")
            return None
    
    def get_external_crypto_price(self, symbol: str) -> Optional[float]:
        """الحصول على السعر من مصدر خارجي (CoinGecko)"""
        try:
            if symbol not in self.crypto_pairs:
                return None
            
            coingecko_id = self.crypto_pairs[symbol]['coingecko_id']
            url = f"https://api.coingecko.com/api/v3/simple/price?ids={coingecko_id}&vs_currencies=usd"
            
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                price = data[coingecko_id]['usd']
                return float(price)
            
            return None
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على السعر الخارجي لـ {symbol}: {str(e)}")
            return None
    
    def get_best_price(self, symbol: str) -> Dict:
        """الحصول على أفضل سعر متوفر"""
        try:
            result = {
                'symbol': symbol,
                'mt5_price': None,
                'external_price': None,
                'final_price': None,
                'source': None
            }
            
            # محاولة الحصول على السعر من MT5
            if self.mt5_connected:
                mt5_price = self.get_mt5_price(symbol)
                if mt5_price:
                    result['mt5_price'] = mt5_price
            
            # محاولة الحصول على السعر الخارجي (للعملات الرقمية فقط)
            if self.is_crypto_symbol(symbol):
                external_price = self.get_external_crypto_price(symbol)
                if external_price:
                    result['external_price'] = external_price

            # تحديد أفضل سعر
            if result['mt5_price'] and result['external_price']:
                # إذا كان الفرق كبير، استخدم السعر الخارجي (أكثر دقة للعملات الرقمية)
                price_diff = abs(result['mt5_price'] - result['external_price']) / result['external_price']
                if price_diff > 0.1:  # فرق أكثر من 10%
                    result['final_price'] = result['external_price']
                    result['source'] = 'external'
                else:
                    result['final_price'] = result['mt5_price']
                    result['source'] = 'mt5'
            elif result['mt5_price']:
                result['final_price'] = result['mt5_price']
                result['source'] = 'mt5'
            elif result['external_price']:
                result['final_price'] = result['external_price']
                result['source'] = 'external'
            
            return result
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على أفضل سعر لـ {symbol}: {str(e)}")
            return {'symbol': symbol, 'final_price': None, 'source': None}
    
    def get_mt5_account_info(self) -> Dict:
        """الحصول على معلومات الحساب من MT5"""
        try:
            if not self.mt5_connected:
                return {}
            
            account_info = mt5.account_info()
            if account_info is None:
                return {}
            
            return {
                'login': account_info.login,
                'balance': account_info.balance,
                'equity': account_info.equity,
                'margin': account_info.margin,
                'free_margin': account_info.margin_free,
                'profit': account_info.profit,
                'currency': account_info.currency,
                'company': account_info.company,
                'name': account_info.name,
                'server': account_info.server,
                'leverage': account_info.leverage,
                'margin_level': account_info.margin_level if account_info.margin > 0 else 0
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على معلومات الحساب: {str(e)}")
            return {}
    
    def get_mt5_positions(self) -> List[Dict]:
        """الحصول على الصفقات المفتوحة من MT5"""
        try:
            if not self.mt5_connected:
                return []
            
            positions = mt5.positions_get()
            if positions is None:
                return []
            
            positions_list = []
            for pos in positions:
                positions_list.append({
                    'ticket': pos.ticket,
                    'symbol': pos.symbol,
                    'type': 'شراء' if pos.type == mt5.ORDER_TYPE_BUY else 'بيع',
                    'volume': pos.volume,
                    'price_open': pos.price_open,
                    'price_current': pos.price_current,
                    'profit': pos.profit,
                    'swap': pos.swap,
                    'comment': pos.comment,
                    'time': datetime.fromtimestamp(pos.time)
                })
            
            return positions_list
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الصفقات المفتوحة: {str(e)}")
            return []
    
    def set_confidence_threshold(self, threshold: float) -> bool:
        """تحديد نسبة الثقة المطلوبة للدخول"""
        if 0 <= threshold <= 100:
            self.confidence_threshold = threshold
            self.logger.info(f"تم تحديد نسبة الثقة إلى: {threshold}%")
            return True
        return False
    
    def connect(self) -> bool:
        """الاتصال بالنظام"""
        try:
            self.logger.info("بدء الاتصال بنظام التداول...")
            
            # الاتصال بـ MT5
            if self.connect_mt5():
                self.connected = True
                
                # عرض الرموز المتوفرة
                available_symbols = self.get_mt5_symbols()
                if available_symbols:
                    self.logger.info(f"الرموز المتوفرة في MT5: {available_symbols}")
                
                # اختبار الحصول على سعر
                test_symbol = 'BTCUSD' if 'BTCUSD' in available_symbols else available_symbols[0] if available_symbols else 'BTC'
                price_info = self.get_best_price(test_symbol)
                
                if price_info['final_price']:
                    self.logger.info(f"سعر {test_symbol}: ${price_info['final_price']:,.2f} (المصدر: {price_info['source']})")
                
                return True
            else:
                self.logger.error("فشل في الاتصال بـ MT5")
                return False
                
        except Exception as e:
            self.logger.error(f"خطأ في الاتصال: {str(e)}")
            return False
    
    def disconnect(self):
        """قطع الاتصال"""
        try:
            if self.mt5_connected:
                mt5.shutdown()
                self.mt5_connected = False
            
            self.connected = False
            self.logger.info("تم قطع الاتصال")
            
        except Exception as e:
            self.logger.error(f"خطأ في قطع الاتصال: {str(e)}")
    
    def get_available_pairs(self) -> List[str]:
        """الحصول على أزواج العملات المتوفرة"""
        if self.mt5_connected:
            return self.get_mt5_symbols()
        else:
            return list(self.crypto_pairs.keys())
    
    def set_current_pair(self, pair: str) -> bool:
        """تحديد الزوج الحالي للتداول"""
        available_pairs = self.get_available_pairs()
        if pair in available_pairs or pair in self.crypto_pairs:
            self.current_pair = pair
            self.logger.info(f"تم تحديد الزوج: {pair}")
            return True
        return False

    def get_historical_data_mt5(self, symbol: str, timeframe=mt5.TIMEFRAME_H1, count: int = 100) -> Optional[pd.DataFrame]:
        """الحصول على البيانات التاريخية من MT5"""
        try:
            if not self.mt5_connected:
                return None

            # الحصول على البيانات
            rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, count)

            if rates is None or len(rates) == 0:
                return None

            # تحويل إلى DataFrame
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            df.set_index('time', inplace=True)

            # إعادة تسمية الأعمدة
            df.rename(columns={
                'open': 'open',
                'high': 'high',
                'low': 'low',
                'close': 'close',
                'tick_volume': 'volume'
            }, inplace=True)

            return df

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على البيانات التاريخية من MT5 لـ {symbol}: {str(e)}")
            return None

    def get_historical_data_external(self, symbol: str, limit: int = 100) -> Optional[pd.DataFrame]:
        """الحصول على البيانات التاريخية من مصدر خارجي"""
        try:
            # فقط للعملات الرقمية
            if not self.is_crypto_symbol(symbol) or symbol not in self.crypto_pairs:
                return None

            coingecko_id = self.crypto_pairs[symbol]['coingecko_id']

            # الحصول على البيانات لآخر 30 يوم
            url = f"https://api.coingecko.com/api/v3/coins/{coingecko_id}/market_chart"
            params = {
                'vs_currency': 'usd',
                'days': '30',
                'interval': 'hourly'
            }

            response = requests.get(url, params=params, timeout=15)

            if response.status_code == 200:
                data = response.json()

                # تحويل البيانات إلى DataFrame
                prices = data['prices']
                volumes = data['total_volumes']

                df = pd.DataFrame(prices, columns=['timestamp', 'close'])
                df['volume'] = [v[1] for v in volumes]
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.set_index('timestamp', inplace=True)

                # إضافة OHLC (تقريبي)
                df['open'] = df['close'].shift(1)
                df['high'] = df[['open', 'close']].max(axis=1) * 1.002
                df['low'] = df[['open', 'close']].min(axis=1) * 0.998

                # إزالة القيم المفقودة
                df = df.dropna()

                return df.tail(limit)

            return None

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على البيانات التاريخية الخارجية لـ {symbol}: {str(e)}")
            return None

    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """حساب المؤشرات الفنية"""
        try:
            if len(df) < 20:
                return df

            # Moving Averages
            df['sma_20'] = df['close'].rolling(window=20).mean()
            df['sma_50'] = df['close'].rolling(window=50).mean()
            df['ema_12'] = df['close'].ewm(span=12).mean()
            df['ema_26'] = df['close'].ewm(span=26).mean()

            # MACD
            df['macd'] = df['ema_12'] - df['ema_26']
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            df['macd_histogram'] = df['macd'] - df['macd_signal']

            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))

            # Bollinger Bands
            df['bb_middle'] = df['close'].rolling(window=20).mean()
            bb_std = df['close'].rolling(window=20).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
            df['bb_lower'] = df['bb_middle'] - (bb_std * 2)

            # Volume indicators
            df['volume_sma'] = df['volume'].rolling(window=20).mean()

            # Price change
            df['price_change'] = df['close'].pct_change()
            df['volatility'] = df['price_change'].rolling(window=20).std()

            return df

        except Exception as e:
            self.logger.error(f"خطأ في حساب المؤشرات الفنية: {str(e)}")
            return df

    def analyze_market(self, symbol: str) -> Dict:
        """تحليل السوق"""
        try:
            self.logger.info(f"تحليل السوق لـ {symbol}...")

            # الحصول على السعر الحالي
            price_info = self.get_best_price(symbol)
            current_price = price_info['final_price']

            if not current_price:
                return {'error': 'فشل في الحصول على السعر'}

            # الحصول على البيانات التاريخية
            df = None

            # محاولة الحصول على البيانات من MT5 أولاً
            if self.mt5_connected:
                df = self.get_historical_data_mt5(symbol)

            # إذا فشل، استخدم المصدر الخارجي
            if df is None or len(df) < 20:
                df = self.get_historical_data_external(symbol)

            if df is None or len(df) < 20:
                return {'error': 'بيانات غير كافية للتحليل'}

            # حساب المؤشرات الفنية
            df = self.calculate_technical_indicators(df)

            # الحصول على آخر القيم
            latest = df.iloc[-1]

            # تحليل الاتجاه
            signals = []
            confidence_factors = []

            # تحليل المتوسطات المتحركة
            if latest['close'] > latest['sma_20']:
                signals.append('buy')
                confidence_factors.append(15)
            else:
                signals.append('sell')
                confidence_factors.append(15)

            # تحليل MACD
            if latest['macd'] > latest['macd_signal']:
                signals.append('buy')
                confidence_factors.append(20)
            else:
                signals.append('sell')
                confidence_factors.append(20)

            # تحليل RSI
            if latest['rsi'] < 30:  # oversold
                signals.append('buy')
                confidence_factors.append(25)
            elif latest['rsi'] > 70:  # overbought
                signals.append('sell')
                confidence_factors.append(25)
            else:
                confidence_factors.append(10)

            # تحليل Bollinger Bands
            if latest['close'] < latest['bb_lower']:
                signals.append('buy')
                confidence_factors.append(20)
            elif latest['close'] > latest['bb_upper']:
                signals.append('sell')
                confidence_factors.append(20)
            else:
                confidence_factors.append(10)

            # تحديد القرار النهائي
            buy_signals = signals.count('buy')
            sell_signals = signals.count('sell')

            if buy_signals > sell_signals:
                decision = 'buy'
            elif sell_signals > buy_signals:
                decision = 'sell'
            else:
                decision = 'hold'

            # حساب نسبة الثقة
            confidence = sum(confidence_factors)
            confidence = min(confidence, 100)

            analysis_result = {
                'symbol': symbol,
                'price': current_price,
                'price_source': price_info['source'],
                'decision': decision,
                'confidence': confidence,
                'rsi': latest['rsi'],
                'macd': latest['macd'],
                'macd_signal': latest['macd_signal'],
                'sma_20': latest['sma_20'],
                'sma_50': latest['sma_50'],
                'bb_upper': latest['bb_upper'],
                'bb_lower': latest['bb_lower'],
                'volatility': latest['volatility'],
                'volume': latest['volume'],
                'timestamp': datetime.now()
            }

            self.logger.info(f"تحليل {symbol}: {decision} بثقة {confidence:.1f}% (السعر: ${current_price:,.2f})")

            return analysis_result

        except Exception as e:
            self.logger.error(f"خطأ في تحليل السوق لـ {symbol}: {str(e)}")
            return {'error': str(e)}

    def should_enter_trade(self, analysis: Dict) -> bool:
        """تحديد ما إذا كان يجب الدخول في صفقة"""
        if 'error' in analysis:
            return False

        confidence = analysis.get('confidence', 0)
        decision = analysis.get('decision', 'hold')

        # التحقق من نسبة الثقة
        if confidence >= self.confidence_threshold and decision in ['buy', 'sell']:
            self.logger.info(f"شروط الدخول متوفرة: {decision} بثقة {confidence:.1f}% (المطلوب: {self.confidence_threshold}%)")
            return True

        self.logger.info(f"شروط الدخول غير متوفرة: {decision} بثقة {confidence:.1f}% (المطلوب: {self.confidence_threshold}%)")
        return False

    def execute_trade_mt5(self, analysis: Dict) -> bool:
        """تنفيذ الصفقة على MT5"""
        try:
            if not self.mt5_connected:
                self.logger.error("غير متصل بـ MT5")
                return False

            # لا نحتاج للتحقق من الشروط مرة أخرى لأن الواجهة تحققت منها بالفعل
            # if not self.should_enter_trade(analysis):
            #     return False

            symbol = analysis['symbol']
            decision = analysis['decision']
            price = analysis['price']
            confidence = analysis['confidence']

            self.logger.info(f"🚀 بدء تنفيذ صفقة {decision.upper()} على {symbol}")

            # التحقق من توفر الرمز في MT5
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                self.logger.error(f"❌ الرمز {symbol} غير متوفر في MT5")
                # محاولة البحث عن رموز مشابهة
                symbols = mt5.symbols_get()
                similar_symbols = [s.name for s in symbols if symbol.lower() in s.name.lower()]
                if similar_symbols:
                    self.logger.info(f"💡 رموز مشابهة متوفرة: {similar_symbols[:5]}")
                return False

            # التأكد من أن الرمز مفعل للتداول
            if not symbol_info.visible:
                self.logger.info(f"🔄 محاولة تفعيل الرمز {symbol}")
                if not mt5.symbol_select(symbol, True):
                    self.logger.error(f"❌ فشل في تفعيل الرمز {symbol}")
                    return False
                else:
                    self.logger.info(f"✅ تم تفعيل الرمز {symbol} بنجاح")

            # حساب حجم الصفقة
            account_info = mt5.account_info()
            if account_info is None:
                self.logger.error("فشل في الحصول على معلومات الحساب")
                return False

            balance = account_info.balance
            risk_amount = balance * self.risk_per_trade

            # حساب حجم الصفقة بناءً على المخاطرة
            point = symbol_info.point
            tick_size = symbol_info.trade_tick_size
            tick_value = symbol_info.trade_tick_value

            if tick_value == 0:
                tick_value = tick_size

            # حساب المسافة لوقف الخسارة
            sl_distance = self.stop_loss_pips * point

            # حساب حجم الصفقة
            if sl_distance > 0 and tick_value > 0:
                lot_size = risk_amount / (sl_distance / point * tick_value)
                lot_size = max(symbol_info.volume_min, min(lot_size, symbol_info.volume_max))
                lot_size = round(lot_size / symbol_info.volume_step) * symbol_info.volume_step
            else:
                lot_size = symbol_info.volume_min

            # تحديد نوع الأمر
            order_type = mt5.ORDER_TYPE_BUY if decision == 'buy' else mt5.ORDER_TYPE_SELL

            # حساب وقف الخسارة وجني الربح
            current_price = mt5.symbol_info_tick(symbol).ask if decision == 'buy' else mt5.symbol_info_tick(symbol).bid

            if decision == 'buy':
                sl = current_price - sl_distance
                tp = current_price + (self.take_profit_pips * point)
            else:
                sl = current_price + sl_distance
                tp = current_price - (self.take_profit_pips * point)

            # إعداد طلب التداول
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": lot_size,
                "type": order_type,
                "price": current_price,
                "sl": sl,
                "tp": tp,
                "deviation": 20,
                "magic": 234000,
                "comment": f"AI Trade - Confidence: {confidence:.1f}%",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            # تنفيذ الأمر
            self.logger.info(f"📤 إرسال أمر التداول...")
            self.logger.info(f"   الرمز: {symbol}")
            self.logger.info(f"   النوع: {'شراء' if decision == 'buy' else 'بيع'}")
            self.logger.info(f"   الحجم: {lot_size}")
            self.logger.info(f"   السعر: {current_price}")

            result = mt5.order_send(request)

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                error_messages = {
                    mt5.TRADE_RETCODE_REQUOTE: "إعادة تسعير - السعر تغير",
                    mt5.TRADE_RETCODE_REJECT: "الطلب مرفوض",
                    mt5.TRADE_RETCODE_CANCEL: "الطلب ملغي",
                    mt5.TRADE_RETCODE_PLACED: "الطلب موضوع",
                    mt5.TRADE_RETCODE_DONE_PARTIAL: "تنفيذ جزئي فقط",
                    mt5.TRADE_RETCODE_ERROR: "خطأ عام",
                    mt5.TRADE_RETCODE_TIMEOUT: "انتهت مهلة الطلب",
                    mt5.TRADE_RETCODE_INVALID: "طلب غير صالح",
                    mt5.TRADE_RETCODE_INVALID_VOLUME: "حجم غير صالح",
                    mt5.TRADE_RETCODE_INVALID_PRICE: "سعر غير صالح",
                    mt5.TRADE_RETCODE_INVALID_STOPS: "مستويات وقف غير صالحة",
                    mt5.TRADE_RETCODE_TRADE_DISABLED: "التداول معطل",
                    mt5.TRADE_RETCODE_MARKET_CLOSED: "السوق مغلق",
                    mt5.TRADE_RETCODE_NO_MONEY: "أموال غير كافية",
                    mt5.TRADE_RETCODE_PRICE_CHANGED: "السعر تغير",
                    mt5.TRADE_RETCODE_PRICE_OFF: "السعر خارج النطاق",
                    mt5.TRADE_RETCODE_INVALID_EXPIRATION: "انتهاء صلاحية غير صالح",
                    mt5.TRADE_RETCODE_ORDER_CHANGED: "الطلب تغير",
                    mt5.TRADE_RETCODE_TOO_MANY_REQUESTS: "طلبات كثيرة جداً",
                    mt5.TRADE_RETCODE_NO_CHANGES: "لا توجد تغييرات",
                    mt5.TRADE_RETCODE_SERVER_DISABLES_AT: "الخادم يعطل التداول الآلي",
                    mt5.TRADE_RETCODE_CLIENT_DISABLES_AT: "العميل يعطل التداول الآلي",
                    mt5.TRADE_RETCODE_LOCKED: "مقفل",
                    mt5.TRADE_RETCODE_FROZEN: "مجمد",
                    mt5.TRADE_RETCODE_INVALID_FILL: "نوع تعبئة غير صالح",
                    mt5.TRADE_RETCODE_CONNECTION: "لا يوجد اتصال",
                    mt5.TRADE_RETCODE_ONLY_REAL: "التداول الحقيقي فقط مسموح",
                    mt5.TRADE_RETCODE_LIMIT_ORDERS: "حد الطلبات المعلقة وصل",
                    mt5.TRADE_RETCODE_LIMIT_VOLUME: "حد الحجم وصل"
                }

                error_msg = error_messages.get(result.retcode, f"خطأ غير معروف: {result.retcode}")
                self.logger.error(f"❌ فشل في تنفيذ الأمر: {error_msg}")
                self.logger.error(f"   كود الخطأ: {result.retcode}")
                self.logger.error(f"   تعليق: {result.comment}")
                self.logger.error(f"   الطلب: {request}")
                return False

            self.logger.info(f"✅ تم تنفيذ الصفقة بنجاح:")
            self.logger.info(f"   الرمز: {symbol}")
            self.logger.info(f"   النوع: {'شراء' if decision == 'buy' else 'بيع'}")
            self.logger.info(f"   الحجم: {lot_size}")
            self.logger.info(f"   السعر: {current_price}")
            self.logger.info(f"   وقف الخسارة: {sl}")
            self.logger.info(f"   جني الربح: {tp}")
            self.logger.info(f"   رقم الصفقة: {result.order}")
            self.logger.info(f"   نسبة الثقة: {confidence:.1f}%")

            return True

        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الصفقة: {str(e)}")
            return False

    def get_account_info(self) -> Dict:
        """الحصول على معلومات الحساب"""
        if self.mt5_connected:
            return self.get_mt5_account_info()
        else:
            return {
                'balance': 0,
                'equity': 0,
                'margin': 0,
                'free_margin': 0,
                'profit': 0,
                'currency': 'USD',
                'company': 'غير متصل',
                'name': 'غير متصل',
                'server': 'غير متصل',
                'leverage': 0,
                'margin_level': 0,
                'open_positions': 0
            }

    def get_open_positions(self) -> List[Dict]:
        """الحصول على الصفقات المفتوحة"""
        if self.mt5_connected:
            return self.get_mt5_positions()
        else:
            return []
