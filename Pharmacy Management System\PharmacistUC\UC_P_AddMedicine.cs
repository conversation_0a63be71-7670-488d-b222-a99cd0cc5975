﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy_Management_System.PharmacistUC
{
    public partial class UC_P_AddMedicine : UserControl
    {
        Function fn = new Function();
        String query;

        // نظام الجرعات الديناميكي
        private List<DosageControl> dosageControls = new List<DosageControl>();
        private int dosageCount = 0;
        private const int MAX_DOSAGES = 4;
        private Guna.UI2.WinForms.Guna2Button btnAddDosage;
        private Panel dosagePanel;

        // كلاس لإدارة عناصر التحكم في الجرعة
        private class DosageControl
        {
            public Guna.UI2.WinForms.Guna2TextBox DosageTextBox { get; set; }
            public Guna.UI2.WinForms.Guna2TextBox QuantityTextBox { get; set; }
            public Label DosageLabel { get; set; }
            public Label QuantityLabel { get; set; }
            public Guna.UI2.WinForms.Guna2Button RemoveButton { get; set; }
            public int Index { get; set; }
        }

        public UC_P_AddMedicine()
        {
            InitializeComponent();

            // إنشاء نظام الجرعات الديناميكي
            InitializeDynamicDosageSystem();

            // تطبيق التصميم العصري
            ApplyModernDesign();

            // الاشتراك في أحداث التغيير
            LanguageManager.LanguageChanged += OnLanguageChanged;
            ModernTheme.ThemeChanged += OnThemeChanged;
        }

        private void UC_P_AddMedicine_Load(object sender, EventArgs e)
        {
            // تطبيق اللغة الحالية
            ApplyLanguage();
        }

        private void InitializeDynamicDosageSystem()
        {
            // إخفاء الجرعات الثابتة القديمة
            HideOldDosageControls();

            // إنشاء لوحة الجرعات
            CreateDosagePanel();

            // إنشاء زر إضافة جرعة
            CreateAddDosageButton();
        }

        private void HideOldDosageControls()
        {
            // إخفاء الجرعات الثابتة القديمة
            
        }

        private void CreateDosagePanel()
        {
            dosagePanel = new Panel();
            dosagePanel.Location = new Point(550, 200);
            dosagePanel.Size = new Size(400, 350);
            dosagePanel.BackColor = ModernTheme.Colors.Surface;
            dosagePanel.BorderStyle = BorderStyle.FixedSingle;
            dosagePanel.AutoScroll = true;
            this.Controls.Add(dosagePanel);
            dosagePanel.BringToFront();
        }

        private void CreateAddDosageButton()
        {
            btnAddDosage = new Guna.UI2.WinForms.Guna2Button();
            btnAddDosage.Text = LanguageManager.GetText("Add Dosage") + " +";
            btnAddDosage.Size = new Size(150, 40);
            btnAddDosage.Location = new Point(550, 150);
            btnAddDosage.FillColor = ModernTheme.Colors.Primary;
            btnAddDosage.ForeColor = ModernTheme.Colors.TextOnPrimary;
            btnAddDosage.BorderRadius = 8;
            btnAddDosage.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnAddDosage.Cursor = Cursors.Hand;
            btnAddDosage.Click += BtnAddDosage_Click;
            this.Controls.Add(btnAddDosage);
            btnAddDosage.BringToFront();
        }

        private void BtnAddDosage_Click(object sender, EventArgs e)
        {
            if (dosageCount < MAX_DOSAGES)
            {
                AddNewDosage();
                UpdateAddDosageButton();
            }
            else
            {
                MessageBox.Show(
                    LanguageManager.GetText("Maximum 4 dosages allowed"),
                    LanguageManager.GetText("Information"),
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );
            }
        }

        private void AddNewDosage()
        {
            dosageCount++;

            var dosageControl = new DosageControl
            {
                Index = dosageCount
            };

            int yPosition = (dosageCount - 1) * 80 + 10;

            // إنشاء تسمية الجرعة
            dosageControl.DosageLabel = new Label();
            dosageControl.DosageLabel.Text = LanguageManager.GetText("Dosage") + " " + dosageCount + ":";
            dosageControl.DosageLabel.Location = new Point(10, yPosition);
            dosageControl.DosageLabel.Size = new Size(80, 20);
            dosageControl.DosageLabel.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            dosageControl.DosageLabel.ForeColor = ModernTheme.Colors.TextPrimary;

            // إنشاء مربع نص الجرعة
            dosageControl.DosageTextBox = new Guna.UI2.WinForms.Guna2TextBox();
            dosageControl.DosageTextBox.Location = new Point(10, yPosition + 25);
            dosageControl.DosageTextBox.Size = new Size(150, 30);
            dosageControl.DosageTextBox.PlaceholderText = LanguageManager.GetText("Enter dosage");
            dosageControl.DosageTextBox.BorderRadius = 6;
            dosageControl.DosageTextBox.Font = new Font("Segoe UI", 9F);

            // إنشاء تسمية الكمية
            dosageControl.QuantityLabel = new Label();
            dosageControl.QuantityLabel.Text = LanguageManager.GetText("Quantity") + ":";
            dosageControl.QuantityLabel.Location = new Point(180, yPosition);
            dosageControl.QuantityLabel.Size = new Size(60, 20);
            dosageControl.QuantityLabel.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            dosageControl.QuantityLabel.ForeColor = ModernTheme.Colors.TextPrimary;

            // إنشاء مربع نص الكمية
            dosageControl.QuantityTextBox = new Guna.UI2.WinForms.Guna2TextBox();
            dosageControl.QuantityTextBox.Location = new Point(180, yPosition + 25);
            dosageControl.QuantityTextBox.Size = new Size(80, 30);
            dosageControl.QuantityTextBox.PlaceholderText = "0";
            dosageControl.QuantityTextBox.BorderRadius = 6;
            dosageControl.QuantityTextBox.Font = new Font("Segoe UI", 9F);

            // إنشاء زر الحذف
            dosageControl.RemoveButton = new Guna.UI2.WinForms.Guna2Button();
            dosageControl.RemoveButton.Text = "✕";
            dosageControl.RemoveButton.Location = new Point(280, yPosition + 25);
            dosageControl.RemoveButton.Size = new Size(30, 30);
            dosageControl.RemoveButton.FillColor = Color.FromArgb(244, 67, 54);
            dosageControl.RemoveButton.ForeColor = Color.White;
            dosageControl.RemoveButton.BorderRadius = 15;
            dosageControl.RemoveButton.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dosageControl.RemoveButton.Cursor = Cursors.Hand;
            dosageControl.RemoveButton.Tag = dosageControl;
            dosageControl.RemoveButton.Click += RemoveDosage_Click;

            // إضافة العناصر إلى اللوحة
            dosagePanel.Controls.Add(dosageControl.DosageLabel);
            dosagePanel.Controls.Add(dosageControl.DosageTextBox);
            dosagePanel.Controls.Add(dosageControl.QuantityLabel);
            dosagePanel.Controls.Add(dosageControl.QuantityTextBox);
            dosagePanel.Controls.Add(dosageControl.RemoveButton);

            // إضافة إلى القائمة
            dosageControls.Add(dosageControl);
        }

        private void RemoveDosage_Click(object sender, EventArgs e)
        {
            var button = sender as Guna.UI2.WinForms.Guna2Button;
            var dosageControl = button.Tag as DosageControl;

            if (dosageControl != null)
            {
                // إزالة العناصر من اللوحة
                dosagePanel.Controls.Remove(dosageControl.DosageLabel);
                dosagePanel.Controls.Remove(dosageControl.DosageTextBox);
                dosagePanel.Controls.Remove(dosageControl.QuantityLabel);
                dosagePanel.Controls.Remove(dosageControl.QuantityTextBox);
                dosagePanel.Controls.Remove(dosageControl.RemoveButton);

                // إزالة من القائمة
                dosageControls.Remove(dosageControl);
                dosageCount--;

                // إعادة ترتيب الجرعات المتبقية
                ReorganizeDosages();
                UpdateAddDosageButton();
            }
        }

        private void ReorganizeDosages()
        {
            for (int i = 0; i < dosageControls.Count; i++)
            {
                var dosageControl = dosageControls[i];
                dosageControl.Index = i + 1;

                int yPosition = i * 80 + 10;

                // تحديث المواضع
                dosageControl.DosageLabel.Location = new Point(10, yPosition);
                dosageControl.DosageTextBox.Location = new Point(10, yPosition + 25);
                dosageControl.QuantityLabel.Location = new Point(180, yPosition);
                dosageControl.QuantityTextBox.Location = new Point(180, yPosition + 25);
                dosageControl.RemoveButton.Location = new Point(280, yPosition + 25);

                // تحديث النص
                dosageControl.DosageLabel.Text = LanguageManager.GetText("Dosage") + " " + (i + 1) + ":";
            }

            dosageCount = dosageControls.Count;
        }

        private void UpdateAddDosageButton()
        {
            if (dosageCount >= MAX_DOSAGES)
            {
                btnAddDosage.Text = LanguageManager.GetText("Maximum dosages reached");
                btnAddDosage.Enabled = false;
                btnAddDosage.FillColor = Color.Gray;
            }
            else
            {
                btnAddDosage.Text = LanguageManager.GetText("Add Dosage") + " + (" + dosageCount + "/" + MAX_DOSAGES + ")";
                btnAddDosage.Enabled = true;
                btnAddDosage.FillColor = ModernTheme.Colors.Primary;
            }
        }

        private bool ValidateDosages()
        {
            foreach (var dosageControl in dosageControls)
            {
                if (string.IsNullOrWhiteSpace(dosageControl.DosageTextBox.Text))
                {
                    MessageBox.Show(
                        LanguageManager.GetText("Please enter dosage") + " " + dosageControl.Index,
                        LanguageManager.GetText("Warning"),
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning
                    );
                    dosageControl.DosageTextBox.Focus();
                    return false;
                }

                if (string.IsNullOrWhiteSpace(dosageControl.QuantityTextBox.Text) ||
                    !int.TryParse(dosageControl.QuantityTextBox.Text, out int quantity) ||
                    quantity <= 0)
                {
                    MessageBox.Show(
                        LanguageManager.GetText("Please enter valid quantity") + " " + dosageControl.Index,
                        LanguageManager.GetText("Warning"),
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning
                    );
                    dosageControl.QuantityTextBox.Focus();
                    return false;
                }
            }
            return true;
        }

        private Dictionary<string, object> CollectDosageData()
        {
            var data = new Dictionary<string, object>();

            // تعبئة البيانات للجرعات الأربع (حتى لو كانت فارغة)
            for (int i = 1; i <= 4; i++)
            {
                if (i <= dosageControls.Count)
                {
                    var dosageControl = dosageControls[i - 1];
                    data[$"dos{i}"] = dosageControl.DosageTextBox.Text.Trim();
                    data[$"dos{i}_qty"] = int.Parse(dosageControl.QuantityTextBox.Text.Trim());
                }
                else
                {
                    data[$"dos{i}"] = "";
                    data[$"dos{i}_qty"] = 0;
                }
            }

            // حساب الكمية الإجمالية
            int totalQuantity = 0;
            for (int i = 1; i <= 4; i++)
            {
                totalQuantity += (int)data[$"dos{i}_qty"];
            }
            data["totalQuantity"] = totalQuantity;

            return data;
        }

        private void ClearAllDosages()
        {
            // إزالة جميع عناصر التحكم في الجرعات
            foreach (var dosageControl in dosageControls)
            {
                dosagePanel.Controls.Remove(dosageControl.DosageLabel);
                dosagePanel.Controls.Remove(dosageControl.DosageTextBox);
                dosagePanel.Controls.Remove(dosageControl.QuantityLabel);
                dosagePanel.Controls.Remove(dosageControl.QuantityTextBox);
                dosagePanel.Controls.Remove(dosageControl.RemoveButton);
            }

            // مسح القائمة وإعادة تعيين العداد
            dosageControls.Clear();
            dosageCount = 0;

            // تحديث زر الإضافة
            UpdateAddDosageButton();
        }

        private void OnLanguageChanged(object sender, EventArgs e)
        {
            ApplyLanguage();
        }

        public void ApplyLanguage()
        {
            // ترجمة العناوين والتسميات
            label1.Text = LanguageManager.GetText("Add Medicine");
            label2.Text = LanguageManager.GetText("Medicine ID");
            label3.Text = LanguageManager.GetText("Medicine Name");
           
            label5.Text = LanguageManager.GetText("Manufacturing Date");
            label6.Text = LanguageManager.GetText("Expiry Date");
            label8.Text = LanguageManager.GetText("Price per unit");
           

            btnAdd.Text = LanguageManager.GetText("Add");
            btnReset.Text = LanguageManager.GetText("Reset");

            // الحفاظ على التصميم من اليسار لليمين في جميع اللغات
            this.RightToLeft = RightToLeft.No;
        }

        private void ApplyModernDesign()
        {
            // تطبيق الوضع الحالي
            this.BackColor = ModernTheme.Colors.Background;

            // تحسين العنوان الرئيسي
            if (label1 != null)
            {
                label1.Font = new Font("Segoe UI", 18F, FontStyle.Bold);
                label1.ForeColor = ModernTheme.Colors.Primary;
            }

            // تحسين الأزرار
            if (btnAdd != null)
            {
                ApplyModernButtonStyle(btnAdd, true);
            }

            if (btnReset != null)
            {
                ApplyModernButtonStyle(btnReset, false);
            }

            // إضافة زر الوضع الليلي
            CreateDarkModeButton();
        }

        private void CreateDarkModeButton()
        {
            // إنشاء زر الوضع الليلي
            if (this.Controls.Find("btnDarkMode", false).Length == 0)
            {
                Guna.UI2.WinForms.Guna2Button btnDarkMode = new Guna.UI2.WinForms.Guna2Button();
                btnDarkMode.Name = "btnDarkMode";
                btnDarkMode.Text = ModernTheme.IsDarkMode ? "🌞" : "🌙";
                btnDarkMode.Size = new Size(40, 30);
                btnDarkMode.Location = new Point(this.Width - 50, 10);
                btnDarkMode.Anchor = AnchorStyles.Top | AnchorStyles.Right;
                btnDarkMode.BorderRadius = 6;
                btnDarkMode.Font = new Font("Segoe UI", 12F);
                btnDarkMode.Cursor = Cursors.Hand;
                btnDarkMode.FillColor = ModernTheme.Colors.Secondary;
                btnDarkMode.ForeColor = ModernTheme.Colors.TextOnPrimary;
                btnDarkMode.HoverState.FillColor = ModernTheme.Colors.SecondaryDark;
                btnDarkMode.Click += (s, e) => ModernTheme.ToggleDarkMode();

                this.Controls.Add(btnDarkMode);
                btnDarkMode.BringToFront();
            }
        }

        private void OnThemeChanged(object sender, EventArgs e)
        {
            ApplyModernDesign();
            ApplyLanguage();
        }

        private void ApplyModernButtonStyle(Guna.UI2.WinForms.Guna2Button button, bool isPrimary)
        {
            button.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            button.Cursor = Cursors.Hand;
            button.Size = new Size(150, 40);
            button.BorderRadius = 8;

            if (isPrimary)
            {
                button.FillColor = ModernTheme.Colors.Primary;
                button.ForeColor = ModernTheme.Colors.TextOnPrimary;
                button.BorderThickness = 0;
                button.HoverState.FillColor = ModernTheme.Colors.PrimaryDark;
            }
            else
            {
                button.FillColor = ModernTheme.Colors.Surface;
                button.ForeColor = ModernTheme.Colors.Primary;
                button.BorderColor = ModernTheme.Colors.Primary;
                button.BorderThickness = 1;
                button.HoverState.FillColor = ModernTheme.Colors.PrimaryLight;
            }
        }

        protected override void OnHandleDestroyed(EventArgs e)
        {
            // إلغاء الاشتراك في حدث تغيير اللغة
            LanguageManager.LanguageChanged -= OnLanguageChanged;
            base.OnHandleDestroyed(e);
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (txtMediId.Text != "" && txtMediName.Text != "" && txtPricePerUnit.Text != "" && txtlu.Text != "" && txtbr.Text != "")
            {
                // التحقق من وجود جرعة واحدة على الأقل
                if (dosageControls.Count == 0)
                {
                    MessageBox.Show(
                        LanguageManager.GetText("Please add at least one dosage"),
                        LanguageManager.GetText("Warning"),
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning
                    );
                    return;
                }

                // التحقق من صحة بيانات الجرعات
                if (!ValidateDosages())
                {
                    return;
                }

                // جمع بيانات الجرعات من النظام الجديد
                var dosageData = CollectDosageData();

                // استخدام البيانات الجديدة
                int quantity = (int)dosageData["totalQuantity"];

                // تحقق من السعر
                if (!long.TryParse(txtPricePerUnit.Text, out long perUnit))
                {
                    MessageBox.Show("يرجى إدخال رقم صحيح في سعر الوحدة.", "Invalid Input", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                string mid = txtMediId.Text;
                Function fn = new Function();
                DataSet ds = fn.getData($"SELECT COUNT(*) FROM medic WHERE mid = '{mid}' AND pharmacy_id = {SessionManager.CurrentPharmacyId}");

                if (ds.Tables[0].Rows[0][0].ToString() != "0")
                {
                    MessageBox.Show("معرف الدواء هذا مستخدم مسبقاً، الرجاء استخدام معرف فريد.", "Duplicate ID", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                using (SqlConnection con = fn.getConnection())
                {
                    string insertQuery = @"INSERT INTO medic
                (mid, mname, mnumber, mnumber_qty, mDate, eDate, quantity, perUnit, lu, br,
                 dos2, dos2_qty, dos3, dos3_qty, dos4, dos4_qty, pharmacy_id)
                VALUES
                (@mid, @mname, @mnumber, @mnumber_qty, @mDate, @eDate, @quantity, @perUnit, @lu, @br,
                 @dos2, @dos2_qty, @dos3, @dos3_qty, @dos4, @dos4_qty, @pharmacy_id)";

                    SqlCommand cmd = new SqlCommand(insertQuery, con);
                    cmd.Parameters.AddWithValue("@mid", txtMediId.Text);
                    cmd.Parameters.AddWithValue("@mname", txtMediName.Text);
                    cmd.Parameters.AddWithValue("@mnumber", dosageData["dos1"]);
                    cmd.Parameters.AddWithValue("@mnumber_qty", dosageData["dos1_qty"]);
                    cmd.Parameters.AddWithValue("@mDate", txtManufacturingDate.Value.ToString("yyyy-MM-dd"));
                    cmd.Parameters.AddWithValue("@eDate", txtExpireDate.Value.ToString("yyyy-MM-dd"));
                    cmd.Parameters.AddWithValue("@quantity", quantity);
                    cmd.Parameters.AddWithValue("@perUnit", perUnit);
                    cmd.Parameters.AddWithValue("@lu", txtlu.Text);
                    cmd.Parameters.AddWithValue("@br", txtbr.Text);
                    cmd.Parameters.AddWithValue("@dos2", dosageData["dos2"]);
                    cmd.Parameters.AddWithValue("@dos2_qty", dosageData["dos2_qty"]);
                    cmd.Parameters.AddWithValue("@dos3", dosageData["dos3"]);
                    cmd.Parameters.AddWithValue("@dos3_qty", dosageData["dos3_qty"]);
                    cmd.Parameters.AddWithValue("@dos4", dosageData["dos4"]);
                    cmd.Parameters.AddWithValue("@dos4_qty", dosageData["dos4_qty"]);
                    cmd.Parameters.AddWithValue("@pharmacy_id", SessionManager.CurrentPharmacyId);

                    try
                    {
                        con.Open();
                        cmd.ExecuteNonQuery();
                        MessageBox.Show(LanguageManager.GetText("Medicine and dosages added successfully"), LanguageManager.GetText("Success"), MessageBoxButtons.OK, MessageBoxIcon.Information);
                        clearAll();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show(LanguageManager.GetText("Database error") + ": " + ex.Message, LanguageManager.GetText("Error"), MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى تعبئة كافة البيانات الأساسية المطلوبة.", "بيانات ناقصة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void btnReset_Click(object sender, EventArgs e)
        {
            clearAll();
        }

        public void clearAll()
        {
            txtMediId.Clear();
            txtMediName.Clear();

            // مسح النظام الديناميكي للجرعات
            ClearAllDosages();

            // مسح الحقول القديمة (مخفية)
            
            txtPricePerUnit.Clear();
            txtManufacturingDate.ResetText();
            txtExpireDate.ResetText();
            txtlu.Clear();
            txtbr.Clear();
        }

        private void txtMediId_TextChanged(object sender, EventArgs e)
        {
            query = "SELECT * FROM medic WHERE mid='" + txtMediId.Text + "'";
            DataSet ds = fn.getData(query);
            if (ds.Tables[0].Rows.Count == 0)
            {
                pictureBox1.ImageLocation = @"D:\\pharmacy\1.png";
            }
            else
            {
                pictureBox1.ImageLocation = @"D:\\pharmacy\0.png";
            }
        }



        private void dosq4_TextChanged(object sender, EventArgs e)
        {

        }
    }
}
