@echo off
echo ========================================
echo   إصلاح الإجراءات المخزنة المفقودة
echo   Fix Missing Stored Procedures
echo ========================================
echo.

echo 🔧 إنشاء الإجراءات المخزنة المطلوبة...
echo.

REM تشغيل سكريپت إنشاء الإجراءات المخزنة
sqlcmd -S NARUTO -E -i create_missing_stored_procedures.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم إنشاء الإجراءات المخزنة بنجاح!
    echo.
    echo الإجراءات التي تم إنشاؤها:
    echo - sp_RegisterPharmacy: تسجيل صيدلية جديدة
    echo - sp_SearchMedicines: البحث عن الأدوية
    echo - sp_GetActivePharmacies: الحصول على الصيدليات النشطة
    echo.
    echo يمكنك الآن تسجيل صيدلية جديدة بدون أخطاء.
) else (
    echo.
    echo ❌ حدث خطأ أثناء إنشاء الإجراءات المخزنة!
    echo.
    echo تأكد من:
    echo 1. تشغيل SQL Server
    echo 2. وجود قاعدة البيانات PharmacyNetworkOnline
    echo 3. صحة اسم الخادم (NARUTO)
    echo 4. وجود صلاحيات الإدارة
    echo.
    echo إذا لم تكن قاعدة البيانات موجودة، شغل:
    echo setup_online_database.bat
)

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
