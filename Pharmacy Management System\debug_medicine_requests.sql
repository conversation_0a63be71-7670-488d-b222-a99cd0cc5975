-- فحص بيانات طلبات الأدوية
USE UnifiedPharmacy;

PRINT '=== فحص بيانات طلبات الأدوية ===';

-- 1. فحص جدول purchase_requests
PRINT '1. فحص جدول purchase_requests:';
SELECT COUNT(*) as 'عدد الطلبات' FROM purchase_requests;

IF EXISTS (SELECT 1 FROM purchase_requests)
BEGIN
    SELECT TOP 5 
        id, buyer_pharmacy_id, seller_pharmacy_id, medicine_id, 
        requested_quantity, status, request_date
    FROM purchase_requests 
    ORDER BY request_date DESC;
END
ELSE
BEGIN
    PRINT '⚠️ جدول purchase_requests فارغ!';
END

-- 2. فحص جدول medic
PRINT '';
PRINT '2. فحص جدول medic:';
SELECT COUNT(*) as 'عدد الأدوية' FROM medic;

IF EXISTS (SELECT 1 FROM medic)
BEGIN
    SELECT TOP 5 
        id, mname, mnumber, quantity, perUnit
    FROM medic 
    ORDER BY id;
END
ELSE
BEGIN
    PRINT '⚠️ جدول medic فارغ!';
END

-- 3. فحص جدول users
PRINT '';
PRINT '3. فحص جدول users:';
SELECT COUNT(*) as 'عدد المستخدمين' FROM users;

IF EXISTS (SELECT 1 FROM users)
BEGIN
    SELECT TOP 5 
        id, name, userRole, mobile
    FROM users 
    ORDER BY id;
END
ELSE
BEGIN
    PRINT '⚠️ جدول users فارغ!';
END

-- 4. اختبار الاستعلام المستخدم في التطبيق
PRINT '';
PRINT '4. اختبار الاستعلام للصيدلية رقم 1:';

DECLARE @pharmacyId INT = 1;

SELECT
    pr.id, pr.requested_quantity as requestedQuantity,
    pr.offered_price as offeredPrice, pr.request_date as requestDate,
    pr.status, pr.response_message as responseMessage,
    m.mname as medicineName, m.mnumber as medicineNumber,
    m.perUnit as originalPrice, '' as description,
    p_buyer.name as buyerPharmacyName,
    p_buyer.mobile as buyerPhone, '' as buyerCity
FROM purchase_requests pr
INNER JOIN medic m ON pr.medicine_id = m.id
INNER JOIN users p_buyer ON pr.buyer_pharmacy_id = p_buyer.id
WHERE pr.seller_pharmacy_id = @pharmacyId
ORDER BY pr.request_date DESC;

-- 5. فحص إذا كانت هناك مشكلة في الربط
PRINT '';
PRINT '5. فحص مشاكل الربط المحتملة:';

-- طلبات بدون أدوية مطابقة
SELECT COUNT(*) as 'طلبات بدون أدوية مطابقة'
FROM purchase_requests pr
LEFT JOIN medic m ON pr.medicine_id = m.id
WHERE m.id IS NULL;

-- طلبات بدون مستخدمين مطابقين
SELECT COUNT(*) as 'طلبات بدون مستخدمين مطابقين'
FROM purchase_requests pr
LEFT JOIN users u ON pr.buyer_pharmacy_id = u.id
WHERE u.id IS NULL;

-- 6. إدراج بيانات تجريبية إذا لم توجد
IF NOT EXISTS (SELECT 1 FROM purchase_requests)
BEGIN
    PRINT '';
    PRINT '6. إدراج بيانات تجريبية:';
    
    -- التأكد من وجود أدوية
    IF NOT EXISTS (SELECT 1 FROM medic)
    BEGIN
        INSERT INTO medic (mname, mnumber, quantity, perUnit, mDate, eDate, originalQuantity)
        VALUES 
        ('باراسيتامول', 'MED001', 100, 5.50, GETDATE(), DATEADD(YEAR, 2, GETDATE()), 100),
        ('أموكسيسيلين', 'MED002', 50, 12.00, GETDATE(), DATEADD(YEAR, 1, GETDATE()), 50),
        ('فيتامين د', 'MED003', 75, 25.00, GETDATE(), DATEADD(YEAR, 2, GETDATE()), 75);
        
        PRINT '✅ تم إدراج أدوية تجريبية';
    END
    
    -- التأكد من وجود مستخدمين
    IF NOT EXISTS (SELECT 1 FROM users WHERE id BETWEEN 1 AND 4)
    BEGIN
        SET IDENTITY_INSERT users ON;
        INSERT INTO users (id, name, userRole, mobile, email, username, pass)
        VALUES 
        (1, 'الصيدلية المركزية', 'admin', '01234567890', '<EMAIL>', 'central', 'pass'),
        (2, 'صيدلية النهضة', 'admin', '01987654321', '<EMAIL>', 'nahda', 'pass'),
        (3, 'صيدلية الشفاء', 'admin', '01122334455', '<EMAIL>', 'shifa', 'pass'),
        (4, 'صيدلية الأمل', 'admin', '01555666777', '<EMAIL>', 'amal', 'pass');
        SET IDENTITY_INSERT users OFF;
        
        PRINT '✅ تم إدراج مستخدمين تجريبيين';
    END
    
    -- إدراج طلبات تجريبية
    INSERT INTO purchase_requests 
    (buyer_pharmacy_id, seller_pharmacy_id, medicine_id, requested_quantity, offered_price, request_message, status, request_date)
    SELECT 2, 1, m.id, 10, 50.00, 'طلب تجريبي', 'pending', GETDATE()
    FROM medic m WHERE m.id = (SELECT MIN(id) FROM medic)
    UNION ALL
    SELECT 3, 1, m.id, 5, 60.00, 'طلب تجريبي 2', 'pending', GETDATE()
    FROM medic m WHERE m.id = (SELECT MIN(id) + 1 FROM medic)
    UNION ALL
    SELECT 4, 1, m.id, 8, 200.00, 'طلب تجريبي 3', 'pending', GETDATE()
    FROM medic m WHERE m.id = (SELECT MIN(id) + 2 FROM medic);
    
    PRINT '✅ تم إدراج طلبات تجريبية';
END

PRINT '';
PRINT '=== انتهى الفحص ===';
خ