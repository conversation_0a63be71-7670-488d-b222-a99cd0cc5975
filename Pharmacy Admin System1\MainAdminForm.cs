using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace Pharmacy_Admin_System
{
    /// <summary>
    /// الواجهة الرئيسية لنظام إدارة الصيدليات المركزي
    /// Main Admin Interface for Central Pharmacy Management System
    /// </summary>
    public partial class MainAdminForm : Form
    {
        private DatabaseManager dbManager;
        private DataRow adminData;
        private Panel sidePanel;
        private Panel contentPanel;
        private Panel topPanel;
        private Label welcomeLabel;
        private Button logoutButton;

        // Menu buttons
        private Button dashboardButton;
        private Button pharmaciesButton;
        private Button approvalButton;
        private Button subscriptionsButton;
        private Button backupButton;
        private Button usersButton;
        private Button reportsButton;
        private Button settingsButton;

        public MainAdminForm(DataRow adminData)
        {
            this.adminData = adminData;
            dbManager = new DatabaseManager();
            InitializeComponent();
            SetupForm();
            LoadDashboard();
        }

        private void SetupForm()
        {
            // Set form properties
            this.BackColor = Color.FromArgb(45, 45, 48);
            this.WindowState = FormWindowState.Maximized;
            this.Text = "نظام إدارة الصيدليات المركزي - " + adminData["fullName"].ToString();

            // Top panel
            topPanel = new Panel
            {
                Size = new Size(this.Width, 60),
                Location = new Point(0, 0),
                BackColor = Color.FromArgb(0, 122, 204),
                Dock = DockStyle.Top
            };
            this.Controls.Add(topPanel);

            // Welcome label
            welcomeLabel = new Label
            {
                Text = $"مرحباً، {adminData["fullName"]} | نظام إدارة الصيدليات المركزي",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(800, 60),
                Location = new Point(20, 0),
                TextAlign = ContentAlignment.MiddleLeft
            };
            topPanel.Controls.Add(welcomeLabel);

            // Logout button
            logoutButton = new Button
            {
                Text = "تسجيل الخروج",
                Font = new Font("Segoe UI", 10),
                Size = new Size(120, 35),
                Location = new Point(this.Width - 150, 12),
                BackColor = Color.FromArgb(180, 50, 50),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };
            logoutButton.FlatAppearance.BorderSize = 0;
            logoutButton.Click += LogoutButton_Click;
            topPanel.Controls.Add(logoutButton);

            // Side panel
            sidePanel = new Panel
            {
                Size = new Size(250, this.Height - 60),
                Location = new Point(0, 60),
                BackColor = Color.FromArgb(62, 62, 66),
                Dock = DockStyle.Left
            };
            this.Controls.Add(sidePanel);

            // Content panel
            contentPanel = new Panel
            {
                Size = new Size(this.Width - 250, this.Height - 60),
                Location = new Point(250, 60),
                BackColor = Color.FromArgb(45, 45, 48),
                Dock = DockStyle.Fill
            };
            this.Controls.Add(contentPanel);

            CreateMenuButtons();
        }

        private void CreateMenuButtons()
        {
            int buttonHeight = 50;
            int startY = 20;
            int spacing = 5;

            // Dashboard
            dashboardButton = CreateMenuButton("لوحة التحكم", startY, true);
            dashboardButton.Click += (s, e) => LoadDashboard();

            // Pharmacies
            pharmaciesButton = CreateMenuButton("الصيدليات المسجلة", startY + (buttonHeight + spacing) * 1);
            pharmaciesButton.Click += (s, e) => LoadPharmacies();

            // Approval
            approvalButton = CreateMenuButton("طلبات التسجيل", startY + (buttonHeight + spacing) * 2);
            approvalButton.Click += (s, e) => LoadApprovalRequests();

            // Subscriptions
            subscriptionsButton = CreateMenuButton("إدارة الاشتراكات", startY + (buttonHeight + spacing) * 3);
            subscriptionsButton.Click += (s, e) => LoadSubscriptions();

            // Backup
            backupButton = CreateMenuButton("النسخ الاحتياطية", startY + (buttonHeight + spacing) * 4);
            backupButton.Click += (s, e) => LoadBackupManagement();

            // Users
            usersButton = CreateMenuButton("إدارة المستخدمين", startY + (buttonHeight + spacing) * 5);
            usersButton.Click += (s, e) => LoadUserManagement();

            // Reports
            reportsButton = CreateMenuButton("التقارير", startY + (buttonHeight + spacing) * 6);
            reportsButton.Click += (s, e) => LoadReports();

            // Settings
            settingsButton = CreateMenuButton("إعدادات النظام", startY + (buttonHeight + spacing) * 7);
            settingsButton.Click += (s, e) => LoadSettings();
        }

        private Button CreateMenuButton(string text, int y, bool isActive = false)
        {
            var button = new Button
            {
                Text = text,
                Font = new Font("Segoe UI", 11),
                Size = new Size(230, 50),
                Location = new Point(10, y),
                BackColor = isActive ? Color.FromArgb(0, 122, 204) : Color.FromArgb(85, 85, 85),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                TextAlign = ContentAlignment.MiddleLeft,
                Cursor = Cursors.Hand
            };
            button.FlatAppearance.BorderSize = 0;
            button.Click += (s, e) => SetActiveButton(button);
            sidePanel.Controls.Add(button);
            return button;
        }

        private void SetActiveButton(Button activeButton)
        {
            // Reset all buttons
            foreach (Control control in sidePanel.Controls)
            {
                if (control is Button btn)
                {
                    btn.BackColor = Color.FromArgb(85, 85, 85);
                }
            }

            // Set active button
            activeButton.BackColor = Color.FromArgb(0, 122, 204);
        }

        private void LoadDashboard()
        {
            contentPanel.Controls.Clear();
            SetActiveButton(dashboardButton);

            var titleLabel = new Label
            {
                Text = "لوحة التحكم الرئيسية",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(400, 40),
                Location = new Point(30, 30)
            };
            contentPanel.Controls.Add(titleLabel);

            // Statistics panels
            CreateStatisticsPanel();
        }

        private void CreateStatisticsPanel()
        {
            try
            {
                // Total pharmacies
                var totalPharmacies = GetStatistic("SELECT COUNT(*) FROM registered_pharmacies");
                CreateStatCard("إجمالي الصيدليات", totalPharmacies.ToString(), Color.FromArgb(0, 122, 204), 30, 100);

                // Active pharmacies
                var activePharmacies = GetStatistic("SELECT COUNT(*) FROM registered_pharmacies WHERE status = 'Approved' AND isActive = 1");
                CreateStatCard("الصيدليات النشطة", activePharmacies.ToString(), Color.FromArgb(76, 175, 80), 280, 100);

                // Pending approvals
                var pendingApprovals = GetStatistic("SELECT COUNT(*) FROM registered_pharmacies WHERE status = 'Pending'");
                CreateStatCard("طلبات التسجيل", pendingApprovals.ToString(), Color.FromArgb(255, 152, 0), 530, 100);

                // Monthly revenue
                var monthlyRevenue = GetStatistic(@"
                    SELECT ISNULL(SUM(amount), 0) 
                    FROM subscription_payments 
                    WHERE MONTH(paymentDate) = MONTH(GETDATE()) AND YEAR(paymentDate) = YEAR(GETDATE())");
                CreateStatCard("إيرادات الشهر", $"{monthlyRevenue:N0} ريال", Color.FromArgb(156, 39, 176), 780, 100);
            }
            catch (Exception ex)
            {
                var errorLabel = new Label
                {
                    Text = $"خطأ في تحميل الإحصائيات: {ex.Message}",
                    ForeColor = Color.Red,
                    Size = new Size(500, 30),
                    Location = new Point(30, 100)
                };
                contentPanel.Controls.Add(errorLabel);
            }
        }

        private void CreateStatCard(string title, string value, Color color, int x, int y)
        {
            var panel = new Panel
            {
                Size = new Size(220, 120),
                Location = new Point(x, y),
                BackColor = color
            };

            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(200, 30),
                Location = new Point(10, 15),
                TextAlign = ContentAlignment.MiddleCenter
            };

            var valueLabel = new Label
            {
                Text = value,
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(200, 50),
                Location = new Point(10, 45),
                TextAlign = ContentAlignment.MiddleCenter
            };

            panel.Controls.Add(titleLabel);
            panel.Controls.Add(valueLabel);
            contentPanel.Controls.Add(panel);
        }

        private decimal GetStatistic(string query)
        {
            try
            {
                var result = dbManager.ExecuteAdminScalar(query);
                return Convert.ToDecimal(result ?? 0);
            }
            catch
            {
                return 0;
            }
        }

        private void LoadPharmacies()
        {
            contentPanel.Controls.Clear();
            
            var titleLabel = new Label
            {
                Text = "إدارة الصيدليات المسجلة",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(400, 40),
                Location = new Point(30, 30)
            };
            contentPanel.Controls.Add(titleLabel);

            var comingSoonLabel = new Label
            {
                Text = "قريباً - واجهة إدارة الصيدليات",
                Font = new Font("Segoe UI", 14),
                ForeColor = Color.LightGray,
                Size = new Size(400, 30),
                Location = new Point(30, 100)
            };
            contentPanel.Controls.Add(comingSoonLabel);
        }

        private void LoadApprovalRequests()
        {
            contentPanel.Controls.Clear();
            
            var titleLabel = new Label
            {
                Text = "طلبات تسجيل الصيدليات",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(400, 40),
                Location = new Point(30, 30)
            };
            contentPanel.Controls.Add(titleLabel);

            var comingSoonLabel = new Label
            {
                Text = "قريباً - واجهة الموافقة على طلبات التسجيل",
                Font = new Font("Segoe UI", 14),
                ForeColor = Color.LightGray,
                Size = new Size(400, 30),
                Location = new Point(30, 100)
            };
            contentPanel.Controls.Add(comingSoonLabel);
        }

        private void LoadSubscriptions()
        {
            contentPanel.Controls.Clear();
            
            var titleLabel = new Label
            {
                Text = "إدارة الاشتراكات",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(400, 40),
                Location = new Point(30, 30)
            };
            contentPanel.Controls.Add(titleLabel);

            var comingSoonLabel = new Label
            {
                Text = "قريباً - واجهة إدارة الاشتراكات",
                Font = new Font("Segoe UI", 14),
                ForeColor = Color.LightGray,
                Size = new Size(400, 30),
                Location = new Point(30, 100)
            };
            contentPanel.Controls.Add(comingSoonLabel);
        }

        private void LoadBackupManagement()
        {
            contentPanel.Controls.Clear();
            
            var titleLabel = new Label
            {
                Text = "إدارة النسخ الاحتياطية",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(400, 40),
                Location = new Point(30, 30)
            };
            contentPanel.Controls.Add(titleLabel);

            var comingSoonLabel = new Label
            {
                Text = "قريباً - واجهة إدارة النسخ الاحتياطية",
                Font = new Font("Segoe UI", 14),
                ForeColor = Color.LightGray,
                Size = new Size(400, 30),
                Location = new Point(30, 100)
            };
            contentPanel.Controls.Add(comingSoonLabel);
        }

        private void LoadUserManagement()
        {
            contentPanel.Controls.Clear();
            
            var titleLabel = new Label
            {
                Text = "إدارة المستخدمين",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(400, 40),
                Location = new Point(30, 30)
            };
            contentPanel.Controls.Add(titleLabel);

            var comingSoonLabel = new Label
            {
                Text = "قريباً - واجهة إدارة المستخدمين",
                Font = new Font("Segoe UI", 14),
                ForeColor = Color.LightGray,
                Size = new Size(400, 30),
                Location = new Point(30, 100)
            };
            contentPanel.Controls.Add(comingSoonLabel);
        }

        private void LoadReports()
        {
            contentPanel.Controls.Clear();
            
            var titleLabel = new Label
            {
                Text = "التقارير والإحصائيات",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(400, 40),
                Location = new Point(30, 30)
            };
            contentPanel.Controls.Add(titleLabel);

            var comingSoonLabel = new Label
            {
                Text = "قريباً - واجهة التقارير والإحصائيات",
                Font = new Font("Segoe UI", 14),
                ForeColor = Color.LightGray,
                Size = new Size(400, 30),
                Location = new Point(30, 100)
            };
            contentPanel.Controls.Add(comingSoonLabel);
        }

        private void LoadSettings()
        {
            contentPanel.Controls.Clear();
            
            var titleLabel = new Label
            {
                Text = "إعدادات النظام",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(400, 40),
                Location = new Point(30, 30)
            };
            contentPanel.Controls.Add(titleLabel);

            var comingSoonLabel = new Label
            {
                Text = "قريباً - واجهة إعدادات النظام",
                Font = new Font("Segoe UI", 14),
                ForeColor = Color.LightGray,
                Size = new Size(400, 30),
                Location = new Point(30, 100)
            };
            contentPanel.Controls.Add(comingSoonLabel);
        }

        private void LogoutButton_Click(object sender, EventArgs e)
        {
            try
            {
                // تسجيل النشاط
                dbManager.LogActivity(
                    Convert.ToInt32(adminData["id"]), 
                    null, 
                    "Logout", 
                    $"تسجيل خروج المدير العام: {adminData["fullName"]}"
                );

                // إغلاق النموذج الحالي وفتح نموذج تسجيل الدخول
                var loginForm = new AdminLoginForm();
                loginForm.Show();
                this.Hide();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الخروج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            Application.Exit();
            base.OnFormClosing(e);
        }
    }
}
