#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import MetaTrader5 as mt5
from datetime import datetime
import sys

def test_crypto_symbols():
    """اختبار العملات الرقمية المتوفرة"""
    
    print("=" * 60)
    print("🔍 اختبار العملات الرقمية المتوفرة في MetaTrader 5")
    print("=" * 60)
    
    # تهيئة MT5
    if not mt5.initialize():
        print("❌ فشل في تهيئة MetaTrader 5")
        return False
    
    try:
        # معلومات الحساب
        account_info = mt5.account_info()
        if account_info:
            print(f"📊 معلومات الحساب:")
            print(f"   رقم الحساب: {account_info.login}")
            print(f"   الرصيد: ${account_info.balance:.2f}")
            print(f"   العملة: {account_info.currency}")
            print(f"   الشركة: {account_info.company}")
            print(f"   الخادم: {account_info.server}")
            print()
        else:
            print("⚠️ لا يمكن الحصول على معلومات الحساب")
            print()
        
        # الحصول على جميع الرموز
        all_symbols = mt5.symbols_get()
        if not all_symbols:
            print("❌ فشل في الحصول على قائمة الرموز")
            return False
        
        print(f"📈 إجمالي الرموز المتوفرة: {len(all_symbols)}")
        print()
        
        # البحث عن العملات الرقمية
        crypto_symbols = []
        crypto_keywords = ['BTC', 'ETH', 'LTC', 'XRP', 'ADA', 'DOT', 'LINK', 'UNI', 'DOGE', 'SHIB']
        
        print("🔍 البحث عن العملات الرقمية...")
        print()
        
        for symbol in all_symbols:
            symbol_name = symbol.name
            
            # فحص العملات الرقمية
            for keyword in crypto_keywords:
                if keyword in symbol_name and len(symbol_name) <= 15:
                    # التأكد من أن الرمز قابل للتداول
                    if symbol.trade_mode == mt5.SYMBOL_TRADE_MODE_FULL:
                        crypto_symbols.append({
                            'name': symbol_name,
                            'description': symbol.description,
                            'currency_base': symbol.currency_base,
                            'currency_profit': symbol.currency_profit,
                            'point': symbol.point,
                            'digits': symbol.digits
                        })
                        break
        
        # عرض العملات الرقمية المتوفرة
        if crypto_symbols:
            print(f"✅ تم العثور على {len(crypto_symbols)} عملة رقمية:")
            print()
            
            for i, crypto in enumerate(crypto_symbols[:20], 1):  # أول 20 عملة
                print(f"   {i:2d}. {crypto['name']:<12} - {crypto['description']}")
                print(f"       العملة الأساسية: {crypto['currency_base']}")
                print(f"       عملة الربح: {crypto['currency_profit']}")
                print(f"       النقاط العشرية: {crypto['digits']}")
                print()
                
        else:
            print("⚠️ لم يتم العثور على عملات رقمية")
            print()
        
        # اختبار بعض العملات الرقمية الشائعة
        test_symbols = ['BTC', 'ETH', 'BTCUSD', 'ETHUSD', 'BTCEUR', 'ETHEUR']
        
        print("🧪 اختبار العملات الرقمية الشائعة:")
        print()
        
        available_for_trading = []
        
        for symbol_name in test_symbols:
            symbol_info = mt5.symbol_info(symbol_name)
            if symbol_info:
                # محاولة إضافة الرمز إلى Market Watch
                if mt5.symbol_select(symbol_name, True):
                    # اختبار الحصول على البيانات
                    rates = mt5.copy_rates_from_pos(symbol_name, mt5.TIMEFRAME_M1, 0, 10)
                    if rates is not None and len(rates) > 0:
                        available_for_trading.append(symbol_name)
                        print(f"   ✅ {symbol_name:<10} - متوفر للتداول")
                        print(f"       الوصف: {symbol_info.description}")
                        print(f"       آخر سعر: {rates[-1]['close']:.5f}")
                    else:
                        print(f"   ⚠️ {symbol_name:<10} - متوفر لكن لا توجد بيانات")
                else:
                    print(f"   ❌ {symbol_name:<10} - لا يمكن إضافته")
            else:
                print(f"   ❌ {symbol_name:<10} - غير متوفر")
        
        print()
        print("=" * 60)
        print("📋 ملخص النتائج:")
        print(f"   إجمالي الرموز: {len(all_symbols)}")
        print(f"   العملات الرقمية المكتشفة: {len(crypto_symbols)}")
        print(f"   العملات المتوفرة للتداول: {len(available_for_trading)}")
        
        if available_for_trading:
            print(f"   الرموز الجاهزة للتداول: {', '.join(available_for_trading)}")
        
        print("=" * 60)
        
        return len(available_for_trading) > 0
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False
        
    finally:
        mt5.shutdown()

if __name__ == "__main__":
    try:
        success = test_crypto_symbols()
        if success:
            print("🎉 الاختبار نجح! يمكن استخدام العملات الرقمية")
        else:
            print("⚠️ لم يتم العثور على عملات رقمية متوفرة للتداول")
            
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
    except Exception as e:
        print(f"❌ خطأ عام: {str(e)}")
    
    input("\nاضغط Enter للخروج...")
