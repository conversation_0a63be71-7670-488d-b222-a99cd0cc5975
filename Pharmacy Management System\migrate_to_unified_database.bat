@echo off
chcp 65001 >nul
echo ========================================
echo 🔄 نقل البيانات إلى قاعدة البيانات الموحدة
echo 🔄 Migrating to Unified Database
echo ========================================
echo.

echo 🎯 هذا السكريپت سيقوم بـ:
echo - إنشاء قاعدة البيانات الموحدة UnifiedPharmacy
echo - نقل جميع البيانات من قاعدة pharmacy القديمة
echo - إنشاء جميع الجداول المطلوبة
echo - إضافة بيانات افتراضية للاختبار
echo - إعداد النظام للعمل مع قاعدة البيانات الموحدة فقط
echo.

echo ⚠️  تحذير: هذا سيحول النظام بالكامل لاستخدام UnifiedPharmacy
echo ⚠️  Warning: This will convert the system to use UnifiedPharmacy only
echo.

pause
echo.

echo 🚀 بدء عملية النقل...
echo Starting migration process...
echo.

echo الخطوة 1: تشغيل سكريپت النقل...
echo Step 1: Running migration script...
sqlcmd -S NARUTO -E -i migrate_to_unified_database.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo   ✅ تم نقل البيانات بنجاح!
    echo   ✅ Migration completed successfully!
    echo ========================================
    echo.
    echo 📊 ما تم إنجازه:
    echo ✅ إنشاء قاعدة البيانات UnifiedPharmacy
    echo ✅ نقل جميع المستخدمين من قاعدة pharmacy
    echo ✅ نقل جميع الأدوية من قاعدة pharmacy
    echo ✅ نقل جميع المبيعات من قاعدة pharmacy
    echo ✅ إنشاء جداول الشبكة والصيدليات
    echo ✅ إضافة بيانات افتراضية للاختبار
    echo ✅ تحديث جميع ملفات البرنامج لاستخدام UnifiedPharmacy
    echo.
    echo 🎯 النتيجة:
    echo - النظام يستخدم الآن قاعدة البيانات الموحدة فقط
    echo - تم إلغاء الاتصال بقاعدة pharmacy القديمة
    echo - تم إلغاء الاتصال بقاعدة PharmacyNetworkOnline
    echo - جميع البيانات متوفرة في UnifiedPharmacy
    echo.
    echo 🔐 بيانات تسجيل الدخول:
    echo المدير: admin / admin123
    echo الصيدلي: pharmacist / pharm123
    echo.
    echo 🧪 اختبر الآن:
    echo 1. شغل البرنامج من Visual Studio
    echo 2. سجل دخول بأي من الحسابات أعلاه
    echo 3. اذهب لصفحة المتجر واضغط "Connect"
    echo 4. ستجد الصيدليات والأدوية تظهر بدون أخطاء
    echo.
    echo 📁 الملفات المحدثة:
    echo - OnlineNetworkManager.cs: تحويل لـ UnifiedPharmacy
    echo - Function.cs: تحويل لـ UnifiedPharmacy
    echo - UC_PrintDesign.cs: تحويل لـ UnifiedPharmacy
    echo - جميع الاتصالات تستخدم UnifiedPharmacy الآن
    echo.
) else (
    echo.
    echo ========================================
    echo   ❌ حدث خطأ أثناء النقل!
    echo   ❌ Migration failed!
    echo ========================================
    echo.
    echo 🔍 الأسباب المحتملة:
    echo 1. SQL Server غير مشغل
    echo 2. اسم الخادم NARUTO غير صحيح
    echo 3. عدم وجود صلاحيات كافية
    echo 4. قاعدة البيانات pharmacy غير موجودة
    echo.
    echo 🛠️  الحلول:
    echo 1. تأكد من تشغيل SQL Server
    echo 2. تحقق من اسم الخادم في الملفات
    echo 3. شغل Command Prompt كمدير
    echo 4. تأكد من وجود قاعدة pharmacy
    echo.
    echo 💡 يمكنك أيضاً:
    echo - فتح migrate_to_unified_database.sql في SQL Server Management Studio
    echo - تشغيله يدوياً بالضغط على F5
    echo.
)

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
