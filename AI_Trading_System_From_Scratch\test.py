import pandas as pd
import numpy as np
from analysis_engine import AnalysisEngine
from simulation_engine import SimulationEngine
from ml_engine import MLEngine
import configparser

def generate_mock_data(symbol, bars=1000):
    np.random.seed(42)
    dates = pd.date_range(start='2023-01-01', periods=bars, freq='1min')
    prices = 1.0 + np.cumsum(np.random.randn(bars) * 0.0001)  # Mock EURUSD prices
    df = pd.DataFrame({
        'time': dates,
        'open': prices,
        'high': prices + abs(np.random.randn(bars) * 0.0001),
        'low': prices - abs(np.random.randn(bars) * 0.0001),
        'close': prices + np.random.randn(bars) * 0.0001,
        'tick_volume': np.random.randint(100, 1000, bars),
        'spread': np.random.randint(1, 5, bars),
        'real_volume': np.random.randint(1000, 10000, bars)
    })
    return df

def test_analysis():
    config = configparser.ConfigParser()
    config.read('config.ini')
    analysis = AnalysisEngine(config)
    # Mock MT5 connection
    analysis.connect_mt5 = lambda: True
    analysis.disconnect_mt5 = lambda: None
    analysis.get_data = lambda symbol: generate_mock_data(symbol)

    result = analysis.analyze_symbol('EURUSD')
    assert result is not None
    assert 'signal' in result
    print("Analysis test passed")

def test_simulation():
    config = configparser.ConfigParser()
    config.read('config.ini')
    analysis = AnalysisEngine(config)
    sim = SimulationEngine(config, analysis)

    mock_df = generate_mock_data('EURUSD')
    mock_df = analysis.calculate_indicators(mock_df)

    df, pnl = sim.simulate_trades('EURUSD', mock_df)
    print(f"Simulation PnL: {pnl}")
    print("Simulation test passed")

def test_ml():
    config = configparser.ConfigParser()
    config.read('config.ini')
    ml = MLEngine(config)

    mock_df = generate_mock_data('EURUSD')
    mock_df = AnalysisEngine(config).calculate_indicators(mock_df)

    ml.train_model(mock_df)
    signal = ml.predict_signal(mock_df)
    print(f"ML Signal: {signal}")
    print("ML test passed")

if __name__ == "__main__":
    test_analysis()
    test_simulation()
    test_ml()
    print("All tests passed!")
