@echo off
chcp 65001 >nul
echo ========================================
echo 🚀 إعداد نظام إدارة الصيدليات المركزي
echo 🚀 Central Pharmacy Management System Setup
echo ========================================
echo.

echo 🎯 هذا السكريپت سيقوم بـ:
echo - إنشاء قاعدة بيانات الإدارة المركزية (PharmacyAdminSystem)
echo - إنشاء جميع الجداول المطلوبة للنظام الإداري
echo - إضافة بيانات افتراضية (خطط اشتراك، مدير عام، إعدادات)
echo - إعداد النظام للاستخدام الفوري
echo.

echo ⚠️  متطلبات التشغيل:
echo - SQL Server يجب أن يكون مشغلاً
echo - اسم الخادم: NARUTO (أو قم بتعديله في الملفات)
echo - صلاحيات إنشاء قواعد البيانات
echo.

pause
echo.

echo 🚀 بدء عملية الإعداد...
echo Starting setup process...
echo.

echo الخطوة 1: إنشاء قاعدة بيانات الإدارة المركزية...
echo Step 1: Creating central admin database...
sqlcmd -S NARUTO -E -i create_admin_database.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo   ✅ تم إعداد النظام بنجاح!
    echo   ✅ System setup completed successfully!
    echo ========================================
    echo.
    echo 📊 ما تم إنشاؤه:
    echo ✅ قاعدة بيانات PharmacyAdminSystem
    echo ✅ جدول المديرين العامين (admin_users)
    echo ✅ جدول الصيدليات المسجلة (registered_pharmacies)
    echo ✅ جدول خطط الاشتراك (subscription_plans)
    echo ✅ جدول مدفوعات الاشتراكات (subscription_payments)
    echo ✅ جدول النسخ الاحتياطية (backup_history)
    echo ✅ جدول سجل النشاطات (activity_log)
    echo ✅ جدول إعدادات النظام (system_settings)
    echo.
    echo 🔐 بيانات تسجيل الدخول للمدير العام:
    echo اسم المستخدم: superadmin
    echo كلمة المرور: admin2025
    echo.
    echo 💰 خطط الاشتراك المتاحة:
    echo - أساسي: 99 ريال/شهر (3 مستخدمين، 500 دواء)
    echo - قياسي: 199 ريال/شهر (5 مستخدمين، 1000 دواء)
    echo - مميز: 299 ريال/شهر (10 مستخدمين، 5000 دواء)
    echo - مؤسسي: 499 ريال/شهر (50 مستخدم، 50000 دواء)
    echo.
    echo 🎯 الميزات المتاحة في النظام:
    echo ✅ إدارة الصيدليات المسجلة
    echo ✅ الموافقة على طلبات التسجيل الجديدة
    echo ✅ إدارة الاشتراكات والمدفوعات
    echo ✅ النسخ الاحتياطية التلقائية
    echo ✅ إدارة المستخدمين والصلاحيات
    echo ✅ التقارير والإحصائيات المتقدمة
    echo ✅ سجل النشاطات والمراقبة
    echo ✅ إعدادات النظام المرنة
    echo.
    echo 🧪 اختبر النظام الآن:
    echo 1. افتح Visual Studio
    echo 2. افتح مشروع "Pharmacy Admin System"
    echo 3. شغل المشروع (F5)
    echo 4. سجل دخول بالبيانات أعلاه
    echo 5. استكشف جميع الميزات!
    echo.
    echo 📁 الملفات المنشأة:
    echo - Pharmacy Admin System.exe (البرنامج الرئيسي)
    echo - قاعدة بيانات PharmacyAdminSystem
    echo - ملفات الإعداد والتكوين
    echo.
    echo 🔗 الاتصال بقواعد البيانات:
    echo - قاعدة الإدارة: PharmacyAdminSystem (للكتابة والقراءة)
    echo - قاعدة الصيدليات: UnifiedPharmacy (للقراءة فقط)
    echo.
    echo 🎊 النظام الإداري المركزي:
    echo - مستقل تماماً عن نظام الصيدليات
    echo - قاعدة بيانات منفصلة ومحمية
    echo - واجهات حديثة وسهلة الاستخدام
    echo - تقارير شاملة ومفصلة
    echo - نسخ احتياطية آمنة
    echo - مراقبة شاملة للنشاطات
    echo.
    echo 🚀 النظام جاهز للاستخدام الفوري!
    echo يمكنك الآن إدارة جميع الصيدليات من مكان واحد
    echo.
) else (
    echo.
    echo ========================================
    echo   ❌ حدث خطأ أثناء الإعداد!
    echo   ❌ Setup process failed!
    echo ========================================
    echo.
    echo 🔍 الأسباب المحتملة:
    echo 1. SQL Server غير مشغل
    echo 2. اسم الخادم NARUTO غير صحيح
    echo 3. عدم وجود صلاحيات كافية لإنشاء قواعد البيانات
    echo 4. مشكلة في الاتصال بقاعدة البيانات
    echo.
    echo 🛠️  الحلول:
    echo 1. تأكد من تشغيل SQL Server
    echo 2. تحقق من اسم الخادم في ملف App.config
    echo 3. شغل Command Prompt كمدير
    echo 4. تحقق من إعدادات الشبكة والجدار الناري
    echo.
    echo 💡 يمكنك أيضاً:
    echo - فتح create_admin_database.sql في SQL Server Management Studio
    echo - تشغيله يدوياً بالضغط على F5
    echo - التأكد من إنشاء قاعدة البيانات بنجاح
    echo.
    echo 📋 خطوات الإعداد اليدوي:
    echo 1. افتح SQL Server Management Studio
    echo 2. اتصل بالخادم NARUTO
    echo 3. افتح ملف create_admin_database.sql
    echo 4. اضغط F5 لتنفيذ السكريپت
    echo 5. تأكد من ظهور رسائل النجاح
    echo 6. شغل برنامج Pharmacy Admin System
    echo.
)

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
