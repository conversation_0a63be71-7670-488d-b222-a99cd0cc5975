#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from datetime import datetime
import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from real_crypto_trading_system import RealCryptoTradingSystem
except ImportError as e:
    print(f"خطأ في استيراد النظام: {e}")
    sys.exit(1)

class AdvancedCryptoGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام تداول العملات الرقمية المتقدم - الأسعار الحقيقية")
        self.root.geometry("1400x900")
        self.root.configure(bg='#0d1117')
        
        # النظام
        self.trading_system = None
        self.is_connected = False
        self.is_trading = False
        
        # إعداد النمط
        self.setup_style()
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # بدء تحديث البيانات
        self.update_data()
        
    def setup_style(self):
        """إعداد النمط"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # ألوان داكنة متقدمة
        style.configure('TFrame', background='#0d1117')
        style.configure('TLabel', background='#0d1117', foreground='#f0f6fc', font=('Segoe UI', 10))
        style.configure('TButton', background='#21262d', foreground='#f0f6fc', font=('Segoe UI', 9))
        style.configure('TEntry', background='#21262d', foreground='#f0f6fc')
        style.configure('TCombobox', background='#21262d', foreground='#f0f6fc')
        style.configure('TLabelFrame', background='#0d1117', foreground='#58a6ff', font=('Segoe UI', 11, 'bold'))
        style.configure('TScale', background='#0d1117')
        
        # أزرار ملونة متقدمة
        style.configure('Success.TButton', background='#238636', foreground='white', font=('Segoe UI', 9, 'bold'))
        style.configure('Danger.TButton', background='#da3633', foreground='white', font=('Segoe UI', 9, 'bold'))
        style.configure('Warning.TButton', background='#bf8700', foreground='white', font=('Segoe UI', 9, 'bold'))
        style.configure('Info.TButton', background='#0969da', foreground='white', font=('Segoe UI', 9, 'bold'))
        style.configure('Purple.TButton', background='#8957e5', foreground='white', font=('Segoe UI', 9, 'bold'))
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # شريط العنوان
        self.create_header(main_frame)
        
        # الإطار العلوي - التحكم والإعدادات
        top_frame = ttk.Frame(main_frame)
        top_frame.pack(fill=tk.X, pady=(0, 15))
        
        # لوحة التحكم (يسار)
        self.create_control_panel(top_frame)
        
        # لوحة الإعدادات (يمين)
        self.create_settings_panel(top_frame)
        
        # الإطار الأوسط - المعلومات والتحليل
        middle_frame = ttk.Frame(main_frame)
        middle_frame.pack(fill=tk.X, pady=(0, 15))
        
        # معلومات الحساب
        self.create_account_panel(middle_frame)
        
        # لوحة التحليل
        self.create_analysis_panel(middle_frame)
        
        # الإطار السفلي - الصفقات والسجل
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.BOTH, expand=True)
        
        # الصفقات المفتوحة
        self.create_positions_panel(bottom_frame)
        
        # سجل الأحداث
        self.create_log_panel(bottom_frame)
        
    def create_header(self, parent):
        """إنشاء شريط العنوان"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, 25))
        
        # العنوان الرئيسي
        title_label = ttk.Label(header_frame, text="🚀 نظام تداول العملات الرقمية المتقدم", 
                               font=('Segoe UI', 20, 'bold'), foreground='#58a6ff')
        title_label.pack()
        
        # العنوان الفرعي
        subtitle_label = ttk.Label(header_frame, text="الأسعار الحقيقية + الذكاء الاصطناعي + إعدادات متقدمة", 
                                  font=('Segoe UI', 12), foreground='#7d8590')
        subtitle_label.pack(pady=(5, 0))
        
    def create_control_panel(self, parent):
        """إنشاء لوحة التحكم"""
        control_frame = ttk.LabelFrame(parent, text="🎮 التحكم الأساسي")
        control_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # الصف الأول - الاتصال
        row1 = ttk.Frame(control_frame)
        row1.pack(fill=tk.X, padx=15, pady=15)
        
        self.connect_btn = ttk.Button(row1, text="🔌 اتصال", command=self.connect_system,
                                     style='Success.TButton', width=15)
        self.connect_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.disconnect_btn = ttk.Button(row1, text="🔌 قطع الاتصال", command=self.disconnect_system,
                                        style='Danger.TButton', state='disabled', width=15)
        self.disconnect_btn.pack(side=tk.LEFT, padx=(0, 20))
        
        # حالة الاتصال
        self.status_label = ttk.Label(row1, text="❌ غير متصل", foreground='#da3633', 
                                     font=('Segoe UI', 11, 'bold'))
        self.status_label.pack(side=tk.LEFT)
        
        # الصف الثاني - اختيار العملة
        row2 = ttk.Frame(control_frame)
        row2.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        ttk.Label(row2, text="💰 زوج العملة:", font=('Segoe UI', 10, 'bold')).pack(side=tk.LEFT)
        self.pair_var = tk.StringVar(value="BTCUSD")
        self.pair_combo = ttk.Combobox(row2, textvariable=self.pair_var, width=12, font=('Segoe UI', 10))
        self.pair_combo['values'] = ['BTCUSD', 'ETHUSD', 'LTCUSD', 'XRPUSD', 'ADAUSD', 'DOTUSD', 'LINKUSD', 'UNIUSD']
        self.pair_combo.pack(side=tk.LEFT, padx=(10, 20))
        
        # زر تحديث السعر
        self.refresh_price_btn = ttk.Button(row2, text="🔄 تحديث السعر", command=self.refresh_price,
                                           style='Info.TButton', width=15)
        self.refresh_price_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # السعر الحالي
        self.current_price_label = ttk.Label(row2, text="السعر: $0.00", foreground='#f85149',
                                            font=('Segoe UI', 11, 'bold'))
        self.current_price_label.pack(side=tk.LEFT, padx=(20, 0))
        
        # الصف الثالث - التداول
        row3 = ttk.Frame(control_frame)
        row3.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        # وضع التداول
        self.demo_var = tk.BooleanVar(value=True)
        self.demo_check = ttk.Checkbutton(row3, text="🛡️ وضع تجريبي آمن", variable=self.demo_var)
        self.demo_check.pack(side=tk.LEFT, padx=(0, 30))
        
        # أزرار التداول
        self.start_btn = ttk.Button(row3, text="🚀 بدء التداول الذكي", command=self.start_trading,
                                   style='Success.TButton', state='disabled', width=20)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_btn = ttk.Button(row3, text="⏹️ إيقاف التداول", command=self.stop_trading,
                                  style='Danger.TButton', state='disabled', width=20)
        self.stop_btn.pack(side=tk.LEFT)
        
    def create_settings_panel(self, parent):
        """إنشاء لوحة الإعدادات"""
        settings_frame = ttk.LabelFrame(parent, text="⚙️ إعدادات التداول المتقدمة")
        settings_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        # إعدادات نسبة الثقة
        confidence_frame = ttk.Frame(settings_frame)
        confidence_frame.pack(fill=tk.X, padx=15, pady=15)
        
        ttk.Label(confidence_frame, text="🎯 نسبة الثقة المطلوبة للدخول:", 
                 font=('Segoe UI', 10, 'bold')).pack(anchor='w')
        
        # شريط تمرير نسبة الثقة
        confidence_control_frame = ttk.Frame(confidence_frame)
        confidence_control_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.confidence_var = tk.DoubleVar(value=70.0)
        self.confidence_scale = ttk.Scale(confidence_control_frame, from_=30, to=100, 
                                         variable=self.confidence_var, orient=tk.HORIZONTAL,
                                         command=self.update_confidence_label)
        self.confidence_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        self.confidence_label = ttk.Label(confidence_control_frame, text="70%", 
                                         font=('Segoe UI', 12, 'bold'), foreground='#f85149')
        self.confidence_label.pack(side=tk.RIGHT)
        
        # أزرار سريعة لنسبة الثقة
        quick_confidence_frame = ttk.Frame(confidence_frame)
        quick_confidence_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(quick_confidence_frame, text="50%", command=lambda: self.set_confidence(50),
                  style='Warning.TButton', width=8).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_confidence_frame, text="70%", command=lambda: self.set_confidence(70),
                  style='Info.TButton', width=8).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_confidence_frame, text="90%", command=lambda: self.set_confidence(90),
                  style='Success.TButton', width=8).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_confidence_frame, text="100%", command=lambda: self.set_confidence(100),
                  style='Purple.TButton', width=8).pack(side=tk.LEFT)
        
        # إعدادات المخاطرة
        risk_frame = ttk.Frame(settings_frame)
        risk_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        ttk.Label(risk_frame, text="💰 نسبة المخاطرة لكل صفقة:", 
                 font=('Segoe UI', 10, 'bold')).pack(anchor='w')
        
        risk_control_frame = ttk.Frame(risk_frame)
        risk_control_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.risk_var = tk.DoubleVar(value=2.0)
        self.risk_scale = ttk.Scale(risk_control_frame, from_=0.5, to=5.0, 
                                   variable=self.risk_var, orient=tk.HORIZONTAL,
                                   command=self.update_risk_label)
        self.risk_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        self.risk_label = ttk.Label(risk_control_frame, text="2.0%", 
                                   font=('Segoe UI', 12, 'bold'), foreground='#f85149')
        self.risk_label.pack(side=tk.RIGHT)
        
        # زر تطبيق الإعدادات
        apply_frame = ttk.Frame(settings_frame)
        apply_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        ttk.Button(apply_frame, text="✅ تطبيق الإعدادات", command=self.apply_settings,
                  style='Success.TButton', width=20).pack()
        
    def create_account_panel(self, parent):
        """إنشاء لوحة معلومات الحساب"""
        account_frame = ttk.LabelFrame(parent, text="📊 معلومات الحساب")
        account_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # شبكة المعلومات
        grid = ttk.Frame(account_frame)
        grid.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # الصف الأول
        ttk.Label(grid, text="💰 الرصيد:", font=('Segoe UI', 10, 'bold')).grid(row=0, column=0, sticky='w', padx=(0, 10))
        self.balance_label = ttk.Label(grid, text="$0.00", foreground='#238636', font=('Segoe UI', 12, 'bold'))
        self.balance_label.grid(row=0, column=1, sticky='w', padx=(0, 30))
        
        ttk.Label(grid, text="📈 الربح/الخسارة:", font=('Segoe UI', 10, 'bold')).grid(row=0, column=2, sticky='w', padx=(0, 10))
        self.profit_label = ttk.Label(grid, text="$0.00", font=('Segoe UI', 12, 'bold'))
        self.profit_label.grid(row=0, column=3, sticky='w')
        
        # الصف الثاني
        ttk.Label(grid, text="📋 الصفقات المفتوحة:", font=('Segoe UI', 10, 'bold')).grid(row=1, column=0, sticky='w', padx=(0, 10))
        self.positions_count_label = ttk.Label(grid, text="0", font=('Segoe UI', 12, 'bold'))
        self.positions_count_label.grid(row=1, column=1, sticky='w', padx=(0, 30))
        
        ttk.Label(grid, text="🔄 حالة النظام:", font=('Segoe UI', 10, 'bold')).grid(row=1, column=2, sticky='w', padx=(0, 10))
        self.system_status_label = ttk.Label(grid, text="متوقف", foreground='#bf8700', font=('Segoe UI', 11, 'bold'))
        self.system_status_label.grid(row=1, column=3, sticky='w')
        
    def create_analysis_panel(self, parent):
        """إنشاء لوحة التحليل"""
        analysis_frame = ttk.LabelFrame(parent, text="📊 التحليل الفني والذكاء الاصطناعي")
        analysis_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        # معلومات التحليل
        analysis_grid = ttk.Frame(analysis_frame)
        analysis_grid.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # الصف الأول
        ttk.Label(analysis_grid, text="🎯 القرار:", font=('Segoe UI', 10, 'bold')).grid(row=0, column=0, sticky='w', padx=(0, 10))
        self.decision_label = ttk.Label(analysis_grid, text="لا يوجد", font=('Segoe UI', 12, 'bold'))
        self.decision_label.grid(row=0, column=1, sticky='w', padx=(0, 30))
        
        ttk.Label(analysis_grid, text="📊 نسبة الثقة:", font=('Segoe UI', 10, 'bold')).grid(row=0, column=2, sticky='w', padx=(0, 10))
        self.analysis_confidence_label = ttk.Label(analysis_grid, text="0%", font=('Segoe UI', 12, 'bold'))
        self.analysis_confidence_label.grid(row=0, column=3, sticky='w')
        
        # الصف الثاني
        ttk.Label(analysis_grid, text="📈 RSI:", font=('Segoe UI', 10, 'bold')).grid(row=1, column=0, sticky='w', padx=(0, 10))
        self.rsi_label = ttk.Label(analysis_grid, text="0.0", font=('Segoe UI', 11))
        self.rsi_label.grid(row=1, column=1, sticky='w', padx=(0, 30))
        
        ttk.Label(analysis_grid, text="📊 MACD:", font=('Segoe UI', 10, 'bold')).grid(row=1, column=2, sticky='w', padx=(0, 10))
        self.macd_label = ttk.Label(analysis_grid, text="0.0", font=('Segoe UI', 11))
        self.macd_label.grid(row=1, column=3, sticky='w')
        
        # زر التحليل اليدوي
        manual_analysis_frame = ttk.Frame(analysis_frame)
        manual_analysis_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        ttk.Button(manual_analysis_frame, text="🔍 تحليل يدوي", command=self.manual_analysis,
                  style='Info.TButton', width=15).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(manual_analysis_frame, text="📊 تحليل متقدم", command=self.advanced_analysis,
                  style='Purple.TButton', width=15).pack(side=tk.LEFT)

    def create_positions_panel(self, parent):
        """إنشاء لوحة الصفقات المفتوحة"""
        positions_frame = ttk.LabelFrame(parent, text="📋 الصفقات المفتوحة")
        positions_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # جدول الصفقات
        columns = ('ID', 'الزوج', 'النوع', 'الحجم', 'سعر الدخول', 'السعر الحالي', 'الربح/الخسارة')
        self.positions_tree = ttk.Treeview(positions_frame, columns=columns, show='headings', height=8)

        # تحديد عناوين الأعمدة
        for col in columns:
            self.positions_tree.heading(col, text=col)
            self.positions_tree.column(col, width=100, anchor='center')

        # شريط التمرير
        positions_scrollbar = ttk.Scrollbar(positions_frame, orient=tk.VERTICAL, command=self.positions_tree.yview)
        self.positions_tree.configure(yscrollcommand=positions_scrollbar.set)

        self.positions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=15, pady=15)
        positions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=15)

    def create_log_panel(self, parent):
        """إنشاء لوحة السجل"""
        log_frame = ttk.LabelFrame(parent, text="📝 سجل الأحداث والتحليلات")
        log_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))

        self.log_text = scrolledtext.ScrolledText(log_frame, height=8,
                                                 bg='#0d1117', fg='#58a6ff',
                                                 font=('Consolas', 9),
                                                 insertbackground='#58a6ff')
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # رسالة ترحيب
        welcome_msg = """
🚀 مرحباً بك في نظام تداول العملات الرقمية المتقدم!

✨ المميزات الجديدة:
• الأسعار الحقيقية مباشرة من CoinGecko API
• إعدادات نسبة الثقة قابلة للتخصيص (30% - 100%)
• تحليل فني متقدم مع مؤشرات متعددة
• تداول أزواج العملات الرقمية الحقيقية (BTCUSD, ETHUSD, إلخ)
• وضع تجريبي آمن مع محاكاة واقعية

📋 خطوات البدء:
1. اضغط "اتصال" للاتصال بالنظام
2. اختر زوج العملة (مثل BTCUSD)
3. حدد نسبة الثقة المطلوبة للدخول
4. اضغط "بدء التداول الذكي"

⚠️ تنبيه: ابدأ دائماً بالوضع التجريبي!
        """
        self.log_text.insert(tk.END, welcome_msg)

    # وظائف التحكم
    def update_confidence_label(self, value):
        """تحديث تسمية نسبة الثقة"""
        confidence = float(value)
        self.confidence_label.config(text=f"{confidence:.0f}%")

        # تغيير اللون حسب النسبة
        if confidence >= 90:
            color = '#238636'  # أخضر
        elif confidence >= 70:
            color = '#0969da'  # أزرق
        elif confidence >= 50:
            color = '#bf8700'  # أصفر
        else:
            color = '#da3633'  # أحمر

        self.confidence_label.config(foreground=color)

    def update_risk_label(self, value):
        """تحديث تسمية نسبة المخاطرة"""
        risk = float(value)
        self.risk_label.config(text=f"{risk:.1f}%")

    def set_confidence(self, value):
        """تحديد نسبة الثقة بسرعة"""
        self.confidence_var.set(value)
        self.update_confidence_label(value)

    def apply_settings(self):
        """تطبيق الإعدادات"""
        if self.trading_system:
            confidence = self.confidence_var.get()
            risk = self.risk_var.get()

            self.trading_system.set_confidence_threshold(confidence)
            self.trading_system.risk_per_trade = risk / 100.0

            self.log_message(f"✅ تم تطبيق الإعدادات:")
            self.log_message(f"   🎯 نسبة الثقة: {confidence:.0f}%")
            self.log_message(f"   💰 نسبة المخاطرة: {risk:.1f}%")
        else:
            messagebox.showwarning("تحذير", "يجب الاتصال بالنظام أولاً!")

    def log_message(self, message):
        """إضافة رسالة للسجل"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, full_message)
        self.log_text.see(tk.END)

    def connect_system(self):
        """الاتصال بالنظام"""
        try:
            self.log_message("🔌 محاولة الاتصال بنظام العملات الرقمية...")

            demo_mode = self.demo_var.get()
            self.trading_system = RealCryptoTradingSystem(demo_mode=demo_mode)

            if self.trading_system.connect():
                self.is_connected = True
                self.status_label.config(text="✅ متصل", foreground='#238636')
                self.connect_btn.config(state='disabled')
                self.disconnect_btn.config(state='normal')
                self.start_btn.config(state='normal')
                self.refresh_price_btn.config(state='normal')

                # تطبيق الإعدادات الحالية
                self.apply_settings()

                # تحديث السعر
                self.refresh_price()

                self.log_message("✅ تم الاتصال بنجاح!")
                self.log_message(f"📊 أزواج العملات المتوفرة: {len(self.trading_system.get_available_pairs())}")

                # تحديث معلومات الحساب
                self.update_account_info()

            else:
                self.log_message("❌ فشل في الاتصال!")
                messagebox.showerror("خطأ", "فشل في الاتصال بنظام العملات الرقمية\nتحقق من اتصال الإنترنت")

        except Exception as e:
            self.log_message(f"❌ خطأ في الاتصال: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في الاتصال: {str(e)}")

    def disconnect_system(self):
        """قطع الاتصال"""
        if self.is_trading:
            self.stop_trading()

        if self.trading_system:
            self.trading_system.disconnect()

        self.is_connected = False
        self.status_label.config(text="❌ غير متصل", foreground='#da3633')
        self.connect_btn.config(state='normal')
        self.disconnect_btn.config(state='disabled')
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='disabled')
        self.refresh_price_btn.config(state='disabled')

        # مسح المعلومات
        self.balance_label.config(text="$0.00")
        self.profit_label.config(text="$0.00", foreground='#f0f6fc')
        self.positions_count_label.config(text="0")
        self.system_status_label.config(text="متوقف", foreground='#bf8700')
        self.current_price_label.config(text="السعر: $0.00")
        self.decision_label.config(text="لا يوجد")
        self.analysis_confidence_label.config(text="0%")
        self.rsi_label.config(text="0.0")
        self.macd_label.config(text="0.0")

        # مسح جدول الصفقات
        for item in self.positions_tree.get_children():
            self.positions_tree.delete(item)

        self.log_message("🔌 تم قطع الاتصال")

    def refresh_price(self):
        """تحديث السعر الحالي"""
        if not self.is_connected:
            return

        try:
            pair = self.pair_var.get()
            symbol = self.trading_system.crypto_pairs[pair]['symbol']
            price = self.trading_system.get_crypto_price(symbol)

            if price:
                self.current_price_label.config(text=f"السعر: ${price:,.2f}")
                self.log_message(f"💰 {pair}: ${price:,.2f}")
            else:
                self.log_message(f"❌ فشل في الحصول على سعر {pair}")

        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث السعر: {str(e)}")

    def manual_analysis(self):
        """تحليل يدوي"""
        if not self.is_connected:
            messagebox.showwarning("تحذير", "يجب الاتصال أولاً!")
            return

        pair = self.pair_var.get()
        self.log_message(f"🔍 بدء التحليل اليدوي لـ {pair}...")

        # تشغيل التحليل في خيط منفصل
        threading.Thread(target=self.run_analysis, args=(pair, False), daemon=True).start()

    def advanced_analysis(self):
        """تحليل متقدم"""
        if not self.is_connected:
            messagebox.showwarning("تحذير", "يجب الاتصال أولاً!")
            return

        pair = self.pair_var.get()
        self.log_message(f"📊 بدء التحليل المتقدم لـ {pair}...")

        # تشغيل التحليل في خيط منفصل
        threading.Thread(target=self.run_analysis, args=(pair, True), daemon=True).start()

    def run_analysis(self, pair, detailed=False):
        """تشغيل التحليل"""
        try:
            analysis = self.trading_system.analyze_market(pair)

            if 'error' in analysis:
                self.log_message(f"❌ خطأ في التحليل: {analysis['error']}")
                return

            decision = analysis['decision']
            confidence = analysis['confidence']
            price = analysis['price']

            # تحديث واجهة التحليل
            self.root.after(0, lambda: self.update_analysis_display(analysis))

            self.log_message(f"📈 نتيجة التحليل لـ {pair}:")
            self.log_message(f"   🎯 القرار: {decision}")
            self.log_message(f"   📊 نسبة الثقة: {confidence:.1f}%")
            self.log_message(f"   💰 السعر: ${price:,.2f}")

            if detailed:
                self.log_message(f"   📈 RSI: {analysis.get('rsi', 0):.1f}")
                self.log_message(f"   📊 MACD: {analysis.get('macd', 0):.4f}")
                self.log_message(f"   📉 SMA20: ${analysis.get('sma_20', 0):,.2f}")
                self.log_message(f"   📈 SMA50: ${analysis.get('sma_50', 0):,.2f}")

            # فحص إمكانية الدخول
            if self.trading_system.should_enter_trade(analysis):
                self.log_message(f"🚀 فرصة دخول متاحة! الثقة: {confidence:.1f}% (المطلوب: {self.trading_system.confidence_threshold:.0f}%)")
            else:
                self.log_message(f"⏳ لا توجد فرصة دخول. الثقة: {confidence:.1f}% (المطلوب: {self.trading_system.confidence_threshold:.0f}%)")

        except Exception as e:
            self.log_message(f"❌ خطأ في التحليل: {str(e)}")

    def update_analysis_display(self, analysis):
        """تحديث عرض التحليل"""
        decision = analysis['decision']
        confidence = analysis['confidence']

        # تحديث القرار
        if decision == 'buy':
            self.decision_label.config(text="شراء 📈", foreground='#238636')
        elif decision == 'sell':
            self.decision_label.config(text="بيع 📉", foreground='#da3633')
        else:
            self.decision_label.config(text="انتظار ⏳", foreground='#bf8700')

        # تحديث نسبة الثقة
        self.analysis_confidence_label.config(text=f"{confidence:.1f}%")
        if confidence >= 70:
            self.analysis_confidence_label.config(foreground='#238636')
        elif confidence >= 50:
            self.analysis_confidence_label.config(foreground='#bf8700')
        else:
            self.analysis_confidence_label.config(foreground='#da3633')

        # تحديث المؤشرات
        self.rsi_label.config(text=f"{analysis.get('rsi', 0):.1f}")
        self.macd_label.config(text=f"{analysis.get('macd', 0):.4f}")

    def start_trading(self):
        """بدء التداول"""
        if not self.is_connected:
            messagebox.showwarning("تحذير", "يجب الاتصال أولاً!")
            return

        pair = self.pair_var.get()
        confidence_threshold = self.confidence_var.get()

        # تأكيد البدء
        mode_text = "تجريبي آمن" if self.demo_var.get() else "حقيقي"
        confirm_msg = f"""هل تريد بدء التداول الذكي؟

الإعدادات:
• الزوج: {pair}
• الوضع: {mode_text}
• نسبة الثقة المطلوبة: {confidence_threshold:.0f}%
• نسبة المخاطرة: {self.risk_var.get():.1f}%

⚠️ تأكد من الإعدادات قبل المتابعة!"""

        if not messagebox.askyesno("تأكيد بدء التداول", confirm_msg):
            return

        self.is_trading = True
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.system_status_label.config(text="🚀 يعمل", foreground='#238636')

        # تحديث إعدادات النظام
        self.trading_system.demo_mode = self.demo_var.get()
        self.trading_system.set_current_pair(pair)
        self.apply_settings()

        self.log_message(f"🚀 بدء التداول الذكي:")
        self.log_message(f"   💰 الزوج: {pair}")
        self.log_message(f"   🛡️ الوضع: {mode_text}")
        self.log_message(f"   🎯 نسبة الثقة: {confidence_threshold:.0f}%")
        self.log_message(f"   💰 نسبة المخاطرة: {self.risk_var.get():.1f}%")

        # بدء التداول في خيط منفصل
        threading.Thread(target=self.trading_loop, daemon=True).start()

    def stop_trading(self):
        """إيقاف التداول"""
        self.is_trading = False
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.system_status_label.config(text="⏹️ متوقف", foreground='#bf8700')
        self.log_message("⏹️ تم إيقاف التداول الذكي")

    def trading_loop(self):
        """حلقة التداول الذكية"""
        while self.is_trading and self.is_connected:
            try:
                pair = self.pair_var.get()
                self.log_message(f"🔍 تحليل {pair} بالذكاء الاصطناعي...")

                # تحليل السوق
                analysis = self.trading_system.analyze_market(pair)

                if 'error' not in analysis:
                    decision = analysis['decision']
                    confidence = analysis['confidence']
                    price = analysis['price']

                    # تحديث واجهة التحليل
                    self.root.after(0, lambda: self.update_analysis_display(analysis))

                    self.log_message(f"📊 نتيجة التحليل:")
                    self.log_message(f"   🎯 القرار: {decision}")
                    self.log_message(f"   📈 الثقة: {confidence:.1f}%")
                    self.log_message(f"   💰 السعر: ${price:,.2f}")

                    # محاولة تنفيذ التداول
                    if self.trading_system.execute_trade(analysis):
                        self.log_message("✅ تم تنفيذ الصفقة بنجاح!")
                        self.log_message("🧠 النظام يتعلم من هذه الصفقة...")
                    else:
                        threshold = self.trading_system.confidence_threshold
                        self.log_message(f"⏳ لا توجد فرصة دخول (الثقة: {confidence:.1f}% < المطلوب: {threshold:.0f}%)")

                # تحديث الصفقات المفتوحة
                self.trading_system.update_positions()

                # تحديث معلومات الحساب
                self.root.after(0, self.update_account_info)
                self.root.after(0, self.update_positions_display)

                # انتظار قبل التحليل التالي (30 ثانية)
                for i in range(30):
                    if not self.is_trading:
                        break
                    time.sleep(1)

            except Exception as e:
                self.log_message(f"❌ خطأ في التداول: {str(e)}")
                time.sleep(10)

    def update_account_info(self):
        """تحديث معلومات الحساب"""
        if not self.is_connected or not self.trading_system:
            return

        try:
            account_info = self.trading_system.get_account_info()

            if account_info:
                balance = account_info.get('balance', 0)
                equity = account_info.get('equity', 0)
                profit = account_info.get('profit', 0)
                open_positions = account_info.get('open_positions', 0)

                self.balance_label.config(text=f"${balance:,.2f}")

                # تلوين الربح/الخسارة
                if profit > 0:
                    self.profit_label.config(text=f"+${profit:,.2f}", foreground='#238636')
                elif profit < 0:
                    self.profit_label.config(text=f"${profit:,.2f}", foreground='#da3633')
                else:
                    self.profit_label.config(text="$0.00", foreground='#f0f6fc')

                self.positions_count_label.config(text=str(open_positions))

        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث معلومات الحساب: {str(e)}")

    def update_positions_display(self):
        """تحديث عرض الصفقات المفتوحة"""
        try:
            # مسح الجدول
            for item in self.positions_tree.get_children():
                self.positions_tree.delete(item)

            # إضافة الصفقات الجديدة
            positions = self.trading_system.get_open_positions()

            for pos in positions:
                profit = pos['profit']
                profit_color = 'green' if profit > 0 else 'red' if profit < 0 else 'black'

                self.positions_tree.insert('', 'end', values=(
                    pos['ticket'],
                    pos['symbol'],
                    pos['type'],
                    f"{pos['volume']:.2f}",
                    f"${pos['price_open']:,.2f}",
                    f"${pos['price_current']:,.2f}",
                    f"${profit:,.2f}"
                ), tags=(profit_color,))

            # تلوين الصفوف
            self.positions_tree.tag_configure('green', foreground='#238636')
            self.positions_tree.tag_configure('red', foreground='#da3633')

        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث الصفقات: {str(e)}")

    def update_data(self):
        """تحديث البيانات دورياً"""
        if self.is_connected:
            self.update_account_info()
            self.update_positions_display()

        # جدولة التحديث التالي (كل 5 ثوانٍ)
        self.root.after(5000, self.update_data)

    def run(self):
        """تشغيل التطبيق"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log_message("⏹️ تم إيقاف التطبيق")
        finally:
            if self.trading_system:
                self.trading_system.disconnect()

    def on_closing(self):
        """عند إغلاق التطبيق"""
        if self.is_trading:
            if messagebox.askokcancel("تأكيد الإغلاق", "النظام يعمل حالياً. هل تريد إيقافه والخروج؟"):
                self.stop_trading()
                time.sleep(1)
                self.disconnect_system()
                self.root.destroy()
        else:
            self.disconnect_system()
            self.root.destroy()

if __name__ == "__main__":
    try:
        app = AdvancedCryptoGUI()
        app.run()
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")
