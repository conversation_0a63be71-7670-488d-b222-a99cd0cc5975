﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="pictureBox1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAALGPC/xhBQAAAlJJREFUaEPt
        mYFNwzAQRTsCIzACIzACG8AGsAFsABvABrABbAAbMAJsAPekGh3uT85O7CaV+qQnRU3OuUucq9NujpRx
        Zr5vZfsguTC/zJ+tbPPZQXFvpgJy2bd6TsxXUxXg5RiOXSU8A34qRX6aq3turkyVbInELg7T49FUCdbI
        GItNtVOTtqoSmyJjMeZeyVtrKxnz3NwLt6ZKoqWcoxvM4WdTnbiHnKv5c0ObpF2qE/a0aYumPfZ4Hkrl
        3LNbdIvW2spJSxvmZsvW2kpyKn5uaH89ptK3+WAyPsnwnUEb5zN1/JBFLfraVMFzfTLHriRFfZgqdkhy
        3YGT9GqtFFHKm6nGGHJnaZOW3tz+/OA51hQBJFWaQzqO3P/gSnBr6dl5wFRri0iUPjPkSs7kLlFBtU4t
        AkovZogKqnFOEQk1bm6ICiq1RRGgxs4NUUEltipi0anVqgi4MdU5ckNU0Ji+CPr63AVe6So7RAUNmReR
        Pp9aTM0iNUQFKYeKSNYWw/H5GGOGqKDcFzMxdhVLi6ktAkNUkJelQVrflHwLy8WdY0oRGKKCvHcmsJRW
        +5XcNcXUIjBEBXnTO/SUlapnThEYooK8CbVvTN8c5haBISrIm1D7hmxdBIaoIG/6WbP0ja5HERiigrws
        IYD3bbXf26sIDFFBXl7+U/slUXUM9iwCQ1RQru9A3Bk/zdgm8USPIjBEBSkp5t+Lv2Dsv8S5hqigIVmp
        8s3t/9dg+9Ls/VtxiApaoyEqaI2GqKA1GqKC1miIClqjIaVLjyUlxyNH+rPZ/ALW5TiMJ/ZzZgAAAABJ
        RU5ErkJggg==
</value>
  </data>
  <data name="btnLogOut.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAALGPC/xhBQAAAWNJREFUaEPN
        msFNA0EUxbYkSqAz6AA6gw6gA+gA5t2iyBM8C/P5lnxZycO+JIdVwrHI0/Bt+NXE3MvDcInnIR3Wwceh
        5mNIh3Qw74yGDuikhuJOaijupIbiTmoo7qSG4k5qKO6khuJOaijupIbiTmoonplnn/vh58W13WoonpkR
        4W5YNUZD8cw8YGZEqBqjofiW1WM0FP9k5RgNxcaqMRqKrRVjNBSvuHuMhuJVd47RUHzGXWM0FJ91x5jj
        5erCzJ38xZjj9erCzJ1kSN4l+rtWDcVnzat/+dH67YioofiMO0ZEDcWr7hoRNRSvuHNE1FBs3T0iaig2
        VoyIGop/smpE1FB8y8oRUUPxzOoRUUPxzMsvHypGRA3FM/O7XsZUjYgaijupobiTGoo7qaG4kxqKO6mh
        uJMaijupobiTGoo7qaG4k5o8CNIBHXwfavJzGh3SwaV/cwoJsp4O+w9zL+OejuMbQNFq2eumuFkAAAAA
        SUVORK5CYII=
</value>
  </data>
  <data name="btnSellMedicine.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAALGPC/xhBQAAAhxJREFUaEPt
        me1NxDAMhm8ERmAENoANYAMYgQ1gA9gARmAD2AA2gA1gA/DzI5KxfNfETiiV8kqPTuqHWze24+R2U1NT
        izoSTh04vjl9O3wKN8Km9CJ4zsC1sBldCThT+BKKI+/CZkV+6FE5ETarN6E4wohtVvdCceSRA1vVhVAc
        KZAvz4o7gcpW0OX734TjsWAdybDqR/gQvJf6a+xHIFqaRG54htemufhwg2dobc6EJhGrnqFWyoP59c63
        EpKe5aPo5PXOt0DehvQkeAZb0MoWEFqokGgaPYO10CFoHWpQa7gVQsrGtf2CumOI0Fx6tTyDHoQNL87L
        MpJ8BCZWLRpSjnOer8v1LeGWmixrwyE67Nzn2fNIqfZBox2x+das2jyhlXgQ6J3OhX1hwHF6Kq7jeu7z
        7FmooCnZhVYLNtlbwsgSHfFf0gutFl4Frdp88yAy0sqUTa3aMPKwFTAkb6FVi34B73wtXZRZaJWQyDSh
        NtdSWnOh1XXfYM2FVteNwkyeZOmS6FrZpi/CsH01khbjTFAjIQK6j8TUfxdDfimUBjH7xxD3Ywd72E2t
        O2rlbRPxx1B09cZLc7+1yW7kMB2a4emjIqKx9OxBl0bR09KmXeTBnp0C1WuIMOw9sNDqyFL/NcyRpdk9
        kqSencIwR6gu+3Yfo2vqQ4utoRMio2KdwYloyeRlrTPYH9aaWJEP0Kvm41CxOTU11U273Q9gCTAYKakS
        twAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="btnMedValidityCheck.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAALGPC/xhBQAAAVNJREFUaEPt
        loFJBDEQAK8ES7AEO9BOtAPtQEuxEy3hO9AOtAPdAQMh7F/OZJMNzw4MPHdJ2OE4/rYgCIKL5NbZbm7E
        D/HHWWZglmZWiEgySxN3onagp8z0b8oQL/IZIiQ/xIt8BpOQt4MeRdurmc9gEnLUa7EGa7S9NaeGvIg1
        WKPtrTk15Evc+/PiHmu0vTWnhiCD3oslXGuNwOkhSYZOL21PQNItxNrlQ07i1Z/81tbg0iEpIvEkautw
        2ZAyAl5FbS0uGaJFPIja2uRyIS0RuFRIawQODflWrp2zJwKHhTAE8NlRC+qNwGEhOXsxFhE4/IkktBir
        CBwWgnsxlhE4NAS1mHfRMgKHh2AZU9IbgVNC8FyMRQROC8EyxioCp4bgo8j78Zxds3B6yCgjRDvM0wjR
        DvO0KQQ+Re1AD5mlGT41VohhBmbphkfqaRAEwUWxbb+c80BvXKb90gAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="btnModifyMedicine.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAALGPC/xhBQAAAYRJREFUaEPt
        mYFNAzEMRW+EjsAIHaEjMAIb0A1ghG4Am9AN6AbtBmUD6o84ybKS2EkTBwk/6alCXJP7vfwqd12CIAj+
        FQfyw1HMN4Qt+UV+O3kkm3n8fc2B/6cmHWFzkDcSA+x+/srzSspJR9gU5IlcB7iSD2SJPYlAPcWJ3xUE
        a58PAD/JDekJwvBzqAqCk8UV4AOsYql5clcQfPL8zVIsIS+ag6zl1tTK34umILzcmpby96A6SKrcmh7l
        rwpSKrfm6PJXBcH+BQdI5dbjQqaOG9mX5rJz8CY+CAb1JoJwIkhHIggngnQkgnAiSEciCCeCNICNZ+qB
        3Jnk54Bdeuq44oM77yvyTvL5rGKXXrwv8g6CkzmRfE6LuCksMqMjuH2ueQSL23OVGUEA+sLnzYmlaGJW
        EIBHTnxuKZagmZlBQK78arkls4Pkyq+WWyKD4Pv6pbPPZAlZflO5JTLIKLUrvZbfXG6JVxBo+TGpGc8g
        2HpUr30ruQd3oxz2Q2cQBMFfY1luqmFCmgNjDd8AAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnViewMedicine.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAALGPC/xhBQAAAbdJREFUaEPd
        mYFNAzEMRTsCo7ABjMAIbAAbwAYwAhvBBrABbAD+VS1FX4Y4Pic++qSnqsfdt63krlAOBt8TfBUvxKVY
        jWS4fBiria2+nV6XDsNNZIjmlw/DTWQIlg/DTWSoLB2Gm8iwZdkw3ESGzJJhuIkMLaYPw01k+BtTh+Em
        MvyLacNwExn2mDIMN5Ghh/RhuIkMvaQOw01kOAIPE4abqDaMFVZpGCus0jBWWKVhrLBKw1hhlYaxwioN
        Y4VVGsYKqzSMFVZpGCus0jBWWKVhrLBK38UbcRgrbKYf4r14KSrXIo7hZ3rekzhEW2S2aLbHo6jnP+OA
        l7bQTNsV6IGtpde5t1lbbJaelWBwDa7FPeOCi2aLfR9F75nb47sOXDjbyGoouiovx3cduHC2I/cGg6cZ
        MlzbiwtnuxV3Tlt0hltx57RFZ5ixtVwPDC6c7dnc7O7PAQNci4xdPH5hZFV0NdyfQ1x0lmfxK4p6J/Z4
        EPX8T9H9xXZbaIXY9xioXaErEcf0noBfp1f3t/R64V7EPYHtNPwvBw6qtmVoGA6qlnEPw0HVWgytzN5p
        h8Gfw/8aDCNDHA4/npeYa78VRvsAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnAddMedicine.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAALGPC/xhBQAAAdNJREFUaEPt
        mGFNRTEMRq8EJCABByABByABB+AAHIADcAIOwAE4gJ4fS8jyXd66boOMfclJbt7bui5r1967LS0tLdXo
        xLg2Ho1n4zOD3/iPMYz9Uzo2bo1XI3f8EMxhLjZ+TUfGvaEcrAFb2Byqc+PdUA5FwCa2h6jlKezBGt3E
        sZOoauEesFaXUBu5iQRrNtWIcNrjzmgikk8tMJLwBUCM9ridvOBDKF8eDGW4lBcDB4BnNaYUfKkS1VYZ
        9HBpJPGsxnio6gBIMmXMw5mRxLMa46Eq8Wt6p5zWG8Enl+hMlSEvrTcCrq75xlBGvPTYyJVRrCdDGfHS
        YyP4Viz1UlRDj43gW7GUAQW1gWsVJxXfixjPagxgw1NniqUmK0LVNpOnbhVLTVZMsxHi9cI43SEPLTUG
        sOHJy2JF+6IE8Z/EsxrjBd+KNc31O01BnKZFQW+GMuSh9Ubwya1p2vhpXqxQ9FWX2sDiEO3fQl9TKGIf
        hjI8EnwIdxFTfA5KioZYhFBIKbWq9h5cVbxUxOjIzbBWOC9+0ogwax5OeyL5etxm2GyW2KXi2FueDqfQ
        NZQOiWKHEzW9GXOYW12xe4nOlDabRFUvZ/zGf4xxd7FLS0v/Xtv2BR3xaiCX6FK9AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnDashbord.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAALGPC/xhBQAAAZ9JREFUaEPt
        mO1NAzEQRFMCJVACJdAJlEQJdAAdQAfQQSiBDmBHyo5Wpzl7k/h8GPlJ70du1uZ88QeXw2QymUz25MP8
        GVTcO1EFI0lUOJJEhSNJVJj11rw3n8yv07XeEhVmXfJofpuqdiuJCtd8N3Gz/llxYz6bsd2WEhUuxbTB
        FHL8eolegyEqjH6aeMoRz2q8mrGvpXdmDdSoti5RoYtvYjkI4HkNtC2tmW4DidMp4nmGuKa2kKgQYmGv
        4TVZttzJiAohnuQaXpMF50zsu6VEhRCH3RpekwVTNPbdUqJCWCJTE8FDiX27XRZ7CVV/iV0GUvojOFtU
        mz0kKoSlxZ5l660XEhXCN/Na0Ifqu6VEhe7agZhhy50qSlTo4p1Y/YtSA22Opuoz2mWxu+cOBrXZHzS6
        DgTixjLTDDWZb6KlRIVrYvE+mPFJ4rDDtR4LW0lUOJJEhSNJVNhCvB3W3hBbSFTYQkdlLSUqdDPbYw3v
        61KabL//ZiDX6KispUSFLXw5qbKWEhWOJFHhSBIVjiT5S6+t54p7n0wmk8keHA6/LmJMDqs09tcAAAAA
        SUVORK5CYII=
</value>
  </data>
  <metadata name="guna2Elipse1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="guna2Elipse2.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>140, 17</value>
  </metadata>
  <metadata name="guna2Elipse3.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>263, 17</value>
  </metadata>
  <data name="guna2Button1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAALGPC/xhBQAAAU5JREFUaEPt
        mcENwjAMRTsCo3UT2IhuAhvBBsF2XjigttAK1TX4STk4iev/SZGlpkv2Timll3GR4YHW7ZGyHnnI2R7n
        zxlJy5FkPQnlLuMk48DSJmg96mp9Zd3JSOK15pcTUy5o/SqjXJlaBsnKpifxitavMkphahnkrkv+MkhJ
        IwahK0hJIwahK0h5aiFszDfNuieEkcZ402Rxl0YaMvW+abKwayMNWZpumixEMTLdNJkPYURhSxrZBKSk
        EYPQFaSkEYPQFaSkEYPQFaSkEYPQFaSkEYPQFaSkEYPQFaSkEYPQFaSkkUaIb79sGTUS6ms8e0aNtPuR
        m4yjDI/7Ea2r9ZXZ+xH2jJ+azA912Z0BSZOwb/r1kzU9mfaabY3W/eimqm6f/x+FAB9/boTcXfwKSEkj
        jfAXpj9zhR2qac4iyWGa5lvkISGaZuJH1z0AAzX8OxUhtBgAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnExit.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAALGPC/xhBQAAAjpJREFUaEPt
        muFNAzEMhTsCo7ABbAAbwAawAWwAm8AGsAFsABvABuDvx0NWelITN7m4qE96QrrGdl7iOLlwmyP+MU6M
        F8Y744vxw/hTkOeQNrTFJg2ujE/GstO1xBYf08ColqP+arw3nhtPjSV4fmmkDW29Lb7wuRrojBdAh66N
        kTTBBttPoxdEjKF4MCrgu7FnQHzhU/6J1R2M3JuRAN/GW+Mo4FtiiNmtIJDrEsGILeV+bxBD6dZFjJ8J
        RHQbnQoQS6m2t5hZIoRSTAiPxpkiBGIrzehTE6ggGLKw11gTu0Af6EuzEO0TI6vTcLDrKqUOGpqN4bvr
        SHBcQATHjtmgSoXL7rMRIQiajfAeQmMM4cxyK9CHkBiO1xhlSCshJEbVir+Z0CxGLzoZq1WTGJXdDDv5
        EqrF0ABmRpWY3kIIJJ+jSIwt6Mde0MiNJDG2oB8zg1TSTCNiMbV05s+82HeKANnLb5UIkHlDrBYBsh5R
        mkQAGmEAqwwGIyRCyHSMD4sAerHCyWwgICRCUBk+6FddoLvXDLOyNzQrB30dBHRB92XMckFHX5ov6ICu
        TEmxmeWY2HpXCm/WVI2ZYojpy3AYOJolphSxd2zy04tZY80QQ+nURYTgZ4ZFd2McBXwTp7sIDxUAyOz0
        3DTxpVmAoQrVAgJqn5Eg/uEfGTlssPUC8L3qqYLN0guC+jzjzLi0lnjuP/Pwtvia+i7EQVOn5gixzXDa
        /gNp4j/PKGcL8hzShrZDFvIR87HZ/ALLSjmva4csFgAAAABJRU5ErkJggg==
</value>
  </data>
</root>