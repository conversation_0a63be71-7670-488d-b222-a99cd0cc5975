-- إنشاء جداول متجر الصيدلية الجديد
USE UnifiedPharmacy;

-- جدول الأدوية المنشورة في المتجر
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='published_medicines' AND xtype='U')
BEGIN
    CREATE TABLE published_medicines (
        id int IDENTITY(1,1) PRIMARY KEY,
        pharmacyId int NOT NULL,
        medicineId int NOT NULL,
        medicineName nvarchar(255) NOT NULL,
        quantity int NOT NULL,
        expiryDate datetime NOT NULL,
        pricePerUnit decimal(10,2) NOT NULL,
        description nvarchar(1000) NULL,
        publishDate datetime NOT NULL DEFAULT GETDATE(),
        isActive bit NOT NULL DEFAULT 1,
        createdAt datetime NOT NULL DEFAULT GETDATE(),
        updatedAt datetime NOT NULL DEFAULT GETDATE(),
        
        -- إضافة مفاتيح خارجية
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id),
        FOREIGN KEY (medicineId) REFERENCES medic(id)
    );
    
    PRINT 'تم إنشاء جدول published_medicines بنجاح';
END
ELSE
BEGIN
    PRINT 'جدول published_medicines موجود بالفعل';
END

-- جدول طلبات الأدوية
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='medicine_requests' AND xtype='U')
BEGIN
    CREATE TABLE medicine_requests (
        id int IDENTITY(1,1) PRIMARY KEY,
        publishedMedicineId int NOT NULL,
        requestingPharmacyId int NOT NULL,
        requestQuantity int NOT NULL,
        requestMessage nvarchar(1000) NULL,
        requestDate datetime NOT NULL DEFAULT GETDATE(),
        status nvarchar(50) NOT NULL DEFAULT 'Pending', -- Pending, Approved, Rejected, Completed
        responseMessage nvarchar(1000) NULL,
        responseDate datetime NULL,
        createdAt datetime NOT NULL DEFAULT GETDATE(),
        updatedAt datetime NOT NULL DEFAULT GETDATE(),
        
        -- إضافة مفاتيح خارجية
        FOREIGN KEY (publishedMedicineId) REFERENCES published_medicines(id),
        FOREIGN KEY (requestingPharmacyId) REFERENCES pharmacies(id)
    );
    
    PRINT 'تم إنشاء جدول medicine_requests بنجاح';
END
ELSE
BEGIN
    PRINT 'جدول medicine_requests موجود بالفعل';
END

-- جدول الرسائل بين الصيدليات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacy_messages' AND xtype='U')
BEGIN
    CREATE TABLE pharmacy_messages (
        id int IDENTITY(1,1) PRIMARY KEY,
        senderPharmacyId int NOT NULL,
        receiverPharmacyId int NOT NULL,
        subject nvarchar(255) NOT NULL,
        message nvarchar(2000) NOT NULL,
        isRead bit NOT NULL DEFAULT 0,
        sentDate datetime NOT NULL DEFAULT GETDATE(),
        readDate datetime NULL,
        relatedRequestId int NULL, -- ربط بطلب دواء معين
        
        -- إضافة مفاتيح خارجية
        FOREIGN KEY (senderPharmacyId) REFERENCES pharmacies(id),
        FOREIGN KEY (receiverPharmacyId) REFERENCES pharmacies(id),
        FOREIGN KEY (relatedRequestId) REFERENCES medicine_requests(id)
    );
    
    PRINT 'تم إنشاء جدول pharmacy_messages بنجاح';
END
ELSE
BEGIN
    PRINT 'جدول pharmacy_messages موجود بالفعل';
END

-- إضافة فهارس لتحسين الأداء
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_published_medicines_pharmacy_active')
BEGIN
    CREATE INDEX IX_published_medicines_pharmacy_active ON published_medicines (pharmacyId, isActive);
    PRINT 'تم إنشاء فهرس IX_published_medicines_pharmacy_active';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_published_medicines_expiry_active')
BEGIN
    CREATE INDEX IX_published_medicines_expiry_active ON published_medicines (expiryDate, isActive);
    PRINT 'تم إنشاء فهرس IX_published_medicines_expiry_active';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_medicine_requests_pharmacy_status')
BEGIN
    CREATE INDEX IX_medicine_requests_pharmacy_status ON medicine_requests (requestingPharmacyId, status);
    PRINT 'تم إنشاء فهرس IX_medicine_requests_pharmacy_status';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_pharmacy_messages_receiver_read')
BEGIN
    CREATE INDEX IX_pharmacy_messages_receiver_read ON pharmacy_messages (receiverPharmacyId, isRead);
    PRINT 'تم إنشاء فهرس IX_pharmacy_messages_receiver_read';
END

-- إضافة بيانات تجريبية للاختبار
PRINT 'إضافة بيانات تجريبية...';

-- التأكد من وجود صيدليات للاختبار
IF NOT EXISTS (SELECT * FROM pharmacies WHERE id = 1)
BEGIN
    INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, address, city, region, phone, email, isActive)
    VALUES ('MAIN001', 'الصيدلية الرئيسية', 'مدير النظام', 'الشارع الرئيسي', 'الرياض', 'الرياض', '0501234567', '<EMAIL>', 1);
END

IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacyCode = 'TEST001')
BEGIN
    INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, address, city, region, phone, email, isActive)
    VALUES ('TEST001', 'صيدلية الاختبار', 'مدير الاختبار', 'شارع الاختبار', 'جدة', 'مكة المكرمة', '0507654321', '<EMAIL>', 1);
END

-- إضافة أدوية تجريبية منشورة
IF NOT EXISTS (SELECT * FROM published_medicines WHERE id = 1)
BEGIN
    INSERT INTO published_medicines (pharmacyId, medicineId, medicineName, quantity, expiryDate, pricePerUnit, description)
    VALUES 
    (2, 1, 'باراسيتامول 500 مجم', 100, DATEADD(month, 6, GETDATE()), 5.50, 'مسكن للألم وخافض للحرارة'),
    (2, 2, 'أموكسيسيلين 250 مجم', 50, DATEADD(month, 8, GETDATE()), 15.75, 'مضاد حيوي واسع المجال'),
    (2, 3, 'فيتامين د 1000 وحدة', 200, DATEADD(month, 12, GETDATE()), 25.00, 'مكمل غذائي لتقوية العظام');
    
    PRINT 'تم إضافة أدوية تجريبية منشورة';
END

-- إضافة طلبات تجريبية
IF NOT EXISTS (SELECT * FROM medicine_requests WHERE id = 1)
BEGIN
    INSERT INTO medicine_requests (publishedMedicineId, requestingPharmacyId, requestQuantity, requestMessage, status)
    VALUES 
    (1, 1, 20, 'نحتاج هذا الدواء بشكل عاجل', 'Pending'),
    (2, 1, 10, 'طلب للمخزون الشهري', 'Approved');
    
    PRINT 'تم إضافة طلبات تجريبية';
END

-- إضافة رسائل تجريبية
IF NOT EXISTS (SELECT * FROM pharmacy_messages WHERE id = 1)
BEGIN
    INSERT INTO pharmacy_messages (senderPharmacyId, receiverPharmacyId, subject, message, relatedRequestId)
    VALUES 
    (1, 2, 'استفسار عن توفر الدواء', 'هل يمكن توفير كمية إضافية من الباراسيتامول؟', 1),
    (2, 1, 'رد على الاستفسار', 'نعم، يمكننا توفير الكمية المطلوبة. يرجى التواصل معنا.', 1);
    
    PRINT 'تم إضافة رسائل تجريبية';
END

PRINT 'تم إنشاء جميع جداول متجر الصيدلية بنجاح!';
PRINT 'يمكنك الآن استخدام الميزات التالية:';
PRINT '- نشر الأدوية في المتجر';
PRINT '- طلب الأدوية من الصيدليات الأخرى';
PRINT '- التواصل بين الصيدليات';
PRINT '- فلترة الأدوية حسب التاريخ والصيدلية';
