-- إعداد قاعدة بيانات UnifiedPharmacy
-- Setup UnifiedPharmacy Database

USE master;

-- إنشاء قاعدة البيانات إذا لم تكن موجودة
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'UnifiedPharmacy')
BEGIN
    CREATE DATABASE UnifiedPharmacy;
    PRINT '✅ تم إنشاء قاعدة البيانات UnifiedPharmacy';
END
ELSE
BEGIN
    PRINT '✅ قاعدة البيانات UnifiedPharmacy موجودة بالفعل';
END

USE UnifiedPharmacy;

PRINT '========================================';
PRINT '   إعداد قاعدة بيانات UnifiedPharmacy';
PRINT '   Setup UnifiedPharmacy Database';
PRINT '========================================';

-- 1. إنشاء جدول الصيدليات
PRINT '';
PRINT '1. إنشاء جدول الصيدليات...';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'pharmacies')
BEGIN
    CREATE TABLE pharmacies (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyCode NVARCHAR(50) UNIQUE NOT NULL,
        pharmacyName NVARCHAR(255) NOT NULL,
        ownerName NVARCHAR(255) NOT NULL,
        licenseNumber NVARCHAR(100) NOT NULL,
        address NVARCHAR(500) NOT NULL,
        city NVARCHAR(100) NOT NULL,
        region NVARCHAR(100) NOT NULL,
        phone NVARCHAR(20) NOT NULL,
        email NVARCHAR(255) NOT NULL,
        isActive BIT DEFAULT 1,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE()
    );
    PRINT '✅ تم إنشاء جدول pharmacies';
END
ELSE
BEGIN
    PRINT '✅ جدول pharmacies موجود بالفعل';
END

-- 2. إنشاء جدول المستخدمين
PRINT '';
PRINT '2. إنشاء جدول المستخدمين...';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'users')
BEGIN
    CREATE TABLE users (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyId INT NOT NULL,
        userRole NVARCHAR(50) NOT NULL, -- Administrator, Pharmacist, Employee
        name NVARCHAR(255) NOT NULL,
        dob DATE NOT NULL,
        mobile BIGINT NOT NULL,
        email NVARCHAR(255) NOT NULL,
        username NVARCHAR(100) UNIQUE NOT NULL,
        pass NVARCHAR(255) NOT NULL,
        isActive BIT DEFAULT 1,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
    );
    PRINT '✅ تم إنشاء جدول users';
END
ELSE
BEGIN
    PRINT '✅ جدول users موجود بالفعل';
END

-- 3. إنشاء جدول جلسات الموظفين
PRINT '';
PRINT '3. إنشاء جدول جلسات الموظفين...';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'employee_sessions')
BEGIN
    CREATE TABLE employee_sessions (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyId INT NOT NULL,
        userId INT NOT NULL,
        username NVARCHAR(100) NOT NULL,
        employeeName NVARCHAR(255) NOT NULL,
        loginTime DATETIME NOT NULL,
        logoutTime DATETIME NULL,
        sessionDate DATE NOT NULL,
        createdAt DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id),
        FOREIGN KEY (userId) REFERENCES users(id)
    );
    PRINT '✅ تم إنشاء جدول employee_sessions';
END
ELSE
BEGIN
    PRINT '✅ جدول employee_sessions موجود بالفعل';
END

-- 4. إدراج صيدلية افتراضية
PRINT '';
PRINT '4. إدراج صيدلية افتراضية...';

IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacyCode = 'MAIN001')
BEGIN
    INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, licenseNumber, address, city, region, phone, email, isActive)
    VALUES ('MAIN001', N'الصيدلية الرئيسية', N'المالك الرئيسي', 'LIC001', N'شارع الملك فهد، الرياض', N'الرياض', N'منطقة الرياض', '**********', '<EMAIL>', 1);
    PRINT '✅ تم إدراج الصيدلية الافتراضية';
END
ELSE
BEGIN
    PRINT '✅ الصيدلية الافتراضية موجودة بالفعل';
END

-- 5. إدراج مستخدمين افتراضيين
PRINT '';
PRINT '5. إدراج مستخدمين افتراضيين...';

DECLARE @pharmacyId INT;
SELECT @pharmacyId = id FROM pharmacies WHERE pharmacyCode = 'MAIN001';

-- مستخدم مدير افتراضي
IF NOT EXISTS (SELECT * FROM users WHERE username = 'admin')
BEGIN
    INSERT INTO users (pharmacyId, userRole, name, dob, mobile, email, username, pass, isActive)
    VALUES (@pharmacyId, 'Administrator', N'مدير النظام', '1990-01-01', 966501234567, '<EMAIL>', 'admin', 'admin123', 1);
    PRINT '✅ تم إدراج المستخدم admin';
END
ELSE
BEGIN
    PRINT '✅ المستخدم admin موجود بالفعل';
END

-- مستخدم صيدلي افتراضي
IF NOT EXISTS (SELECT * FROM users WHERE username = 'pharmacist')
BEGIN
    INSERT INTO users (pharmacyId, userRole, name, dob, mobile, email, username, pass, isActive)
    VALUES (@pharmacyId, 'Pharmacist', N'الصيدلي الرئيسي', '1985-01-01', 966501234568, '<EMAIL>', 'pharmacist', 'pharm123', 1);
    PRINT '✅ تم إدراج المستخدم pharmacist';
END
ELSE
BEGIN
    PRINT '✅ المستخدم pharmacist موجود بالفعل';
END

-- 6. عرض ملخص النظام
PRINT '';
PRINT '6. ملخص النظام:';

DECLARE @totalUsers INT, @totalPharmacies INT;
SELECT @totalUsers = COUNT(*) FROM users WHERE isActive = 1;
SELECT @totalPharmacies = COUNT(*) FROM pharmacies WHERE isActive = 1;

PRINT 'عدد المستخدمين النشطين: ' + CAST(@totalUsers AS VARCHAR(10));
PRINT 'عدد الصيدليات النشطة: ' + CAST(@totalPharmacies AS VARCHAR(10));

-- عرض جميع المستخدمين مع صيدلياتهم
PRINT '';
PRINT 'جميع المستخدمين النشطين:';
SELECT 
    u.id,
    u.username,
    u.name,
    u.userRole,
    p.pharmacyName,
    p.pharmacyCode
FROM users u
INNER JOIN pharmacies p ON u.pharmacyId = p.id
WHERE u.isActive = 1
ORDER BY u.id;

PRINT '';
PRINT '========================================';
PRINT '   ✅ تم إعداد قاعدة البيانات بنجاح!';
PRINT '========================================';
PRINT '';
PRINT 'بيانات تسجيل الدخول الافتراضية:';
PRINT '• المدير: admin / admin123';
PRINT '• الصيدلي: pharmacist / pharm123';
PRINT '• الصيدلية: الصيدلية الرئيسية (MAIN001)';
PRINT '';
