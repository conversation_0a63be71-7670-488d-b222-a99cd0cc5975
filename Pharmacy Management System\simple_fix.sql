USE PharmacyNetworkOnline
GO

CREATE PROCEDURE sp_RegisterPharmacy
    @pharmacyName NVARCHAR(250),
    @ownerName NVARCHAR(250),
    @licenseNumber VARCHAR(100),
    @address NVARCHAR(500),
    @city NVARCHAR(100),
    @region NVARCHAR(100),
    @phone VARCHAR(20),
    @email VARCHAR(250),
    @adminName NVARCHAR(250),
    @adminUsername VARCHAR(250),
    @adminPassword VARCHAR(500)
AS
BEGIN
    SET NOCOUNT ON
    
    DECLARE @pharmacyId INT
    DECLARE @pharmacyCode VARCHAR(20)
    DECLARE @nextNumber INT
    
    SELECT @nextNumber = ISNULL(MAX(CAST(SUBSTRING(pharmacyCode, 3, 3) AS INT)), 0) + 1 FROM pharmacies
    SET @pharmacyCode = 'PH' + RIGHT('000' + CAST(@nextNumber AS VARCHAR), 3)
    
    BEGIN TRY
        INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, licenseNumber, address, city, region, phone, email, isActive, registrationDate, lastOnline, subscriptionType)
        VALUES (@pharmacyCode, @pharmacyName, @ownerName, @licenseNumber, @address, @city, @region, @phone, @email, 1, GETDATE(), GETDATE(), 'Basic')
        
        SET @pharmacyId = SCOPE_IDENTITY()
        
        INSERT INTO network_users (pharmacyId, userRole, name, username, passwordHash, email, isActive, createdAt, updatedAt)
        VALUES (@pharmacyId, 'Admin', @adminName, @adminUsername, @adminPassword, @email, 1, GETDATE(), GETDATE())
        
        SELECT @pharmacyId as PharmacyId, @pharmacyCode as PharmacyCode, 'تم تسجيل الصيدلية بنجاح' as Message
        
    END TRY
    BEGIN CATCH
        SELECT 0 as PharmacyId, '' as PharmacyCode, 'خطأ في التسجيل' as Message
    END CATCH
END
GO

CREATE PROCEDURE sp_SearchMedicines
    @searchTerm NVARCHAR(250) = '',
    @pharmacyId INT = NULL,
    @category NVARCHAR(100) = '',
    @minPrice DECIMAL(10,2) = 0,
    @maxPrice DECIMAL(10,2) = 999999,
    @pageNumber INT = 1,
    @pageSize INT = 20
AS
BEGIN
    SET NOCOUNT ON
    
    DECLARE @offset INT = (@pageNumber - 1) * @pageSize
    
    SELECT m.id, m.pharmacyId, p.pharmacyName, p.pharmacyCode, p.city, p.region, p.phone,
           m.medicineName, m.genericName, m.brandName, m.manufacturer, m.category,
           m.dosageForm, m.strength, m.availableQuantity, m.unitPrice, m.wholesalePrice,
           m.expiryDate, DATEDIFF(day, GETDATE(), m.expiryDate) as daysToExpiry
    FROM networkmedicines m
    INNER JOIN pharmacies p ON m.pharmacyId = p.id
    WHERE m.isAvailableForSale = 1 
        AND m.availableQuantity > 0 
        AND m.expiryDate > GETDATE()
        AND p.isActive = 1
        AND (@pharmacyId IS NULL OR m.pharmacyId != @pharmacyId)
        AND (@searchTerm = '' OR m.medicineName LIKE '%' + @searchTerm + '%')
        AND (@category = '' OR m.category = @category)
        AND m.unitPrice BETWEEN @minPrice AND @maxPrice
    ORDER BY m.medicineName
    OFFSET @offset ROWS
    FETCH NEXT @pageSize ROWS ONLY
END
GO

CREATE PROCEDURE sp_GetActivePharmacies
AS
BEGIN
    SET NOCOUNT ON
    
    SELECT p.id, p.pharmacyCode, p.pharmacyName, p.ownerName, p.city, p.region,
           p.phone, p.email, p.lastOnline, p.subscriptionType,
           COUNT(DISTINCT u.id) as totalUsers, 
           COUNT(DISTINCT m.id) as totalMedicines,
           0 as totalOrdersSent, 
           0 as totalOrdersReceived, 
           0 as averageRating
    FROM pharmacies p
    LEFT JOIN network_users u ON p.id = u.pharmacyId AND u.isActive = 1
    LEFT JOIN networkmedicines m ON p.id = m.pharmacyId AND m.isAvailableForSale = 1
    WHERE p.isActive = 1
    GROUP BY p.id, p.pharmacyCode, p.pharmacyName, p.ownerName, p.city, p.region, p.phone, p.email, p.lastOnline, p.subscriptionType
    ORDER BY p.pharmacyName
END
GO

PRINT 'تم إنشاء جميع الإجراءات المخزنة بنجاح'
