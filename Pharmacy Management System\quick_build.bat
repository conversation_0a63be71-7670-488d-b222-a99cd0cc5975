@echo off
echo تنظيف وإعادة بناء المشروع...

REM تنظيف المجلدات القديمة
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"

echo تم تنظيف المجلدات القديمة.

REM محاولة البناء باستخدام MSBuild
echo البحث عن MSBuild...

if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    echo تم العثور على MSBuild 2022
    "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" "Pharmacy Management System.csproj" /p:Configuration=Debug /p:Platform="Any CPU" /t:Rebuild
    goto :end
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    echo تم العثور على MSBuild 2019
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" "Pharmacy Management System.csproj" /p:Configuration=Debug /p:Platform="Any CPU" /t:Rebuild
    goto :end
)

echo لم يتم العثور على MSBuild. يرجى إعادة بناء المشروع من Visual Studio.

:end
echo انتهى.
pause
