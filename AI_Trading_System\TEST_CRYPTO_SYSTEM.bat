@echo off
chcp 65001 > nul
title 🧪 اختبار نظام تداول العملات الرقمية - Test Crypto Trading System

echo.
echo ================================================================
echo 🧪 اختبار نظام تداول العملات الرقمية
echo TEST CRYPTOCURRENCY TRADING SYSTEM
echo ================================================================
echo.

echo 💡 هذا الاختبار سيتحقق من:
echo    📊 الحصول على أسعار العملات الرقمية
echo    📈 تحليل البيانات التاريخية والمؤشرات الفنية
echo    🎯 تحليل معنويات السوق واتخاذ القرارات
echo    💼 تنفيذ الصفقات التجريبية
echo    🧠 نظام التعلم الآلي والتنبؤ
echo    🔄 التحليل الكامل المتكامل
echo.

echo 🔧 متطلبات الاختبار:
echo    ✅ Python 3.8+ مثبت
echo    ✅ مكتبات Python مثبتة
echo    ✅ اتصال بالإنترنت
echo.

echo 🚨 ملاحظة مهمة:
echo    🛡️ هذا اختبار آمن - لا يتم تداول حقيقي
echo    📊 يستخدم بيانات حقيقية للاختبار
echo    💰 جميع الصفقات تجريبية
echo.

pause

echo 🔄 تحقق من المتطلبات...

python -c "import pandas, numpy, requests, sklearn, ta" 2>nul
if errorlevel 1 (
    echo ❌ مكتبات مفقودة! محاولة التثبيت...
    pip install pandas numpy requests scikit-learn ta
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        echo 💡 جرب يدوياً: pip install pandas numpy requests scikit-learn ta
        pause
        exit /b 1
    )
)

echo ✅ المتطلبات متوفرة

echo.
echo 🧪 بدء اختبار النظام...
echo.

python test_crypto_system.py

echo.
echo ================================================================
echo ✅ انتهى الاختبار
echo ================================================================
echo.

echo 💡 إذا نجحت جميع الاختبارات:
echo    🚀 النظام جاهز للاستخدام
echo    🎯 يمكنك تشغيل: RUN_CRYPTO_TRADING.bat
echo    📊 ابدأ بوضع المحاكاة للتعلم
echo.

echo ⚠️ إذا فشلت بعض الاختبارات:
echo    🌐 تحقق من الاتصال بالإنترنت
echo    📦 تأكد من تثبيت جميع المكتبات
echo    🔧 راجع رسائل الخطأ أعلاه
echo.

pause
