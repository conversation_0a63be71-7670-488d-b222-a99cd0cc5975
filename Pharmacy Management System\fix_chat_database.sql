-- إصلاح مشكلة المحادثات في قاعدة البيانات
USE UnifiedPharmacy;

PRINT '=== إصلاح جدول المحادثات ===';

-- 1. حذف الجدول القديم وإنشاء جدول جديد بالهيكل الصحيح
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacy_messages')
BEGIN
    PRINT 'حذف الجدول القديم...';
    DROP TABLE pharmacy_messages;
END

-- 2. إنشاء جدول المحادثات بالهيكل الصحيح
PRINT 'إنشاء جدول المحادثات الجديد...';
CREATE TABLE pharmacy_messages (
    id INT IDENTITY(1,1) PRIMARY KEY,
    sender_pharmacy_id INT NOT NULL,
    receiver_pharmacy_id INT NOT NULL,
    subject NVARCHAR(255) DEFAULT N'رسالة عامة',
    message_content NVARCHAR(MAX) NOT NULL,
    sent_date DATETIME DEFAULT GETDATE(),
    is_read BIT DEFAULT 0,
    read_date DATETIME NULL,
    related_request_id INT NULL,
    FOREIGN KEY (sender_pharmacy_id) REFERENCES pharmacies(id),
    FOREIGN KEY (receiver_pharmacy_id) REFERENCES pharmacies(id),
    FOREIGN KEY (related_request_id) REFERENCES purchase_requests(id)
);

PRINT '✅ تم إنشاء جدول pharmacy_messages بالهيكل الصحيح';

-- 3. إضافة بيانات تجريبية للمحادثات
PRINT 'إضافة بيانات تجريبية...';

-- التحقق من وجود صيدليات
DECLARE @pharmacy1 INT, @pharmacy2 INT;
SELECT TOP 1 @pharmacy1 = id FROM pharmacies ORDER BY id;
SELECT TOP 1 @pharmacy2 = id FROM pharmacies WHERE id != @pharmacy1 ORDER BY id;

IF @pharmacy1 IS NOT NULL AND @pharmacy2 IS NOT NULL
BEGIN
    -- إضافة رسائل تجريبية
    INSERT INTO pharmacy_messages (sender_pharmacy_id, receiver_pharmacy_id, subject, message_content)
    VALUES 
    (@pharmacy1, @pharmacy2, N'استفسار عن الأدوية', N'مرحباً، هل لديكم دواء الباراسيتامول متوفر؟'),
    (@pharmacy2, @pharmacy1, N'رد: استفسار عن الأدوية', N'نعم، لدينا كمية جيدة من الباراسيتامول'),
    (@pharmacy1, @pharmacy2, N'طلب شراء', N'ممتاز، أريد شراء 50 علبة منه');
    
    PRINT '✅ تم إضافة رسائل تجريبية';
END
ELSE
BEGIN
    PRINT '⚠️ لا توجد صيدليات كافية لإضافة رسائل تجريبية';
END

-- 4. عرض ملخص البيانات
PRINT '';
PRINT '=== ملخص البيانات ===';
SELECT COUNT(*) as 'عدد الرسائل' FROM pharmacy_messages;

-- 5. اختبار الاستعلام المستخدم في الكود
PRINT '';
PRINT '=== اختبار الاستعلام ===';
SELECT
    pm.id,
    pm.sender_pharmacy_id,
    pm.receiver_pharmacy_id,
    ISNULL(pm.subject, 'رسالة عامة') as subject,
    pm.message_content,
    pm.sent_date,
    pm.is_read,
    p1.pharmacyName as sender_pharmacy_name,
    p2.pharmacyName as receiver_pharmacy_name
FROM pharmacy_messages pm
LEFT JOIN pharmacies p1 ON pm.sender_pharmacy_id = p1.id
LEFT JOIN pharmacies p2 ON pm.receiver_pharmacy_id = p2.id
ORDER BY pm.sent_date DESC;

PRINT '';
PRINT '=== انتهى الإصلاح ===';
PRINT 'يمكنك الآن استخدام المحادثات بدون أخطاء!';
