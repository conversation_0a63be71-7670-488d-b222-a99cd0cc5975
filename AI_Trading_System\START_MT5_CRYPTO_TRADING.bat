@echo off
chcp 65001 > nul
title نظام تداول العملات الرقمية على MetaTrader 5

echo.
echo ========================================
echo 🚀 نظام تداول العملات الرقمية على MT5
echo ========================================
echo.
echo ✨ المميزات:
echo • الاتصال المباشر بـ MetaTrader 5
echo • عرض معلومات الحساب الحقيقية
echo • الأسعار الحقيقية للعملات الرقمية
echo • إعدادات نسبة الثقة قابلة للتخصيص
echo • تحليل فني متقدم مع الذكاء الاصطناعي
echo • تداول حقيقي على MT5
echo.
echo ⚠️ تأكد من تشغيل MetaTrader 5 وتسجيل الدخول!
echo.

echo 🔍 فحص المتطلبات...

python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت!
    echo يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر

echo.
echo 📦 فحص المكتبات المطلوبة...

python -c "import MetaTrader5, requests, pandas, numpy, sklearn" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ بعض المكتبات مفقودة، جاري التثبيت...
    pip install MetaTrader5 requests pandas numpy scikit-learn
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات!
        pause
        exit /b 1
    )
)

echo ✅ جميع المكتبات متوفرة

echo.
echo 🔍 فحص اتصال MetaTrader 5...

python -c "import MetaTrader5 as mt5; print('✅ MT5 متصل' if mt5.initialize() else '❌ MT5 غير متصل'); mt5.shutdown()" 2>nul
if errorlevel 1 (
    echo ❌ لا يمكن الاتصال بـ MetaTrader 5!
    echo تحقق من:
    echo • تشغيل MetaTrader 5
    echo • تسجيل الدخول في MT5
    echo • تفعيل التداول الآلي
    echo.
    pause
    exit /b 1
)

echo.
echo 🚀 بدء تشغيل النظام...
echo.

python basic_mt5_gui.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل النظام!
    echo تحقق من:
    echo • تشغيل MetaTrader 5
    echo • تسجيل الدخول في MT5
    echo • اتصال الإنترنت
    echo • وجود جميع الملفات المطلوبة
    echo.
    pause
)

echo.
echo 👋 شكراً لاستخدام نظام تداول العملات الرقمية على MT5!
pause
