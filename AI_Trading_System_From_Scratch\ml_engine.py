import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
import joblib
import os

class MLEngine:
    def __init__(self, config):
        self.config = config
        self.model_path = 'model.pkl'
        self.model = None
        self.load_model()

    def load_model(self):
        if os.path.exists(self.model_path):
            self.model = joblib.load(self.model_path)
        else:
            self.model = RandomForestClassifier(n_estimators=100, random_state=42)

    def save_model(self):
        joblib.dump(self.model, self.model_path)

    def prepare_features(self, df):
        # Features: SMA_20, SMA_50, RSI, etc.
        features = ['SMA_20', 'SMA_50', 'RSI']
        df = df.dropna()
        X = df[features]
        # For simplicity, create labels based on future price movement
        df['future_return'] = df['close'].shift(-1) - df['close']
        df['label'] = (df['future_return'] > 0).astype(int)  # 1 for up, 0 for down
        y = df['label'].dropna()
        X = X.iloc[:-1]  # Align
        return X, y

    def train_model(self, df):
        X, y = self.prepare_features(df)
        if len(X) == 0:
            return
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        self.model.fit(X_train, y_train)
        predictions = self.model.predict(X_test)
        accuracy = accuracy_score(y_test, predictions)
        print(f"Model accuracy: {accuracy}")
        self.save_model()

    def predict_signal(self, df):
        if self.model is None:
            return 'HOLD'
        latest = df.iloc[-1:]
        features = ['SMA_20', 'SMA_50', 'RSI']
        if any(pd.isna(latest[features].values)):
            return 'HOLD'
        pred = self.model.predict(latest[features])
        if pred[0] == 1:
            return 'BUY'
        else:
            return 'SELL'

    def learn_from_trade(self, trade_result):
        # For self-learning, retrain with new data if available
        # For now, placeholder
        pass
