@echo off
echo ========================================
echo    إصلاح أخطاء البناء - متجر الصيدلية
echo ========================================
echo.

echo المشكلة: Visual Studio لا يتعرف على الملفات الجديدة
echo الحل: إعادة تحميل المشروع وإعادة البناء
echo.

echo الخطوة 1: تنظيف المشروع...
if exist "bin" rmdir /s /q "bin" 2>nul
if exist "obj" rmdir /s /q "obj" 2>nul
echo تم تنظيف مجلدات البناء.

echo.
echo الخطوة 2: التحقق من وجود الملفات الجديدة...

set FILES_OK=1

if not exist "PharmacistUC\UC_P_PharmacyStore.cs" (
    echo ❌ ملف UC_P_PharmacyStore.cs مفقود
    set FILES_OK=0
)

if not exist "PharmacistUC\UC_P_PharmacyStore.Designer.cs" (
    echo ❌ ملف UC_P_PharmacyStore.Designer.cs مفقود
    set FILES_OK=0
)

if not exist "PharmacistUC\UC_P_PharmacyStore.resx" (
    echo ❌ ملف UC_P_PharmacyStore.resx مفقود
    set FILES_OK=0
)

if not exist "PublishMedicineForm.cs" (
    echo ❌ ملف PublishMedicineForm.cs مفقود
    set FILES_OK=0
)

if not exist "RequestMedicineForm.cs" (
    echo ❌ ملف RequestMedicineForm.cs مفقود
    set FILES_OK=0
)

if %FILES_OK%==1 (
    echo ✅ جميع الملفات موجودة
) else (
    echo ❌ بعض الملفات مفقودة - يرجى إعادة إنشائها
    pause
    exit /b 1
)

echo.
echo الخطوة 3: محاولة البناء...

REM البحث عن MSBuild
set MSBUILD_FOUND=0

if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    set MSBUILD_FOUND=1
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
    set MSBUILD_FOUND=1
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
    set MSBUILD_FOUND=1
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
    set MSBUILD_FOUND=1
)

if %MSBUILD_FOUND%==0 (
    echo ❌ لم يتم العثور على MSBuild
    echo.
    echo الحلول البديلة:
    echo 1. فتح المشروع في Visual Studio
    echo 2. إغلاق Visual Studio إذا كان مفتوحاً
    echo 3. إعادة فتح المشروع
    echo 4. Build -^> Rebuild Solution
    echo.
    goto :manual_instructions
)

echo تم العثور على MSBuild: %MSBUILD_PATH%
echo جاري البناء...

%MSBUILD_PATH% "Pharmacy Management System.csproj" /p:Configuration=Debug /t:Rebuild /verbosity:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo ✅ تم إصلاح المشكلة بنجاح!
    echo ========================================
    echo.
    echo الآن يمكنك:
    echo 1. فتح المشروع في Visual Studio
    echo 2. تشغيل البرنامج (F5)
    echo 3. تسجيل الدخول كموظف
    echo 4. الضغط على زر "متجر الأدوية"
    echo.
    echo صفحة متجر الصيدلية جاهزة للاستخدام! 🎉
    echo.
) else (
    echo.
    echo ========================================
    echo ❌ فشل في البناء التلقائي
    echo ========================================
    echo.
    goto :manual_instructions
)

goto :end

:manual_instructions
echo الحل اليدوي:
echo.
echo 1. إغلاق Visual Studio تماماً إذا كان مفتوحاً
echo.
echo 2. فتح Visual Studio مرة أخرى
echo.
echo 3. فتح المشروع: File -^> Open -^> Project/Solution
echo    اختيار: Pharmacy Management System.sln
echo.
echo 4. في Solution Explorer، تأكد من ظهور الملفات:
echo    📁 PharmacistUC/
echo      📄 UC_P_PharmacyStore.cs
echo      📄 UC_P_PharmacyStore.Designer.cs
echo      📄 UC_P_PharmacyStore.resx
echo    📄 PublishMedicineForm.cs
echo    📄 RequestMedicineForm.cs
echo.
echo 5. إذا لم تظهر الملفات:
echo    - Right-click على المشروع
echo    - Add -^> Existing Item
echo    - إضافة الملفات المفقودة
echo.
echo 6. Build -^> Rebuild Solution
echo.
echo 7. إصلاح أي أخطاء تظهر
echo.
echo 8. تشغيل البرنامج والاستمتاع بمتجر الصيدلية!
echo.

:end
pause
