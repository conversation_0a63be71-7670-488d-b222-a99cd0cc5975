@echo off
echo ========================================
echo   إعادة بناء مشروع نظام إدارة الصيدلية
echo        مع التحديثات الجديدة للواجهة
echo ========================================
echo.

echo تنظيف المشروع...
if exist "bin\Debug" rmdir /s /q "bin\Debug"
if exist "bin\Release" rmdir /s /q "bin\Release"
if exist "obj\Debug" rmdir /s /q "obj\Debug"
if exist "obj\Release" rmdir /s /q "obj\Release"

echo تم تنظيف المجلدات القديمة.
echo.

echo البحث عن MSBuild...
set MSBUILD_PATH=""

REM البحث عن Visual Studio 2022
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

REM البحث عن Visual Studio 2019
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

REM البحث عن .NET Framework MSBuild
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2017\BuildTools\MSBuild\15.0\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2017\BuildTools\MSBuild\15.0\Bin\MSBuild.exe"
    goto :build
)

echo لم يتم العثور على MSBuild. يرجى التأكد من تثبيت Visual Studio.
echo يمكنك إعادة بناء المشروع يدوياً من Visual Studio:
echo 1. افتح Visual Studio
echo 2. افتح المشروع
echo 3. اضغط Build ^> Rebuild Solution
pause
exit /b 1

:build
echo تم العثور على MSBuild: %MSBUILD_PATH%
echo.

echo إعادة بناء المشروع...
%MSBUILD_PATH% "Pharmacy Management System.csproj" /p:Configuration=Debug /p:Platform="Any CPU" /t:Rebuild

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo   تم إعادة بناء المشروع بنجاح!
    echo ========================================
    echo.
    echo الميزات الجديدة المضافة:
    echo - قائمة اختيار الجرعات في صفحة البيع
    echo - تصميم محسن بألوان احترافية
    echo - عرض الكمية المتبقية لكل جرعة
    echo - أزرار وواجهة عربية محسنة
    echo.
    echo يمكنك الآن تشغيل البرنامج من:
    echo bin\Debug\Pharmacy Management System.exe
    echo.
) else (
    echo.
    echo ========================================
    echo   فشل في إعادة بناء المشروع!
    echo ========================================
    echo.
    echo يرجى إعادة بناء المشروع يدوياً من Visual Studio:
    echo 1. افتح Visual Studio
    echo 2. افتح ملف "Pharmacy Management System.sln"
    echo 3. اضغط Build ^> Rebuild Solution
    echo.
)

pause
