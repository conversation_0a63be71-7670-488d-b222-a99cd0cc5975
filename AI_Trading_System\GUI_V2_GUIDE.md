# 🎨 دليل الواجهة الرسومية المتطورة - النظام الذكي V2

## 🌟 **نظرة عامة**

تم تطوير واجهة مستخدم رسومية متطورة وشاملة للنظام الذكي للتداول. الواجهة تتميز بالثيم الداكن الحديث وتوفر جميع الأدوات اللازمة لمراقبة والتحكم في التداول الآلي.

---

## 🚀 **كيفية التشغيل**

### **الطريقة السريعة:**
```bash
run_intelligent_gui_v2.bat
```

### **الطريقة المباشرة:**
```bash
python intelligent_gui_v2.py
```

---

## 🖥️ **مكونات الواجهة**

### **1. شريط الحالة العلوي**
```
🚀 نظام التداول الذكي المتطور    [❌ غير متصل] [⏸️ متوقف] [12:34:56]
```
- **العنوان**: اسم النظام
- **حالة الاتصال**: متصل/غير متصل بـ MT5
- **حالة التداول**: نشط/متوقف
- **آخر تحديث**: الوقت الحالي

### **2. القسم الأيسر - الرسم البياني المباشر**
- 📈 **رسم بياني مباشر** للأسعار
- 🕐 **أزرار الإطارات الزمنية**: M15, H1, H4, D1
- 📊 **تحديث تلقائي** كل 5 ثوان
- 🎨 **ثيم داكن** مريح للعين

### **3. القسم الأيمن - التحكم والمعلومات**

#### **أ. معلومات الحساب 💰**
```
الرصيد:        $1,250.00
الأسهم:        $1,275.50
الربح اليوم:   +$25.50
الرمز:         EURUSD
```

#### **ب. أزرار التحكم 🎮**
- **🔌 اتصال/قطع الاتصال**: الاتصال بـ MetaTrader 5
- **▶️ بدء/إيقاف التداول**: تشغيل التداول الآلي
- **🔄 دورة واحدة**: تنفيذ دورة تداول واحدة
- **🧪 اختبار تاريخي**: تشغيل اختبار على البيانات التاريخية
- **⚙️ الإعدادات**: فتح نافذة الإعدادات

#### **ج. تقرير المخاطر 📊**
```
يومية:    ████░░ 4.2% / 5.0%
أسبوعية: ██░░░░ 12.8% / 15.0%
شهرية:   █░░░░░ 8.5% / 30.0%
```
- **أشرطة تقدم مرئية** للمخاطر
- **ألوان تحذيرية** عند الاقتراب من الحدود
- **تحديث مباشر** للمخاطر المتراكمة

#### **د. الإعدادات السريعة ⚡**
- **شريط الثقة الدنيا**: 50% - 90%
- **شريط مخاطر الصفقة**: 1% - 5%
- **تحديث فوري** للقيم

### **4. القسم السفلي - التبويبات**

#### **تبويب الصفقات المفتوحة 📋**
```
┌─────────┬──────┬──────┬──────────┬─────────────┬─────────────┬─────────┐
│ الرمز   │ النوع │ الحجم │ سعر الفتح │ السعر الحالي │ الربح/الخسارة │ الوقت   │
├─────────┼──────┼──────┼──────────┼─────────────┼─────────────┼─────────┤
│ EURUSD  │ شراء │ 0.02 │ 1.08456  │ 1.08556     │ +$12.50     │ 10:30   │
│ GBPUSD  │ بيع  │ 0.01 │ 1.26789  │ 1.26689     │ +$8.20      │ 11:15   │
└─────────┴──────┴──────┴──────────┴─────────────┴─────────────┴─────────┘
```
- **جدول مفصل** للصفقات المفتوحة
- **ألوان مميزة**: أخضر للربح، أحمر للخسارة
- **أزرار التحكم**: إغلاق الكل، تحديث

#### **تبويب تاريخ الصفقات 📈**
```
┌─────────┬─────────┬──────┬──────┬──────────┬─────────────┬─────────────┬──────┐
│ الوقت   │ الرمز   │ النوع │ الحجم │ سعر الفتح │ سعر الإغلاق │ الربح/الخسارة │ المدة │
├─────────┼─────────┼──────┼──────┼──────────┼─────────────┼─────────────┼──────┤
│ 09:30   │ EURUSD  │ شراء │ 0.01 │ 1.08400  │ 1.08500     │ +$10.00     │ 45م  │
│ 10:15   │ GBPUSD  │ بيع  │ 0.01 │ 1.26800  │ 1.26700     │ +$8.50      │ 30م  │
└─────────┴─────────┴──────┴──────┴──────────┴─────────────┴─────────────┴──────┘
```
- **سجل كامل** للصفقات المنجزة
- **إمكانية التصدير** إلى CSV
- **خيار المسح** للبيانات القديمة

#### **تبويب السجلات 📝**
```
[12:34:56] ✅ تم تهيئة النظام التجاري بنجاح
[12:35:01] 🔄 محاولة الاتصال بـ MetaTrader 5...
[12:35:03] ✅ تم الاتصال بنجاح
[12:35:10] 🚀 بدء التداول الذكي...
[12:35:15] 🧠 بدء التحليل الذكي للسوق...
[12:35:18] 📊 حالة السوق: اتجاه صاعد قوي
[12:35:20] 🎉 تم تنفيذ صفقة شراء بنجاح!
```
- **سجل مفصل** لجميع الأحداث
- **طوابع زمنية** دقيقة
- **رموز تعبيرية** للتمييز السريع
- **إمكانية الحفظ** والمسح

#### **تبويب الإحصائيات 📊**
```
إجمالي الصفقات: 45        الصفقات الرابحة: 32
الصفقات الخاسرة: 13        معدل الفوز: 71.1%
إجمالي الربح: $245.80     متوسط الربح: $12.30
متوسط الخسارة: -$8.50     أقصى انخفاض: 4.2%
عامل الربح: 2.15          نسبة شارب: 1.85
```
- **إحصائيات شاملة** للأداء
- **رسوم بيانية** للأرباح اليومية
- **مخططات دائرية** لتوزيع الصفقات
- **تحديث تلقائي** للبيانات

---

## ⚙️ **نافذة الإعدادات**

### **إعدادات التداول:**
- **الرمز**: اختيار العملة (EURUSD, GBPUSD, USDJPY, AUDUSD)
- **حجم الصفقة**: من 0.01 إلى 1.0 لوت
- **مستوى الثقة**: الحد الأدنى للدخول في الصفقات
- **مخاطر الصفقة**: النسبة المئوية للمخاطر

### **إعدادات المخاطر:**
- **المخاطر اليومية**: الحد الأقصى يومياً
- **المخاطر الأسبوعية**: الحد الأقصى أسبوعياً
- **المخاطر الشهرية**: الحد الأقصى شهرياً

### **إعدادات الاتصال:**
- **خادم MT5**: عنوان الخادم
- **رقم الحساب**: رقم حساب التداول
- **كلمة المرور**: كلمة مرور الحساب

---

## 🎨 **الثيم الداكن المتطور**

### **الألوان المستخدمة:**
- **الخلفية الرئيسية**: #1e1e1e (رمادي داكن)
- **الخلفية الثانوية**: #2d2d2d (رمادي متوسط)
- **النص الرئيسي**: #ffffff (أبيض)
- **اللون الأخضر**: #00ff88 (للأرباح)
- **اللون الأحمر**: #ff4444 (للخسائر)
- **اللون الأزرق**: #4488ff (للمعلومات)
- **اللون الأصفر**: #ffaa00 (للتحذيرات)

### **مميزات التصميم:**
- ✅ **مريح للعين** في الإضاءة المنخفضة
- ✅ **تباين عالي** للنصوص
- ✅ **ألوان مميزة** للحالات المختلفة
- ✅ **تصميم حديث** وأنيق

---

## 🔧 **الوظائف المتقدمة**

### **1. التحديث التلقائي:**
- **كل 5 ثوان**: تحديث البيانات الأساسية
- **كل دقيقة**: تحديث الرسم البياني
- **كل 5 دقائق**: تحديث الإحصائيات

### **2. التداول الآمن:**
- **تأكيد الإجراءات**: قبل الإغلاق أو المسح
- **حماية من الأخطاء**: معالجة شاملة للاستثناءات
- **سجلات مفصلة**: لتتبع جميع الأحداث

### **3. إدارة البيانات:**
- **تصدير CSV**: للصفقات والإحصائيات
- **حفظ السجلات**: بتنسيق نصي
- **نسخ احتياطية**: تلقائية للبيانات المهمة

---

## 📱 **التوافق والمتطلبات**

### **المتطلبات الأساسية:**
- **Python 3.7+**
- **tkinter** (مدمج مع Python)
- **MetaTrader5** library
- **pandas & numpy**
- **matplotlib** (للرسوم البيانية)
- **scikit-learn** (للذكاء الاصطناعي)

### **المتطلبات الاختيارية:**
- **TA-Lib** (للمؤشرات المتقدمة)
- **plotly** (للرسوم التفاعلية)

### **أنظمة التشغيل المدعومة:**
- ✅ **Windows 10/11** (موصى به)
- ✅ **Windows 8.1**
- ⚠️ **Linux** (محدود - بدون MT5)
- ⚠️ **macOS** (محدود - بدون MT5)

---

## 🚨 **نصائح الاستخدام**

### **للمبتدئين:**
1. ✅ ابدأ بحساب Demo
2. ✅ اختبر الواجهة بدون تداول حقيقي
3. ✅ راقب السجلات بعناية
4. ✅ ابدأ بإعدادات محافظة

### **للمتقدمين:**
1. ✅ خصص الإعدادات حسب استراتيجيتك
2. ✅ استخدم الاختبار التاريخي للتحسين
3. ✅ راقب تقارير المخاطر باستمرار
4. ✅ صدّر البيانات للتحليل الخارجي

### **للأمان:**
1. ⚠️ لا تترك النظام بدون مراقبة
2. ⚠️ تأكد من إعدادات المخاطر
3. ⚠️ احتفظ بنسخ احتياطية من الإعدادات
4. ⚠️ راجع السجلات بانتظام

---

## 🔧 **استكشاف الأخطاء**

### **مشاكل شائعة:**

#### **1. "النظام التجاري غير متوفر"**
```
الحل: تأكد من وجود ملف intelligent_trading_system_v2.py
```

#### **2. "فشل في الاتصال بـ MT5"**
```
الحل: 
- افتح MetaTrader 5
- فعّل التداول الآلي
- تحقق من بيانات الدخول
```

#### **3. "الرسم البياني غير متوفر"**
```
الحل: pip install matplotlib
```

#### **4. "خطأ في تحديث البيانات"**
```
الحل: أعد تشغيل الواجهة
```

---

## 🏆 **الخلاصة**

الواجهة الرسومية المتطورة توفر:

✅ **تجربة مستخدم متكاملة** للتداول الذكي
✅ **مراقبة شاملة** لجميع جوانب التداول
✅ **تحكم كامل** في النظام والإعدادات
✅ **تصميم حديث** ومريح للاستخدام
✅ **أمان عالي** مع حماية من الأخطاء

**الواجهة الآن جاهزة للاستخدام وتوفر تجربة تداول احترافية! 🚀**

---

*تم تطوير هذه الواجهة بعناية فائقة لتوفر أفضل تجربة تداول ذكي. استخدمها بحكمة ومسؤولية.*
