# تعليمات تشغيل نظام إدارة الصيدلية المحدث

## 🚀 خطوات التشغيل:

### 1. إعداد قاعدة البيانات:
قم بتشغيل الاستعلامات التالية في SQL Server Management Studio:

```sql
-- إنشاء جدول المبيعات
CREATE TABLE sales (
    id INT IDENTITY(1,1) PRIMARY KEY,
    mid VARCHAR(250),
    medicineName VARCHAR(250),
    dosage VARCHAR(100),
    quantity INT,
    pricePerUnit BIGINT,
    totalPrice BIGINT,
    employeeUsername VARCHAR(250),
    employeeName VARCHAR(250),
    saleDate DATETIME DEFAULT GETDATE()
);

-- إنشاء جدول جلسات الموظفين
CREATE TABLE employee_sessions (
    id INT IDENTITY(1,1) PRIMARY KEY,
    username VARCHAR(250),
    employeeName VARCHAR(250),
    loginTime DATETIME,
    logoutTime DATETIME NULL,
    sessionDate DATE
);

-- تحديث الكميات الأصلية للأدوية الموجودة
UPDATE medic 
SET originalQuantity = quantity 
WHERE originalQuantity = 0 OR originalQuantity IS NULL;

UPDATE medic 
SET originalNewQuantity = ISNULL(newQuantity, 0) 
WHERE originalNewQuantity = 0 OR originalNewQuantity IS NULL;
```

### 2. تشغيل البرنامج:
1. افتح المشروع في Visual Studio
2. اضغط F5 أو Build > Start Debugging
3. سجل دخول كصيدلي أو مسؤول

## 📋 الميزات الجديدة:

### للصيادلة:
- **بيع الأدوية**: يتم حفظ كل عملية بيع في قاعدة البيانات
- **تسجيل الجلسات**: يتم تسجيل دخول وخروج الموظف تلقائياً
- **عرض الجرعات**: يمكن رؤية الجرعات المختلفة للدواء الواحد
- **حساب الكمية المتبقية**: عرض دقيق للكميات المتاحة

### للمسؤولين:
- **تقرير المبيعات**: عرض جميع المبيعات مع تفاصيل الموظفين
- **البحث والتصفية**: البحث حسب اسم الدواء أو الموظف
- **تصفية حسب التاريخ**: عرض المبيعات في فترة زمنية محددة
- **تصفية حسب الموظف**: عرض مبيعات موظف معين
- **جلسات العمل**: مراقبة أوقات دخول وخروج الموظفين

## 🔧 استخدام النظام:

### بيع الأدوية:
1. سجل دخول كصيدلي
2. اذهب إلى "Sell Medicine"
3. اختر الدواء من القائمة
4. أدخل الكمية المطلوبة
5. اضغط "Add to Cart"
6. كرر للأدوية الأخرى
7. اضغط "Sell and Print" لإتمام البيع

### عرض تقرير المبيعات:
1. سجل دخول كمسؤول
2. اذهب إلى "Sales Report"
3. استخدم مربع البحث للبحث عن دواء أو موظف
4. استخدم فلتر التاريخ لتحديد فترة زمنية
5. اختر موظف معين من القائمة المنسدلة
6. اضغط "عرض جلسات الموظفين" لرؤية أوقات العمل

## 📊 البيانات المعروضة:

### في تقرير المبيعات:
- رقم العملية
- اسم الموظف
- اسم المستخدم
- اسم الدواء
- الجرعة
- الكمية
- سعر الوحدة
- إجمالي السعر
- تاريخ البيع

### في جلسات الموظفين:
- اسم المستخدم
- اسم الموظف
- وقت الدخول
- وقت الخروج
- مدة الجلسة

## ⚠️ ملاحظات مهمة:

1. **النسخ الاحتياطي**: قم بعمل نسخة احتياطية من قاعدة البيانات قبل التحديث
2. **الصلاحيات**: تأكد من وجود صيادلة مسجلين في النظام
3. **الجرعات**: يمكن إضافة جرعات مختلفة للدواء الواحد باستخدام أعمدة dos2, dos3, dos4
4. **التقارير**: المبيعات تظهر فقط بعد إتمام عمليات بيع فعلية

## 🐛 حل المشاكل:

### إذا لم تظهر المبيعات:
- تأكد من وجود عمليات بيع مكتملة
- تحقق من اتصال قاعدة البيانات
- تأكد من إنشاء جدول sales

### إذا لم تظهر جلسات الموظفين:
- تأكد من تسجيل دخول الصيادلة
- تحقق من إنشاء جدول employee_sessions

### مشاكل في الكميات:
- تأكد من تحديث originalQuantity و originalNewQuantity
- تحقق من صحة بيانات الأدوية في جدول medic
