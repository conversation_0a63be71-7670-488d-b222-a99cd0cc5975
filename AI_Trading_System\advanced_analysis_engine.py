#!/usr/bin/env python3
"""
Advanced Analysis Engine - محرك التحليل المتقدم
نظام تحليل متطور للتداول الذكي مع إدارة رأس المال والمخاطر
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

try:
    import MetaTrader5 as mt5
except ImportError:
    mt5 = None

try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False

class AdvancedAnalysisEngine:
    """
    محرك التحليل المتقدم للتداول الذكي
    """
    
    def __init__(self, symbol: str = "EURUSD", min_balance: float = 10.0):
        """
        تهيئة محرك التحليل المتقدم
        """
        self.symbol = symbol
        self.min_balance = min_balance
        self.logger = self._setup_logger()
        
        # إعدادات التحليل
        self.analysis_config = {
            'min_confidence': 0.45,  # الحد الأدنى للثقة - مخفض للتداول الحقيقي
            'max_risk_per_trade': 0.02,  # 2% مخاطر لكل صفقة
            'max_daily_risk': 0.05,  # 5% مخاطر يومية
            'min_reward_risk_ratio': 2.0,  # نسبة المكافأة للمخاطر
            'lookback_periods': [14, 21, 50, 100, 200],  # فترات التحليل
            'timeframes': ['M15', 'H1', 'H4', 'D1'],  # الإطارات الزمنية
        }
        
        # إحصائيات الأداء
        self.performance_stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_profit': 0.0,
            'max_drawdown': 0.0,
            'win_rate': 0.0,
            'profit_factor': 0.0,
            'sharpe_ratio': 0.0,
            'last_analysis_time': None
        }
        
        # ذاكرة التحليل للتعلم الذاتي
        self.analysis_memory = {
            'successful_patterns': [],
            'failed_patterns': [],
            'market_conditions': [],
            'optimization_history': []
        }
    
    def _setup_logger(self) -> logging.Logger:
        """
        إعداد نظام السجلات
        """
        logger = logging.getLogger(f'AdvancedAnalysis_{self.symbol}')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def get_multi_timeframe_data(self) -> Dict[str, pd.DataFrame]:
        """
        الحصول على البيانات من إطارات زمنية متعددة
        """
        try:
            if not mt5 or not mt5.initialize():
                self.logger.error("❌ فشل في تهيئة MT5")
                return {}
            
            timeframe_map = {
                'M15': mt5.TIMEFRAME_M15,
                'H1': mt5.TIMEFRAME_H1,
                'H4': mt5.TIMEFRAME_H4,
                'D1': mt5.TIMEFRAME_D1
            }
            
            multi_data = {}
            
            for tf_name, tf_value in timeframe_map.items():
                try:
                    # الحصول على البيانات
                    rates = mt5.copy_rates_from_pos(self.symbol, tf_value, 0, 500)
                    
                    if rates is not None and len(rates) > 0:
                        df = pd.DataFrame(rates)
                        df['time'] = pd.to_datetime(df['time'], unit='s')
                        df.set_index('time', inplace=True)
                        df.columns = ['Open', 'High', 'Low', 'Close', 'Volume', 'Spread', 'Real_Volume']
                        
                        multi_data[tf_name] = df
                        self.logger.info(f"✅ تم الحصول على بيانات {tf_name}: {len(df)} شمعة")
                    
                except Exception as e:
                    self.logger.warning(f"⚠️ فشل في الحصول على بيانات {tf_name}: {e}")
                    continue
            
            return multi_data
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في الحصول على البيانات متعددة الإطارات: {e}")
            return {}
    
    def calculate_advanced_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        حساب المؤشرات الفنية المتقدمة
        """
        try:
            if df is None or len(df) < 50:
                return df
            
            # التأكد من وجود الأعمدة المطلوبة
            required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
            for col in required_cols:
                if col not in df.columns:
                    self.logger.error(f"❌ العمود {col} غير موجود")
                    return df
            
            # تحويل إلى numpy arrays
            open_prices = df['Open'].values
            high_prices = df['High'].values
            low_prices = df['Low'].values
            close_prices = df['Close'].values
            volume = df['Volume'].values
            
            if TALIB_AVAILABLE:
                # المؤشرات المتقدمة باستخدام TA-Lib
                df = self._calculate_talib_advanced_indicators(df, open_prices, high_prices, low_prices, close_prices, volume)
            else:
                # المؤشرات المبسطة
                df = self._calculate_simple_advanced_indicators(df)
            
            # مؤشرات مخصصة إضافية
            df = self._calculate_custom_indicators(df)
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في حساب المؤشرات المتقدمة: {e}")
            return df
    
    def _calculate_talib_advanced_indicators(self, df, open_prices, high_prices, low_prices, close_prices, volume):
        """
        حساب المؤشرات المتقدمة باستخدام TA-Lib
        """
        try:
            # المتوسطات المتحركة المتقدمة
            for period in [5, 10, 20, 50, 100, 200]:
                df[f'SMA_{period}'] = talib.SMA(close_prices, timeperiod=period)
                df[f'EMA_{period}'] = talib.EMA(close_prices, timeperiod=period)
            
            # مؤشرات الزخم المتقدمة
            df['RSI_14'] = talib.RSI(close_prices, timeperiod=14)
            df['RSI_21'] = talib.RSI(close_prices, timeperiod=21)
            df['MACD'], df['MACD_signal'], df['MACD_hist'] = talib.MACD(close_prices, fastperiod=12, slowperiod=26, signalperiod=9)
            df['Stoch_K'], df['Stoch_D'] = talib.STOCH(high_prices, low_prices, close_prices, fastk_period=14, slowk_period=3, slowd_period=3)
            df['Williams_R'] = talib.WILLR(high_prices, low_prices, close_prices, timeperiod=14)
            df['CCI'] = talib.CCI(high_prices, low_prices, close_prices, timeperiod=14)
            df['MFI'] = talib.MFI(high_prices, low_prices, close_prices, volume, timeperiod=14)
            
            # البولنجر باندز المتقدمة
            for period in [20, 50]:
                for std in [1.5, 2.0, 2.5]:
                    upper, middle, lower = talib.BBANDS(close_prices, timeperiod=period, nbdevup=std, nbdevdn=std)
                    df[f'BB_upper_{period}_{std}'] = upper
                    df[f'BB_middle_{period}_{std}'] = middle
                    df[f'BB_lower_{period}_{std}'] = lower
                    df[f'BB_width_{period}_{std}'] = (upper - lower) / middle * 100
                    df[f'BB_position_{period}_{std}'] = (close_prices - lower) / (upper - lower) * 100
            
            # مؤشرات التقلب
            df['ATR_14'] = talib.ATR(high_prices, low_prices, close_prices, timeperiod=14)
            df['ATR_21'] = talib.ATR(high_prices, low_prices, close_prices, timeperiod=21)
            df['NATR'] = talib.NATR(high_prices, low_prices, close_prices, timeperiod=14)
            df['TRANGE'] = talib.TRANGE(high_prices, low_prices, close_prices)
            
            # مؤشرات الاتجاه المتقدمة
            df['ADX'] = talib.ADX(high_prices, low_prices, close_prices, timeperiod=14)
            df['ADXR'] = talib.ADXR(high_prices, low_prices, close_prices, timeperiod=14)
            df['DX'] = talib.DX(high_prices, low_prices, close_prices, timeperiod=14)
            df['PLUS_DI'] = talib.PLUS_DI(high_prices, low_prices, close_prices, timeperiod=14)
            df['MINUS_DI'] = talib.MINUS_DI(high_prices, low_prices, close_prices, timeperiod=14)
            df['AROON_up'], df['AROON_down'] = talib.AROON(high_prices, low_prices, timeperiod=14)
            df['AROONOSC'] = talib.AROONOSC(high_prices, low_prices, timeperiod=14)
            
            # مؤشرات الحجم المتقدمة
            df['OBV'] = talib.OBV(close_prices, volume)
            df['AD'] = talib.AD(high_prices, low_prices, close_prices, volume)
            df['ADOSC'] = talib.ADOSC(high_prices, low_prices, close_prices, volume, fastperiod=3, slowperiod=10)
            
            # أنماط الشموع المتقدمة
            candlestick_patterns = [
                'DOJI', 'HAMMER', 'HANGINGMAN', 'INVERTED_HAMMER', 'SHOOTING_STAR',
                'ENGULFING', 'HARAMI', 'PIERCING', 'DARK_CLOUD_COVER', 'MORNING_STAR',
                'EVENING_STAR', 'THREE_WHITE_SOLDIERS', 'THREE_BLACK_CROWS', 'SPINNING_TOP',
                'MARUBOZU', 'DRAGONFLY_DOJI', 'GRAVESTONE_DOJI', 'LONG_LEGGED_DOJI'
            ]
            
            for pattern in candlestick_patterns:
                try:
                    func = getattr(talib, f'CDL{pattern}')
                    df[f'CANDLE_{pattern}'] = func(open_prices, high_prices, low_prices, close_prices)
                except AttributeError:
                    continue
            
            self.logger.info("✅ تم حساب المؤشرات المتقدمة باستخدام TA-Lib")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في حساب مؤشرات TA-Lib: {e}")
            return df
    
    def _calculate_simple_advanced_indicators(self, df):
        """
        حساب المؤشرات المتقدمة بدون TA-Lib
        """
        try:
            # المتوسطات المتحركة
            for period in [5, 10, 20, 50, 100, 200]:
                df[f'SMA_{period}'] = df['Close'].rolling(window=period).mean()
                df[f'EMA_{period}'] = df['Close'].ewm(span=period).mean()
            
            # RSI متقدم
            for period in [14, 21]:
                delta = df['Close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
                rs = gain / loss
                df[f'RSI_{period}'] = 100 - (100 / (1 + rs))
            
            # MACD
            df['MACD'] = df['EMA_12'] - df['EMA_26']
            df['MACD_signal'] = df['MACD'].ewm(span=9).mean()
            df['MACD_hist'] = df['MACD'] - df['MACD_signal']
            
            # البولنجر باندز
            for period in [20, 50]:
                sma = df['Close'].rolling(window=period).mean()
                std = df['Close'].rolling(window=period).std()
                df[f'BB_upper_{period}_2.0'] = sma + (std * 2)
                df[f'BB_middle_{period}_2.0'] = sma
                df[f'BB_lower_{period}_2.0'] = sma - (std * 2)
                df[f'BB_width_{period}_2.0'] = ((sma + (std * 2)) - (sma - (std * 2))) / sma * 100
                df[f'BB_position_{period}_2.0'] = (df['Close'] - (sma - (std * 2))) / ((sma + (std * 2)) - (sma - (std * 2))) * 100
            
            # ATR مبسط
            df['High_Low'] = df['High'] - df['Low']
            df['High_Close'] = np.abs(df['High'] - df['Close'].shift())
            df['Low_Close'] = np.abs(df['Low'] - df['Close'].shift())
            df['TR'] = df[['High_Low', 'High_Close', 'Low_Close']].max(axis=1)
            df['ATR_14'] = df['TR'].rolling(window=14).mean()
            df['ATR_21'] = df['TR'].rolling(window=21).mean()
            
            # تنظيف الأعمدة المؤقتة
            df.drop(['High_Low', 'High_Close', 'Low_Close', 'TR'], axis=1, inplace=True)
            
            self.logger.info("✅ تم حساب المؤشرات المتقدمة المبسطة")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في حساب المؤشرات المبسطة: {e}")
            return df
    
    def _calculate_custom_indicators(self, df):
        """
        حساب المؤشرات المخصصة
        """
        try:
            # مؤشر القوة النسبية للسعر
            df['Price_Strength'] = (df['Close'] - df['Close'].rolling(20).min()) / (df['Close'].rolling(20).max() - df['Close'].rolling(20).min()) * 100
            
            # مؤشر الزخم المتعدد الفترات
            for period in [5, 10, 20]:
                df[f'Momentum_{period}'] = (df['Close'] / df['Close'].shift(period) - 1) * 100
            
            # مؤشر التقلب النسبي
            df['Volatility_Ratio'] = df['ATR_14'] / df['Close'] * 100
            
            # مؤشر الاتجاه المركب
            df['Trend_Composite'] = (
                (df['Close'] > df['SMA_20']).astype(int) +
                (df['SMA_20'] > df['SMA_50']).astype(int) +
                (df['SMA_50'] > df['SMA_100']).astype(int)
            ) / 3 * 100
            
            # مؤشر جودة الإشارة
            df['Signal_Quality'] = 0.0
            
            # حساب جودة الإشارة بناءً على عدة عوامل
            if 'RSI_14' in df.columns:
                # RSI في المنطقة المثلى
                df['Signal_Quality'] += ((df['RSI_14'] > 30) & (df['RSI_14'] < 70)).astype(int) * 20
            
            if 'ADX' in df.columns:
                # ADX يشير لاتجاه قوي
                df['Signal_Quality'] += (df['ADX'] > 25).astype(int) * 30
            
            if 'BB_position_20_2.0' in df.columns:
                # السعر ليس في المناطق المتطرفة
                df['Signal_Quality'] += ((df['BB_position_20_2.0'] > 20) & (df['BB_position_20_2.0'] < 80)).astype(int) * 25
            
            # التقلب معتدل
            if 'Volatility_Ratio' in df.columns:
                df['Signal_Quality'] += ((df['Volatility_Ratio'] > 0.5) & (df['Volatility_Ratio'] < 2.0)).astype(int) * 25
            
            self.logger.info("✅ تم حساب المؤشرات المخصصة")
            return df

        except Exception as e:
            self.logger.error(f"❌ خطأ في حساب المؤشرات المخصصة: {e}")
            return df

    def calculate_smart_position_size(self, balance: float, confidence: float,
                                    risk_percentage: float = 0.02,
                                    stop_loss_pips: float = 50) -> Dict:
        """
        حساب حجم الصفقة الذكي بناءً على إدارة رأس المال
        """
        try:
            # الحصول على معلومات الرمز
            if not mt5:
                return {'volume': 0.01, 'risk_amount': balance * risk_percentage}

            symbol_info = mt5.symbol_info(self.symbol)
            if not symbol_info:
                return {'volume': 0.01, 'risk_amount': balance * risk_percentage}

            # حساب قيمة النقطة
            tick_info = mt5.symbol_info_tick(self.symbol)
            if not tick_info:
                return {'volume': 0.01, 'risk_amount': balance * risk_percentage}

            # حساب المخاطر المسموحة
            max_risk_amount = balance * risk_percentage
            adjusted_risk = max_risk_amount * confidence  # تعديل المخاطر حسب الثقة

            # حساب قيمة النقطة بالدولار
            if self.symbol.endswith('USD'):
                pip_value = symbol_info.trade_tick_value * 10  # للأزواج المقومة بالدولار
            else:
                pip_value = symbol_info.trade_tick_value * 10 / tick_info.bid  # للأزواج الأخرى

            # حساب الحجم المطلوب
            if pip_value > 0 and stop_loss_pips > 0:
                required_volume = adjusted_risk / (stop_loss_pips * pip_value)
            else:
                required_volume = 0.01

            # تطبيق حدود الحجم
            min_volume = symbol_info.volume_min
            max_volume = min(symbol_info.volume_max, balance / 1000)  # حد أقصى آمن

            # تقريب الحجم حسب خطوة الحجم
            volume_step = symbol_info.volume_step
            final_volume = max(min_volume, min(required_volume, max_volume))
            final_volume = round(final_volume / volume_step) * volume_step

            # حساب المخاطر الفعلية
            actual_risk = final_volume * stop_loss_pips * pip_value
            risk_percentage_actual = (actual_risk / balance) * 100

            # حساب الهامش المطلوب
            margin_required = final_volume * tick_info.ask * symbol_info.margin_initial / 100

            result = {
                'volume': final_volume,
                'risk_amount': actual_risk,
                'risk_percentage': risk_percentage_actual,
                'margin_required': margin_required,
                'pip_value': pip_value,
                'confidence_adjusted': confidence,
                'stop_loss_pips': stop_loss_pips,
                'max_affordable_volume': max_volume,
                'recommended_balance': margin_required * 10  # 10x الهامش كحد أدنى آمن
            }

            self.logger.info(f"💰 حجم الصفقة المحسوب: {final_volume}")
            self.logger.info(f"💰 المخاطر: ${actual_risk:.2f} ({risk_percentage_actual:.2f}%)")
            self.logger.info(f"💰 الهامش المطلوب: ${margin_required:.2f}")

            return result

        except Exception as e:
            self.logger.error(f"❌ خطأ في حساب حجم الصفقة: {e}")
            return {
                'volume': 0.01,
                'risk_amount': balance * 0.02,
                'risk_percentage': 2.0,
                'margin_required': 100.0,
                'pip_value': 1.0,
                'confidence_adjusted': confidence,
                'stop_loss_pips': 50,
                'max_affordable_volume': 0.01,
                'recommended_balance': 1000.0
            }

    def assess_market_conditions(self, multi_data: Dict[str, pd.DataFrame]) -> Dict:
        """
        تقييم ظروف السوق الحالية
        """
        try:
            if not multi_data:
                return {'condition': 'unknown', 'strength': 0.0, 'recommendation': 'hold'}

            conditions = {
                'trend_strength': 0.0,
                'volatility_level': 0.0,
                'momentum_quality': 0.0,
                'support_resistance': 0.0,
                'overall_score': 0.0,
                'condition': 'neutral',
                'recommendation': 'hold',
                'confidence': 0.0
            }

            # تحليل الاتجاه من الإطارات المختلفة
            trend_scores = []
            volatility_scores = []
            momentum_scores = []

            for timeframe, df in multi_data.items():
                if df is None or len(df) < 50:
                    continue

                # تحليل الاتجاه
                if 'SMA_20' in df.columns and 'SMA_50' in df.columns:
                    trend_score = 0
                    latest_close = df['Close'].iloc[-1]
                    sma_20 = df['SMA_20'].iloc[-1]
                    sma_50 = df['SMA_50'].iloc[-1]

                    if latest_close > sma_20 > sma_50:
                        trend_score = 1  # اتجاه صاعد
                    elif latest_close < sma_20 < sma_50:
                        trend_score = -1  # اتجاه هابط

                    trend_scores.append(trend_score)

                # تحليل التقلب
                if 'ATR_14' in df.columns:
                    atr_current = df['ATR_14'].iloc[-1]
                    atr_avg = df['ATR_14'].rolling(50).mean().iloc[-1]
                    volatility_ratio = atr_current / atr_avg if atr_avg > 0 else 1
                    volatility_scores.append(volatility_ratio)

                # تحليل الزخم
                if 'RSI_14' in df.columns and 'MACD' in df.columns:
                    rsi = df['RSI_14'].iloc[-1]
                    macd = df['MACD'].iloc[-1]
                    macd_signal = df['MACD_signal'].iloc[-1]

                    momentum_score = 0
                    if 30 < rsi < 70 and macd > macd_signal:
                        momentum_score = 1
                    elif 30 < rsi < 70 and macd < macd_signal:
                        momentum_score = -1

                    momentum_scores.append(momentum_score)

            # حساب النتائج النهائية
            if trend_scores:
                conditions['trend_strength'] = np.mean(trend_scores)

            if volatility_scores:
                conditions['volatility_level'] = np.mean(volatility_scores)

            if momentum_scores:
                conditions['momentum_quality'] = np.mean(momentum_scores)

            # حساب النتيجة الإجمالية
            conditions['overall_score'] = (
                conditions['trend_strength'] * 0.4 +
                conditions['momentum_quality'] * 0.3 +
                min(conditions['volatility_level'], 2.0) * 0.2 +  # تحديد التقلب
                conditions['support_resistance'] * 0.1
            )

            # تحديد حالة السوق
            if conditions['overall_score'] > 0.6:
                conditions['condition'] = 'bullish'
                conditions['recommendation'] = 'buy'
                conditions['confidence'] = min(conditions['overall_score'], 1.0)
            elif conditions['overall_score'] < -0.6:
                conditions['condition'] = 'bearish'
                conditions['recommendation'] = 'sell'
                conditions['confidence'] = min(abs(conditions['overall_score']), 1.0)
            else:
                conditions['condition'] = 'neutral'
                conditions['recommendation'] = 'hold'
                conditions['confidence'] = 0.5

            self.logger.info(f"📊 حالة السوق: {conditions['condition']}")
            self.logger.info(f"📊 التوصية: {conditions['recommendation']}")
            self.logger.info(f"📊 مستوى الثقة: {conditions['confidence']:.2%}")

            return conditions

        except Exception as e:
            self.logger.error(f"❌ خطأ في تقييم ظروف السوق: {e}")
            return {
                'condition': 'unknown',
                'strength': 0.0,
                'recommendation': 'hold',
                'confidence': 0.0,
                'overall_score': 0.0
            }

    def make_intelligent_decision(self, multi_data: Dict[str, pd.DataFrame],
                                balance: float) -> Dict:
        """
        اتخاذ قرار تداول ذكي بناءً على التحليل الشامل
        """
        try:
            # تقييم ظروف السوق
            market_conditions = self.assess_market_conditions(multi_data)

            # التحقق من الحد الأدنى للثقة
            if market_conditions['confidence'] < self.analysis_config['min_confidence']:
                return {
                    'decision': 'hold',
                    'confidence': market_conditions['confidence'],
                    'reason': f'مستوى الثقة منخفض ({market_conditions["confidence"]:.2%})',
                    'position_size': 0.0,
                    'risk_reward_ratio': 0.0,
                    'stop_loss': 0.0,
                    'take_profit': 0.0
                }

            # التحقق من الرصيد الكافي
            if balance < self.min_balance:
                return {
                    'decision': 'hold',
                    'confidence': 0.0,
                    'reason': f'الرصيد أقل من الحد الأدنى (${balance:.2f} < ${self.min_balance})',
                    'position_size': 0.0,
                    'risk_reward_ratio': 0.0,
                    'stop_loss': 0.0,
                    'take_profit': 0.0
                }

            # الحصول على البيانات الرئيسية للتحليل
            main_data = multi_data.get('H1') or multi_data.get('M15') or list(multi_data.values())[0]
            if main_data is None or len(main_data) < 50:
                return {
                    'decision': 'hold',
                    'confidence': 0.0,
                    'reason': 'بيانات غير كافية للتحليل',
                    'position_size': 0.0,
                    'risk_reward_ratio': 0.0,
                    'stop_loss': 0.0,
                    'take_profit': 0.0
                }

            # حساب مستويات الدعم والمقاومة
            support_resistance = self._calculate_support_resistance(main_data)

            # حساب إيقاف الخسارة وجني الربح الديناميكي
            current_price = main_data['Close'].iloc[-1]
            atr = main_data.get('ATR_14', pd.Series([0.001])).iloc[-1]

            if market_conditions['recommendation'] == 'buy':
                stop_loss_price = current_price - (atr * 2)
                take_profit_price = current_price + (atr * 4)
                stop_loss_pips = abs(current_price - stop_loss_price) * 10000
            elif market_conditions['recommendation'] == 'sell':
                stop_loss_price = current_price + (atr * 2)
                take_profit_price = current_price - (atr * 4)
                stop_loss_pips = abs(stop_loss_price - current_price) * 10000
            else:
                return {
                    'decision': 'hold',
                    'confidence': market_conditions['confidence'],
                    'reason': 'لا توجد إشارة واضحة للدخول',
                    'position_size': 0.0,
                    'risk_reward_ratio': 0.0,
                    'stop_loss': 0.0,
                    'take_profit': 0.0
                }

            # حساب نسبة المكافأة للمخاطر
            risk_distance = abs(current_price - stop_loss_price)
            reward_distance = abs(take_profit_price - current_price)
            risk_reward_ratio = reward_distance / risk_distance if risk_distance > 0 else 0

            # التحقق من نسبة المكافأة للمخاطر
            if risk_reward_ratio < self.analysis_config['min_reward_risk_ratio']:
                return {
                    'decision': 'hold',
                    'confidence': market_conditions['confidence'],
                    'reason': f'نسبة المكافأة للمخاطر منخفضة ({risk_reward_ratio:.2f})',
                    'position_size': 0.0,
                    'risk_reward_ratio': risk_reward_ratio,
                    'stop_loss': stop_loss_price,
                    'take_profit': take_profit_price
                }

            # حساب حجم الصفقة الذكي
            position_info = self.calculate_smart_position_size(
                balance=balance,
                confidence=market_conditions['confidence'],
                risk_percentage=self.analysis_config['max_risk_per_trade'],
                stop_loss_pips=stop_loss_pips
            )

            # التحقق من الهامش المطلوب
            if position_info['margin_required'] > balance * 0.8:  # لا تستخدم أكثر من 80% من الرصيد كهامش
                return {
                    'decision': 'hold',
                    'confidence': market_conditions['confidence'],
                    'reason': f'الهامش المطلوب مرتفع جداً (${position_info["margin_required"]:.2f})',
                    'position_size': 0.0,
                    'risk_reward_ratio': risk_reward_ratio,
                    'stop_loss': stop_loss_price,
                    'take_profit': take_profit_price
                }

            # قرار نهائي
            decision = {
                'decision': market_conditions['recommendation'],
                'confidence': market_conditions['confidence'],
                'reason': f'إشارة {market_conditions["recommendation"]} قوية مع ظروف سوق {market_conditions["condition"]}',
                'position_size': position_info['volume'],
                'risk_reward_ratio': risk_reward_ratio,
                'stop_loss': stop_loss_price,
                'take_profit': take_profit_price,
                'risk_amount': position_info['risk_amount'],
                'margin_required': position_info['margin_required'],
                'market_conditions': market_conditions,
                'support_resistance': support_resistance,
                'atr': atr,
                'current_price': current_price
            }

            self.logger.info(f"🧠 قرار ذكي: {decision['decision'].upper()}")
            self.logger.info(f"🧠 الثقة: {decision['confidence']:.2%}")
            self.logger.info(f"🧠 حجم الصفقة: {decision['position_size']}")
            self.logger.info(f"🧠 نسبة المكافأة/المخاطر: {decision['risk_reward_ratio']:.2f}")

            return decision

        except Exception as e:
            self.logger.error(f"❌ خطأ في اتخاذ القرار الذكي: {e}")
            return {
                'decision': 'hold',
                'confidence': 0.0,
                'reason': f'خطأ في التحليل: {str(e)}',
                'position_size': 0.0,
                'risk_reward_ratio': 0.0,
                'stop_loss': 0.0,
                'take_profit': 0.0
            }

    def _calculate_support_resistance(self, df: pd.DataFrame) -> Dict:
        """
        حساب مستويات الدعم والمقاومة
        """
        try:
            if len(df) < 50:
                return {'support': [], 'resistance': []}

            # استخدام القمم والقيعان المحلية
            highs = df['High'].rolling(window=10, center=True).max()
            lows = df['Low'].rolling(window=10, center=True).min()

            # العثور على مستويات المقاومة (القمم)
            resistance_levels = []
            for i in range(10, len(df) - 10):
                if df['High'].iloc[i] == highs.iloc[i]:
                    resistance_levels.append(df['High'].iloc[i])

            # العثور على مستويات الدعم (القيعان)
            support_levels = []
            for i in range(10, len(df) - 10):
                if df['Low'].iloc[i] == lows.iloc[i]:
                    support_levels.append(df['Low'].iloc[i])

            # ترتيب وتنظيف المستويات
            resistance_levels = sorted(list(set(resistance_levels)), reverse=True)[:5]
            support_levels = sorted(list(set(support_levels)))[:5]

            return {
                'support': support_levels,
                'resistance': resistance_levels,
                'nearest_support': min(support_levels) if support_levels else 0,
                'nearest_resistance': max(resistance_levels) if resistance_levels else 0
            }

        except Exception as e:
            self.logger.error(f"❌ خطأ في حساب الدعم والمقاومة: {e}")
            return {'support': [], 'resistance': []}

    def backtest_strategy(self, historical_data: pd.DataFrame,
                         initial_balance: float = 1000.0) -> Dict:
        """
        اختبار الاستراتيجية على البيانات التاريخية
        """
        try:
            if historical_data is None or len(historical_data) < 200:
                return {'error': 'بيانات تاريخية غير كافية'}

            # إعداد المحاكاة
            balance = initial_balance
            positions = []
            trades_history = []
            equity_curve = []

            # حساب المؤشرات للبيانات التاريخية
            df = self.calculate_advanced_indicators(historical_data.copy())

            self.logger.info(f"🧪 بدء اختبار الاستراتيجية على {len(df)} شمعة")

            for i in range(100, len(df) - 1):  # ترك مساحة للمؤشرات
                current_data = df.iloc[:i+1]

                # محاكاة اتخاذ القرار
                mock_multi_data = {'H1': current_data}
                decision = self.make_intelligent_decision(mock_multi_data, balance)

                current_price = df['Close'].iloc[i]
                next_price = df['Close'].iloc[i + 1]

                # تنفيذ القرار
                if decision['decision'] in ['buy', 'sell'] and decision['position_size'] > 0:
                    # فتح صفقة جديدة
                    position = {
                        'type': decision['decision'],
                        'entry_price': current_price,
                        'size': decision['position_size'],
                        'stop_loss': decision['stop_loss'],
                        'take_profit': decision['take_profit'],
                        'entry_time': i,
                        'confidence': decision['confidence']
                    }
                    positions.append(position)

                # فحص الصفقات المفتوحة
                closed_positions = []
                for pos_idx, position in enumerate(positions):
                    # فحص إيقاف الخسارة وجني الربح
                    if position['type'] == 'buy':
                        if next_price <= position['stop_loss'] or next_price >= position['take_profit']:
                            # إغلاق الصفقة
                            exit_price = position['stop_loss'] if next_price <= position['stop_loss'] else position['take_profit']
                            profit = (exit_price - position['entry_price']) * position['size'] * 100000
                        else:
                            continue
                    else:  # sell
                        if next_price >= position['stop_loss'] or next_price <= position['take_profit']:
                            # إغلاق الصفقة
                            exit_price = position['stop_loss'] if next_price >= position['stop_loss'] else position['take_profit']
                            profit = (position['entry_price'] - exit_price) * position['size'] * 100000
                        else:
                            continue

                    # تسجيل الصفقة
                    trade = {
                        'type': position['type'],
                        'entry_price': position['entry_price'],
                        'exit_price': exit_price,
                        'size': position['size'],
                        'profit': profit,
                        'duration': i - position['entry_time'],
                        'confidence': position['confidence']
                    }
                    trades_history.append(trade)
                    balance += profit
                    closed_positions.append(pos_idx)

                # إزالة الصفقات المغلقة
                for idx in reversed(closed_positions):
                    positions.pop(idx)

                # تسجيل منحنى الأسهم
                equity_curve.append({
                    'time': i,
                    'balance': balance,
                    'open_positions': len(positions)
                })

            # حساب الإحصائيات
            stats = self._calculate_backtest_stats(trades_history, initial_balance, balance)

            result = {
                'initial_balance': initial_balance,
                'final_balance': balance,
                'total_return': ((balance - initial_balance) / initial_balance) * 100,
                'trades_history': trades_history,
                'equity_curve': equity_curve,
                'statistics': stats,
                'total_trades': len(trades_history)
            }

            self.logger.info(f"🧪 انتهاء الاختبار: {len(trades_history)} صفقة")
            self.logger.info(f"🧪 العائد الإجمالي: {result['total_return']:.2f}%")
            self.logger.info(f"🧪 معدل الفوز: {stats['win_rate']:.2f}%")

            return result

        except Exception as e:
            self.logger.error(f"❌ خطأ في اختبار الاستراتيجية: {e}")
            return {'error': str(e)}

    def _calculate_backtest_stats(self, trades: List[Dict],
                                initial_balance: float,
                                final_balance: float) -> Dict:
        """
        حساب إحصائيات الاختبار
        """
        try:
            if not trades:
                return {
                    'win_rate': 0.0,
                    'profit_factor': 0.0,
                    'max_drawdown': 0.0,
                    'average_win': 0.0,
                    'average_loss': 0.0,
                    'largest_win': 0.0,
                    'largest_loss': 0.0
                }

            profits = [trade['profit'] for trade in trades]
            winning_trades = [p for p in profits if p > 0]
            losing_trades = [p for p in profits if p < 0]

            stats = {
                'total_trades': len(trades),
                'winning_trades': len(winning_trades),
                'losing_trades': len(losing_trades),
                'win_rate': (len(winning_trades) / len(trades)) * 100 if trades else 0,
                'total_profit': sum(profits),
                'gross_profit': sum(winning_trades) if winning_trades else 0,
                'gross_loss': abs(sum(losing_trades)) if losing_trades else 0,
                'profit_factor': (sum(winning_trades) / abs(sum(losing_trades))) if losing_trades else float('inf'),
                'average_win': np.mean(winning_trades) if winning_trades else 0,
                'average_loss': np.mean(losing_trades) if losing_trades else 0,
                'largest_win': max(winning_trades) if winning_trades else 0,
                'largest_loss': min(losing_trades) if losing_trades else 0,
                'max_drawdown': 0.0,  # سيتم حسابه لاحقاً
                'sharpe_ratio': 0.0   # سيتم حسابه لاحقاً
            }

            return stats

        except Exception as e:
            self.logger.error(f"❌ خطأ في حساب الإحصائيات: {e}")
            return {}

    def learn_from_results(self, trade_result: Dict, decision_data: Dict):
        """
        التعلم من نتائج التداول لتحسين الأداء
        """
        try:
            # تسجيل النتيجة في الذاكرة
            learning_record = {
                'timestamp': datetime.now(),
                'decision': decision_data,
                'result': trade_result,
                'market_conditions': decision_data.get('market_conditions', {}),
                'success': trade_result.get('profit', 0) > 0
            }

            if learning_record['success']:
                self.analysis_memory['successful_patterns'].append(learning_record)
                # الاحتفاظ بآخر 100 نمط ناجح
                if len(self.analysis_memory['successful_patterns']) > 100:
                    self.analysis_memory['successful_patterns'].pop(0)
            else:
                self.analysis_memory['failed_patterns'].append(learning_record)
                # الاحتفاظ بآخر 100 نمط فاشل
                if len(self.analysis_memory['failed_patterns']) > 100:
                    self.analysis_memory['failed_patterns'].pop(0)

            # تحديث الإحصائيات
            self.performance_stats['total_trades'] += 1
            if learning_record['success']:
                self.performance_stats['winning_trades'] += 1
            else:
                self.performance_stats['losing_trades'] += 1

            self.performance_stats['total_profit'] += trade_result.get('profit', 0)
            self.performance_stats['win_rate'] = (
                self.performance_stats['winning_trades'] /
                self.performance_stats['total_trades'] * 100
            )

            # تحسين المعاملات بناءً على النتائج
            self._optimize_parameters()

            self.logger.info(f"📚 تم التعلم من النتيجة: {'نجح' if learning_record['success'] else 'فشل'}")
            self.logger.info(f"📚 معدل الفوز الحالي: {self.performance_stats['win_rate']:.2f}%")

        except Exception as e:
            self.logger.error(f"❌ خطأ في التعلم من النتائج: {e}")

    def _optimize_parameters(self):
        """
        تحسين المعاملات بناءً على الأداء
        """
        try:
            # تحليل الأنماط الناجحة والفاشلة
            successful_patterns = self.analysis_memory['successful_patterns']
            failed_patterns = self.analysis_memory['failed_patterns']

            if len(successful_patterns) < 10 or len(failed_patterns) < 10:
                return  # بيانات غير كافية للتحسين

            # تحليل مستويات الثقة الناجحة
            successful_confidences = [p['decision']['confidence'] for p in successful_patterns]
            failed_confidences = [p['decision']['confidence'] for p in failed_patterns]

            avg_successful_confidence = np.mean(successful_confidences)
            avg_failed_confidence = np.mean(failed_confidences)

            # تعديل الحد الأدنى للثقة
            if avg_successful_confidence > avg_failed_confidence:
                new_min_confidence = (avg_successful_confidence + avg_failed_confidence) / 2
                self.analysis_config['min_confidence'] = max(0.5, min(0.9, new_min_confidence))

            # تحليل نسب المكافأة للمخاطر الناجحة
            successful_rr = [p['decision'].get('risk_reward_ratio', 0) for p in successful_patterns if p['decision'].get('risk_reward_ratio', 0) > 0]
            if successful_rr:
                avg_successful_rr = np.mean(successful_rr)
                self.analysis_config['min_reward_risk_ratio'] = max(1.5, min(3.0, avg_successful_rr * 0.8))

            # حفظ سجل التحسين
            optimization_record = {
                'timestamp': datetime.now(),
                'min_confidence': self.analysis_config['min_confidence'],
                'min_reward_risk_ratio': self.analysis_config['min_reward_risk_ratio'],
                'win_rate': self.performance_stats['win_rate'],
                'total_trades': self.performance_stats['total_trades']
            }

            self.analysis_memory['optimization_history'].append(optimization_record)

            self.logger.info(f"🔧 تم تحسين المعاملات:")
            self.logger.info(f"   الحد الأدنى للثقة: {self.analysis_config['min_confidence']:.3f}")
            self.logger.info(f"   نسبة المكافأة/المخاطر: {self.analysis_config['min_reward_risk_ratio']:.2f}")

        except Exception as e:
            self.logger.error(f"❌ خطأ في تحسين المعاملات: {e}")
