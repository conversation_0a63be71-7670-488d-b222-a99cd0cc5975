﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnUpdate.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAALGPC/xhBQAAAYRJREFUaEPt
        mYFNAzEMRW+EjsAIHaEjMAIb0A1ghG4Am9AN6AbtBmUD6o84ybKS2EkTBwk/6alCXJP7vfwqd12CIAj+
        FQfyw1HMN4Qt+UV+O3kkm3n8fc2B/6cmHWFzkDcSA+x+/srzSspJR9gU5IlcB7iSD2SJPYlAPcWJ3xUE
        a58PAD/JDekJwvBzqAqCk8UV4AOsYql5clcQfPL8zVIsIS+ag6zl1tTK34umILzcmpby96A6SKrcmh7l
        rwpSKrfm6PJXBcH+BQdI5dbjQqaOG9mX5rJz8CY+CAb1JoJwIkhHIggngnQkgnAiSEciCCeCNICNZ+qB
        3Jnk54Bdeuq44oM77yvyTvL5rGKXXrwv8g6CkzmRfE6LuCksMqMjuH2ueQSL23OVGUEA+sLnzYmlaGJW
        EIBHTnxuKZagmZlBQK78arkls4Pkyq+WWyKD4Pv6pbPPZAlZflO5JTLIKLUrvZbfXG6JVxBo+TGpGc8g
        2HpUr30ruQd3oxz2Q2cQBMFfY1luqmFCmgNjDd8AAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnReset.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAALGPC/xhBQAAAoJJREFUaEPt
        mutNxTAMhe8IjMAIbAAbwAawAWwAG8AGsAFsABvABrABbAD+flSyTp2mj6Q3RfdIRyq3iR+N7dQpuwP+
        MY6M58Zb46vx0/grfDdyjzGMZU4zuDQ+G9XosWQuMnK4MvJwINfFcG2MnvpcfhuHHPK6uF6MM2NJB5TI
        RodCxy3CvVEFen4Zn4wXRow5MXrwN/cejB/GSEZHdHno/VkgKUlUFQZ/jBimRo/BsZG5yIhko7MrCHpv
        MjAw5QRPH2OWAmNxKNLROaO/T0JqJQihOSuQAzKjkItsmIRIAIq65a4BZL8YVa9yNKKlJpTWArpUv+co
        UHF0IitRC91mpzqHOAoqlJyoGU5z9qUsbow6qUZie/CgVGeOWejTWSMvCK2pzgwCgX4wG1WJfWJ1aNmj
        cm0O0e5ZOzeqgJc57wQxu0ncGb0ja25+RfFm9I6wQpuElt2oudkEvBOw1UTP9u7qSKvwkcN1D94J2Cqy
        duqAFkMLm9TOHvRdp8Vk1/YibC22UH5108bmHrawIWrXiM09qLdhRdgzfMWCYdS0/tIYJXqya235NV4P
        Q7A1CW2sYAuNFTaoXeGu7qFluIWk1yQf1WLs4/BhCFFuZFejg64K1aLmcVAK6NRKNanhiw7oOD5dG9GR
        7eQ3jujI9NG4FtCl+mdX0dTJeM0wQ3bq8Hw2EBo5Q9zWKADI1JyA2LD44aWcgSx/iX0GGVEowSJOdBhy
        BvK9b84KMWfou2RRJzyiAuBJWPBk+WeAU6N3jmt+4x5johDyrP56RPnTfaYkkb1qU8fuWtIhZI3esWsA
        5WO++aXI3L06oCApaXTo2mhBo9UiebnHGMZWSeQD9o/d7g+khi7SA5xU/QAAAABJRU5ErkJggg==
</value>
  </data>
</root>