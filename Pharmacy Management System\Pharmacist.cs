﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy_Management_System
{
    public partial class Pharmacist : Form
    {
        private bool dragging = false;
        private Point startPoint = new Point(0, 0);

        // معلومات الموظف الحالي
        public string CurrentUsername { get; set; }
        public string CurrentEmployeeName { get; set; }

        Function fn = new Function();
        string query;

        public Pharmacist()
        {
            InitializeComponent();
        }

        public Pharmacist(string username, string employeeName)
        {
            InitializeComponent();
            CurrentUsername = username;
            CurrentEmployeeName = employeeName;

            // تحسين إعدادات النموذج للتكبير
            this.WindowState = FormWindowState.Normal;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(1200, 800);

            // تسجيل دخول الموظف
            recordEmployeeLogin();

            // تطبيق التصميم العصري
            ApplyModernDesign();

            // الاشتراك في أحداث التغيير
            LanguageManager.LanguageChanged += OnLanguageChanged;
            ModernTheme.ThemeChanged += OnThemeChanged;

            // تطبيق اللغة الحالية
            ApplyLanguage();
        }

        private void btnLogOut_Click(object sender, EventArgs e)
        {
            // تأكيد تسجيل الخروج
            DialogResult result = MessageBox.Show(LanguageManager.GetText("Are you sure you want to logout?"), LanguageManager.GetText("Confirm Logout"),
                                                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // تسجيل خروج الموظف
                recordEmployeeLogout();

                // حفظ كود الصيدلية الحالي قبل الخروج
                if (SessionManager.IsLoggedIn && !string.IsNullOrEmpty(SessionManager.CurrentPharmacyCode))
                {
                    PharmacyCodeLoginForm.SavePharmacyCode(SessionManager.CurrentPharmacyCode);
                }

                // العودة إلى واجهة تسجيل الدخول الموحدة
                PharmacyCodeLoginForm loginForm = new PharmacyCodeLoginForm();
                loginForm.Show();
                this.Hide();
            }
        }

        private void recordEmployeeLogin()
        {
            try
            {
                // إنشاء جدول جلسات الموظفين إذا لم يكن موجوداً
                query = @"IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'employee_sessions')
                         BEGIN
                             CREATE TABLE employee_sessions (
                                 id INT IDENTITY(1,1) PRIMARY KEY,
                                 username NVARCHAR(250),
                                 employeeName NVARCHAR(250),
                                 loginTime DATETIME DEFAULT GETDATE(),
                                 logoutTime DATETIME NULL,
                                 sessionDate DATE DEFAULT CONVERT(DATE, GETDATE()),
                                 pharmacy_id INT
                             )
                         END";
                fn.setData(query, "");

                // تسجيل دخول الموظف مع معالجة الأسماء العربية
                string safeUsername = CurrentUsername.Replace("'", "''");
                string safeEmployeeName = CurrentEmployeeName.Replace("'", "''");

                query = @"INSERT INTO employee_sessions (username, employeeName, loginTime, sessionDate, pharmacy_id)
                         VALUES (N'" + safeUsername + "', N'" + safeEmployeeName + "', GETDATE(), CONVERT(DATE, GETDATE()), " + SessionManager.CurrentPharmacyId + ")";
                fn.setData(query, "");
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ للتشخيص
                System.Diagnostics.Debug.WriteLine($"خطأ في تسجيل دخول الموظف: {ex.Message}");
            }
        }

        private void recordEmployeeLogout()
        {
            try
            {
                // تحديث وقت الخروج للجلسة الحالية مع معالجة الأسماء العربية
                string safeUsername = CurrentUsername.Replace("'", "''");

                query = @"UPDATE employee_sessions
                         SET logoutTime = GETDATE()
                         WHERE username = N'" + safeUsername + @"'
                           AND logoutTime IS NULL
                           AND sessionDate = CONVERT(DATE, GETDATE())
                           AND pharmacy_id = " + SessionManager.CurrentPharmacyId;
                fn.setData(query, "");
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ للتشخيص
                System.Diagnostics.Debug.WriteLine($"خطأ في تسجيل خروج الموظف: {ex.Message}");
            }
        }

        // دالة لإخفاء جميع UserControls
        private void HideAllUserControls()
        {
            if (uC_P_AddMedicine1 != null) uC_P_AddMedicine1.Visible = false;
            if (uC_P_UpdateMedicine1 != null) uC_P_UpdateMedicine1.Visible = false;
            if (uC_P_ViewMedicines1 != null) uC_P_ViewMedicines1.Visible = false;
            if (uC_P_MedicineValidityCheck1 != null) uC_P_MedicineValidityCheck1.Visible = false;
            if (uC_P_Dashbord1 != null) uC_P_Dashbord1.Visible = false;
            if (uC__P_SellMedicine1 != null) uC__P_SellMedicine1.Visible = false;
            if (uC_P_PharmacyStore1 != null) uC_P_PharmacyStore1.Visible = false;

        }

        private void btnDashbord_Click(object sender, EventArgs e)
        {
            HideAllUserControls();
            if (uC_P_Dashbord1 != null)
            {
                uC_P_Dashbord1.Visible = true;
                uC_P_Dashbord1.BringToFront();
            }
        }

        private void Pharmacist_Load(object sender, EventArgs e)
        {
            // إخفاء جميع UserControls في البداية
            HideAllUserControls();

            // عرض لوحة التحكم افتراضياً
            btnDashbord.PerformClick();

            // تحسين مظهر النموذج
            this.StartPosition = FormStartPosition.CenterScreen;
        }

        private void btnAddMedicine_Click(object sender, EventArgs e)
        {
            HideAllUserControls();
            if (uC_P_AddMedicine1 != null)
            {
                uC_P_AddMedicine1.Visible = true;
                uC_P_AddMedicine1.BringToFront();
            }
        }

        private void btnViewMedicine_Click(object sender, EventArgs e)
        {
            HideAllUserControls();
            if (uC_P_ViewMedicines1 != null)
            {
                uC_P_ViewMedicines1.Visible = true;
                uC_P_ViewMedicines1.BringToFront();
            }
        }

        private void btnModifyMedicine_Click(object sender, EventArgs e)
        {
            HideAllUserControls();
            if (uC_P_UpdateMedicine1 != null)
            {
                uC_P_UpdateMedicine1.Visible = true;
                uC_P_UpdateMedicine1.BringToFront();
            }
        }

        private void dashbord1_Load(object sender, EventArgs e)
        {

        }

        private void btnMedValidityCheck_Click(object sender, EventArgs e)
        {
            HideAllUserControls();
            if (uC_P_MedicineValidityCheck1 != null)
            {
                uC_P_MedicineValidityCheck1.Visible = true;
                uC_P_MedicineValidityCheck1.BringToFront();
            }
        }

        private void btnSellMedicine_Click(object sender, EventArgs e)
        {
            HideAllUserControls();

            if (uC__P_SellMedicine1 != null)
            {
                // تمرير معلومات الموظف إلى صفحة البيع
                uC__P_SellMedicine1.CurrentUsername = CurrentUsername;
                uC__P_SellMedicine1.CurrentEmployeeName = CurrentEmployeeName;

                uC__P_SellMedicine1.Visible = true;
                uC__P_SellMedicine1.BringToFront();
            }
        }

        private void btnOnlineNetwork_Click(object sender, EventArgs e)
        {
            HideAllUserControls();

            if (uC_P_PharmacyStore1 != null)
            {
                uC_P_PharmacyStore1.Visible = true;
                uC_P_PharmacyStore1.BringToFront();
            }
        }



        private void uC__P_SellMedicine1_Load(object sender, EventArgs e)
        {

        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            // تأكيد الخروج من التطبيق
            DialogResult result = MessageBox.Show(LanguageManager.GetText("Are you sure you want to exit?"), LanguageManager.GetText("Confirm Exit"),
                                                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // تسجيل خروج الموظف قبل إغلاق التطبيق
                recordEmployeeLogout();

                // حفظ كود الصيدلية الحالي قبل الخروج
                if (SessionManager.IsLoggedIn && !string.IsNullOrEmpty(SessionManager.CurrentPharmacyCode))
                {
                    PharmacyCodeLoginForm.SavePharmacyCode(SessionManager.CurrentPharmacyCode);
                }

                Application.Exit();
            }
        }

        private void guna2Button1_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void guna2Panel2_MouseDown(object sender, MouseEventArgs e)
        {
            dragging = true;
            startPoint = new Point(e.X, e.Y);
        }

        private void guna2Panel2_MouseMove(object sender, MouseEventArgs e)
        {
            if (dragging)
            {
                Point p = PointToScreen(e.Location);
                this.Location = new Point(p.X - startPoint.X, p.Y - startPoint.Y);
            }
        }

        private void guna2Panel2_MouseUp(object sender, MouseEventArgs e)
        {
            dragging = false;
        }

        // معالج إغلاق النموذج لحفظ تسجيل الخروج
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // تسجيل خروج الموظف عند إغلاق التطبيق من زر X
            recordEmployeeLogout();
            base.OnFormClosing(e);
        }

        private void OnLanguageChanged(object sender, EventArgs e)
        {
            ApplyLanguage();
        }

        private void ApplyLanguage()
        {
            // تطبيق الترجمات على أزرار القائمة
            btnDashbord.Text = LanguageManager.GetText("Dashboard");
            btnAddMedicine.Text = LanguageManager.GetText("Add Medicine");
            btnViewMedicine.Text = LanguageManager.GetText("View Medicine");
            btnModifyMedicine.Text = LanguageManager.GetText("Medicine Validation");
            btnSellMedicine.Text = LanguageManager.GetText("Sell Medicine");
            btnOnlineNetwork.Text = LanguageManager.GetText("Pharmacy Store");

            btnLogOut.Text = LanguageManager.GetText("Logout");

            // تطبيق اللغة على UserControls المحملة
            ApplyLanguageToUserControls();

            // الحفاظ على التصميم من اليسار لليمين في جميع اللغات
            this.RightToLeft = RightToLeft.No;
            this.RightToLeftLayout = false;
        }

        private void ApplyLanguageToUserControls()
        {
            try
            {
                // تطبيق اللغة على جميع UserControls المحملة
                if (uC_P_Dashbord1 != null && uC_P_Dashbord1.Visible)
                {
                    uC_P_Dashbord1.ApplyLanguage();
                }
                if (uC_P_AddMedicine1 != null && uC_P_AddMedicine1.Visible)
                {
                    uC_P_AddMedicine1.ApplyLanguage();
                }
                if (uC_P_ViewMedicines1 != null && uC_P_ViewMedicines1.Visible)
                {
                    uC_P_ViewMedicines1.ApplyLanguage();
                }
                if (uC_P_MedicineValidityCheck1 != null && uC_P_MedicineValidityCheck1.Visible)
                {
                    uC_P_MedicineValidityCheck1.ApplyLanguage();
                }
                if (uC__P_SellMedicine1 != null && uC__P_SellMedicine1.Visible)
                {
                    uC__P_SellMedicine1.ApplyLanguage();
                }
                if (uC_P_PharmacyStore1 != null && uC_P_PharmacyStore1.Visible)
                {
                    // تطبيق اللغة على صفحة متجر الصيدلية
                    // سيتم تطبيق اللغة تلقائياً من خلال الأحداث
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine(string.Format("خطأ في تطبيق اللغة على UserControls: {0}", ex.Message));
            }
        }

        private void ApplyModernDesign()
        {
            // تطبيق الوضع الحالي
            ModernTheme.ApplyThemeToForm(this);

            // تحسين شريط العنوان
            if (panel1 != null)
            {
                panel1.BackColor = ModernTheme.Colors.Primary;
            }

            // تحسين القائمة الجانبية
            if (panel2 != null)
            {
                panel2.BackColor = ModernTheme.Colors.Surface;
            }

            // تحسين الأزرار في القائمة الجانبية
            ApplyModernSidebarButtons();

            // إضافة زر الوضع الليلي
            CreateDarkModeButton();
        }

        private void CreateDarkModeButton()
        {
            // إنشاء زر الوضع الليلي في الشريط العلوي guna2Panel2
            var topPanel = this.Controls.Find("guna2Panel2", true).FirstOrDefault();
            if (topPanel != null && topPanel.Controls.Find("btnDarkMode", false).Length == 0)
            {
                Guna.UI2.WinForms.Guna2Button btnDarkMode = new Guna.UI2.WinForms.Guna2Button();
                btnDarkMode.Name = "btnDarkMode";
                btnDarkMode.Text = ModernTheme.IsDarkMode ? "🌞 فاتح" : "🌙 ليلي";
                btnDarkMode.Size = new Size(80, 25);
                btnDarkMode.Location = new Point(topPanel.Width - 90, 3);
                btnDarkMode.Anchor = AnchorStyles.Top | AnchorStyles.Right;
                btnDarkMode.BorderRadius = 8;
                btnDarkMode.Font = new Font("Segoe UI", 8F);
                btnDarkMode.Cursor = Cursors.Hand;
                btnDarkMode.FillColor = ModernTheme.Colors.Secondary;
                btnDarkMode.ForeColor = ModernTheme.Colors.TextOnPrimary;
                btnDarkMode.HoverState.FillColor = ModernTheme.Colors.SecondaryDark;
                btnDarkMode.Click += (s, e) => ModernTheme.ToggleDarkMode();

                topPanel.Controls.Add(btnDarkMode);
                btnDarkMode.BringToFront();
            }
        }

        private void OnThemeChanged(object sender, EventArgs e)
        {
            ApplyModernDesign();
            ApplyLanguage();
        }

        private void ApplyModernSidebarButtons()
        {
            // تحسين أزرار القائمة الجانبية
            ApplyModernButtonStyle(btnDashbord);
            ApplyModernButtonStyle(btnAddMedicine);
            ApplyModernButtonStyle(btnViewMedicine);
            ApplyModernButtonStyle(btnModifyMedicine);
            ApplyModernButtonStyle(btnSellMedicine);
            ApplyModernButtonStyle(btnOnlineNetwork);

            // زر تسجيل الخروج بلون مختلف
            if (btnLogOut != null)
            {
                btnLogOut.FillColor = Color.Transparent;
                btnLogOut.ForeColor = Color.FromArgb(220, 53, 69);
                btnLogOut.Font = new Font("Segoe UI", 11F);
                btnLogOut.TextAlign = HorizontalAlignment.Left;
                btnLogOut.Cursor = Cursors.Hand;
                btnLogOut.HoverState.FillColor = Color.FromArgb(30, 220, 53, 69);
                btnLogOut.BorderThickness = 0;
            }

            // تحسين تسمية اسم المستخدم (إذا كانت موجودة)
            // userNameLabel غير موجود في هذا التصميم
        }

        private void ApplyModernButtonStyle(Guna.UI2.WinForms.Guna2Button button)
        {
            if (button != null)
            {
                button.FillColor = Color.Transparent;
                button.ForeColor = ModernTheme.Colors.TextPrimary;
                button.Font = new Font("Segoe UI", 11F);
                button.TextAlign = HorizontalAlignment.Left;
                button.Cursor = Cursors.Hand;
                button.HoverState.FillColor = ModernTheme.Colors.PrimaryLight;
                button.BorderThickness = 0;
            }
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            // إلغاء الاشتراك في حدث تغيير اللغة
            LanguageManager.LanguageChanged -= OnLanguageChanged;
            base.OnFormClosed(e);
        }
    }
}
