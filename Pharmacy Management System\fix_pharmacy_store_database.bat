@echo off
chcp 65001 >nul
echo ===============================================
echo إصلاح قاعدة البيانات لمتجر الصيدلية
echo Fix Pharmacy Store Database
echo ===============================================
echo.

echo 🔍 فحص حالة قاعدة البيانات الحالية...
echo Checking current database status...
sqlcmd -S NARUTO -E -i "check_pharmacy_store_database.sql"

echo.
echo 🔧 تطبيق إصلاحات قاعدة البيانات...
echo Applying database fixes...
sqlcmd -S NARUTO -E -i "fix_pharmacy_store_requests_database.sql"

echo.
echo ✅ تم إكمال إصلاح قاعدة البيانات
echo Database fix completed successfully
echo.

echo 📊 إدراج بيانات تجريبية للاختبار...
echo Inserting test data...
sqlcmd -S NARUTO -E -i "insert_test_data_pharmacy_store.sql"

echo.
echo 🔍 فحص حالة قاعدة البيانات بعد الإصلاح...
echo Checking database status after fix...
sqlcmd -S NARUTO -E -i "check_pharmacy_store_database.sql"

echo.
echo ===============================================
echo تم إكمال جميع العمليات بنجاح
echo All operations completed successfully
echo ===============================================
echo.
echo 💡 ملاحظة: إذا كانت طلبات الأدوية لا تزال لا تظهر،
echo    تأكد من أن SessionManager.CurrentPharmacyId يحتوي على قيمة صحيحة
echo    (يجب أن تكون 1، 2، أو 3 للبيانات التجريبية)
echo.
pause
