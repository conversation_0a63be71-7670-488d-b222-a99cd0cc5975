using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Windows.Forms;

namespace Pharmacy_Management_System
{
    /// <summary>
    /// كلاس للعمل مع قاعدة البيانات الموحدة UnifiedPharmacy
    /// </summary>
    public class UnifiedPharmacyFunction
    {
        private string connectionString;

        public UnifiedPharmacyFunction()
        {
            // سلسلة الاتصال بقاعدة البيانات الموحدة
            connectionString = @"Data Source=NARUTO;Initial Catalog=UnifiedPharmacy;Integrated Security=True";
        }

        #region Database Connection Methods

        /// <summary>
        /// تنفيذ استعلام مع معاملات وإرجاع DataSet
        /// </summary>
        public DataSet ExecuteQuery(string query, Dictionary<string, object> parameters = null)
        {
            DataSet dataSet = new DataSet();
            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        // إضافة المعاملات إذا وجدت
                        if (parameters != null)
                        {
                            foreach (var param in parameters)
                            {
                                command.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                            }
                        }

                        using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                        {
                            connection.Open();
                            adapter.Fill(dataSet);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine("خطأ في تنفيذ الاستعلام: " + ex.Message);
                // إزالة MessageBox لتجنب ظهور رسائل خطأ غير مرغوب فيها
                // MessageBox.Show($"خطأ في قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            return dataSet;
        }

        /// <summary>
        /// تنفيذ استعلام بدون إرجاع بيانات (INSERT, UPDATE, DELETE)
        /// </summary>
        public bool ExecuteNonQuery(string query, Dictionary<string, object> parameters = null)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        // إضافة المعاملات إذا وجدت
                        if (parameters != null)
                        {
                            foreach (var param in parameters)
                            {
                                command.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                            }
                        }

                        connection.Open();
                        int rowsAffected = command.ExecuteNonQuery();
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine("خطأ في تنفيذ الاستعلام: " + ex.Message);
                MessageBox.Show("خطأ في قاعدة البيانات: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// التأكد من وجود صيدلية افتراضية وإنشاؤها إذا لم تكن موجودة
        /// </summary>
        private int EnsureDefaultPharmacyExists()
        {
            try
            {
                // البحث عن صيدلية افتراضية
                string checkQuery = "SELECT id FROM pharmacies WHERE pharmacyCode = 'DEFAULT'";
                DataSet result = ExecuteQuery(checkQuery);

                if (result.Tables[0].Rows.Count > 0)
                {
                    return Convert.ToInt32(result.Tables[0].Rows[0]["id"]);
                }

                // إنشاء صيدلية افتراضية جديدة
                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyName", "صيدلية افتراضية"},
                    {"@pharmacyCode", "DEFAULT"},
                    {"@ownerName", "مدير النظام"},
                    {"@address", "عنوان افتراضي"},
                    {"@city", "المدينة"},
                    {"@phone", "0000000000"}
                };

                string insertQuery = @"
                    INSERT INTO pharmacies (pharmacyName, pharmacyCode, ownerName, address, city, phone, isActive)
                    VALUES (@pharmacyName, @pharmacyCode, @ownerName, @address, @city, @phone, 1);
                    SELECT SCOPE_IDENTITY();";

                DataSet insertResult = ExecuteQuery(insertQuery, parameters);
                if (insertResult.Tables[0].Rows.Count > 0)
                {
                    return Convert.ToInt32(insertResult.Tables[0].Rows[0][0]);
                }

                return 1; // إرجاع ID افتراضي
            }
            catch
            {
                return 1; // إرجاع ID افتراضي في حالة الخطأ
            }
        }

        /// <summary>
        /// التأكد من وجود جدول pharmacies
        /// </summary>
        private void EnsurePharmaciesTableExists()
        {
            try
            {
                string createTableQuery = @"
                    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies' AND xtype='U')
                    BEGIN
                        CREATE TABLE pharmacies (
                            id int IDENTITY(1,1) PRIMARY KEY,
                            pharmacyName nvarchar(255) NOT NULL,
                            pharmacyCode nvarchar(50) UNIQUE NOT NULL,
                            ownerName nvarchar(255) NULL,
                            address nvarchar(500) NULL,
                            city nvarchar(100) NULL,
                            phone nvarchar(20) NULL,
                            email nvarchar(255) NULL,
                            isActive bit DEFAULT 1,
                            subscriptionType nvarchar(50) DEFAULT 'Basic',
                            registrationDate datetime DEFAULT GETDATE()
                        )
                    END";

                ExecuteQuery(createTableQuery);
            }
            catch (Exception ex)
            {
                Debug.WriteLine("خطأ في إنشاء جدول pharmacies: " + ex.Message);
            }
        }

        /// <summary>
        /// التأكد من وجود عمود isActive في جدول users
        /// </summary>
        private void EnsureUsersTableStructure()
        {
            try
            {
                // التحقق من وجود عمود isActive
                string checkIsActiveQuery = @"
                    SELECT COUNT(*)
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'isActive'";

                DataSet checkResult = ExecuteQuery(checkIsActiveQuery);
                bool hasIsActiveColumn = checkResult.Tables.Count > 0 &&
                                       checkResult.Tables[0].Rows.Count > 0 &&
                                       Convert.ToInt32(checkResult.Tables[0].Rows[0][0]) > 0;

                if (!hasIsActiveColumn)
                {
                    string addIsActiveQuery = @"
                        ALTER TABLE users
                        ADD isActive bit NOT NULL DEFAULT 1";

                    ExecuteQuery(addIsActiveQuery);
                }

                // التحقق من وجود عمود pharmacyId
                string checkPharmacyIdQuery = @"
                    SELECT COUNT(*)
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'pharmacyId'";

                DataSet checkPharmacyIdResult = ExecuteQuery(checkPharmacyIdQuery);
                bool hasPharmacyIdColumn = checkPharmacyIdResult.Tables.Count > 0 &&
                                         checkPharmacyIdResult.Tables[0].Rows.Count > 0 &&
                                         Convert.ToInt32(checkPharmacyIdResult.Tables[0].Rows[0][0]) > 0;

                if (!hasPharmacyIdColumn)
                {
                    int defaultPharmacyId = EnsureDefaultPharmacyExists();
                    string addPharmacyIdQuery = $@"
                        ALTER TABLE users
                        ADD pharmacyId int NULL DEFAULT {defaultPharmacyId}";

                    ExecuteQuery(addPharmacyIdQuery);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine("خطأ في تحديث بنية جدول users: " + ex.Message);
            }
        }

        #endregion

        #region Pharmacy Management Methods

        /// <summary>
        /// الحصول على بيانات الصيدلية بالكود
        /// </summary>
        public DataSet GetPharmacyByCode(string pharmacyCode)
        {
            try
            {
                Debug.WriteLine("البحث عن الصيدلية بالكود: " + pharmacyCode);

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyCode", pharmacyCode}
                };

                string query = @"
                    SELECT id, pharmacyCode, pharmacyName, ownerName,
                           phone, address, city, email, isActive,
                           registrationDate, lastOnline
                    FROM pharmacies
                    WHERE pharmacyCode = @pharmacyCode";

                return ExecuteQuery(query, parameters);
            }
            catch (Exception ex)
            {
                Debug.WriteLine("خطأ في البحث عن الصيدلية: " + ex.Message);
                return new DataSet();
            }
        }

        #endregion

        #region Authentication Methods

        /// <summary>
        /// التحقق من صحة بيانات تسجيل الدخول
        /// </summary>
        public DataSet ValidateLogin(string username, string password)
        {
            try
            {
                Debug.WriteLine("محاولة تسجيل الدخول: " + username);

                // التأكد من وجود جدول pharmacies وصيدلية افتراضية
                int defaultPharmacyId = EnsureDefaultPharmacyExists();

                // التأكد من بنية جدول users
                EnsureUsersTableStructure();

                var parameters = new Dictionary<string, object>
                {
                    {"@username", username},
                    {"@password", password}
                };

                // استعلام بسيط وآمن
                parameters.Add("@defaultPharmacyId", defaultPharmacyId);

                string query = @"
                    SELECT
                        u.id,
                        u.username,
                        u.name,
                        u.userRole,
                        ISNULL(u.email, '') as email,
                        ISNULL(u.mobile, '') as mobile,
                        ISNULL(u.pharmacyId, @defaultPharmacyId) as pharmacyId,
                        ISNULL(p.pharmacyName, 'صيدلية افتراضية') as pharmacyName,
                        ISNULL(p.pharmacyCode, 'DEFAULT') as pharmacyCode,
                        ISNULL(p.ownerName, '') as ownerName,
                        ISNULL(p.address, '') as address,
                        ISNULL(p.city, '') as city,
                        ISNULL(p.phone, '') as pharmacyPhone
                    FROM users u
                    LEFT JOIN pharmacies p ON u.pharmacyId = p.id
                    WHERE u.username = @username
                      AND u.pass = @password";

                DataSet result = ExecuteQuery(query, parameters);
                Debug.WriteLine("نتيجة تسجيل الدخول: " + result.Tables[0].Rows.Count + " صف");

                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine("خطأ في تسجيل الدخول: " + ex.Message);

                // في حالة الخطأ، جرب استعلام بسيط
                try
                {
                    var simpleParameters = new Dictionary<string, object>
                    {
                        {"@username", username},
                        {"@password", password}
                    };

                    string simpleQuery = @"
                        SELECT
                            u.id,
                            u.username,
                            u.name,
                            u.userRole,
                            ISNULL(u.email, '') as email,
                            ISNULL(u.mobile, '') as mobile,
                            1 as pharmacyId,
                            'صيدلية افتراضية' as pharmacyName,
                            'DEFAULT' as pharmacyCode,
                            '' as ownerName,
                            '' as address,
                            '' as city,
                            '' as pharmacyPhone
                        FROM users u
                        WHERE u.username = @username
                          AND u.pass = @password";

                    return ExecuteQuery(simpleQuery, simpleParameters);
                }
                catch
                {
                    return new DataSet();
                }
            }
        }

        /// <summary>
        /// التحقق من صحة بيانات تسجيل الدخول مع تحديد الصيدلية
        /// </summary>
        public DataSet ValidateLoginWithPharmacy(string username, string password, int pharmacyId)
        {
            try
            {
                Debug.WriteLine("محاولة تسجيل الدخول للصيدلية " + pharmacyId + ": " + username);

                var parameters = new Dictionary<string, object>
                {
                    {"@username", username},
                    {"@password", password},
                    {"@pharmacyId", pharmacyId}
                };

                string query = @"
                    SELECT
                        u.id,
                        u.username,
                        u.name,
                        u.userRole,
                        ISNULL(u.email, '') as email,
                        ISNULL(u.mobile, '') as mobile,
                        u.pharmacyId,
                        p.pharmacyName,
                        p.pharmacyCode,
                        p.ownerName,
                        p.address,
                        p.city,
                        p.phone as pharmacyPhone
                    FROM users u
                    INNER JOIN pharmacies p ON u.pharmacyId = p.id
                    WHERE u.username = @username
                      AND u.pass = @password
                      AND u.pharmacyId = @pharmacyId
                      AND u.isActive = 1
                      AND p.isActive = 1";

                DataSet result = ExecuteQuery(query, parameters);
                Debug.WriteLine("نتيجة تسجيل الدخول للصيدلية: " + result.Tables[0].Rows.Count + " صف");

                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine("خطأ في تسجيل الدخول للصيدلية: " + ex.Message);
                return new DataSet();
            }
        }

        /// <summary>
        /// إنشاء حساب مستخدم جديد
        /// </summary>
        public bool CreateUser(int pharmacyId, string userRole, string name, DateTime dob,
                              long mobile, string email, string username, string password)
        {
            try
            {
                // التحقق من عدم وجود اسم المستخدم مسبقاً
                if (IsUsernameExists(username))
                {
                    MessageBox.Show("اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر", "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

                // التأكد من وجود الصيدلية أو إنشاء صيدلية افتراضية
                if (pharmacyId <= 0)
                {
                    pharmacyId = EnsureDefaultPharmacyExists();
                }

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", pharmacyId},
                    {"@userRole", userRole},
                    {"@name", name},
                    {"@dob", dob},
                    {"@mobile", mobile},
                    {"@email", email},
                    {"@username", username},
                    {"@password", password}
                };

                string query = @"
                    INSERT INTO users (pharmacyId, userRole, name, dob, mobile, email, username, pass, isActive)
                    VALUES (@pharmacyId, @userRole, @name, @dob, @mobile, @email, @username, @password, 1)";

                bool success = ExecuteNonQuery(query, parameters);
                
                if (success)
                {
                    Debug.WriteLine("تم إنشاء المستخدم بنجاح: " + username);
                }

                return success;
            }
            catch (Exception ex)
            {
                Debug.WriteLine("خطأ في إنشاء المستخدم: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود اسم المستخدم
        /// </summary>
        public bool IsUsernameExists(string username)
        {
            var parameters = new Dictionary<string, object>
            {
                {"@username", username}
            };

            string query = "SELECT COUNT(*) as count FROM users WHERE username = @username";
            DataSet result = ExecuteQuery(query, parameters);

            if (result.Tables.Count > 0 && result.Tables[0].Rows.Count > 0)
            {
                int count = Convert.ToInt32(result.Tables[0].Rows[0]["count"]);
                return count > 0;
            }

            return false;
        }

        #endregion

        #region Pharmacy Methods

        /// <summary>
        /// الحصول على جميع الصيدليات النشطة
        /// </summary>
        public DataSet GetActivePharmacies()
        {
            string query = @"
                SELECT id, pharmacyCode, pharmacyName, ownerName, address, city, region, phone, email
                FROM pharmacies 
                WHERE isActive = 1 
                ORDER BY pharmacyName";

            return ExecuteQuery(query);
        }

        /// <summary>
        /// إنشاء صيدلية جديدة
        /// </summary>
        public bool CreatePharmacy(string pharmacyCode, string pharmacyName, string ownerName,
                                 string licenseNumber, string address, string city, string region,
                                 string phone, string email)
        {
            try
            {
                // التحقق من عدم وجود رمز الصيدلية مسبقاً
                if (IsPharmacyCodeExists(pharmacyCode))
                {
                    MessageBox.Show("رمز الصيدلية موجود بالفعل، يرجى اختيار رمز آخر", "خطأ", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyCode", pharmacyCode},
                    {"@pharmacyName", pharmacyName},
                    {"@ownerName", ownerName},
                    {"@licenseNumber", licenseNumber},
                    {"@address", address},
                    {"@city", city},
                    {"@region", region},
                    {"@phone", phone},
                    {"@email", email}
                };

                string query = @"
                    INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, licenseNumber, 
                                          address, city, region, phone, email, isActive)
                    VALUES (@pharmacyCode, @pharmacyName, @ownerName, @licenseNumber, 
                           @address, @city, @region, @phone, @email, 1)";

                return ExecuteNonQuery(query, parameters);
            }
            catch (Exception ex)
            {
                Debug.WriteLine("خطأ في إنشاء الصيدلية: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود رمز الصيدلية
        /// </summary>
        public bool IsPharmacyCodeExists(string pharmacyCode)
        {
            var parameters = new Dictionary<string, object>
            {
                {"@pharmacyCode", pharmacyCode}
            };

            string query = "SELECT COUNT(*) as count FROM pharmacies WHERE pharmacyCode = @pharmacyCode";
            DataSet result = ExecuteQuery(query, parameters);

            if (result.Tables.Count > 0 && result.Tables[0].Rows.Count > 0)
            {
                int count = Convert.ToInt32(result.Tables[0].Rows[0]["count"]);
                return count > 0;
            }

            return false;
        }

        #endregion

        #region Session Management

        /// <summary>
        /// تسجيل جلسة دخول جديدة
        /// </summary>
        public bool RecordLoginSession(int pharmacyId, int userId, string username, string employeeName)
        {
            try
            {
                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", pharmacyId},
                    {"@userId", userId},
                    {"@username", username},
                    {"@employeeName", employeeName},
                    {"@loginTime", DateTime.Now},
                    {"@sessionDate", DateTime.Now.Date}
                };

                string query = @"
                    INSERT INTO employee_sessions (pharmacyId, userId, username, employeeName, loginTime, sessionDate)
                    VALUES (@pharmacyId, @userId, @username, @employeeName, @loginTime, @sessionDate)";

                return ExecuteNonQuery(query, parameters);
            }
            catch (Exception ex)
            {
                Debug.WriteLine("خطأ في تسجيل جلسة الدخول: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تسجيل خروج المستخدم
        /// </summary>
        public bool RecordLogoutSession(int userId)
        {
            try
            {
                var parameters = new Dictionary<string, object>
                {
                    {"@userId", userId},
                    {"@logoutTime", DateTime.Now}
                };

                string query = @"
                    UPDATE employee_sessions 
                    SET logoutTime = @logoutTime 
                    WHERE userId = @userId AND logoutTime IS NULL";

                return ExecuteNonQuery(query, parameters);
            }
            catch (Exception ex)
            {
                Debug.WriteLine("خطأ في تسجيل الخروج: " + ex.Message);
                return false;
            }
        }

        #endregion

        #region Messaging Methods

        /// <summary>
        /// إرسال رسالة بين الصيدليات
        /// </summary>
        public bool SendPharmacyMessage(int senderPharmacyId, int receiverPharmacyId, string subject, string messageContent)
        {
            try
            {
                var parameters = new Dictionary<string, object>
                {
                    {"@senderPharmacyId", senderPharmacyId},
                    {"@receiverPharmacyId", receiverPharmacyId},
                    {"@subject", subject},
                    {"@messageContent", messageContent},
                    {"@sentDate", DateTime.Now}
                };

                // استخدام أسماء الأعمدة الصحيحة من قاعدة البيانات
                string query = @"
                    INSERT INTO pharmacy_messages (sender_pharmacy_id, receiver_pharmacy_id, subject, message_content, sent_date, is_read)
                    VALUES (@senderPharmacyId, @receiverPharmacyId, @subject, @messageContent, @sentDate, 0)";

                bool success = ExecuteNonQuery(query, parameters);
                Debug.WriteLine("إرسال رسالة: " + (success ? "نجح" : "فشل") + " - من " + senderPharmacyId + " إلى " + receiverPharmacyId);

                // إنشاء إشعار للصيدلية المستقبلة
                if (success)
                {
                    CreateMessageNotification(receiverPharmacyId, senderPharmacyId, subject);
                }

                return success;
            }
            catch (Exception ex)
            {
                Debug.WriteLine("خطأ في إرسال الرسالة: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// الحصول على الرسائل بين صيدليتين
        /// </summary>
        public DataSet GetPharmacyMessages(int pharmacy1Id, int pharmacy2Id)
        {
            try
            {
                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacy1Id", pharmacy1Id},
                    {"@pharmacy2Id", pharmacy2Id}
                };

                // استخدام أسماء الأعمدة الصحيحة من قاعدة البيانات
                string query = @"
                    SELECT
                        pm.id,
                        pm.sender_pharmacy_id,
                        pm.receiver_pharmacy_id,
                        ISNULL(pm.subject, 'رسالة عامة') as subject,
                        pm.message_content,
                        pm.sent_date,
                        pm.is_read,
                        p1.pharmacyName as sender_pharmacy_name,
                        p2.pharmacyName as receiver_pharmacy_name
                    FROM pharmacy_messages pm
                    LEFT JOIN pharmacies p1 ON pm.sender_pharmacy_id = p1.id
                    LEFT JOIN pharmacies p2 ON pm.receiver_pharmacy_id = p2.id
                    WHERE (pm.sender_pharmacy_id = @pharmacy1Id AND pm.receiver_pharmacy_id = @pharmacy2Id)
                       OR (pm.sender_pharmacy_id = @pharmacy2Id AND pm.receiver_pharmacy_id = @pharmacy1Id)
                    ORDER BY pm.sent_date ASC";

                DataSet result = ExecuteQuery(query, parameters);
                Debug.WriteLine("تم تحميل " + result.Tables[0].Rows.Count + " رسالة بين الصيدليات " + pharmacy1Id + " و " + pharmacy2Id);
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine("خطأ في الحصول على الرسائل: " + ex.Message);
                return new DataSet();
            }
        }

        /// <summary>
        /// إنشاء إشعار رسالة
        /// </summary>
        private bool CreateMessageNotification(int targetPharmacyId, int senderPharmacyId, string subject)
        {
            try
            {
                // الحصول على اسم الصيدلية المرسلة
                var senderParameters = new Dictionary<string, object>
                {
                    {"@senderPharmacyId", senderPharmacyId}
                };

                string senderQuery = "SELECT pharmacyName FROM pharmacies WHERE id = @senderPharmacyId";
                DataSet senderResult = ExecuteQuery(senderQuery, senderParameters);

                string senderName = "صيدلية غير معروفة";
                if (senderResult.Tables.Count > 0 && senderResult.Tables[0].Rows.Count > 0)
                {
                    senderName = senderResult.Tables[0].Rows[0]["pharmacyName"].ToString();
                }

                // إنشاء الإشعار
                var parameters = new Dictionary<string, object>
                {
                    {"@pharmacyId", targetPharmacyId},
                    {"@notificationType", "message"},
                    {"@title", "رسالة جديدة من " + senderName},
                    {"@content", "الموضوع: " + subject},
                    {"@relatedId", senderPharmacyId}
                };

                string query = @"
                    INSERT INTO notifications (pharmacy_id, notification_type, title, content, related_id, created_date, is_read)
                    VALUES (@pharmacyId, @notificationType, @title, @content, @relatedId, GETDATE(), 0)";

                return ExecuteNonQuery(query, parameters);
            }
            catch (Exception ex)
            {
                Debug.WriteLine("خطأ في إنشاء إشعار الرسالة: " + ex.Message);
                return false;
            }
        }

        #endregion
    }
}
