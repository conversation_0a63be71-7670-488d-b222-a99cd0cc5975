using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Microsoft.Win32;

namespace Pharmacy_Management_System
{
    public static class ModernTheme
    {
        // متغير للوضع الحالي
        public static bool IsDarkMode { get; set; }

        // Static constructor لتهيئة القيم الافتراضية
        static ModernTheme()
        {
            IsDarkMode = false;
        }

        // خصائص الألوان المطلوبة للتوافق مع الكود الموجود
        public static Color BackgroundColor => IsDarkMode ? Colors.Dark.Background : Colors.Light.Background;
        public static Color AccentColor => IsDarkMode ? Colors.Dark.Secondary : Colors.Light.Secondary;
        public static Color SecondaryColor => IsDarkMode ? Colors.Dark.Secondary : Colors.Light.Secondary;
        public static Color PrimaryTextColor => IsDarkMode ? Colors.Dark.TextPrimary : Colors.Light.TextPrimary;
        public static Color SecondaryTextColor => IsDarkMode ? Colors.Dark.TextSecondary : Colors.Light.TextSecondary;
        public static Color CardBackgroundColor => IsDarkMode ? Colors.Dark.Surface : Colors.Light.Surface;
        public static Color PrimaryColor => IsDarkMode ? Colors.Dark.Primary : Colors.Light.Primary;
        public static Color BorderColor => IsDarkMode ? Colors.Dark.Border : Colors.Light.Border;
        public static Color InputBackgroundColor => IsDarkMode ? Colors.Dark.Surface : Colors.Light.Surface;
        public static Color InputBorderColor => IsDarkMode ? Colors.Dark.Border : Colors.Light.Border;
        public static Color SecondaryBackgroundColor => IsDarkMode ? Color.FromArgb(45, 45, 48) : Color.FromArgb(245, 245, 245);

        // خصائص إضافية للتوافق
        public static Color Colors_Primary => PrimaryColor;
        public static Color Colors_TextOnPrimary => IsDarkMode ? Colors.Dark.TextOnPrimary : Colors.Light.TextOnPrimary;
        public static Color Colors_PrimaryDark => IsDarkMode ? Colors.Dark.PrimaryDark : Colors.Light.PrimaryDark;
        public static Color Colors_Surface => CardBackgroundColor;
        public static Color Colors_PrimaryLight => IsDarkMode ? Colors.Dark.PrimaryLight : Colors.Light.PrimaryLight;
        public static Color Colors_TextPrimary => PrimaryTextColor;
        public static Color Colors_SurfaceVariant => IsDarkMode ? Colors.Dark.SurfaceVariant : Colors.Light.SurfaceVariant;

        // حدث تغيير التصميم
        public static event EventHandler ThemeChanged;

        // نظام الألوان المناسب للصيدلية
        public static class Colors
        {
            // ألوان الوضع الفاتح - ألوان جميلة للصيدلية
            public static class Light
            {
                public static readonly Color Primary = Color.FromArgb(0, 150, 136);       // تيل طبي جميل
                public static readonly Color PrimaryDark = Color.FromArgb(0, 121, 107);   // تيل داكن
                public static readonly Color PrimaryLight = Color.FromArgb(178, 223, 219); // تيل فاتح

                public static readonly Color Secondary = Color.FromArgb(63, 81, 181);     // بنفسجي أنيق
                public static readonly Color SecondaryDark = Color.FromArgb(48, 63, 159); // بنفسجي داكن
                public static readonly Color SecondaryLight = Color.FromArgb(197, 202, 233); // بنفسجي فاتح

                public static readonly Color Background = Color.FromArgb(248, 249, 250);   // خلفية ناعمة
                public static readonly Color Surface = Color.White;                        // سطح أبيض نقي
                public static readonly Color SurfaceVariant = Color.FromArgb(240, 242, 247); // سطح متغير ناعم

                public static readonly Color TextPrimary = Color.FromArgb(38, 50, 56);     // نص داكن أنيق
                public static readonly Color TextSecondary = Color.FromArgb(96, 125, 139); // نص ثانوي ناعم
                public static readonly Color TextOnPrimary = Color.White;                  // نص أبيض على التيل

                public static readonly Color Success = Color.FromArgb(76, 175, 80);        // أخضر نجاح
                public static readonly Color Warning = Color.FromArgb(255, 193, 7);        // أصفر ذهبي
                public static readonly Color Error = Color.FromArgb(244, 67, 54);          // أحمر أنيق
                public static readonly Color Info = Color.FromArgb(33, 150, 243);          // أزرق معلومات

                public static readonly Color Border = Color.FromArgb(224, 224, 224);       // حدود ناعمة
                public static readonly Color Shadow = Color.FromArgb(30, 0, 0, 0);         // ظل شفاف
            }

            // ألوان الوضع الليلي - ألوان أنيقة ومريحة للعين
            public static class Dark
            {
                public static readonly Color Primary = Color.FromArgb(128, 203, 196);      // تيل فاتح أنيق
                public static readonly Color PrimaryDark = Color.FromArgb(0, 150, 136);    // تيل متوسط
                public static readonly Color PrimaryLight = Color.FromArgb(178, 223, 219); // تيل فاتح جداً

                public static readonly Color Secondary = Color.FromArgb(121, 134, 203);    // بنفسجي فاتح
                public static readonly Color SecondaryDark = Color.FromArgb(63, 81, 181);  // بنفسجي متوسط
                public static readonly Color SecondaryLight = Color.FromArgb(197, 202, 233); // بنفسجي فاتح جداً

                public static readonly Color Background = Color.FromArgb(18, 18, 18);      // خلفية داكنة ناعمة
                public static readonly Color Surface = Color.FromArgb(30, 30, 30);         // سطح داكن أنيق
                public static readonly Color SurfaceVariant = Color.FromArgb(42, 42, 42);  // سطح متغير داكن

                public static readonly Color TextPrimary = Color.FromArgb(255, 255, 255);  // نص أبيض نقي
                public static readonly Color TextSecondary = Color.FromArgb(158, 158, 158); // نص رمادي ناعم
                public static readonly Color TextOnPrimary = Color.FromArgb(18, 18, 18);   // نص داكن على التيل

                public static readonly Color Success = Color.FromArgb(129, 199, 132);      // أخضر نجاح ناعم
                public static readonly Color Warning = Color.FromArgb(255, 193, 7);        // أصفر ذهبي
                public static readonly Color Error = Color.FromArgb(239, 154, 154);        // أحمر ناعم
                public static readonly Color Info = Color.FromArgb(144, 202, 249);         // أزرق معلومات ناعم

                public static readonly Color Border = Color.FromArgb(66, 66, 66);          // حدود داكنة ناعمة
                public static readonly Color Shadow = Color.FromArgb(50, 0, 0, 0);         // ظل شفاف
            }

            // خصائص ديناميكية تعتمد على الوضع الحالي
            public static Color Primary => IsDarkMode ? Dark.Primary : Light.Primary;
            public static Color PrimaryDark => IsDarkMode ? Dark.PrimaryDark : Light.PrimaryDark;
            public static Color PrimaryLight => IsDarkMode ? Dark.PrimaryLight : Light.PrimaryLight;
            public static Color Secondary => IsDarkMode ? Dark.Secondary : Light.Secondary;
            public static Color SecondaryDark => IsDarkMode ? Dark.SecondaryDark : Light.SecondaryDark;
            public static Color SecondaryLight => IsDarkMode ? Dark.SecondaryLight : Light.SecondaryLight;
            public static Color Background => IsDarkMode ? Dark.Background : Light.Background;
            public static Color Surface => IsDarkMode ? Dark.Surface : Light.Surface;
            public static Color SurfaceVariant => IsDarkMode ? Dark.SurfaceVariant : Light.SurfaceVariant;
            public static Color TextPrimary => IsDarkMode ? Dark.TextPrimary : Light.TextPrimary;
            public static Color TextSecondary => IsDarkMode ? Dark.TextSecondary : Light.TextSecondary;
            public static Color TextOnPrimary => IsDarkMode ? Dark.TextOnPrimary : Light.TextOnPrimary;
            public static Color Success => IsDarkMode ? Dark.Success : Light.Success;
            public static Color Warning => IsDarkMode ? Dark.Warning : Light.Warning;
            public static Color Error => IsDarkMode ? Dark.Error : Light.Error;
            public static Color Info => IsDarkMode ? Dark.Info : Light.Info;
            public static Color Border => IsDarkMode ? Dark.Border : Light.Border;
            public static Color Shadow => IsDarkMode ? Dark.Shadow : Light.Shadow;
        }
        
        // إعدادات الخطوط العصرية
        public static class Fonts
        {
            public static readonly Font Title = new Font("Segoe UI", 24F, FontStyle.Bold);
            public static readonly Font Heading = new Font("Segoe UI", 18F, FontStyle.Bold);
            public static readonly Font Subheading = new Font("Segoe UI", 14F, FontStyle.Bold);
            public static readonly Font Body = new Font("Segoe UI", 11F, FontStyle.Regular);
            public static readonly Font Caption = new Font("Segoe UI", 9F, FontStyle.Regular);
            public static readonly Font Button = new Font("Segoe UI", 10F, FontStyle.Bold);
        }
        
        // إعدادات الحدود والزوايا
        public static class Borders
        {
            public static readonly int CornerRadius = 8;
            public static readonly int LargeCornerRadius = 12;
            public static readonly int SmallCornerRadius = 4;
            public static readonly int BorderWidth = 1;
        }
        
        // إعدادات المسافات
        public static class Spacing
        {
            public static readonly int XSmall = 4;
            public static readonly int Small = 8;
            public static readonly int Medium = 16;
            public static readonly int Large = 24;
            public static readonly int XLarge = 32;
        }
        
        // تطبيق التصميم على الأزرار مع تأثيرات بصرية
        public static void ApplyModernButton(Button button, bool isPrimary = true)
        {
            button.FlatStyle = FlatStyle.Flat;
            button.Font = Fonts.Button;
            button.Size = new Size(120, 40);
            button.Cursor = Cursors.Hand;

            if (isPrimary)
            {
                button.BackColor = Colors.Primary;
                button.ForeColor = Colors.TextOnPrimary;
                button.FlatAppearance.BorderSize = 0;
                button.FlatAppearance.MouseOverBackColor = Colors.PrimaryDark;
                button.FlatAppearance.MouseDownBackColor = Colors.PrimaryDark;
            }
            else
            {
                button.BackColor = Colors.Surface;
                button.ForeColor = Colors.Primary;
                button.FlatAppearance.BorderColor = Colors.Primary;
                button.FlatAppearance.BorderSize = 1;
                button.FlatAppearance.MouseOverBackColor = Colors.PrimaryLight;
                button.FlatAppearance.MouseDownBackColor = Colors.PrimaryLight;
            }

            // إضافة تأثيرات hover متقدمة
            AddButtonHoverEffects(button, isPrimary);
        }

        // إضافة تأثيرات hover للأزرار
        private static void AddButtonHoverEffects(Button button, bool isPrimary)
        {
            Color originalBackColor = button.BackColor;
            Color originalForeColor = button.ForeColor;

            button.MouseEnter += (sender, e) =>
            {
                if (isPrimary)
                {
                    button.BackColor = Colors.PrimaryDark;
                    // تأثير رفع الزر
                    button.Location = new Point(button.Location.X, button.Location.Y - 1);
                }
                else
                {
                    button.BackColor = Colors.PrimaryLight;
                    button.ForeColor = Colors.TextPrimary;
                }
            };

            button.MouseLeave += (sender, e) =>
            {
                button.BackColor = originalBackColor;
                button.ForeColor = originalForeColor;
                // إعادة الزر لموقعه الأصلي
                if (isPrimary)
                {
                    button.Location = new Point(button.Location.X, button.Location.Y + 1);
                }
            };

            button.MouseDown += (sender, e) =>
            {
                if (isPrimary)
                {
                    button.BackColor = Color.FromArgb(40, 80, 160);
                    // تأثير الضغط
                    button.Location = new Point(button.Location.X, button.Location.Y + 1);
                }
            };

            button.MouseUp += (sender, e) =>
            {
                if (isPrimary)
                {
                    button.BackColor = Colors.PrimaryDark;
                    button.Location = new Point(button.Location.X, button.Location.Y - 1);
                }
            };
        }
        
        // تطبيق التصميم على النماذج
        public static void ApplyModernForm(Form form)
        {
            form.BackColor = Colors.Background;
            form.Font = Fonts.Body;
            form.ForeColor = Colors.TextPrimary;
        }
        
        // تطبيق التصميم على اللوحات
        public static void ApplyModernPanel(Panel panel, bool isCard = false)
        {
            if (isCard)
            {
                panel.BackColor = Colors.Surface;
                panel.Padding = new Padding(Spacing.Medium);
                // إضافة ظل (سيتم تنفيذه لاحقاً)
            }
            else
            {
                panel.BackColor = Colors.Background;
            }
        }
        
        // تطبيق التصميم على حقول النص
        public static void ApplyModernTextBox(TextBox textBox)
        {
            textBox.Font = Fonts.Body;
            textBox.BackColor = Colors.Surface;
            textBox.ForeColor = Colors.TextPrimary;
            textBox.BorderStyle = BorderStyle.FixedSingle;
            textBox.Height = 35;
        }
        
        // تطبيق التصميم على التسميات
        public static void ApplyModernLabel(Label label, bool isHeading = false)
        {
            if (isHeading)
            {
                label.Font = Fonts.Heading;
                label.ForeColor = Colors.TextPrimary;
            }
            else
            {
                label.Font = Fonts.Body;
                label.ForeColor = Colors.TextSecondary;
            }
        }
        
        // تطبيق التصميم على جداول البيانات
        public static void ApplyModernDataGridView(DataGridView dgv)
        {
            dgv.BackgroundColor = Colors.Surface;
            dgv.BorderStyle = BorderStyle.None;
            dgv.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgv.DefaultCellStyle.BackColor = Colors.Surface;
            dgv.DefaultCellStyle.ForeColor = Colors.TextPrimary;
            dgv.DefaultCellStyle.Font = Fonts.Body;
            dgv.DefaultCellStyle.SelectionBackColor = Colors.PrimaryLight;
            dgv.DefaultCellStyle.SelectionForeColor = Colors.TextPrimary;
            dgv.ColumnHeadersDefaultCellStyle.BackColor = Colors.SecondaryLight;
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Colors.TextPrimary;
            dgv.ColumnHeadersDefaultCellStyle.Font = Fonts.Subheading;
            dgv.EnableHeadersVisualStyles = false;
            dgv.GridColor = Colors.Border;
            dgv.RowHeadersVisible = false;
            dgv.AllowUserToAddRows = false;
            dgv.AllowUserToDeleteRows = false;
            dgv.ReadOnly = true;
            dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
        }
        
        // إنشاء بطاقة عصرية
        public static Panel CreateModernCard(int width = 300, int height = 200)
        {
            Panel card = new Panel
            {
                Size = new Size(width, height),
                BackColor = Colors.Surface,
                Padding = new Padding(Spacing.Medium)
            };
            
            return card;
        }
        
        // إنشاء زر عصري
        public static Button CreateModernButton(string text, bool isPrimary = true)
        {
            Button button = new Button
            {
                Text = text,
                Size = new Size(120, 40)
            };

            ApplyModernButton(button, isPrimary);
            return button;
        }

        // إضافة تأثير انتقال سلس للألوان
        public static void AddColorTransition(Control control, Color fromColor, Color toColor, int duration = 200)
        {
            Timer timer = new Timer();
            timer.Interval = 10;
            int steps = duration / timer.Interval;
            int currentStep = 0;

            int rStep = (toColor.R - fromColor.R) / steps;
            int gStep = (toColor.G - fromColor.G) / steps;
            int bStep = (toColor.B - fromColor.B) / steps;

            timer.Tick += (sender, e) =>
            {
                if (currentStep >= steps)
                {
                    control.BackColor = toColor;
                    timer.Stop();
                    timer.Dispose();
                    return;
                }

                int newR = fromColor.R + (rStep * currentStep);
                int newG = fromColor.G + (gStep * currentStep);
                int newB = fromColor.B + (bStep * currentStep);

                control.BackColor = Color.FromArgb(
                    Math.Max(0, Math.Min(255, newR)),
                    Math.Max(0, Math.Min(255, newG)),
                    Math.Max(0, Math.Min(255, newB))
                );

                currentStep++;
            };

            timer.Start();
        }

        // إضافة تأثير fade in للعناصر
        public static void FadeIn(Control control, int duration = 500)
        {
            control.Visible = true;
            Timer timer = new Timer();
            timer.Interval = 10;
            int steps = duration / timer.Interval;
            int currentStep = 0;
            double opacityStep = 1.0 / steps;

            // للنماذج فقط
            if (control is Form form)
            {
                form.Opacity = 0;
                timer.Tick += (sender, e) =>
                {
                    if (currentStep >= steps)
                    {
                        form.Opacity = 1.0;
                        timer.Stop();
                        timer.Dispose();
                        return;
                    }

                    form.Opacity = opacityStep * currentStep;
                    currentStep++;
                };

                timer.Start();
            }
        }

        // إنشاء بطاقة عصرية مع ظل
        public static Panel CreateModernCardWithShadow(int width = 300, int height = 200)
        {
            Panel shadowPanel = new Panel
            {
                Size = new Size(width + 4, height + 4),
                BackColor = Colors.Shadow,
                BorderStyle = BorderStyle.None
            };

            Panel card = new Panel
            {
                Size = new Size(width, height),
                BackColor = Colors.Surface,
                Padding = new Padding(Spacing.Medium),
                Location = new Point(2, 2),
                BorderStyle = BorderStyle.None
            };

            shadowPanel.Controls.Add(card);
            return shadowPanel;
        }

        // تبديل الوضع الليلي (تم نقلها للأسفل)

        // تحميل إعدادات الوضع من الريجستري
        public static void LoadThemeSettings()
        {
            try
            {
                Microsoft.Win32.RegistryKey key = Microsoft.Win32.Registry.CurrentUser.OpenSubKey(@"SOFTWARE\PharmacyManagement");
                if (key != null)
                {
                    object darkModeValue = key.GetValue("DarkMode");
                    if (darkModeValue != null)
                    {
                        IsDarkMode = bool.Parse(darkModeValue.ToString());
                    }
                    key.Close();
                }
            }
            catch
            {
                // في حالة الخطأ، استخدم الوضع الفاتح كافتراضي
                IsDarkMode = false;
            }
        }

        // حدث تغيير الوضع (تم نقله للأعلى)

        // إضافة ميزة التكبير بالضغط المزدوج على الشريط العلوي
        public static void AddDoubleClickMaximize(Form form)
        {
            // البحث عن panel1 (الشريط العلوي الأصلي)
            Panel titlePanel = form.Controls.Find("panel1", true).FirstOrDefault() as Panel;

            if (titlePanel != null)
            {
                titlePanel.DoubleClick += (s, e) => ToggleMaximize(form);
                titlePanel.Cursor = Cursors.Hand;

                // إضافة الحدث لجميع العناصر في الشريط العلوي
                foreach (Control control in titlePanel.Controls)
                {
                    if (!(control is Button) && !(control.GetType().Name.Contains("Guna2Button")))
                    {
                        control.DoubleClick += (s, e) => ToggleMaximize(form);
                        control.Cursor = Cursors.Hand;
                    }
                }
            }
        }

        private static void ToggleMaximize(Form form)
        {
            if (form.WindowState == FormWindowState.Maximized)
            {
                form.WindowState = FormWindowState.Normal;
            }
            else
            {
                form.WindowState = FormWindowState.Maximized;
            }
        }

        // تطبيق الوضع على النموذج
        public static void ApplyThemeToForm(Form form)
        {
            form.BackColor = Colors.Background;
            form.ForeColor = Colors.TextPrimary;

            // إضافة ميزة التكبير بالضغط المزدوج
            AddDoubleClickMaximize(form);

            // تطبيق الوضع على جميع العناصر الفرعية
            ApplyThemeToControls(form.Controls);
        }

        // تطبيق الوضع على مجموعة من العناصر
        private static void ApplyThemeToControls(Control.ControlCollection controls)
        {
            foreach (Control control in controls)
            {
                // تطبيق الألوان حسب نوع العنصر
                if (control is Panel panel)
                {
                    // تحديد لون الخلفية حسب اسم اللوحة
                    if (panel.Name == "panel1") // شريط العنوان
                    {
                        panel.BackColor = Colors.Primary;
                    }
                    else if (panel.Name == "panel2") // القائمة الجانبية
                    {
                        panel.BackColor = Colors.Surface;
                    }
                    else if (panel.Name == "panel3") // منطقة المحتوى
                    {
                        panel.BackColor = Colors.Background;
                    }
                    else
                    {
                        panel.BackColor = Colors.Surface;
                    }
                }
                else if (control is Label label)
                {
                    label.ForeColor = Colors.TextPrimary;
                    if (label.BackColor != Color.Transparent && label.Parent?.Name != "panel1")
                        label.BackColor = Colors.Surface;
                }
                else if (control is TextBox textBox)
                {
                    textBox.BackColor = Colors.Surface;
                    textBox.ForeColor = Colors.TextPrimary;
                }
                else if (control is ListBox listBox)
                {
                    listBox.BackColor = Colors.Surface;
                    listBox.ForeColor = Colors.TextPrimary;
                }
                else if (control is DataGridView dgv)
                {
                    dgv.BackgroundColor = Colors.Surface;
                    dgv.DefaultCellStyle.BackColor = Colors.Surface;
                    dgv.DefaultCellStyle.ForeColor = Colors.TextPrimary;
                    dgv.ColumnHeadersDefaultCellStyle.BackColor = Colors.SurfaceVariant;
                    dgv.ColumnHeadersDefaultCellStyle.ForeColor = Colors.TextPrimary;
                }
                else if (control.GetType().Name.Contains("Guna2DataGridView"))
                {
                    // تطبيق الألوان على Guna2DataGridView
                    var guna2Dgv = control as dynamic;
                    try
                    {
                        guna2Dgv.BackgroundColor = Colors.Surface;
                        guna2Dgv.DefaultCellStyle.BackColor = Colors.Surface;
                        guna2Dgv.DefaultCellStyle.ForeColor = Colors.TextPrimary;
                        guna2Dgv.ColumnHeadersDefaultCellStyle.BackColor = Colors.SurfaceVariant;
                        guna2Dgv.ColumnHeadersDefaultCellStyle.ForeColor = Colors.TextPrimary;
                    }
                    catch { /* تجاهل الأخطاء */ }
                }

                // تطبيق الوضع على العناصر الفرعية
                if (control.HasChildren)
                {
                    ApplyThemeToControls(control.Controls);
                }
            }
        }

        /// <summary>
        /// تفعيل حدث تغيير التصميم
        /// </summary>
        public static void OnThemeChanged()
        {
            ThemeChanged?.Invoke(null, EventArgs.Empty);
        }

        /// <summary>
        /// تغيير الوضع وتفعيل الحدث
        /// </summary>
        public static void ToggleDarkMode()
        {
            IsDarkMode = !IsDarkMode;
            OnThemeChanged();
        }
    }
}
