import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

class AnalysisEngine:
    def __init__(self, config):
        self.config = config
        self.symbols = config['TRADING']['symbols'].split(',')
        self.timeframe = int(config['ANALYSIS']['timeframe'])
        self.bars = int(config['ANALYSIS']['bars'])

    def connect_mt5(self):
        if not mt5.initialize():
            print("MT5 initialization failed")
            return False
        return True

    def disconnect_mt5(self):
        mt5.shutdown()

    def get_data(self, symbol):
        rates = mt5.copy_rates_from_pos(symbol, self.timeframe, 0, self.bars)
        if rates is None:
            print(f"Failed to get data for {symbol}")
            return None
        df = pd.DataFrame(rates)
        df['time'] = pd.to_datetime(df['time'], unit='s')
        return df

    def calculate_indicators(self, df):
        # Simple moving averages
        df['SMA_20'] = df['close'].rolling(window=20).mean()
        df['SMA_50'] = df['close'].rolling(window=50).mean()

        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))

        return df

    def generate_signal(self, df):
        # Simple strategy: Buy if SMA_20 > SMA_50 and RSI < 30, Sell if opposite
        latest = df.iloc[-1]
        if latest['SMA_20'] > latest['SMA_50'] and latest['RSI'] < 30:
            return 'BUY'
        elif latest['SMA_20'] < latest['SMA_50'] and latest['RSI'] > 70:
            return 'SELL'
        return 'HOLD'

    def analyze_symbol(self, symbol):
        df = self.get_data(symbol)
        if df is None:
            return None
        df = self.calculate_indicators(df)
        signal = self.generate_signal(df)
        return {'symbol': symbol, 'signal': signal, 'data': df}
