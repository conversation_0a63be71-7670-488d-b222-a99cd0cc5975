@echo off
chcp 65001 > nul
title نظام تداول العملات الرقمية المحسن

echo ============================================================
echo           🚀 نظام تداول العملات الرقمية الذكي
echo                    MetaTrader 5 + AI
echo ============================================================
echo.

echo 🔍 فحص النظام...

REM التحقق من الملفات المطلوبة
if not exist "crypto_trading_gui.py" (
    echo ❌ ملف النظام غير موجود!
    pause
    exit /b 1
)

if not exist "mt5_crypto_trading_system.py" (
    echo ❌ ملف النظام الأساسي غير موجود!
    pause
    exit /b 1
)

echo ✅ ملفات النظام موجودة

REM التحقق من Python
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت!
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM التحقق من MetaTrader5
python -c "import MetaTrader5" > nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت MetaTrader5...
    pip install MetaTrader5
)

echo ✅ MetaTrader5 جاهز

echo.
echo 🚀 تشغيل النظام...
echo.

python crypto_trading_gui.py

if errorlevel 1 (
    echo.
    echo ❌ خطأ في التشغيل!
    echo تأكد من تشغيل MetaTrader 5
    echo.
)

pause
