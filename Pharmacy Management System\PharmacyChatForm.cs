using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace Pharmacy_Management_System
{
    /// <summary>
    /// نافذة المحادثة بين الصيدليات
    /// Pharmacy Chat Form for inter-pharmacy communication
    /// </summary>
    public partial class PharmacyChatForm : Form
    {
        private UnifiedPharmacyFunction unifiedDb;
        private int targetPharmacyId;
        private string targetPharmacyName;
        private int currentPharmacyId;
        private Timer refreshTimer;

        // UI Controls
        private Panel panelHeader;
        private Label lblPharmacyName;
        private Button btnClose;
        private Panel panelMessages;
        private RichTextBox rtbMessages;
        private Panel panelInput;
        private TextBox txtMessage;
        private Button btnSend;
        private Label lblStatus;

        public PharmacyChatForm()
        {
            InitializeComponent();
            unifiedDb = new UnifiedPharmacyFunction();

            // التحقق من معرف الصيدلية الحالية
            if (SessionManager.CurrentPharmacyId <= 0)
            {
                MessageBox.Show("خطأ: لم يتم تحديد صيدلية. يرجى تسجيل الدخول مرة أخرى.", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Close();
                return;
            }

            currentPharmacyId = SessionManager.CurrentPharmacyId;
            SetupForm();
        }

        public PharmacyChatForm(int pharmacyId, string pharmacyName) : this()
        {
            if (SessionManager.CurrentPharmacyId <= 0)
            {
                return; // تم إغلاق النافذة في المنشئ الأساسي
            }

            targetPharmacyId = pharmacyId;
            targetPharmacyName = pharmacyName;

            if (lblPharmacyName != null)
            {
                lblPharmacyName.Text = $"محادثة مع {pharmacyName}";
            }

            LoadMessages();
        }

        private void SetupForm()
        {
            // Form properties
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(500, 400);
            this.BackColor = Color.FromArgb(45, 45, 48);
            this.Text = "محادثة الصيدلية";

            // Header panel
            panelHeader = new Panel
            {
                Dock = DockStyle.Top,
                Height = 50,
                BackColor = Color.FromArgb(0, 122, 204)
            };
            this.Controls.Add(panelHeader);

            // Pharmacy name label
            lblPharmacyName = new Label
            {
                Text = "محادثة الصيدلية",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(10, 15),
                Size = new Size(400, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            panelHeader.Controls.Add(lblPharmacyName);

            // Close button
            btnClose = new Button
            {
                Text = "✕",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Size = new Size(30, 30),
                Location = new Point(550, 10),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            btnClose.FlatAppearance.BorderSize = 0;
            btnClose.Click += (s, e) => this.Close();
            panelHeader.Controls.Add(btnClose);

            // Messages panel
            panelMessages = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(62, 62, 66),
                Padding = new Padding(10)
            };
            this.Controls.Add(panelMessages);

            // Messages rich text box
            rtbMessages = new RichTextBox
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Font = new Font("Segoe UI", 10),
                ReadOnly = true,
                ScrollBars = RichTextBoxScrollBars.Vertical
            };
            panelMessages.Controls.Add(rtbMessages);

            // Input panel
            panelInput = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 80,
                BackColor = Color.FromArgb(62, 62, 66),
                Padding = new Padding(10)
            };
            this.Controls.Add(panelInput);

            // Message input textbox
            txtMessage = new TextBox
            {
                Location = new Point(10, 10),
                Size = new Size(450, 25),
                Font = new Font("Segoe UI", 10),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            txtMessage.KeyDown += TxtMessage_KeyDown;
            panelInput.Controls.Add(txtMessage);

            // Send button
            btnSend = new Button
            {
                Text = "إرسال",
                Location = new Point(470, 10),
                Size = new Size(100, 50),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            btnSend.FlatAppearance.BorderSize = 0;
            btnSend.Click += BtnSend_Click;
            panelInput.Controls.Add(btnSend);

            // Status label
            lblStatus = new Label
            {
                Location = new Point(10, 45),
                Size = new Size(450, 20),
                Font = new Font("Segoe UI", 8),
                ForeColor = Color.LightGray,
                Text = "اكتب رسالتك واضغط Enter أو زر الإرسال"
            };
            panelInput.Controls.Add(lblStatus);

            // Setup refresh timer
            refreshTimer = new Timer();
            refreshTimer.Interval = 5000; // 5 seconds
            refreshTimer.Tick += RefreshTimer_Tick;
            refreshTimer.Start();
        }

        private void TxtMessage_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter && !e.Shift)
            {
                e.Handled = true;
                SendMessage();
            }
        }

        private void BtnSend_Click(object sender, EventArgs e)
        {
            SendMessage();
        }

        private void RefreshTimer_Tick(object sender, EventArgs e)
        {
            LoadMessages();
        }

        private void SendMessage()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtMessage.Text))
                {
                    lblStatus.Text = "يرجى كتابة رسالة قبل الإرسال";
                    lblStatus.ForeColor = Color.Red;
                    return;
                }

                if (targetPharmacyId <= 0)
                {
                    lblStatus.Text = "لم يتم تحديد الصيدلية المستهدفة";
                    lblStatus.ForeColor = Color.Red;
                    return;
                }

                // إرسال الرسالة
                bool success = unifiedDb.SendPharmacyMessage(
                    currentPharmacyId,
                    targetPharmacyId,
                    "رسالة عامة",
                    txtMessage.Text.Trim()
                );

                if (success)
                {
                    txtMessage.Clear();
                    lblStatus.Text = "تم إرسال الرسالة بنجاح";
                    lblStatus.ForeColor = Color.Green;
                    LoadMessages();
                }
                else
                {
                    lblStatus.Text = "فشل في إرسال الرسالة";
                    lblStatus.ForeColor = Color.Red;
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"خطأ في الإرسال: {ex.Message}";
                lblStatus.ForeColor = Color.Red;
            }
        }

        private void LoadMessages()
        {
            try
            {
                if (targetPharmacyId <= 0) return;

                DataSet messages = unifiedDb.GetPharmacyMessages(currentPharmacyId, targetPharmacyId);

                rtbMessages.Clear();

                if (messages.Tables.Count > 0 && messages.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow row in messages.Tables[0].Rows)
                    {
                        string senderName = row["sender_pharmacy_name"].ToString();
                        string messageText = row["message_content"].ToString();
                        DateTime sentDate = Convert.ToDateTime(row["sent_date"]);
                        bool isFromMe = Convert.ToInt32(row["sender_pharmacy_id"]) == currentPharmacyId;

                        // تنسيق الرسالة
                        string timeStr = sentDate.ToString("MM/dd HH:mm");
                        string messageFormat = isFromMe ?
                            $"[{timeStr}] أنت: {messageText}\n\n" :
                            $"[{timeStr}] {senderName}: {messageText}\n\n";

                        // إضافة الرسالة مع التنسيق
                        int startIndex = rtbMessages.TextLength;
                        rtbMessages.AppendText(messageFormat);

                        // تلوين الرسائل
                        rtbMessages.Select(startIndex, messageFormat.Length);
                        rtbMessages.SelectionColor = isFromMe ? Color.Blue : Color.Black;
                        rtbMessages.SelectionFont = new Font("Segoe UI", 10, isFromMe ? FontStyle.Bold : FontStyle.Regular);
                    }

                    // التمرير للأسفل
                    rtbMessages.SelectionStart = rtbMessages.Text.Length;
                    rtbMessages.ScrollToCaret();
                }
                else
                {
                    rtbMessages.Text = "لا توجد رسائل بعد. ابدأ المحادثة!";
                    rtbMessages.SelectionColor = Color.Gray;
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ فقط بدون عرض رسالة للمستخدم لتجنب الرسائل المتكررة
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الرسائل: {ex.Message}");
                rtbMessages.Text = "خطأ في تحميل الرسائل";
                rtbMessages.SelectionColor = Color.Red;
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            refreshTimer?.Stop();
            refreshTimer?.Dispose();
            base.OnFormClosing(e);
        }
    }
}
