using System;
using System.Drawing;
using System.Windows.Forms;

namespace Pharmacy_Management_System
{
    public partial class PublishMedicineForm : Form
    {
        public int PublishQuantity { get; private set; }
        public string Description { get; private set; }

        private string medicineName;
        private int maxQuantity;
        private DateTime expiryDate;
        private decimal pricePerUnit;

        public PublishMedicineForm(string medicineName, int maxQuantity, DateTime expiryDate, decimal pricePerUnit)
        {
            InitializeComponent();
            
            this.medicineName = medicineName;
            this.maxQuantity = maxQuantity;
            this.expiryDate = expiryDate;
            this.pricePerUnit = pricePerUnit;
            
            InitializeForm();
            ApplyLanguage();
            ApplyModernDesign();
        }

        private void InitializeForm()
        {
            // إعداد النموذج
            this.Text = "نشر دواء في المتجر";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // عرض معلومات الدواء
            lblMedicineName.Text = $"اسم الدواء: {medicineName}";
            lblQuantityAvailable.Text = $"الكمية المتاحة: {maxQuantity}";
            lblExpiryDate.Text = $"تاريخ الانتهاء: {expiryDate:yyyy-MM-dd}";

            // إعداد عناصر التحكم
            numQuantity.Maximum = maxQuantity;
            numQuantity.Minimum = 1;
            numQuantity.Value = Math.Min(10, maxQuantity);
        }

        private void ApplyLanguage()
        {
            try
            {
                this.Text = LanguageManager.GetText("Publish Medicine");
                lblTitle.Text = LanguageManager.GetText("Publish Medicine");
                lblQuantityToPublish.Text = LanguageManager.GetText("Quantity to Publish");
                lblDescriptionLabel.Text = LanguageManager.GetText("Description (Optional)");
                btnPublish.Text = LanguageManager.GetText("Publish");
                btnCancel.Text = LanguageManager.GetText("Cancel");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق اللغة: {ex.Message}");
            }
        }

        private void ApplyModernDesign()
        {
            try
            {
                // تطبيق التصميم العصري
                this.BackColor = Color.White;
                this.ForeColor = Color.Black;

                // تطبيق التصميم على التسميات
                foreach (Control control in this.Controls)
                {
                    if (control is Label)
                    {
                        control.ForeColor = Color.Black;
                    }
                    else if (control is Panel)
                    {
                        control.BackColor = Color.White;
                        foreach (Control subControl in control.Controls)
                        {
                            if (subControl is Label)
                            {
                                subControl.ForeColor = Color.Black;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق التصميم العصري: {ex.Message}");
            }
        }

        private void btnPublish_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (numQuantity.Value <= 0)
                {
                    MessageBox.Show("يجب أن تكون الكمية أكبر من صفر", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (numQuantity.Value > maxQuantity)
                {
                    MessageBox.Show($"الكمية لا يمكن أن تكون أكبر من {maxQuantity}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // حفظ البيانات
                PublishQuantity = (int)numQuantity.Value;
                Description = txtDescription.Text.Trim();

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في نشر الدواء: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void numQuantity_ValueChanged(object sender, EventArgs e)
        {
            // تحديث معلومات الكمية
            if (numQuantity.Value > maxQuantity)
            {
                numQuantity.Value = maxQuantity;
            }
        }

        private void panelMain_Paint(object sender, PaintEventArgs e)
        {

        }
    }
}
