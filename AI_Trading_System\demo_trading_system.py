"""
نظام التداول التجريبي - يعمل بدون MT5
"""

import random
import time
from datetime import datetime
from typing import Dict, List
import logging

class DemoTradingSystem:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.connected = False
        self.demo_balance = 10000.0  # رصيد تجريبي
        self.demo_positions = []
        self.trade_counter = 1000
        
        # أسعار تجريبية للرموز
        self.demo_prices = {
            'EURUSD': 1.0850,
            'GBPUSD': 1.2650,
            'USDJPY': 149.50,
            'XAUUSD': 2650.00,
            'BTCUSD': 45000.00,
            'ETHUSD': 2800.00,
            'AUDUSD': 0.6750,
            'USDCAD': 1.3450,
            'NZDUSD': 0.6150,
            'USDCHF': 0.8950
        }
        
    def connect(self) -> bool:
        """الاتصال بالنظام التجريبي"""
        try:
            self.logger.info("🔗 الاتصال بنظام التداول التجريبي...")
            time.sleep(1)  # محاكاة وقت الاتصال
            self.connected = True
            self.logger.info("✅ تم الاتصال بنجاح بالنظام التجريبي")
            return True
        except Exception as e:
            self.logger.error(f"❌ فشل في الاتصال: {str(e)}")
            return False
    
    def disconnect(self):
        """قطع الاتصال"""
        self.connected = False
        self.logger.info("🔌 تم قطع الاتصال من النظام التجريبي")
    
    def get_account_info(self) -> Dict:
        """الحصول على معلومات الحساب التجريبي"""
        if not self.connected:
            return {}
        
        # حساب الربح/الخسارة من الصفقات المفتوحة
        floating_profit = sum([pos.get('profit', 0) for pos in self.demo_positions])
        
        return {
            'login': 'DEMO123456',
            'balance': self.demo_balance,
            'equity': self.demo_balance + floating_profit,
            'margin': 0,
            'free_margin': self.demo_balance + floating_profit,
            'profit': floating_profit,
            'currency': 'USD',
            'company': 'Demo Trading Company',
            'name': 'Demo Account',
            'server': 'Demo-Server',
            'leverage': 100,
            'margin_level': 0
        }
    
    def get_current_price(self, symbol: str) -> float:
        """الحصول على السعر الحالي مع تقلبات عشوائية"""
        if symbol not in self.demo_prices:
            return 0.0
        
        base_price = self.demo_prices[symbol]
        # إضافة تقلبات عشوائية صغيرة (±0.1%)
        variation = random.uniform(-0.001, 0.001)
        current_price = base_price * (1 + variation)
        
        # تحديث السعر الأساسي تدريجياً
        self.demo_prices[symbol] = current_price
        
        return current_price
    
    def execute_trade(self, symbol: str, decision: str, confidence: float, price: float) -> Dict:
        """تنفيذ صفقة تجريبية"""
        try:
            if not self.connected:
                return {
                    'success': False,
                    'message': 'غير متصل بالنظام التجريبي',
                    'ticket': None,
                    'price': price
                }
            
            # حساب حجم الصفقة بناءً على الثقة
            base_lot = 0.01
            confidence_multiplier = confidence / 100
            lot_size = round(base_lot * confidence_multiplier, 2)
            lot_size = max(0.01, min(lot_size, 0.1))  # حد أدنى 0.01 وحد أقصى 0.1
            
            # حساب قيمة الصفقة
            trade_value = lot_size * 100000  # قيمة اللوت الواحد
            if symbol.endswith('USD') and not symbol.startswith('USD'):
                trade_value = lot_size * 100000 * price
            elif symbol.startswith('USD'):
                trade_value = lot_size * 100000
            elif 'USD' in symbol:
                trade_value = lot_size * 100000 * price
            else:
                trade_value = lot_size * price
            
            # فحص الرصيد
            required_margin = trade_value * 0.01  # هامش 1%
            if required_margin > self.demo_balance:
                return {
                    'success': False,
                    'message': f'رصيد غير كافي. مطلوب: ${required_margin:.2f}, متوفر: ${self.demo_balance:.2f}',
                    'ticket': None,
                    'price': price
                }
            
            # الحصول على السعر الحالي
            current_price = self.get_current_price(symbol)
            if current_price == 0:
                return {
                    'success': False,
                    'message': f'الرمز {symbol} غير متوفر',
                    'ticket': None,
                    'price': price
                }
            
            # محاكاة انزلاق السعر (slippage)
            slippage = random.uniform(-0.0001, 0.0001)
            execution_price = current_price * (1 + slippage)
            
            # إنشاء الصفقة
            ticket = self.trade_counter
            self.trade_counter += 1
            
            # حساب وقف الخسارة وجني الربح
            if decision == 'buy':
                sl_price = execution_price * 0.99  # 1% وقف خسارة
                tp_price = execution_price * 1.02  # 2% جني ربح
            else:
                sl_price = execution_price * 1.01  # 1% وقف خسارة
                tp_price = execution_price * 0.98  # 2% جني ربح
            
            position = {
                'ticket': ticket,
                'symbol': symbol,
                'type': decision,
                'volume': lot_size,
                'open_price': execution_price,
                'sl': sl_price,
                'tp': tp_price,
                'open_time': datetime.now(),
                'profit': 0,
                'comment': f"AI Trade - Confidence: {confidence:.1f}%"
            }
            
            self.demo_positions.append(position)
            
            # خصم الهامش من الرصيد
            self.demo_balance -= required_margin
            
            self.logger.info(f"✅ تم تنفيذ صفقة تجريبية بنجاح!")
            self.logger.info(f"   رقم التذكرة: {ticket}")
            self.logger.info(f"   الرمز: {symbol}")
            self.logger.info(f"   النوع: {'شراء' if decision == 'buy' else 'بيع'}")
            self.logger.info(f"   الحجم: {lot_size}")
            self.logger.info(f"   السعر: {execution_price:.5f}")
            
            return {
                'success': True,
                'message': 'تم تنفيذ الصفقة التجريبية بنجاح',
                'ticket': ticket,
                'price': execution_price,
                'volume': lot_size
            }
            
        except Exception as e:
            error_msg = f"خطأ في تنفيذ الصفقة التجريبية: {str(e)}"
            self.logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'ticket': None,
                'price': price
            }
    
    def update_positions(self):
        """تحديث الصفقات المفتوحة"""
        if not self.connected or not self.demo_positions:
            return
        
        positions_to_close = []
        
        for i, position in enumerate(self.demo_positions):
            symbol = position['symbol']
            current_price = self.get_current_price(symbol)
            
            if current_price == 0:
                continue
            
            # حساب الربح/الخسارة
            if position['type'] == 'buy':
                profit_pips = current_price - position['open_price']
            else:
                profit_pips = position['open_price'] - current_price
            
            # تحويل النقاط إلى دولار (تقريبي)
            if symbol.startswith('USD'):
                profit_usd = profit_pips * position['volume'] * 100000
            elif symbol.endswith('USD'):
                profit_usd = profit_pips * position['volume'] * 100000
            else:
                profit_usd = profit_pips * position['volume'] * 100000 * current_price
            
            position['profit'] = profit_usd
            
            # فحص وقف الخسارة وجني الربح
            should_close = False
            close_reason = ""
            
            if position['type'] == 'buy':
                if current_price <= position['sl']:
                    should_close = True
                    close_reason = "Stop Loss"
                elif current_price >= position['tp']:
                    should_close = True
                    close_reason = "Take Profit"
            else:
                if current_price >= position['sl']:
                    should_close = True
                    close_reason = "Stop Loss"
                elif current_price <= position['tp']:
                    should_close = True
                    close_reason = "Take Profit"
            
            if should_close:
                positions_to_close.append((i, close_reason))
        
        # إغلاق الصفقات
        for i, close_reason in reversed(positions_to_close):
            position = self.demo_positions[i]
            self.demo_balance += position['profit']  # إضافة الربح/الخسارة
            
            self.logger.info(f"🔄 إغلاق صفقة {position['ticket']} - {close_reason}")
            self.logger.info(f"   الربح/الخسارة: ${position['profit']:.2f}")
            
            del self.demo_positions[i]
    
    def get_positions(self) -> List[Dict]:
        """الحصول على الصفقات المفتوحة"""
        self.update_positions()
        return self.demo_positions.copy()
    
    def get_available_symbols(self) -> List[str]:
        """الحصول على الرموز المتوفرة"""
        return list(self.demo_prices.keys())
    
    def is_market_open(self) -> bool:
        """فحص ما إذا كان السوق مفتوح (دائماً مفتوح في النظام التجريبي)"""
        return True
