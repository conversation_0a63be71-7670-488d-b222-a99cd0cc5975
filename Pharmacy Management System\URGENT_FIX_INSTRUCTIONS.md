# 🚨 إصلاح عاجل - مشكلة تسجيل الدخول

## **المشكلة:**
- لا يمكن تسجيل الدخول
- خطأ: "Invalid object name employee_sessions"
- لا يمكن إنشاء حسابات جديدة

## **السبب:**
قاعدة البيانات الموحدة `UnifiedPharmacy` غير موجودة أو غير مكتملة.

---

## 🔧 **الحل السريع (5 دقائق):**

### **الخطوة 1: إنشاء قاعدة البيانات**

1. **افتح SQL Server Management Studio**
2. **اتصل بالخادم** (NARUTO)
3. **انسخ والصق هذا الكود:**

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE UnifiedPharmacy;
GO

USE UnifiedPharmacy;
GO

-- جدول الصيدليات
CREATE TABLE pharmacies (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacyCode VARCHAR(20) UNIQUE NOT NULL,
    pharmacyName NVARCHAR(250) NOT NULL,
    ownerName NVARCHAR(250) NOT NULL,
    licenseNumber VARCHAR(100) NOT NULL,
    address NVARCHAR(500) NOT NULL,
    city NVARCHAR(100) NOT NULL,
    region NVARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(250) NOT NULL,
    isActive BIT DEFAULT 1,
    registrationDate DATETIME DEFAULT GETDATE()
);

-- جدول المستخدمين
CREATE TABLE users (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacyId INT NOT NULL,
    userRole VARCHAR(50) NOT NULL,
    name NVARCHAR(250) NOT NULL,
    dob VARCHAR(250) NOT NULL,
    mobile BIGINT NOT NULL,
    email VARCHAR(250) NOT NULL,
    username VARCHAR(250) NOT NULL,
    pass VARCHAR(250) NOT NULL,
    isActive BIT DEFAULT 1,
    FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
);

-- جدول جلسات الموظفين
CREATE TABLE employee_sessions (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacyId INT NOT NULL,
    username VARCHAR(250),
    employeeName NVARCHAR(250),
    loginTime DATETIME,
    logoutTime DATETIME NULL,
    sessionDate DATE,
    FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
);

-- إضافة صيدلية افتراضية
INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, licenseNumber, address, city, region, phone, email)
VALUES ('MAIN001', 'الصيدلية الرئيسية', 'مدير النظام', 'LIC001', 'العنوان الرئيسي', 'المدينة', 'المنطقة', '**********', '<EMAIL>');

-- إضافة مستخدمين افتراضيين
INSERT INTO users (pharmacyId, userRole, name, dob, mobile, email, username, pass)
VALUES 
(1, 'Administrator', 'مدير النظام', '1980-01-01', **********, '<EMAIL>', 'admin', 'admin123'),
(1, 'Employee', 'موظف الصيدلية', '1990-01-01', **********, '<EMAIL>', 'employee', 'emp123');

PRINT 'تم إنشاء قاعدة البيانات بنجاح!';
```

4. **اضغط F5** لتنفيذ الكود

---

### **الخطوة 2: اختبار النظام**

1. **شغل البرنامج** (F5 في Visual Studio)
2. **اضغط "Select Pharmacy"** (الزر الأخضر)
3. **اختر "الصيدلية الرئيسية"**
4. **اضغط "اختيار"**
5. **سجل دخول بـ:**
   - **المدير:** `admin` / `admin123`
   - **الموظف:** `employee` / `emp123`

---

## ✅ **النتيجة المتوقعة:**

- ✅ **لا توجد رسائل خطأ**
- ✅ **تسجيل الدخول يعمل**
- ✅ **يمكن إنشاء حسابات جديدة**
- ✅ **جميع الميزات متاحة**

---

## 🔄 **إذا لم يعمل الحل:**

### **الحل البديل 1:**
```sql
-- تحقق من وجود قاعدة البيانات
SELECT name FROM sys.databases WHERE name = 'UnifiedPharmacy';

-- إذا لم تظهر نتائج، أعد تشغيل الكود أعلاه
```

### **الحل البديل 2:**
1. **احذف قاعدة البيانات القديمة:**
```sql
DROP DATABASE UnifiedPharmacy;
```
2. **أعد تشغيل كود الإنشاء**

### **الحل البديل 3:**
1. **استخدم الملف الجاهز:**
   - افتح `CREATE_UNIFIED_DATABASE.sql`
   - اضغط F5 لتنفيذه

---

## 🎯 **نصائح مهمة:**

### **للتأكد من النجاح:**
```sql
USE UnifiedPharmacy;
SELECT COUNT(*) as 'عدد الصيدليات' FROM pharmacies;
SELECT COUNT(*) as 'عدد المستخدمين' FROM users;
```

### **إذا ظهرت أخطاء أخرى:**
1. **تأكد من تشغيل SQL Server**
2. **تأكد من صحة اسم الخادم** (NARUTO)
3. **تأكد من الصلاحيات**

---

## 🚀 **بعد الإصلاح:**

**ستتمكن من:**
- ✅ **تسجيل الدخول بنجاح**
- ✅ **إنشاء حسابات جديدة**
- ✅ **استخدام جميع ميزات النظام**
- ✅ **اختيار الصيدلية**
- ✅ **تتبع جلسات المستخدمين**

---

## 📞 **إذا احتجت مساعدة إضافية:**

1. **تحقق من Error List في Visual Studio**
2. **تحقق من Output في SQL Server**
3. **تأكد من وجود جميع الجداول**

**🎉 بعد تنفيذ هذه الخطوات، سيعمل النظام بشكل مثالي!**

---

## 📋 **ملفات مساعدة أخرى:**
- `FIX_DATABASE_ERROR.md` - تفاصيل أكثر عن الخطأ
- `CREATE_UNIFIED_DATABASE.sql` - السكريپت الكامل
- `ERRORS_FIXED.md` - قائمة الأخطاء المُصلحة
- `READY_TO_RUN.md` - دليل التشغيل الشامل

**🎯 هذا الحل سيصلح المشكلة فوراً!**
