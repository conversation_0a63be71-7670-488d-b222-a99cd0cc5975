namespace Pharmacy_Management_System.PharmacistUC
{
    partial class UC_P_PharmacyStore
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // إلغاء الاشتراك في الأحداث
                // تم نقل هذا إلى الكود الرئيسي

                // إيقاف المؤقت
                refreshTimer?.Stop();
                refreshTimer?.Dispose();

                if (components != null)
                {
                    components.Dispose();
                }
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.tabControlMain = new System.Windows.Forms.TabControl();
            this.tabPageLocalMedicines = new System.Windows.Forms.TabPage();
            this.txtSearchLocal = new System.Windows.Forms.TextBox();
            this.lblSearchLocal = new System.Windows.Forms.Label();
            this.btnPublishMedicine = new System.Windows.Forms.Button();
            this.btnRefreshLocal = new System.Windows.Forms.Button();
            this.dataGridViewLocalMedicines = new System.Windows.Forms.DataGridView();
            this.lblLocalMedicinesTitle = new System.Windows.Forms.Label();
            this.panelNotifications = new System.Windows.Forms.Panel();
            this.btnNotifications = new System.Windows.Forms.Button();
            this.lblNotificationCount = new System.Windows.Forms.Label();
            this.panelNotificationDropdown = new System.Windows.Forms.Panel();
            this.listViewNotifications = new System.Windows.Forms.ListView();
            this.tabPagePublishedMedicines = new System.Windows.Forms.TabPage();
            this.panelFilters = new System.Windows.Forms.Panel();
            this.btnRefreshPublished = new System.Windows.Forms.Button();
            this.dtpFilterDate = new System.Windows.Forms.DateTimePicker();
            this.lblFilterDate = new System.Windows.Forms.Label();
            this.chkFilterByDate = new System.Windows.Forms.CheckBox();
            this.cmbFilterPharmacy = new System.Windows.Forms.ComboBox();
            this.lblFilterPharmacy = new System.Windows.Forms.Label();
            this.cmbFilterExpiry = new System.Windows.Forms.ComboBox();
            this.lblFilterExpiry = new System.Windows.Forms.Label();
            this.txtSearchPublished = new System.Windows.Forms.TextBox();
            this.lblSearchPublished = new System.Windows.Forms.Label();
            this.btnRequestMedicine = new System.Windows.Forms.Button();
            this.dataGridViewPublishedMedicines = new System.Windows.Forms.DataGridView();
            this.lblPublishedMedicinesTitle = new System.Windows.Forms.Label();
            this.tabPageMyPublished = new System.Windows.Forms.TabPage();
            this.btnRefreshMyPublished = new System.Windows.Forms.Button();
            this.btnDeleteMyPublished = new System.Windows.Forms.Button();
            this.btnEditMyPublished = new System.Windows.Forms.Button();
            this.dataGridViewMyPublished = new System.Windows.Forms.DataGridView();
            this.lblMyPublishedTitle = new System.Windows.Forms.Label();
            this.tabPageMedicineRequests = new System.Windows.Forms.TabPage();
            this.btnMessagesRequests = new System.Windows.Forms.Button();
            this.btnRefreshRequests = new System.Windows.Forms.Button();
            this.btnRejectRequest = new System.Windows.Forms.Button();
            this.btnAcceptRequest = new System.Windows.Forms.Button();
            this.dataGridViewMedicineRequests = new System.Windows.Forms.DataGridView();
            this.lblMedicineRequestsTitle = new System.Windows.Forms.Label();
            this.tabControlMain.SuspendLayout();
            this.tabPageLocalMedicines.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewLocalMedicines)).BeginInit();
            this.tabPagePublishedMedicines.SuspendLayout();
            this.panelFilters.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewPublishedMedicines)).BeginInit();
            this.tabPageMyPublished.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewMyPublished)).BeginInit();
            this.tabPageMedicineRequests.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewMedicineRequests)).BeginInit();
            this.SuspendLayout();
            // 
            // tabControlMain
            // 
            this.tabControlMain.Controls.Add(this.tabPageLocalMedicines);
            this.tabControlMain.Controls.Add(this.tabPagePublishedMedicines);
            this.tabControlMain.Controls.Add(this.tabPageMyPublished);
            this.tabControlMain.Controls.Add(this.tabPageMedicineRequests);
            this.tabControlMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlMain.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F);
            this.tabControlMain.Location = new System.Drawing.Point(0, 0);
            this.tabControlMain.Name = "tabControlMain";
            this.tabControlMain.SelectedIndex = 0;
            this.tabControlMain.Size = new System.Drawing.Size(1200, 800);
            this.tabControlMain.TabIndex = 0;
            // 
            // tabPageLocalMedicines
            // 
            this.tabPageLocalMedicines.Controls.Add(this.txtSearchLocal);
            this.tabPageLocalMedicines.Controls.Add(this.lblSearchLocal);
            this.tabPageLocalMedicines.Controls.Add(this.btnPublishMedicine);
            this.tabPageLocalMedicines.Controls.Add(this.btnRefreshLocal);
            this.tabPageLocalMedicines.Controls.Add(this.dataGridViewLocalMedicines);
            this.tabPageLocalMedicines.Controls.Add(this.lblLocalMedicinesTitle);
            this.tabPageLocalMedicines.Location = new System.Drawing.Point(4, 29);
            this.tabPageLocalMedicines.Name = "tabPageLocalMedicines";
            this.tabPageLocalMedicines.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageLocalMedicines.Size = new System.Drawing.Size(1192, 767);
            this.tabPageLocalMedicines.TabIndex = 0;
            this.tabPageLocalMedicines.Text = "الأدوية المحلية";
            this.tabPageLocalMedicines.UseVisualStyleBackColor = true;
            this.tabPageLocalMedicines.Click += new System.EventHandler(this.tabPageLocalMedicines_Click);
            // 
            // btnPublishMedicine
            // 
            this.btnPublishMedicine.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPublishMedicine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(150)))), ((int)(((byte)(136)))));
            this.btnPublishMedicine.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnPublishMedicine.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnPublishMedicine.ForeColor = System.Drawing.Color.White;
            this.btnPublishMedicine.Location = new System.Drawing.Point(1000, 700);
            this.btnPublishMedicine.Name = "btnPublishMedicine";
            this.btnPublishMedicine.Size = new System.Drawing.Size(150, 40);
            this.btnPublishMedicine.TabIndex = 2;
            this.btnPublishMedicine.Text = "نشر الدواء";
            this.btnPublishMedicine.UseVisualStyleBackColor = false;
            this.btnPublishMedicine.Click += new System.EventHandler(this.btnPublishMedicine_Click);
            //
            // btnRefreshLocal
            //
            this.btnRefreshLocal.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRefreshLocal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(33)))), ((int)(((byte)(150)))), ((int)(((byte)(243)))));
            this.btnRefreshLocal.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRefreshLocal.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnRefreshLocal.ForeColor = System.Drawing.Color.White;
            this.btnRefreshLocal.Location = new System.Drawing.Point(830, 700);
            this.btnRefreshLocal.Name = "btnRefreshLocal";
            this.btnRefreshLocal.Size = new System.Drawing.Size(150, 40);
            this.btnRefreshLocal.TabIndex = 3;
            this.btnRefreshLocal.Text = "تحديث القائمة";
            this.btnRefreshLocal.UseVisualStyleBackColor = false;
            this.btnRefreshLocal.Click += new System.EventHandler(this.btnRefreshLocal_Click);
            //
            // dataGridViewLocalMedicines
            // 
            this.dataGridViewLocalMedicines.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridViewLocalMedicines.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dataGridViewLocalMedicines.BackgroundColor = System.Drawing.Color.White;
            this.dataGridViewLocalMedicines.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewLocalMedicines.Location = new System.Drawing.Point(20, 60);
            this.dataGridViewLocalMedicines.Name = "dataGridViewLocalMedicines";
            this.dataGridViewLocalMedicines.ReadOnly = true;
            this.dataGridViewLocalMedicines.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewLocalMedicines.Size = new System.Drawing.Size(1150, 620);
            this.dataGridViewLocalMedicines.TabIndex = 1;
            // 
            // lblLocalMedicinesTitle
            // 
            this.lblLocalMedicinesTitle.AutoSize = true;
            this.lblLocalMedicinesTitle.Font = new System.Drawing.Font("Microsoft Sans Serif", 16F, System.Drawing.FontStyle.Bold);
            this.lblLocalMedicinesTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(150)))), ((int)(((byte)(136)))));
            this.lblLocalMedicinesTitle.Location = new System.Drawing.Point(20, 20);
            this.lblLocalMedicinesTitle.Name = "lblLocalMedicinesTitle";
            this.lblLocalMedicinesTitle.Size = new System.Drawing.Size(178, 26);
            this.lblLocalMedicinesTitle.TabIndex = 0;
            this.lblLocalMedicinesTitle.Text = "الأدوية المتاحة للنشر";
            //
            // lblSearchLocal
            //
            this.lblSearchLocal.AutoSize = true;
            this.lblSearchLocal.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Regular);
            this.lblSearchLocal.Location = new System.Drawing.Point(950, 60);
            this.lblSearchLocal.Name = "lblSearchLocal";
            this.lblSearchLocal.Size = new System.Drawing.Size(85, 19);
            this.lblSearchLocal.TabIndex = 5;
            this.lblSearchLocal.Text = "البحث عن دواء:";
            //
            // txtSearchLocal
            //
            this.txtSearchLocal.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.txtSearchLocal.Location = new System.Drawing.Point(700, 57);
            this.txtSearchLocal.Name = "txtSearchLocal";
            this.txtSearchLocal.Size = new System.Drawing.Size(240, 25);
            this.txtSearchLocal.TabIndex = 6;
            this.txtSearchLocal.TextChanged += new System.EventHandler(this.txtSearchLocal_TextChanged);
            //
            // tabPagePublishedMedicines
            // 
            this.tabPagePublishedMedicines.Controls.Add(this.panelFilters);
            this.tabPagePublishedMedicines.Controls.Add(this.btnRequestMedicine);
            this.tabPagePublishedMedicines.Controls.Add(this.dataGridViewPublishedMedicines);
            this.tabPagePublishedMedicines.Controls.Add(this.lblPublishedMedicinesTitle);
            this.tabPagePublishedMedicines.Location = new System.Drawing.Point(4, 29);
            this.tabPagePublishedMedicines.Name = "tabPagePublishedMedicines";
            this.tabPagePublishedMedicines.Padding = new System.Windows.Forms.Padding(3);
            this.tabPagePublishedMedicines.Size = new System.Drawing.Size(1192, 767);
            this.tabPagePublishedMedicines.TabIndex = 1;
            this.tabPagePublishedMedicines.Text = "الأدوية المنشورة";
            this.tabPagePublishedMedicines.UseVisualStyleBackColor = true;
            // 
            // panelFilters
            // 
            this.panelFilters.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.panelFilters.Controls.Add(this.btnRefreshPublished);
            this.panelFilters.Controls.Add(this.dtpFilterDate);
            this.panelFilters.Controls.Add(this.lblFilterDate);
            this.panelFilters.Controls.Add(this.chkFilterByDate);
            this.panelFilters.Controls.Add(this.cmbFilterPharmacy);
            this.panelFilters.Controls.Add(this.lblFilterPharmacy);
            this.panelFilters.Controls.Add(this.cmbFilterExpiry);
            this.panelFilters.Controls.Add(this.lblFilterExpiry);
            this.panelFilters.Controls.Add(this.txtSearchPublished);
            this.panelFilters.Controls.Add(this.lblSearchPublished);
            this.panelFilters.Location = new System.Drawing.Point(20, 60);
            this.panelFilters.Name = "panelFilters";
            this.panelFilters.Size = new System.Drawing.Size(1150, 120);
            this.panelFilters.TabIndex = 1;
            // 
            // cmbFilterPharmacy
            // 
            this.cmbFilterPharmacy.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbFilterPharmacy.Font = new System.Drawing.Font("Microsoft Sans Serif", 10F);
            this.cmbFilterPharmacy.FormattingEnabled = true;
            this.cmbFilterPharmacy.Location = new System.Drawing.Point(750, 40);
            this.cmbFilterPharmacy.Name = "cmbFilterPharmacy";
            this.cmbFilterPharmacy.Size = new System.Drawing.Size(200, 24);
            this.cmbFilterPharmacy.TabIndex = 5;
            //
            // btnRefreshPublished
            //
            this.btnRefreshPublished.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(150)))), ((int)(((byte)(136)))));
            this.btnRefreshPublished.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRefreshPublished.Font = new System.Drawing.Font("Microsoft Sans Serif", 10F, System.Drawing.FontStyle.Bold);
            this.btnRefreshPublished.ForeColor = System.Drawing.Color.White;
            this.btnRefreshPublished.Location = new System.Drawing.Point(970, 80);
            this.btnRefreshPublished.Name = "btnRefreshPublished";
            this.btnRefreshPublished.Size = new System.Drawing.Size(120, 30);
            this.btnRefreshPublished.TabIndex = 10;
            this.btnRefreshPublished.Text = "تحديث القائمة";
            this.btnRefreshPublished.UseVisualStyleBackColor = false;
            this.btnRefreshPublished.Click += new System.EventHandler(this.btnRefreshPublished_Click);
            //
            // dtpFilterDate
            //
            this.dtpFilterDate.Font = new System.Drawing.Font("Microsoft Sans Serif", 10F);
            this.dtpFilterDate.Location = new System.Drawing.Point(500, 80);
            this.dtpFilterDate.Name = "dtpFilterDate";
            this.dtpFilterDate.Size = new System.Drawing.Size(200, 23);
            this.dtpFilterDate.TabIndex = 9;
            this.dtpFilterDate.ValueChanged += new System.EventHandler(this.dtpFilterDate_ValueChanged);
            //
            // lblFilterDate
            //
            this.lblFilterDate.AutoSize = true;
            this.lblFilterDate.Font = new System.Drawing.Font("Microsoft Sans Serif", 10F);
            this.lblFilterDate.Location = new System.Drawing.Point(720, 85);
            this.lblFilterDate.Name = "lblFilterDate";
            this.lblFilterDate.Size = new System.Drawing.Size(75, 17);
            this.lblFilterDate.TabIndex = 8;
            this.lblFilterDate.Text = "تاريخ النشر:";
            //
            // chkFilterByDate
            //
            this.chkFilterByDate.AutoSize = true;
            this.chkFilterByDate.Font = new System.Drawing.Font("Microsoft Sans Serif", 10F);
            this.chkFilterByDate.Location = new System.Drawing.Point(350, 85);
            this.chkFilterByDate.Name = "chkFilterByDate";
            this.chkFilterByDate.Size = new System.Drawing.Size(130, 21);
            this.chkFilterByDate.TabIndex = 7;
            this.chkFilterByDate.Text = "فلترة حسب التاريخ";
            this.chkFilterByDate.UseVisualStyleBackColor = true;
            this.chkFilterByDate.CheckedChanged += new System.EventHandler(this.chkFilterByDate_CheckedChanged);
            //
            // lblFilterPharmacy
            // 
            this.lblFilterPharmacy.AutoSize = true;
            this.lblFilterPharmacy.Font = new System.Drawing.Font("Microsoft Sans Serif", 10F);
            this.lblFilterPharmacy.Location = new System.Drawing.Point(750, 20);
            this.lblFilterPharmacy.Name = "lblFilterPharmacy";
            this.lblFilterPharmacy.Size = new System.Drawing.Size(77, 17);
            this.lblFilterPharmacy.TabIndex = 4;
            this.lblFilterPharmacy.Text = "فلترة بالصيدلية:";
            // 
            // cmbFilterExpiry
            // 
            this.cmbFilterExpiry.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbFilterExpiry.Font = new System.Drawing.Font("Microsoft Sans Serif", 10F);
            this.cmbFilterExpiry.FormattingEnabled = true;
            this.cmbFilterExpiry.Location = new System.Drawing.Point(500, 40);
            this.cmbFilterExpiry.Name = "cmbFilterExpiry";
            this.cmbFilterExpiry.Size = new System.Drawing.Size(200, 24);
            this.cmbFilterExpiry.TabIndex = 3;
            // 
            // lblFilterExpiry
            // 
            this.lblFilterExpiry.AutoSize = true;
            this.lblFilterExpiry.Font = new System.Drawing.Font("Microsoft Sans Serif", 10F);
            this.lblFilterExpiry.Location = new System.Drawing.Point(500, 20);
            this.lblFilterExpiry.Name = "lblFilterExpiry";
            this.lblFilterExpiry.Size = new System.Drawing.Size(99, 17);
            this.lblFilterExpiry.TabIndex = 2;
            this.lblFilterExpiry.Text = "فلترة بتاريخ الانتهاء:";
            // 
            // txtSearchPublished
            // 
            this.txtSearchPublished.Font = new System.Drawing.Font("Microsoft Sans Serif", 10F);
            this.txtSearchPublished.Location = new System.Drawing.Point(20, 40);
            this.txtSearchPublished.Name = "txtSearchPublished";
            this.txtSearchPublished.Size = new System.Drawing.Size(400, 23);
            this.txtSearchPublished.TabIndex = 1;
            // 
            // lblSearchPublished
            // 
            this.lblSearchPublished.AutoSize = true;
            this.lblSearchPublished.Font = new System.Drawing.Font("Microsoft Sans Serif", 10F);
            this.lblSearchPublished.Location = new System.Drawing.Point(20, 20);
            this.lblSearchPublished.Name = "lblSearchPublished";
            this.lblSearchPublished.Size = new System.Drawing.Size(92, 17);
            this.lblSearchPublished.TabIndex = 0;
            this.lblSearchPublished.Text = "البحث باسم الدواء:";
            // 
            // btnRequestMedicine
            // 
            this.btnRequestMedicine.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRequestMedicine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(87)))), ((int)(((byte)(34)))));
            this.btnRequestMedicine.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRequestMedicine.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnRequestMedicine.ForeColor = System.Drawing.Color.White;
            this.btnRequestMedicine.Location = new System.Drawing.Point(1000, 700);
            this.btnRequestMedicine.Name = "btnRequestMedicine";
            this.btnRequestMedicine.Size = new System.Drawing.Size(150, 40);
            this.btnRequestMedicine.TabIndex = 3;
            this.btnRequestMedicine.Text = "طلب الدواء";
            this.btnRequestMedicine.UseVisualStyleBackColor = false;
            this.btnRequestMedicine.Click += new System.EventHandler(this.btnRequestMedicine_Click);
            // 
            // dataGridViewPublishedMedicines
            // 
            this.dataGridViewPublishedMedicines.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridViewPublishedMedicines.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dataGridViewPublishedMedicines.BackgroundColor = System.Drawing.Color.White;
            this.dataGridViewPublishedMedicines.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewPublishedMedicines.Location = new System.Drawing.Point(20, 200);
            this.dataGridViewPublishedMedicines.Name = "dataGridViewPublishedMedicines";
            this.dataGridViewPublishedMedicines.ReadOnly = true;
            this.dataGridViewPublishedMedicines.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewPublishedMedicines.Size = new System.Drawing.Size(1150, 520);
            this.dataGridViewPublishedMedicines.TabIndex = 2;
            // 
            // lblPublishedMedicinesTitle
            // 
            this.lblPublishedMedicinesTitle.AutoSize = true;
            this.lblPublishedMedicinesTitle.Font = new System.Drawing.Font("Microsoft Sans Serif", 16F, System.Drawing.FontStyle.Bold);
            this.lblPublishedMedicinesTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(87)))), ((int)(((byte)(34)))));
            this.lblPublishedMedicinesTitle.Location = new System.Drawing.Point(20, 20);
            this.lblPublishedMedicinesTitle.Name = "lblPublishedMedicinesTitle";
            this.lblPublishedMedicinesTitle.Size = new System.Drawing.Size(245, 26);
            this.lblPublishedMedicinesTitle.TabIndex = 0;
            this.lblPublishedMedicinesTitle.Text = "الأدوية المتاحة من الصيدليات";
            // 
            // tabPageMyPublished
            //
            this.tabPageMyPublished.Controls.Add(this.btnRefreshMyPublished);
            this.tabPageMyPublished.Controls.Add(this.btnDeleteMyPublished);
            this.tabPageMyPublished.Controls.Add(this.btnEditMyPublished);
            this.tabPageMyPublished.Controls.Add(this.dataGridViewMyPublished);
            this.tabPageMyPublished.Controls.Add(this.lblMyPublishedTitle);
            this.tabPageMyPublished.Location = new System.Drawing.Point(4, 29);
            this.tabPageMyPublished.Name = "tabPageMyPublished";
            this.tabPageMyPublished.Size = new System.Drawing.Size(1192, 767);
            this.tabPageMyPublished.TabIndex = 2;
            this.tabPageMyPublished.Text = "أدويتي المعروضة";
            this.tabPageMyPublished.UseVisualStyleBackColor = true;
            // 
            // btnDeleteMyPublished
            // 
            this.btnDeleteMyPublished.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnDeleteMyPublished.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(244)))), ((int)(((byte)(67)))), ((int)(((byte)(54)))));
            this.btnDeleteMyPublished.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnDeleteMyPublished.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnDeleteMyPublished.ForeColor = System.Drawing.Color.White;
            this.btnDeleteMyPublished.Location = new System.Drawing.Point(830, 700);
            this.btnDeleteMyPublished.Name = "btnDeleteMyPublished";
            this.btnDeleteMyPublished.Size = new System.Drawing.Size(150, 40);
            this.btnDeleteMyPublished.TabIndex = 3;
            this.btnDeleteMyPublished.Text = "حذف العرض";
            this.btnDeleteMyPublished.UseVisualStyleBackColor = false;
            this.btnDeleteMyPublished.Click += new System.EventHandler(this.btnDeleteMyPublished_Click);
            // 
            // btnEditMyPublished
            // 
            this.btnEditMyPublished.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnEditMyPublished.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(33)))), ((int)(((byte)(150)))), ((int)(((byte)(243)))));
            this.btnEditMyPublished.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnEditMyPublished.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnEditMyPublished.ForeColor = System.Drawing.Color.White;
            this.btnEditMyPublished.Location = new System.Drawing.Point(1000, 700);
            this.btnEditMyPublished.Name = "btnEditMyPublished";
            this.btnEditMyPublished.Size = new System.Drawing.Size(150, 40);
            this.btnEditMyPublished.TabIndex = 2;
            this.btnEditMyPublished.Text = "تعديل العرض";
            this.btnEditMyPublished.UseVisualStyleBackColor = false;
            this.btnEditMyPublished.Click += new System.EventHandler(this.btnEditMyPublished_Click);
            //
            // btnRefreshMyPublished
            //
            this.btnRefreshMyPublished.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRefreshMyPublished.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(76)))), ((int)(((byte)(175)))), ((int)(((byte)(80)))));
            this.btnRefreshMyPublished.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRefreshMyPublished.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnRefreshMyPublished.ForeColor = System.Drawing.Color.White;
            this.btnRefreshMyPublished.Location = new System.Drawing.Point(660, 700);
            this.btnRefreshMyPublished.Name = "btnRefreshMyPublished";
            this.btnRefreshMyPublished.Size = new System.Drawing.Size(150, 40);
            this.btnRefreshMyPublished.TabIndex = 4;
            this.btnRefreshMyPublished.Text = "تحديث البيانات";
            this.btnRefreshMyPublished.UseVisualStyleBackColor = false;
            this.btnRefreshMyPublished.Click += new System.EventHandler(this.btnRefreshMyPublished_Click);
            //
            // dataGridViewMyPublished
            // 
            this.dataGridViewMyPublished.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridViewMyPublished.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dataGridViewMyPublished.BackgroundColor = System.Drawing.Color.White;
            this.dataGridViewMyPublished.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewMyPublished.Location = new System.Drawing.Point(20, 60);
            this.dataGridViewMyPublished.Name = "dataGridViewMyPublished";
            this.dataGridViewMyPublished.ReadOnly = true;
            this.dataGridViewMyPublished.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewMyPublished.Size = new System.Drawing.Size(1150, 620);
            this.dataGridViewMyPublished.TabIndex = 1;
            // 
            // lblMyPublishedTitle
            // 
            this.lblMyPublishedTitle.AutoSize = true;
            this.lblMyPublishedTitle.Font = new System.Drawing.Font("Microsoft Sans Serif", 16F, System.Drawing.FontStyle.Bold);
            this.lblMyPublishedTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(33)))), ((int)(((byte)(150)))), ((int)(((byte)(243)))));
            this.lblMyPublishedTitle.Location = new System.Drawing.Point(20, 20);
            this.lblMyPublishedTitle.Name = "lblMyPublishedTitle";
            this.lblMyPublishedTitle.Size = new System.Drawing.Size(164, 26);
            this.lblMyPublishedTitle.TabIndex = 0;
            this.lblMyPublishedTitle.Text = "الأدوية التي نشرتها";
            //
            // tabPageMedicineRequests
            //
            this.tabPageMedicineRequests.Controls.Add(this.btnMessagesRequests);
            this.tabPageMedicineRequests.Controls.Add(this.btnRefreshRequests);
            this.tabPageMedicineRequests.Controls.Add(this.btnRejectRequest);
            this.tabPageMedicineRequests.Controls.Add(this.btnAcceptRequest);
            this.tabPageMedicineRequests.Controls.Add(this.dataGridViewMedicineRequests);
            this.tabPageMedicineRequests.Controls.Add(this.lblMedicineRequestsTitle);
            this.tabPageMedicineRequests.Location = new System.Drawing.Point(4, 29);
            this.tabPageMedicineRequests.Name = "tabPageMedicineRequests";
            this.tabPageMedicineRequests.Size = new System.Drawing.Size(1192, 767);
            this.tabPageMedicineRequests.TabIndex = 3;
            this.tabPageMedicineRequests.Text = "طلبات الأدوية";
            this.tabPageMedicineRequests.UseVisualStyleBackColor = true;
            //
            // btnMessagesRequests
            //
            this.btnMessagesRequests.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnMessagesRequests.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(33)))), ((int)(((byte)(150)))), ((int)(((byte)(243)))));
            this.btnMessagesRequests.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnMessagesRequests.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnMessagesRequests.ForeColor = System.Drawing.Color.White;
            this.btnMessagesRequests.Location = new System.Drawing.Point(20, 700);
            this.btnMessagesRequests.Name = "btnMessagesRequests";
            this.btnMessagesRequests.Size = new System.Drawing.Size(150, 40);
            this.btnMessagesRequests.TabIndex = 5;
            this.btnMessagesRequests.Text = "الرسائل";
            this.btnMessagesRequests.UseVisualStyleBackColor = false;
            //
            // btnRefreshRequests
            //
            this.btnRefreshRequests.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnRefreshRequests.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(76)))), ((int)(((byte)(175)))), ((int)(((byte)(80)))));
            this.btnRefreshRequests.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRefreshRequests.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnRefreshRequests.ForeColor = System.Drawing.Color.White;
            this.btnRefreshRequests.Location = new System.Drawing.Point(190, 700);
            this.btnRefreshRequests.Name = "btnRefreshRequests";
            this.btnRefreshRequests.Size = new System.Drawing.Size(150, 40);
            this.btnRefreshRequests.TabIndex = 4;
            this.btnRefreshRequests.Text = "تحديث";
            this.btnRefreshRequests.UseVisualStyleBackColor = false;
            //
            // btnRejectRequest
            //
            this.btnRejectRequest.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRejectRequest.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(244)))), ((int)(((byte)(67)))), ((int)(((byte)(54)))));
            this.btnRejectRequest.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRejectRequest.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnRejectRequest.ForeColor = System.Drawing.Color.White;
            this.btnRejectRequest.Location = new System.Drawing.Point(830, 700);
            this.btnRejectRequest.Name = "btnRejectRequest";
            this.btnRejectRequest.Size = new System.Drawing.Size(150, 40);
            this.btnRejectRequest.TabIndex = 3;
            this.btnRejectRequest.Text = "رفض الطلب";
            this.btnRejectRequest.UseVisualStyleBackColor = false;
            //
            // btnAcceptRequest
            //
            this.btnAcceptRequest.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnAcceptRequest.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(76)))), ((int)(((byte)(175)))), ((int)(((byte)(80)))));
            this.btnAcceptRequest.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnAcceptRequest.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnAcceptRequest.ForeColor = System.Drawing.Color.White;
            this.btnAcceptRequest.Location = new System.Drawing.Point(1000, 700);
            this.btnAcceptRequest.Name = "btnAcceptRequest";
            this.btnAcceptRequest.Size = new System.Drawing.Size(150, 40);
            this.btnAcceptRequest.TabIndex = 2;
            this.btnAcceptRequest.Text = "قبول الطلب";
            this.btnAcceptRequest.UseVisualStyleBackColor = false;
            //
            // dataGridViewMedicineRequests
            //
            this.dataGridViewMedicineRequests.AllowUserToAddRows = false;
            this.dataGridViewMedicineRequests.AllowUserToDeleteRows = false;
            this.dataGridViewMedicineRequests.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridViewMedicineRequests.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dataGridViewMedicineRequests.BackgroundColor = System.Drawing.Color.White;
            this.dataGridViewMedicineRequests.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewMedicineRequests.Location = new System.Drawing.Point(20, 60);
            this.dataGridViewMedicineRequests.MultiSelect = false;
            this.dataGridViewMedicineRequests.Name = "dataGridViewMedicineRequests";
            this.dataGridViewMedicineRequests.ReadOnly = true;
            this.dataGridViewMedicineRequests.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewMedicineRequests.Size = new System.Drawing.Size(1130, 620);
            this.dataGridViewMedicineRequests.TabIndex = 1;
            //
            // lblMedicineRequestsTitle
            //
            this.lblMedicineRequestsTitle.AutoSize = true;
            this.lblMedicineRequestsTitle.Font = new System.Drawing.Font("Microsoft Sans Serif", 16F, System.Drawing.FontStyle.Bold);
            this.lblMedicineRequestsTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(33)))), ((int)(((byte)(150)))), ((int)(((byte)(243)))));
            this.lblMedicineRequestsTitle.Location = new System.Drawing.Point(20, 20);
            this.lblMedicineRequestsTitle.Name = "lblMedicineRequestsTitle";
            this.lblMedicineRequestsTitle.Size = new System.Drawing.Size(164, 26);
            this.lblMedicineRequestsTitle.TabIndex = 0;
            this.lblMedicineRequestsTitle.Text = "طلبات الأدوية";
            //
            // UC_P_PharmacyStore
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.tabControlMain);
            this.Name = "UC_P_PharmacyStore";
            this.Size = new System.Drawing.Size(1200, 800);
            this.tabControlMain.ResumeLayout(false);
            this.tabPageLocalMedicines.ResumeLayout(false);
            this.tabPageLocalMedicines.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewLocalMedicines)).EndInit();
            this.tabPagePublishedMedicines.ResumeLayout(false);
            this.tabPagePublishedMedicines.PerformLayout();
            this.panelFilters.ResumeLayout(false);
            this.panelFilters.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewPublishedMedicines)).EndInit();
            this.tabPageMyPublished.ResumeLayout(false);
            this.tabPageMyPublished.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewMyPublished)).EndInit();
            this.tabPageMedicineRequests.ResumeLayout(false);
            this.tabPageMedicineRequests.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewMedicineRequests)).EndInit();
            //
            // panelNotifications
            //
            this.panelNotifications.Controls.Add(this.btnNotifications);
            this.panelNotifications.Controls.Add(this.lblNotificationCount);
            this.panelNotifications.Controls.Add(this.panelNotificationDropdown);
            this.panelNotifications.Location = new System.Drawing.Point(1050, 10);
            this.panelNotifications.Name = "panelNotifications";
            this.panelNotifications.Size = new System.Drawing.Size(140, 200);
            this.panelNotifications.TabIndex = 1;
            this.Controls.Add(this.panelNotifications);
            //
            // btnNotifications
            //
            this.btnNotifications.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.btnNotifications.FlatAppearance.BorderSize = 0;
            this.btnNotifications.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnNotifications.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold);
            this.btnNotifications.ForeColor = System.Drawing.Color.White;
            this.btnNotifications.Location = new System.Drawing.Point(0, 0);
            this.btnNotifications.Name = "btnNotifications";
            this.btnNotifications.Size = new System.Drawing.Size(140, 40);
            this.btnNotifications.TabIndex = 0;
            this.btnNotifications.Text = "🔔 الإشعارات";
            this.btnNotifications.UseVisualStyleBackColor = false;
            this.btnNotifications.Click += new System.EventHandler(this.btnNotifications_Click);
            //
            // lblNotificationCount
            //
            this.lblNotificationCount.BackColor = System.Drawing.Color.Red;
            this.lblNotificationCount.Font = new System.Drawing.Font("Segoe UI", 8F, System.Drawing.FontStyle.Bold);
            this.lblNotificationCount.ForeColor = System.Drawing.Color.White;
            this.lblNotificationCount.Location = new System.Drawing.Point(115, 5);
            this.lblNotificationCount.Name = "lblNotificationCount";
            this.lblNotificationCount.Size = new System.Drawing.Size(20, 20);
            this.lblNotificationCount.TabIndex = 1;
            this.lblNotificationCount.Text = "0";
            this.lblNotificationCount.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.lblNotificationCount.Visible = false;
            //
            // panelNotificationDropdown
            //
            this.panelNotificationDropdown.BackColor = System.Drawing.Color.White;
            this.panelNotificationDropdown.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.panelNotificationDropdown.Controls.Add(this.listViewNotifications);
            this.panelNotificationDropdown.Location = new System.Drawing.Point(-200, 45);
            this.panelNotificationDropdown.Name = "panelNotificationDropdown";
            this.panelNotificationDropdown.Size = new System.Drawing.Size(340, 150);
            this.panelNotificationDropdown.TabIndex = 2;
            this.panelNotificationDropdown.Visible = false;
            //
            // listViewNotifications
            //
            this.listViewNotifications.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewNotifications.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.listViewNotifications.FullRowSelect = true;
            this.listViewNotifications.GridLines = true;
            this.listViewNotifications.Location = new System.Drawing.Point(0, 0);
            this.listViewNotifications.Name = "listViewNotifications";
            this.listViewNotifications.Size = new System.Drawing.Size(338, 148);
            this.listViewNotifications.TabIndex = 0;
            this.listViewNotifications.UseCompatibleStateImageBehavior = false;
            this.listViewNotifications.View = System.Windows.Forms.View.Details;
            this.listViewNotifications.DoubleClick += new System.EventHandler(this.listViewNotifications_DoubleClick);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tabControlMain;
        private System.Windows.Forms.TabPage tabPageLocalMedicines;
        private System.Windows.Forms.TextBox txtSearchLocal;
        private System.Windows.Forms.Label lblSearchLocal;
        private System.Windows.Forms.Button btnPublishMedicine;
        private System.Windows.Forms.Button btnRefreshLocal;
        private System.Windows.Forms.DataGridView dataGridViewLocalMedicines;
        private System.Windows.Forms.Label lblLocalMedicinesTitle;
        private System.Windows.Forms.TabPage tabPagePublishedMedicines;
        private System.Windows.Forms.Panel panelFilters;
        private System.Windows.Forms.Button btnRefreshPublished;
        private System.Windows.Forms.DateTimePicker dtpFilterDate;
        private System.Windows.Forms.Label lblFilterDate;
        private System.Windows.Forms.CheckBox chkFilterByDate;
        private System.Windows.Forms.ComboBox cmbFilterPharmacy;
        private System.Windows.Forms.Label lblFilterPharmacy;
        private System.Windows.Forms.ComboBox cmbFilterExpiry;
        private System.Windows.Forms.Label lblFilterExpiry;
        private System.Windows.Forms.TextBox txtSearchPublished;
        private System.Windows.Forms.Label lblSearchPublished;
        private System.Windows.Forms.Button btnRequestMedicine;
        private System.Windows.Forms.DataGridView dataGridViewPublishedMedicines;
        private System.Windows.Forms.Label lblPublishedMedicinesTitle;
        private System.Windows.Forms.TabPage tabPageMyPublished;
        private System.Windows.Forms.Button btnRefreshMyPublished;
        private System.Windows.Forms.Button btnDeleteMyPublished;
        private System.Windows.Forms.Button btnEditMyPublished;
        private System.Windows.Forms.DataGridView dataGridViewMyPublished;
        private System.Windows.Forms.Label lblMyPublishedTitle;
        private System.Windows.Forms.Panel panelNotifications;
        private System.Windows.Forms.Button btnNotifications;
        private System.Windows.Forms.Label lblNotificationCount;
        private System.Windows.Forms.Panel panelNotificationDropdown;
        private System.Windows.Forms.ListView listViewNotifications;
        private System.Windows.Forms.TabPage tabPageMedicineRequests;
        private System.Windows.Forms.Button btnMessagesRequests;
        private System.Windows.Forms.Button btnRefreshRequests;
        private System.Windows.Forms.Button btnRejectRequest;
        private System.Windows.Forms.Button btnAcceptRequest;
        private System.Windows.Forms.DataGridView dataGridViewMedicineRequests;
        private System.Windows.Forms.Label lblMedicineRequestsTitle;
    }
}
