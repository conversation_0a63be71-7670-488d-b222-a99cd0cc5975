using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Threading.Tasks;
using System.Security.Cryptography;
using System.Text;

namespace Pharmacy_Management_System
{
    /// <summary>
    /// مدير الشبكة الأونلاين لربط الصيدليات
    /// </summary>
    public class OnlineNetworkManager
    {
        private static OnlineNetworkManager _instance;
        private static readonly object _lock = new object();
        
        // معلومات الصيدلية الحالية
        public static int CurrentPharmacyId { get; set; }
        public static string CurrentPharmacyCode { get; set; }
        public static string CurrentPharmacyName { get; set; }
        public static int CurrentUserId { get; set; }
        public static string CurrentUsername { get; set; }
        public static bool IsOnlineMode { get; set; } = false;

        // سلسلة الاتصال بقاعدة البيانات المركزية
        private string _onlineConnectionString;
        private string _localConnectionString;

        private OnlineNetworkManager()
        {
            // سلسلة الاتصال بقاعدة البيانات الموحدة - تم التحويل من PharmacyNetworkOnline إلى UnifiedPharmacy
            _onlineConnectionString = "data source = NARUTO; database=UnifiedPharmacy; integrated security =True";

            // سلسلة الاتصال بقاعدة البيانات الموحدة - تم التحويل من pharmacy إلى UnifiedPharmacy
            _localConnectionString = "data source = NARUTO; database=UnifiedPharmacy; integrated security =True";
        }

        public static OnlineNetworkManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new OnlineNetworkManager();
                    }
                }
                return _instance;
            }
        }

        #region اتصال قاعدة البيانات

        public SqlConnection GetOnlineConnection()
        {
            return new SqlConnection(_onlineConnectionString);
        }

        public SqlConnection GetLocalConnection()
        {
            return new SqlConnection(_localConnectionString);
        }

        public async Task<bool> TestOnlineConnection()
        {
            try
            {
                using (var connection = GetOnlineConnection())
                {
                    await connection.OpenAsync();
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region إدارة الصيدليات

        /// <summary>
        /// تسجيل صيدلية جديدة في الشبكة
        /// </summary>
        public async Task<(bool Success, string PharmacyCode, string Message)> RegisterPharmacy(
            string pharmacyName, string ownerName, string licenseNumber, 
            string address, string city, string region, string phone, string email,
            string adminName, string adminUsername, string adminPassword)
        {
            try
            {
                using (var connection = GetOnlineConnection())
                {
                    await connection.OpenAsync();
                    
                    using (var command = new SqlCommand("sp_RegisterPharmacy", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        
                        command.Parameters.AddWithValue("@pharmacyName", pharmacyName);
                        command.Parameters.AddWithValue("@ownerName", ownerName);
                        command.Parameters.AddWithValue("@licenseNumber", licenseNumber);
                        command.Parameters.AddWithValue("@address", address);
                        command.Parameters.AddWithValue("@city", city);
                        command.Parameters.AddWithValue("@region", region);
                        command.Parameters.AddWithValue("@phone", phone);
                        command.Parameters.AddWithValue("@email", email);
                        command.Parameters.AddWithValue("@adminName", adminName);
                        command.Parameters.AddWithValue("@adminUsername", adminUsername);
                        command.Parameters.AddWithValue("@adminPassword", HashPassword(adminPassword));

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                return (true, reader["PharmacyCode"].ToString(), reader["Message"].ToString());
                            }
                        }
                    }
                }
                
                return (false, "", "فشل في تسجيل الصيدلية");
            }
            catch (Exception ex)
            {
                return (false, "", $"خطأ: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على قائمة الصيدليات النشطة
        /// </summary>
        public async Task<DataTable> GetActivePharmacies()
        {
            try
            {
                using (var connection = GetOnlineConnection())
                {
                    await connection.OpenAsync();

                    // التأكد من وجود جدول pharmacies
                    await EnsurePharmaciesTableExists(connection);

                    // إضافة بيانات تجريبية إذا كان الجدول فارغاً
                    await EnsureSampleData(connection);

                    string query = @"
                        SELECT
                            p.id,
                            p.pharmacyCode,
                            p.pharmacyName,
                            ISNULL(p.ownerName, '') as ownerName,
                            ISNULL(p.city, '') as city,
                            ISNULL(p.region, '') as region,
                            ISNULL(p.phone, '') as phone,
                            ISNULL(p.email, '') as email,
                            p.lastOnline,
                            ISNULL(p.subscriptionType, 'Basic') as subscriptionType,
                            0 as totalUsers,
                            0 as totalMedicines,
                            0 as totalOrdersSent,
                            0 as totalOrdersReceived,
                            0 as averageRating
                        FROM pharmacies p
                        WHERE p.isActive = 1
                        ORDER BY p.pharmacyName";

                    using (var adapter = new SqlDataAdapter(query, connection))
                    {
                        var dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب الصيدليات: {ex.Message}");
            }
        }

        /// <summary>
        /// تسجيل دخول المستخدم للشبكة
        /// </summary>
        public async Task<(bool Success, string Message)> LoginToNetwork(string username, string password)
        {
            try
            {
                using (var connection = GetOnlineConnection())
                {
                    await connection.OpenAsync();
                    
                    string query = @"
                        SELECT u.id, u.pharmacyId, u.name, p.pharmacyCode, p.pharmacyName, u.passwordHash
                        FROM network_users u
                        INNER JOIN pharmacies p ON u.pharmacyId = p.id
                        WHERE u.username = @username AND u.isActive = 1 AND p.isActive = 1";
                    
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@username", username);
                        
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                string storedHash = reader["passwordHash"].ToString();
                                
                                if (VerifyPassword(password, storedHash))
                                {
                                    CurrentUserId = Convert.ToInt32(reader["id"]);
                                    CurrentPharmacyId = Convert.ToInt32(reader["pharmacyId"]);
                                    CurrentPharmacyCode = reader["pharmacyCode"].ToString();
                                    CurrentPharmacyName = reader["pharmacyName"].ToString();
                                    CurrentUsername = username;
                                    IsOnlineMode = true;
                                    
                                    // تحديث آخر تسجيل دخول
                                    await UpdateLastLogin(CurrentUserId);
                                    
                                    return (true, "تم تسجيل الدخول بنجاح");
                                }
                                else
                                {
                                    return (false, "كلمة المرور غير صحيحة");
                                }
                            }
                            else
                            {
                                return (false, "اسم المستخدم غير موجود");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return (false, $"خطأ في تسجيل الدخول: {ex.Message}");
            }
        }

        private async Task UpdateLastLogin(int userId)
        {
            try
            {
                using (var connection = GetOnlineConnection())
                {
                    await connection.OpenAsync();
                    
                    string query = "UPDATE network_users SET lastLogin = GETDATE() WHERE id = @userId";
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", userId);
                        await command.ExecuteNonQueryAsync();
                    }
                }
            }
            catch
            {
                // تجاهل الأخطاء في تحديث آخر تسجيل دخول
            }
        }

        #endregion

        #region إنشاء الجداول

        /// <summary>
        /// التأكد من وجود جدول pharmacies وإنشاؤه إذا لم يكن موجوداً
        /// </summary>
        private async Task EnsurePharmaciesTableExists(SqlConnection connection)
        {
            try
            {
                string checkTableQuery = @"
                    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies' AND xtype='U')
                    BEGIN
                        CREATE TABLE pharmacies (
                            id int IDENTITY(1,1) PRIMARY KEY,
                            pharmacyCode nvarchar(50) UNIQUE NOT NULL,
                            pharmacyName nvarchar(255) NOT NULL,
                            ownerName nvarchar(255) NULL,
                            licenseNumber nvarchar(100) NULL,
                            address nvarchar(500) NULL,
                            city nvarchar(100) NULL,
                            region nvarchar(100) NULL,
                            phone nvarchar(20) NULL,
                            email nvarchar(255) NULL,
                            subscriptionType nvarchar(50) DEFAULT 'Basic',
                            isActive bit DEFAULT 1,
                            dateRegistered datetime DEFAULT GETDATE(),
                            lastOnline datetime NULL
                        )
                    END";

                using (var command = new SqlCommand(checkTableQuery, connection))
                {
                    await command.ExecuteNonQueryAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء جدول pharmacies: {ex.Message}");
            }
        }

        /// <summary>
        /// التأكد من وجود جدول networkmedicines وإنشاؤه إذا لم يكن موجوداً
        /// </summary>
        private async Task EnsureNetworkMedicinesTableExists(SqlConnection connection)
        {
            try
            {
                // التأكد من وجود جدول pharmacies أولاً
                await EnsurePharmaciesTableExists(connection);

                string checkTableQuery = @"
                    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='networkmedicines' AND xtype='U')
                    BEGIN
                        CREATE TABLE networkmedicines (
                            id int IDENTITY(1,1) PRIMARY KEY,
                            pharmacyId int NOT NULL,
                            medicineName nvarchar(255) NOT NULL,
                            manufacturer nvarchar(255) NULL,
                            category nvarchar(100) NULL,
                            pricePerUnit decimal(10,2) NOT NULL,
                            availableQuantity int NOT NULL,
                            expiryDate datetime NOT NULL,
                            description nvarchar(500) NULL,
                            dateAdded datetime DEFAULT GETDATE(),
                            isActive bit DEFAULT 1,
                            isAvailableForSale bit DEFAULT 1
                        )
                    END";

                using (var command = new SqlCommand(checkTableQuery, connection))
                {
                    await command.ExecuteNonQueryAsync();
                }

                // التأكد من وجود جميع الأعمدة المطلوبة
                await EnsureNetworkMedicinesColumns(connection);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء جدول networkmedicines: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من وجود عمود في جدول
        /// </summary>
        private async Task<bool> CheckColumnExists(SqlConnection connection, string tableName, string columnName)
        {
            try
            {
                string checkQuery = @"
                    SELECT COUNT(*)
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = @tableName AND COLUMN_NAME = @columnName";

                using (var command = new SqlCommand(checkQuery, connection))
                {
                    command.Parameters.AddWithValue("@tableName", tableName);
                    command.Parameters.AddWithValue("@columnName", columnName);

                    int count = (int)await command.ExecuteScalarAsync();
                    return count > 0;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التأكد من وجود جميع الأعمدة المطلوبة في جدول networkmedicines
        /// </summary>
        private async Task EnsureNetworkMedicinesColumns(SqlConnection connection)
        {
            try
            {
                // التحقق من وجود عمود pricePerUnit
                if (!await CheckColumnExists(connection, "networkmedicines", "pricePerUnit"))
                {
                    string addPriceColumnQuery = @"
                        ALTER TABLE networkmedicines
                        ADD pricePerUnit decimal(10,2) NOT NULL DEFAULT 0.0";

                    using (var command = new SqlCommand(addPriceColumnQuery, connection))
                    {
                        await command.ExecuteNonQueryAsync();
                    }
                }

                // التحقق من وجود عمود description
                if (!await CheckColumnExists(connection, "networkmedicines", "description"))
                {
                    string addDescriptionColumnQuery = @"
                        ALTER TABLE networkmedicines
                        ADD description nvarchar(500) NULL DEFAULT ''";

                    using (var command = new SqlCommand(addDescriptionColumnQuery, connection))
                    {
                        await command.ExecuteNonQueryAsync();
                    }
                }

                // التحقق من وجود عمود isActive
                if (!await CheckColumnExists(connection, "networkmedicines", "isActive"))
                {
                    string addIsActiveColumnQuery = @"
                        ALTER TABLE networkmedicines
                        ADD isActive bit NOT NULL DEFAULT 1";

                    using (var command = new SqlCommand(addIsActiveColumnQuery, connection))
                    {
                        await command.ExecuteNonQueryAsync();
                    }
                }

                // التحقق من وجود عمود isAvailableForSale
                if (!await CheckColumnExists(connection, "networkmedicines", "isAvailableForSale"))
                {
                    string addIsAvailableColumnQuery = @"
                        ALTER TABLE networkmedicines
                        ADD isAvailableForSale bit NOT NULL DEFAULT 1";

                    using (var command = new SqlCommand(addIsAvailableColumnQuery, connection))
                    {
                        await command.ExecuteNonQueryAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة أعمدة جدول networkmedicines: {ex.Message}");
            }
        }

        /// <summary>
        /// إضافة بيانات تجريبية إذا كانت الجداول فارغة
        /// </summary>
        private async Task EnsureSampleData(SqlConnection connection)
        {
            try
            {
                // التحقق من وجود صيدليات
                string checkPharmaciesQuery = "SELECT COUNT(*) FROM pharmacies WHERE isActive = 1";
                using (var command = new SqlCommand(checkPharmaciesQuery, connection))
                {
                    int pharmacyCount = (int)await command.ExecuteScalarAsync();

                    if (pharmacyCount == 0)
                    {
                        // إضافة صيدليات تجريبية
                        string insertPharmaciesQuery = @"
                            INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, city, region, phone, email, subscriptionType, isActive)
                            VALUES
                            ('PH001', 'صيدلية النور', 'أحمد محمد', 'الرياض', 'الرياض', '0501234567', '<EMAIL>', 'Premium', 1),
                            ('PH002', 'صيدلية الشفاء', 'فاطمة علي', 'جدة', 'مكة المكرمة', '0507654321', '<EMAIL>', 'Basic', 1),
                            ('PH003', 'صيدلية الصحة', 'محمد سالم', 'الدمام', 'الشرقية', '0509876543', '<EMAIL>', 'Premium', 1),
                            ('PH004', 'صيدلية الأمل', 'سارة أحمد', 'المدينة المنورة', 'المدينة المنورة', '0502468135', '<EMAIL>', 'Basic', 1),
                            ('PH005', 'صيدلية الحياة', 'عبدالله خالد', 'الطائف', 'مكة المكرمة', '0508642097', '<EMAIL>', 'Premium', 1)";

                        using (var insertCommand = new SqlCommand(insertPharmaciesQuery, connection))
                        {
                            await insertCommand.ExecuteNonQueryAsync();
                        }
                    }
                }

                // التحقق من وجود أدوية في الشبكة
                string checkMedicinesQuery = "SELECT COUNT(*) FROM networkmedicines WHERE isActive = 1";
                using (var command = new SqlCommand(checkMedicinesQuery, connection))
                {
                    int medicineCount = (int)await command.ExecuteScalarAsync();

                    if (medicineCount == 0)
                    {
                        // إضافة أدوية تجريبية
                        string insertMedicinesQuery = @"
                            INSERT INTO networkmedicines (pharmacyId, medicineName, manufacturer, category, pricePerUnit, availableQuantity, expiryDate, description, isActive, isAvailableForSale)
                            VALUES
                            (1, 'باراسيتامول 500 مجم', 'شركة الدواء السعودية', 'مسكنات', 15.50, 100, DATEADD(YEAR, 2, GETDATE()), 'مسكن للألم وخافض للحرارة', 1, 1),
                            (1, 'أموكسيسيلين 250 مجم', 'شركة المضادات الحيوية', 'مضادات حيوية', 45.00, 50, DATEADD(YEAR, 1, GETDATE()), 'مضاد حيوي واسع المجال', 1, 1),
                            (2, 'إيبوبروفين 400 مجم', 'شركة الأدوية المتقدمة', 'مضادات الالتهاب', 25.75, 75, DATEADD(MONTH, 18, GETDATE()), 'مضاد للالتهاب ومسكن', 1, 1),
                            (2, 'أوميبرازول 20 مجم', 'شركة الجهاز الهضمي', 'أدوية الجهاز الهضمي', 35.25, 60, DATEADD(YEAR, 2, GETDATE()), 'لعلاج قرحة المعدة', 1, 1),
                            (3, 'لوراتادين 10 مجم', 'شركة الحساسية', 'مضادات الهيستامين', 20.00, 80, DATEADD(MONTH, 15, GETDATE()), 'لعلاج الحساسية', 1, 1),
                            (3, 'سيتريزين 10 مجم', 'شركة الأدوية الحديثة', 'مضادات الهيستامين', 18.50, 90, DATEADD(YEAR, 1, GETDATE()), 'مضاد للحساسية', 1, 1),
                            (4, 'ديكلوفيناك 50 مجم', 'شركة المسكنات', 'مضادات الالتهاب', 30.00, 40, DATEADD(MONTH, 20, GETDATE()), 'مضاد للالتهاب ومسكن قوي', 1, 1),
                            (4, 'سيمفاستاتين 20 مجم', 'شركة القلب والأوعية', 'أدوية القلب', 55.75, 35, DATEADD(YEAR, 2, GETDATE()), 'لخفض الكوليسترول', 1, 1),
                            (5, 'ميتفورمين 500 مجم', 'شركة السكري', 'أدوية السكري', 40.25, 70, DATEADD(MONTH, 24, GETDATE()), 'لعلاج السكري النوع الثاني', 1, 1),
                            (5, 'أملوديبين 5 مجم', 'شركة ضغط الدم', 'أدوية ضغط الدم', 32.50, 65, DATEADD(YEAR, 1, GETDATE()), 'لعلاج ارتفاع ضغط الدم', 1, 1)";

                        using (var insertCommand = new SqlCommand(insertMedicinesQuery, connection))
                        {
                            await insertCommand.ExecuteNonQueryAsync();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة البيانات التجريبية: {ex.Message}");
            }
        }

        #endregion

        #region إدارة الأدوية

        /// <summary>
        /// البحث عن الأدوية في الشبكة
        /// </summary>
        public async Task<DataTable> SearchNetworkMedicines(string searchTerm = "", string category = "",
            decimal minPrice = 0, decimal maxPrice = 999999, int pageNumber = 1, int pageSize = 20)
        {
            try
            {
                using (var connection = GetOnlineConnection())
                {
                    await connection.OpenAsync();

                    // التأكد من وجود جدول networkmedicines
                    await EnsureNetworkMedicinesTableExists(connection);

                    // استعلام آمن يتعامل مع الأعمدة المفقودة
                    string query = @"
                        SELECT TOP (@pageSize)
                            nm.id,
                            nm.pharmacyId,
                            nm.medicineName,
                            ISNULL(nm.manufacturer, 'غير محدد') as manufacturer,
                            ISNULL(nm.category, 'عام') as category,
                            CASE
                                WHEN EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'networkmedicines' AND COLUMN_NAME = 'pricePerUnit')
                                THEN ISNULL(nm.pricePerUnit, 0.0)
                                ELSE 0.0
                            END as pricePerUnit,
                            nm.availableQuantity,
                            nm.expiryDate,
                            CASE
                                WHEN EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'networkmedicines' AND COLUMN_NAME = 'description')
                                THEN ISNULL(nm.description, '')
                                ELSE ''
                            END as description,
                            p.pharmacyName,
                            p.pharmacyCode,
                            ISNULL(p.city, '') as city,
                            ISNULL(p.phone, '') as phone
                        FROM networkmedicines nm
                        INNER JOIN pharmacies p ON nm.pharmacyId = p.id
                        WHERE nm.pharmacyId != @currentPharmacyId
                          AND nm.availableQuantity > 0
                          AND nm.expiryDate > GETDATE()
                          AND (@searchTerm = '' OR nm.medicineName LIKE '%' + @searchTerm + '%')
                        ORDER BY nm.medicineName";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@searchTerm", searchTerm ?? "");
                        command.Parameters.AddWithValue("@currentPharmacyId", CurrentPharmacyId);
                        command.Parameters.AddWithValue("@pageSize", pageSize);

                        using (var adapter = new SqlDataAdapter(command))
                        {
                            var dataTable = new DataTable();
                            adapter.Fill(dataTable);
                            return dataTable;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث عن الأدوية: {ex.Message}");
            }
        }

        /// <summary>
        /// مشاركة دواء من المخزون المحلي للشبكة
        /// </summary>
        public async Task<bool> ShareMedicineToNetwork(string localMedicineId, string medicineName, 
            string manufacturer, decimal unitPrice, decimal wholesalePrice, int availableQuantity,
            DateTime expiryDate, string category = "", string dosageForm = "", string strength = "")
        {
            try
            {
                using (var connection = GetOnlineConnection())
                {
                    await connection.OpenAsync();
                    
                    string query = @"
                        INSERT INTO networkmedicines
                        (pharmacyId, localMedicineId, medicineName, manufacturer, category, dosageForm,
                         strength, availableQuantity, unitPrice, wholesalePrice, expiryDate, isAvailableForSale)
                        VALUES
                        (@pharmacyId, @localMedicineId, @medicineName, @manufacturer, @category, @dosageForm,
                         @strength, @availableQuantity, @unitPrice, @wholesalePrice, @expiryDate, 1)";
                    
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@pharmacyId", CurrentPharmacyId);
                        command.Parameters.AddWithValue("@localMedicineId", localMedicineId);
                        command.Parameters.AddWithValue("@medicineName", medicineName);
                        command.Parameters.AddWithValue("@manufacturer", manufacturer);
                        command.Parameters.AddWithValue("@category", category ?? "");
                        command.Parameters.AddWithValue("@dosageForm", dosageForm ?? "");
                        command.Parameters.AddWithValue("@strength", strength ?? "");
                        command.Parameters.AddWithValue("@availableQuantity", availableQuantity);
                        command.Parameters.AddWithValue("@unitPrice", unitPrice);
                        command.Parameters.AddWithValue("@wholesalePrice", wholesalePrice);
                        command.Parameters.AddWithValue("@expiryDate", expiryDate);

                        int result = await command.ExecuteNonQueryAsync();
                        return result > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في مشاركة الدواء: {ex.Message}");
            }
        }

        #endregion

        #region الأمان والتشفير

        private string HashPassword(string password)
        {
            using (SHA256 sha256Hash = SHA256.Create())
            {
                byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(password + "PharmacyNetworkSalt"));
                return Convert.ToBase64String(bytes);
            }
        }

        private bool VerifyPassword(string password, string hash)
        {
            string hashOfInput = HashPassword(password);
            return hashOfInput.Equals(hash);
        }

        #endregion

        #region تسجيل النشاطات

        public async Task LogActivity(string activityType, string description, string relatedEntityType = null, int? relatedEntityId = null)
        {
            try
            {
                if (!IsOnlineMode) return;

                using (var connection = GetOnlineConnection())
                {
                    await connection.OpenAsync();
                    
                    string query = @"
                        INSERT INTO activity_logs (pharmacyId, userId, activityType, activityDescription, relatedEntityType, relatedEntityId)
                        VALUES (@pharmacyId, @userId, @activityType, @description, @relatedEntityType, @relatedEntityId)";
                    
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@pharmacyId", CurrentPharmacyId);
                        command.Parameters.AddWithValue("@userId", CurrentUserId);
                        command.Parameters.AddWithValue("@activityType", activityType);
                        command.Parameters.AddWithValue("@description", description);
                        command.Parameters.AddWithValue("@relatedEntityType", relatedEntityType ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@relatedEntityId", relatedEntityId ?? (object)DBNull.Value);

                        await command.ExecuteNonQueryAsync();
                    }
                }
            }
            catch
            {
                // تجاهل أخطاء تسجيل النشاطات
            }
        }

        #endregion
    }
}
