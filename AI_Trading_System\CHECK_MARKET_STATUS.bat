@echo off
chcp 65001 > nul
title 📊 فحص حالة السوق - Market Status Checker

echo.
echo ================================================================
echo 📊 فحص حالة السوق
echo MARKET STATUS CHECKER
echo ================================================================
echo.

echo 💡 هذا الأداة ستتحقق من:
echo    🕐 أوقات التداول الحالية
echo    📊 حالة السوق (مفتوح/مغلق)
echo    💰 الأسعار الحالية
echo    📈 إمكانية التداول الآن
echo.

echo 🔌 متطلبات الفحص:
echo    ✅ MetaTrader 5 يعمل ومسجل دخول
echo    ✅ ملف config.ini محدث ببيانات حسابك
echo    ✅ اتصال بالإنترنت
echo.

pause

echo 🔄 فحص حالة السوق...
echo.

python MARKET_STATUS_CHECKER.py

echo.
echo ================================================================
echo ✅ انتهى الفحص
echo ================================================================
echo.

echo 💡 إذا كان السوق مفتوح:
echo    🚀 يمكنك تشغيل: RUN_SIMPLE_REAL_TRADER.bat
echo    🔥 سيتم فتح صفقات حقيقية
echo    📊 تحقق من MetaTrader 5 لرؤية النتائج
echo.

echo ⏰ إذا كان السوق مغلق:
echo    🕐 انتظر حتى أوقات التداول
echo    🌍 سوق الفوركس يعمل 24/5 (الاثنين-الجمعة)
echo    🔄 جرب مرة أخرى خلال أوقات التداول
echo.

pause
