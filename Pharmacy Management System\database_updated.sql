-- إنشاء قاعدة البيانات (اختياري - قم بتشغيله فقط إذا لم تكن قاعدة البيانات موجودة)
-- CREATE DATABASE pharmacy;
-- GO

-- USE pharmacy;
-- GO

-- ملاحظة: قاعدة البيانات pharmacy موجودة بالفعل، لذا سنقوم فقط بإضافة الجداول والأعمدة المطلوبة

-- الجداول الموجودة بالفعل (users و medic) لا نحتاج لإعادة إنشائها
-- فقط سنضيف الأعمدة المطلوبة إذا لم تكن موجودة

-- إضافة أعمدة الكميات الأصلية إلى جدول medic إذا لم تكن موجودة
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'originalQuantity')
    ALTER TABLE medic ADD originalQuantity BIGINT DEFAULT 0;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'medic' AND COLUMN_NAME = 'originalNewQuantity')
    ALTER TABLE medic ADD originalNewQuantity BIGINT DEFAULT 0;

-- جدول المبيعات الجديد (متوافق مع أنواع البيانات الموجودة)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='sales' AND xtype='U')
CREATE TABLE sales (
    id INT IDENTITY(1,1) PRIMARY KEY,
    mid VARCHAR(250),
    medicineName VARCHAR(250),
    dosage VARCHAR(100),
    quantity INT,
    pricePerUnit BIGINT,
    totalPrice BIGINT,
    employeeUsername VARCHAR(250),
    employeeName VARCHAR(250),
    saleDate DATETIME DEFAULT GETDATE()
);

-- جدول جلسات عمل الموظفين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='employee_sessions' AND xtype='U')
CREATE TABLE employee_sessions (
    id INT IDENTITY(1,1) PRIMARY KEY,
    username VARCHAR(250),
    employeeName VARCHAR(250),
    loginTime DATETIME,
    logoutTime DATETIME NULL,
    sessionDate DATE
);

-- جدول الجرعات المختلفة للأدوية
CREATE TABLE medicine_dosages (
    id INT IDENTITY(1,1) PRIMARY KEY,
    mid INT,
    dosageName NVARCHAR(100),
    dosageQuantity INT,
    dosagePrice DECIMAL(10,2),
    remainingQuantity INT DEFAULT 0,
    isActive BIT DEFAULT 1,
    FOREIGN KEY (mid) REFERENCES medic(id)
);

-- إدراج بيانات جرعات افتراضية للأدوية الموجودة
INSERT INTO medicine_dosages (mid, dosageName, dosageQuantity, dosagePrice, remainingQuantity)
SELECT id, 'جرعة عادية', quantity, perUnit, quantity
FROM medic
WHERE NOT EXISTS (SELECT 1 FROM medicine_dosages md WHERE md.mid = medic.id);

-- تحديث الكميات الأصلية للأدوية الموجودة
UPDATE medic 
SET originalQuantity = quantity 
WHERE originalQuantity = 0 OR originalQuantity IS NULL;

UPDATE medic 
SET originalNewQuantity = ISNULL(newQuantity, 0) 
WHERE originalNewQuantity = 0 OR originalNewQuantity IS NULL;

-- فهارس لتحسين الأداء
CREATE INDEX IX_sales_employeeUsername ON sales(employeeUsername);
CREATE INDEX IX_sales_saleDate ON sales(saleDate);
CREATE INDEX IX_employee_sessions_username ON employee_sessions(username);
CREATE INDEX IX_employee_sessions_sessionDate ON employee_sessions(sessionDate);
CREATE INDEX IX_medicine_dosages_mid ON medicine_dosages(mid);

-- استعلامات مفيدة للتقارير

-- 1. عرض جميع المبيعات مع معلومات الموظفين
/*
SELECT 
    s.id AS 'رقم العملية',
    s.employeeName AS 'اسم الموظف',
    s.employeeUsername AS 'اسم المستخدم',
    s.medicineName AS 'اسم الدواء',
    s.dosage AS 'الجرعة',
    s.quantity AS 'الكمية',
    s.pricePerUnit AS 'سعر الوحدة',
    s.totalPrice AS 'إجمالي السعر',
    CONVERT(VARCHAR, s.saleDate, 120) AS 'تاريخ البيع'
FROM sales s
ORDER BY s.saleDate DESC;
*/

-- 2. عرض جلسات عمل الموظفين
/*
SELECT 
    es.username AS 'اسم المستخدم',
    es.employeeName AS 'اسم الموظف',
    CONVERT(VARCHAR, es.loginTime, 120) AS 'وقت الدخول',
    CASE 
        WHEN es.logoutTime IS NULL THEN 'لم يسجل الخروج'
        ELSE CONVERT(VARCHAR, es.logoutTime, 120)
    END AS 'وقت الخروج',
    CASE 
        WHEN es.logoutTime IS NULL THEN 'جلسة نشطة'
        ELSE CAST(DATEDIFF(MINUTE, es.loginTime, es.logoutTime) AS VARCHAR) + ' دقيقة'
    END AS 'مدة الجلسة'
FROM employee_sessions es
ORDER BY es.loginTime DESC;
*/

-- 3. تقرير مبيعات حسب الموظف
/*
SELECT 
    s.employeeName AS 'اسم الموظف',
    COUNT(*) AS 'عدد العمليات',
    SUM(s.quantity) AS 'إجمالي الكمية المباعة',
    SUM(s.totalPrice) AS 'إجمالي المبيعات'
FROM sales s
GROUP BY s.employeeName, s.employeeUsername
ORDER BY SUM(s.totalPrice) DESC;
*/

-- 4. تقرير مبيعات حسب التاريخ
/*
SELECT 
    CONVERT(DATE, s.saleDate) AS 'التاريخ',
    COUNT(*) AS 'عدد العمليات',
    SUM(s.quantity) AS 'إجمالي الكمية',
    SUM(s.totalPrice) AS 'إجمالي المبيعات'
FROM sales s
GROUP BY CONVERT(DATE, s.saleDate)
ORDER BY CONVERT(DATE, s.saleDate) DESC;
*/

-- 5. عرض الأدوية مع الجرعات المتاحة
/*
SELECT 
    m.mname AS 'اسم الدواء',
    md.dosageName AS 'اسم الجرعة',
    md.dosageQuantity AS 'كمية الجرعة',
    md.dosagePrice AS 'سعر الجرعة',
    md.remainingQuantity AS 'الكمية المتبقية'
FROM medic m
JOIN medicine_dosages md ON m.id = md.mid
WHERE md.isActive = 1
ORDER BY m.mname, md.dosageName;
*/

-- إجراءات مخزنة مفيدة

-- إجراء لتسجيل دخول الموظف
CREATE PROCEDURE sp_RecordEmployeeLogin
    @username NVARCHAR(100),
    @employeeName NVARCHAR(255)
AS
BEGIN
    INSERT INTO employee_sessions (username, employeeName, loginTime, sessionDate)
    VALUES (@username, @employeeName, GETDATE(), CONVERT(DATE, GETDATE()));
END;

-- إجراء لتسجيل خروج الموظف
CREATE PROCEDURE sp_RecordEmployeeLogout
    @username NVARCHAR(100)
AS
BEGIN
    UPDATE employee_sessions 
    SET logoutTime = GETDATE() 
    WHERE username = @username 
      AND logoutTime IS NULL 
      AND sessionDate = CONVERT(DATE, GETDATE());
END;

-- إجراء لحفظ عملية بيع
CREATE PROCEDURE sp_RecordSale
    @mid INT,
    @medicineName NVARCHAR(255),
    @dosage NVARCHAR(100),
    @quantity INT,
    @pricePerUnit DECIMAL(10,2),
    @totalPrice DECIMAL(10,2),
    @employeeUsername NVARCHAR(100),
    @employeeName NVARCHAR(255)
AS
BEGIN
    INSERT INTO sales (mid, medicineName, dosage, quantity, pricePerUnit, totalPrice, employeeUsername, employeeName, saleDate)
    VALUES (@mid, @medicineName, @dosage, @quantity, @pricePerUnit, @totalPrice, @employeeUsername, @employeeName, GETDATE());
END;

-- منح الصلاحيات (اختياري)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON users TO [pharmacy_user];
-- GRANT SELECT, INSERT, UPDATE, DELETE ON medic TO [pharmacy_user];
-- GRANT SELECT, INSERT, UPDATE, DELETE ON sales TO [pharmacy_user];
-- GRANT SELECT, INSERT, UPDATE, DELETE ON employee_sessions TO [pharmacy_user];
-- GRANT SELECT, INSERT, UPDATE, DELETE ON medicine_dosages TO [pharmacy_user];

PRINT 'تم إنشاء قاعدة البيانات والجداول بنجاح!';
