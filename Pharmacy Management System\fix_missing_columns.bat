@echo off
chcp 65001 >nul
echo ========================================
echo 🔧 إصلاح الأعمدة المفقودة في قاعدة البيانات
echo 🔧 Fix Missing Database Columns
echo ========================================
echo.

echo 🎯 هذا السكريپت سيقوم بـ:
echo - إصلاح خطأ "Invalid column name 'pharmacyId'"
echo - إصلاح خطأ "Invalid column name 'userId'"
echo - إضافة جميع الأعمدة المفقودة في جدول users
echo - إنشاء جدول pharmacies إذا لم يكن موجوداً
echo - إضافة صيدلية افتراضية
echo - إضافة مستخدمين افتراضيين
echo - إضافة أدوية تجريبية للشبكة
echo.

echo ⚠️  هذا سيحل مشكلة زر "Connect" في صفحة المتجر
echo ⚠️  This will fix the "Connect" button issue in store page
echo.

pause
echo.

echo 🚀 بدء عملية الإصلاح...
echo Starting fix process...
echo.

echo الخطوة 1: تشغيل سكريپت إصلاح الأعمدة...
echo Step 1: Running column fix script...
sqlcmd -S NARUTO -E -i fix_missing_columns.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo   ✅ تم إصلاح جميع المشاكل بنجاح!
    echo   ✅ All issues fixed successfully!
    echo ========================================
    echo.
    echo 📊 ما تم إصلاحه:
    echo ✅ إضافة عمود pharmacyId إلى جدول users
    echo ✅ إضافة عمود isActive إلى جدول users
    echo ✅ إضافة عمود createdDate إلى جدول users
    echo ✅ إنشاء جدول pharmacies مع صيدلية افتراضية
    echo ✅ إضافة Foreign Key بين users و pharmacies
    echo ✅ تحديث جميع المستخدمين الموجودين
    echo ✅ إضافة مستخدمين افتراضيين (admin, pharmacist)
    echo ✅ إنشاء جدول networkmedicines مع أدوية تجريبية
    echo.
    echo 🎯 النتيجة:
    echo - لن تظهر رسالة "Invalid column name 'pharmacyId'" بعد الآن
    echo - لن تظهر رسالة "Invalid column name 'userId'" بعد الآن
    echo - زر "Connect" في صفحة المتجر سيعمل بدون أخطاء
    echo - ستظهر الصيدليات والأدوية في الشبكة
    echo.
    echo 🔐 بيانات تسجيل الدخول:
    echo المدير: admin / admin123
    echo الصيدلي: pharmacist / pharm123
    echo.
    echo 🧪 اختبر الآن:
    echo 1. شغل البرنامج من Visual Studio
    echo 2. سجل دخول بأي من الحسابات أعلاه
    echo 3. اذهب لصفحة "Pharmacy Store"
    echo 4. اضغط زر "Connect" ✅ (بدون رسائل خطأ!)
    echo 5. ستجد الصيدليات في تبويب "Pharmacies"
    echo 6. ستجد الأدوية في تبويب "Search"
    echo.
    echo 📁 المشاكل المحلولة:
    echo ✅ خطأ "Invalid column name 'pharmacyId'"
    echo ✅ خطأ "Invalid column name 'userId'"
    echo ✅ مشكلة زر "Connect" في صفحة المتجر
    echo ✅ عدم ظهور البيانات في الشبكة
    echo ✅ مشاكل تسجيل الدخول مع قاعدة البيانات
    echo.
    echo 🎊 النظام الآن:
    echo - مستقر تماماً مع قاعدة البيانات الموحدة
    echo - جميع الأعمدة المطلوبة موجودة
    echo - بيانات افتراضية غنية للاختبار
    echo - شبكة صيدليات تعمل بشكل مثالي
    echo.
) else (
    echo.
    echo ========================================
    echo   ❌ حدث خطأ أثناء الإصلاح!
    echo   ❌ Fix process failed!
    echo ========================================
    echo.
    echo 🔍 الأسباب المحتملة:
    echo 1. SQL Server غير مشغل
    echo 2. اسم الخادم NARUTO غير صحيح
    echo 3. عدم وجود صلاحيات كافية
    echo 4. قاعدة البيانات UnifiedPharmacy غير موجودة
    echo.
    echo 🛠️  الحلول:
    echo 1. تأكد من تشغيل SQL Server
    echo 2. تحقق من اسم الخادم في الملفات
    echo 3. شغل Command Prompt كمدير
    echo 4. شغل migrate_to_unified_database.bat أولاً
    echo.
    echo 💡 يمكنك أيضاً:
    echo - فتح fix_missing_columns.sql في SQL Server Management Studio
    echo - تشغيله يدوياً بالضغط على F5
    echo.
    echo 📋 الترتيب الصحيح:
    echo 1. شغل migrate_to_unified_database.bat
    echo 2. ثم شغل fix_missing_columns.bat
    echo 3. ثم اختبر البرنامج
    echo.
)

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
