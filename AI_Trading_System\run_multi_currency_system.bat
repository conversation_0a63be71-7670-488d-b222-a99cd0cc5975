@echo off
title Multi-Currency Intelligent Trading System V3

echo.
echo ================================================================
echo   MULTI-CURRENCY INTELLIGENT TRADING SYSTEM V3
echo   Advanced AI Trading with Multiple Currencies and Strategies
echo ================================================================
echo.
echo 🌟 FEATURES:
echo ✅ 12+ Currency Pairs Simultaneous Analysis
echo ✅ 6 Advanced Trading Strategies
echo ✅ 9 Timeframes Analysis (M1 to MN1)
echo ✅ Self-Learning and Adaptation
echo ✅ Cross-Currency Pattern Recognition
echo ✅ Advanced Risk Management
echo ✅ Real-time Strategy Optimization
echo.

echo 📊 SUPPORTED CURRENCIES:
echo    Major Pairs: EURUSD, GBPUSD, USDJPY, AUDUSD, USDCAD, NZDUSD
echo    Cross Pairs: EURGBP, EURJPY, GBPJPY, AUDJPY, USDCH<PERSON>, EURCHF
echo.

echo 🧠 TRADING STRATEGIES:
echo    1. Trend Following    4. Momentum
echo    2. Mean Reversion     5. Scalping
echo    3. Breakout          6. Swing Trading
echo.

echo ⏰ TIMEFRAMES:
echo    M1, M5, M15, M30, H1, H4, D1, W1, MN1
echo.

echo Installing required libraries...
pip install MetaTrader5 pandas numpy scikit-learn talib --quiet

echo.
echo 🚀 Starting Multi-Currency Intelligent Trading System...
echo.

python multi_currency_intelligent_system.py

if errorlevel 1 (
    echo.
    echo ❌ Error starting system!
    echo 💡 Make sure MetaTrader 5 is installed and running
    echo 💡 Check your internet connection
    echo 💡 Verify config.ini settings
    echo.
)

pause
