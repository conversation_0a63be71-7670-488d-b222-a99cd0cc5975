#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
from typing import Dict, List, Optional
import MetaTrader5 as mt5

# إعداد المكتبات للتحليل الفني
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    print("⚠️ تحذير: مكتبة TA-Lib غير متوفرة، سيتم استخدام مؤشرات بديلة")

from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler

class RealCryptoTradingSystem:
    def __init__(self, demo_mode=True):
        self.demo_mode = demo_mode
        self.connected = False
        
        # إعدادات التداول
        self.confidence_threshold = 70.0  # نسبة الثقة المطلوبة للدخول (افتراضي 70%)
        self.risk_per_trade = 0.02  # 2% من رأس المال لكل صفقة
        self.stop_loss_pips = 500   # وقف الخسارة
        self.take_profit_pips = 1000  # جني الربح
        
        # أزواج العملات الرقمية المدعومة
        self.crypto_pairs = {
            'BTCUSD': {'name': 'Bitcoin', 'symbol': 'BTC', 'min_price': 10000, 'max_price': 100000},
            'ETHUSD': {'name': 'Ethereum', 'symbol': 'ETH', 'min_price': 1000, 'max_price': 10000},
            'LTCUSD': {'name': 'Litecoin', 'symbol': 'LTC', 'min_price': 50, 'max_price': 500},
            'XRPUSD': {'name': 'Ripple', 'symbol': 'XRP', 'min_price': 0.3, 'max_price': 3},
            'ADAUSD': {'name': 'Cardano', 'symbol': 'ADA', 'min_price': 0.2, 'max_price': 5},
            'DOTUSD': {'name': 'Polkadot', 'symbol': 'DOT', 'min_price': 5, 'max_price': 50},
            'LINKUSD': {'name': 'Chainlink', 'symbol': 'LINK', 'min_price': 5, 'max_price': 50},
            'UNIUSD': {'name': 'Uniswap', 'symbol': 'UNI', 'min_price': 3, 'max_price': 30}
        }
        
        self.current_pair = 'BTCUSD'
        
        # بيانات المحاكاة
        self.demo_balance = 10000.0
        self.demo_equity = 10000.0
        self.demo_positions = {}
        self.position_counter = 1000
        
        # نماذج التعلم الآلي
        self.price_model = None
        self.scaler = StandardScaler()
        self.model_trained = False
        
        # إعداد السجلات
        self.logger = self._setup_logger()
        
        # بيانات السوق
        self.market_data = {}
        
    def _setup_logger(self):
        """إعداد نظام السجلات"""
        logger = logging.getLogger('RealCryptoTrading')
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            os.makedirs('logs', exist_ok=True)
            handler = logging.FileHandler(f'logs/real_crypto_trading_{datetime.now().strftime("%Y%m%d")}.log',
                                        encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger
    
    def set_confidence_threshold(self, threshold: float):
        """تحديد نسبة الثقة المطلوبة للدخول"""
        if 0 <= threshold <= 100:
            self.confidence_threshold = threshold
            self.logger.info(f"تم تحديد نسبة الثقة إلى: {threshold}%")
            return True
        return False
    
    def get_crypto_price(self, symbol: str) -> Optional[float]:
        """الحصول على السعر الحقيقي للعملة الرقمية"""
        try:
            # استخدام CoinGecko API للحصول على الأسعار الحقيقية
            crypto_id_map = {
                'BTC': 'bitcoin',
                'ETH': 'ethereum', 
                'LTC': 'litecoin',
                'XRP': 'ripple',
                'ADA': 'cardano',
                'DOT': 'polkadot',
                'LINK': 'chainlink',
                'UNI': 'uniswap'
            }
            
            if symbol in crypto_id_map:
                url = f"https://api.coingecko.com/api/v3/simple/price?ids={crypto_id_map[symbol]}&vs_currencies=usd"
                response = requests.get(url, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    price = data[crypto_id_map[symbol]]['usd']
                    return float(price)
            
            return None
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على سعر {symbol}: {str(e)}")
            return None
    
    def get_historical_data(self, pair: str, timeframe: str = '1h', limit: int = 100) -> Optional[pd.DataFrame]:
        """الحصول على البيانات التاريخية"""
        try:
            symbol = self.crypto_pairs[pair]['symbol']
            
            # استخدام CoinGecko API للبيانات التاريخية
            crypto_id_map = {
                'BTC': 'bitcoin',
                'ETH': 'ethereum',
                'LTC': 'litecoin', 
                'XRP': 'ripple',
                'ADA': 'cardano',
                'DOT': 'polkadot',
                'LINK': 'chainlink',
                'UNI': 'uniswap'
            }
            
            if symbol not in crypto_id_map:
                return None
            
            # الحصول على البيانات لآخر 30 يوم
            url = f"https://api.coingecko.com/api/v3/coins/{crypto_id_map[symbol]}/market_chart"
            params = {
                'vs_currency': 'usd',
                'days': '30',
                'interval': 'hourly'
            }
            
            response = requests.get(url, params=params, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                
                # تحويل البيانات إلى DataFrame
                prices = data['prices']
                volumes = data['total_volumes']
                
                df = pd.DataFrame(prices, columns=['timestamp', 'close'])
                df['volume'] = [v[1] for v in volumes]
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.set_index('timestamp', inplace=True)
                
                # إضافة OHLC (تقريبي)
                df['open'] = df['close'].shift(1)
                df['high'] = df[['open', 'close']].max(axis=1) * 1.002  # تقريب للـ high
                df['low'] = df[['open', 'close']].min(axis=1) * 0.998   # تقريب للـ low
                
                # إزالة القيم المفقودة
                df = df.dropna()
                
                return df.tail(limit)
            
            return None
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على البيانات التاريخية لـ {pair}: {str(e)}")
            return None
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """حساب المؤشرات الفنية"""
        try:
            if len(df) < 20:
                return df
            
            # Moving Averages
            df['sma_20'] = df['close'].rolling(window=20).mean()
            df['sma_50'] = df['close'].rolling(window=50).mean()
            df['ema_12'] = df['close'].ewm(span=12).mean()
            df['ema_26'] = df['close'].ewm(span=26).mean()
            
            # MACD
            df['macd'] = df['ema_12'] - df['ema_26']
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            df['macd_histogram'] = df['macd'] - df['macd_signal']
            
            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # Bollinger Bands
            df['bb_middle'] = df['close'].rolling(window=20).mean()
            bb_std = df['close'].rolling(window=20).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
            df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
            
            # Volume indicators
            df['volume_sma'] = df['volume'].rolling(window=20).mean()
            
            # Price change
            df['price_change'] = df['close'].pct_change()
            df['volatility'] = df['price_change'].rolling(window=20).std()
            
            return df
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب المؤشرات الفنية: {str(e)}")
            return df
    
    def analyze_market(self, pair: str) -> Dict:
        """تحليل السوق للعملة الرقمية"""
        try:
            self.logger.info(f"تحليل السوق لـ {pair}...")
            
            # الحصول على السعر الحالي
            symbol = self.crypto_pairs[pair]['symbol']
            current_price = self.get_crypto_price(symbol)
            
            if not current_price:
                return {'error': 'فشل في الحصول على السعر'}
            
            # الحصول على البيانات التاريخية
            df = self.get_historical_data(pair)
            
            if df is None or len(df) < 20:
                return {'error': 'بيانات غير كافية للتحليل'}
            
            # حساب المؤشرات الفنية
            df = self.calculate_technical_indicators(df)
            
            # الحصول على آخر القيم
            latest = df.iloc[-1]
            
            # تحليل الاتجاه
            signals = []
            confidence_factors = []
            
            # تحليل المتوسطات المتحركة
            if latest['close'] > latest['sma_20']:
                signals.append('buy')
                confidence_factors.append(15)
            else:
                signals.append('sell')
                confidence_factors.append(15)
            
            # تحليل MACD
            if latest['macd'] > latest['macd_signal']:
                signals.append('buy')
                confidence_factors.append(20)
            else:
                signals.append('sell')
                confidence_factors.append(20)
            
            # تحليل RSI
            if latest['rsi'] < 30:  # oversold
                signals.append('buy')
                confidence_factors.append(25)
            elif latest['rsi'] > 70:  # overbought
                signals.append('sell')
                confidence_factors.append(25)
            else:
                confidence_factors.append(10)
            
            # تحليل Bollinger Bands
            if latest['close'] < latest['bb_lower']:
                signals.append('buy')
                confidence_factors.append(20)
            elif latest['close'] > latest['bb_upper']:
                signals.append('sell')
                confidence_factors.append(20)
            else:
                confidence_factors.append(10)
            
            # تحديد القرار النهائي
            buy_signals = signals.count('buy')
            sell_signals = signals.count('sell')
            
            if buy_signals > sell_signals:
                decision = 'buy'
            elif sell_signals > buy_signals:
                decision = 'sell'
            else:
                decision = 'hold'
            
            # حساب نسبة الثقة
            confidence = sum(confidence_factors)
            
            # إضافة عوامل إضافية للثقة
            if abs(latest['macd_histogram']) > 0.1:
                confidence += 10
            
            if latest['volatility'] < 0.05:  # تقلبات منخفضة
                confidence += 5
            
            # تحديد نسبة الثقة النهائية
            confidence = min(confidence, 100)
            
            analysis_result = {
                'pair': pair,
                'price': current_price,
                'decision': decision,
                'confidence': confidence,
                'rsi': latest['rsi'],
                'macd': latest['macd'],
                'macd_signal': latest['macd_signal'],
                'sma_20': latest['sma_20'],
                'sma_50': latest['sma_50'],
                'bb_upper': latest['bb_upper'],
                'bb_lower': latest['bb_lower'],
                'volatility': latest['volatility'],
                'volume': latest['volume'],
                'timestamp': datetime.now()
            }
            
            self.logger.info(f"تحليل {pair}: {decision} بثقة {confidence:.1f}%")
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل السوق لـ {pair}: {str(e)}")
            return {'error': str(e)}
    
    def should_enter_trade(self, analysis: Dict) -> bool:
        """تحديد ما إذا كان يجب الدخول في صفقة"""
        if 'error' in analysis:
            return False
        
        confidence = analysis.get('confidence', 0)
        decision = analysis.get('decision', 'hold')
        
        # التحقق من نسبة الثقة
        if confidence >= self.confidence_threshold and decision in ['buy', 'sell']:
            self.logger.info(f"شروط الدخول متوفرة: {decision} بثقة {confidence:.1f}% (المطلوب: {self.confidence_threshold}%)")
            return True
        
        self.logger.info(f"شروط الدخول غير متوفرة: {decision} بثقة {confidence:.1f}% (المطلوب: {self.confidence_threshold}%)")
        return False

    def execute_trade(self, analysis: Dict) -> bool:
        """تنفيذ الصفقة"""
        try:
            if not self.should_enter_trade(analysis):
                return False

            pair = analysis['pair']
            decision = analysis['decision']
            price = analysis['price']
            confidence = analysis['confidence']

            # حساب حجم الصفقة
            if self.demo_mode:
                account_balance = self.demo_balance
            else:
                # في الوضع الحقيقي، يمكن ربطه بـ MT5 أو منصة أخرى
                account_balance = 10000  # مثال

            risk_amount = account_balance * self.risk_per_trade

            # تحديد نوع الأمر
            order_type = 'BUY' if decision == 'buy' else 'SELL'

            # حساب وقف الخسارة وجني الربح
            if decision == 'buy':
                stop_loss = price - (self.stop_loss_pips * 0.01)
                take_profit = price + (self.take_profit_pips * 0.01)
            else:
                stop_loss = price + (self.stop_loss_pips * 0.01)
                take_profit = price - (self.take_profit_pips * 0.01)

            # حساب حجم الصفقة بناءً على المخاطرة
            pip_value = 0.01  # قيمة النقطة
            distance_to_sl = abs(price - stop_loss)
            lot_size = risk_amount / distance_to_sl if distance_to_sl > 0 else 0.01
            lot_size = max(0.01, min(lot_size, 1.0))  # حد أدنى وأقصى

            if self.demo_mode:
                # تنفيذ في المحاكاة
                position_id = self.position_counter
                self.position_counter += 1

                position = {
                    'id': position_id,
                    'pair': pair,
                    'type': order_type,
                    'volume': lot_size,
                    'open_price': price,
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'open_time': datetime.now(),
                    'confidence': confidence,
                    'profit': 0.0
                }

                self.demo_positions[position_id] = position

                self.logger.info(f"✅ تم فتح صفقة تجريبية:")
                self.logger.info(f"   الزوج: {pair}")
                self.logger.info(f"   النوع: {order_type}")
                self.logger.info(f"   الحجم: {lot_size:.2f}")
                self.logger.info(f"   سعر الدخول: ${price:.2f}")
                self.logger.info(f"   وقف الخسارة: ${stop_loss:.2f}")
                self.logger.info(f"   جني الربح: ${take_profit:.2f}")
                self.logger.info(f"   نسبة الثقة: {confidence:.1f}%")

                return True
            else:
                # تنفيذ حقيقي (يمكن ربطه بـ MT5 أو API أخرى)
                self.logger.info(f"🚀 محاولة فتح صفقة حقيقية:")
                self.logger.info(f"   الزوج: {pair}")
                self.logger.info(f"   النوع: {order_type}")
                self.logger.info(f"   الحجم: {lot_size:.2f}")
                self.logger.info(f"   السعر: ${price:.2f}")

                # هنا يمكن إضافة كود التنفيذ الحقيقي
                # مثل استخدام API للوسيط أو MT5

                return True

        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الصفقة: {str(e)}")
            return False

    def update_positions(self):
        """تحديث الصفقات المفتوحة"""
        try:
            if not self.demo_mode or not self.demo_positions:
                return

            positions_to_close = []

            for pos_id, position in self.demo_positions.items():
                pair = position['pair']
                symbol = self.crypto_pairs[pair]['symbol']
                current_price = self.get_crypto_price(symbol)

                if current_price:
                    # حساب الربح/الخسارة
                    if position['type'] == 'BUY':
                        profit = (current_price - position['open_price']) * position['volume'] * 100
                    else:
                        profit = (position['open_price'] - current_price) * position['volume'] * 100

                    position['profit'] = profit
                    position['current_price'] = current_price

                    # فحص وقف الخسارة وجني الربح
                    should_close = False
                    close_reason = ""

                    if position['type'] == 'BUY':
                        if current_price <= position['stop_loss']:
                            should_close = True
                            close_reason = "وقف خسارة"
                        elif current_price >= position['take_profit']:
                            should_close = True
                            close_reason = "جني ربح"
                    else:
                        if current_price >= position['stop_loss']:
                            should_close = True
                            close_reason = "وقف خسارة"
                        elif current_price <= position['take_profit']:
                            should_close = True
                            close_reason = "جني ربح"

                    if should_close:
                        positions_to_close.append((pos_id, close_reason, profit))

            # إغلاق الصفقات
            for pos_id, reason, profit in positions_to_close:
                position = self.demo_positions[pos_id]
                self.demo_balance += profit
                self.demo_equity = self.demo_balance

                self.logger.info(f"🔒 تم إغلاق الصفقة {pos_id}:")
                self.logger.info(f"   السبب: {reason}")
                self.logger.info(f"   الربح/الخسارة: ${profit:.2f}")
                self.logger.info(f"   الرصيد الجديد: ${self.demo_balance:.2f}")

                del self.demo_positions[pos_id]

        except Exception as e:
            self.logger.error(f"خطأ في تحديث الصفقات: {str(e)}")

    def get_account_info(self) -> Dict:
        """الحصول على معلومات الحساب"""
        try:
            if self.demo_mode:
                # حساب إجمالي الربح من الصفقات المفتوحة
                total_profit = sum(pos.get('profit', 0) for pos in self.demo_positions.values())

                return {
                    'balance': self.demo_balance,
                    'equity': self.demo_balance + total_profit,
                    'margin': 0,
                    'free_margin': self.demo_balance + total_profit,
                    'profit': total_profit,
                    'currency': 'USD',
                    'company': 'Real Crypto Trading System',
                    'name': 'Demo Account',
                    'server': 'Demo Server',
                    'leverage': 100,
                    'margin_level': 0,
                    'open_positions': len(self.demo_positions)
                }
            else:
                # في الوضع الحقيقي، يمكن الحصول على المعلومات من MT5 أو API أخرى
                return {
                    'balance': 0,
                    'equity': 0,
                    'margin': 0,
                    'free_margin': 0,
                    'profit': 0,
                    'currency': 'USD',
                    'company': 'Real Broker',
                    'name': 'Live Account',
                    'server': 'Live Server',
                    'leverage': 100,
                    'margin_level': 0,
                    'open_positions': 0
                }

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على معلومات الحساب: {str(e)}")
            return {}

    def get_open_positions(self) -> List[Dict]:
        """الحصول على الصفقات المفتوحة"""
        try:
            if self.demo_mode:
                positions = []
                for pos_id, position in self.demo_positions.items():
                    positions.append({
                        'ticket': pos_id,
                        'symbol': position['pair'],
                        'type': 'شراء' if position['type'] == 'BUY' else 'بيع',
                        'volume': position['volume'],
                        'price_open': position['open_price'],
                        'price_current': position.get('current_price', position['open_price']),
                        'profit': position.get('profit', 0),
                        'swap': 0,
                        'comment': f"AI Trade - Confidence: {position['confidence']:.1f}%",
                        'time': position['open_time']
                    })
                return positions
            else:
                return []

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الصفقات المفتوحة: {str(e)}")
            return []

    def connect(self) -> bool:
        """الاتصال بالنظام"""
        try:
            self.logger.info("محاولة الاتصال بنظام تداول العملات الرقمية...")

            # اختبار الاتصال بالإنترنت والـ API
            test_price = self.get_crypto_price('BTC')
            if test_price:
                self.connected = True
                self.logger.info(f"✅ تم الاتصال بنجاح! سعر البيتكوين: ${test_price:.2f}")
                return True
            else:
                self.logger.error("❌ فشل في الاتصال بـ API")
                return False

        except Exception as e:
            self.logger.error(f"خطأ في الاتصال: {str(e)}")
            return False

    def disconnect(self):
        """قطع الاتصال"""
        self.connected = False
        self.logger.info("تم قطع الاتصال")

    def get_available_pairs(self) -> List[str]:
        """الحصول على أزواج العملات المتوفرة"""
        return list(self.crypto_pairs.keys())

    def set_current_pair(self, pair: str) -> bool:
        """تحديد الزوج الحالي للتداول"""
        if pair in self.crypto_pairs:
            self.current_pair = pair
            self.logger.info(f"تم تحديد الزوج: {pair}")
            return True
        return False
