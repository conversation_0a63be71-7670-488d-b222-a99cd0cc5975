#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Multi-Currency Intelligent Trading System V3
Advanced AI Trading System with Multiple Currencies and Strategies

Features:
- ✅ Multiple currency pairs simultaneous analysis
- ✅ Multiple strategies per currency
- ✅ Multiple timeframes analysis (M1 to MN1)
- ✅ Self-learning and adaptation
- ✅ Cross-currency pattern recognition
- ✅ Advanced risk management
- ✅ Real-time strategy optimization
"""

import os
import sys
import time
import threading
import configparser
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

# Core libraries
try:
    import numpy as np
    import pandas as pd
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import train_test_split
    import joblib
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("⚠️ scikit-learn not available. Install: pip install scikit-learn")

# MetaTrader 5
try:
    import MetaTrader5 as mt5
    MT5_AVAILABLE = True
except ImportError:
    MT5_AVAILABLE = False
    print("⚠️ MetaTrader5 not available. Install: pip install MetaTrader5")

# Technical Analysis
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    print("⚠️ TA-Lib not available. Using basic indicators.")

class MultiCurrencyIntelligentSystem:
    """
    🧠 Multi-Currency Intelligent Trading System
    
    Advanced AI system that:
    - Analyzes multiple currency pairs simultaneously
    - Uses multiple strategies per currency
    - Learns from experience across all currencies
    - Adapts strategies based on market conditions
    - Optimizes performance continuously
    """
    
    def __init__(self, config_file='config.ini'):
        """Initialize the multi-currency intelligent system"""
        print("🚀 Initializing Multi-Currency Intelligent Trading System V3...")
        
        # Load configuration
        self.config = configparser.ConfigParser()
        self.config.read(config_file)
        
        # System state
        self.is_connected = False
        self.is_trading = False
        self.system_start_time = datetime.now()
        
        # Multi-currency setup
        self.setup_currencies()
        
        # Multi-strategy setup
        self.setup_strategies()
        
        # Multi-timeframe setup
        self.setup_timeframes()
        
        # Learning system
        self.setup_learning_system()
        
        # Performance tracking
        self.performance_tracker = {}
        self.strategy_performance = {}
        self.currency_performance = {}
        
        # Active positions and analysis
        self.active_positions = {}
        self.active_analysis = {}
        self.market_conditions = {}
        
        print("✅ Multi-Currency Intelligent System initialized successfully!")
    
    def setup_currencies(self):
        """Setup multiple currency pairs for analysis"""
        try:
            # Get currency pairs from config
            major_pairs = self.config.get('MULTI_CURRENCY_TRADING', 'major_pairs', fallback='EURUSD,GBPUSD,USDJPY').split(',')
            cross_pairs = self.config.get('MULTI_CURRENCY_TRADING', 'cross_pairs', fallback='EURGBP,EURJPY').split(',')
            active_symbols = self.config.get('MULTI_CURRENCY_TRADING', 'active_symbols', fallback='EURUSD,GBPUSD,USDJPY').split(',')
            
            # Clean and organize currency pairs
            self.major_pairs = [pair.strip() for pair in major_pairs]
            self.cross_pairs = [pair.strip() for pair in cross_pairs]
            self.active_symbols = [pair.strip() for pair in active_symbols]
            self.all_currencies = list(set(self.major_pairs + self.cross_pairs))
            
            # Trading limits
            self.max_positions_total = int(self.config.get('MULTI_CURRENCY_TRADING', 'max_positions_total', fallback='15'))
            self.max_positions_per_symbol = int(self.config.get('MULTI_CURRENCY_TRADING', 'max_positions_per_symbol', fallback='2'))
            self.max_concurrent_analysis = int(self.config.get('MULTI_CURRENCY_TRADING', 'max_concurrent_analysis', fallback='12'))
            
            print(f"📈 Configured {len(self.active_symbols)} currency pairs for analysis:")
            for i, symbol in enumerate(self.active_symbols, 1):
                print(f"   {i}. {symbol}")
                
        except Exception as e:
            print(f"❌ Error setting up currencies: {e}")
            # Fallback to basic setup
            self.active_symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
            self.major_pairs = self.active_symbols
            self.cross_pairs = []
            self.all_currencies = self.active_symbols
    
    def setup_strategies(self):
        """Setup multiple trading strategies"""
        try:
            # Get strategies from config
            strategies_str = self.config.get('MULTI_STRATEGY_ANALYSIS', 'strategies', 
                                           fallback='trend_following,mean_reversion,breakout,momentum')
            self.strategies = [strategy.strip() for strategy in strategies_str.split(',')]
            
            # Strategy configurations
            self.strategy_configs = {
                'trend_following': {
                    'indicators': ['SMA', 'EMA', 'MACD', 'ADX'],
                    'min_trend_strength': 0.7,
                    'timeframes': ['H1', 'H4', 'D1']
                },
                'mean_reversion': {
                    'indicators': ['RSI', 'Bollinger_Bands', 'Stochastic'],
                    'oversold_threshold': 30,
                    'overbought_threshold': 70,
                    'timeframes': ['M15', 'M30', 'H1']
                },
                'breakout': {
                    'indicators': ['ATR', 'Volume', 'Support_Resistance'],
                    'min_volatility': 0.5,
                    'timeframes': ['M5', 'M15', 'M30']
                },
                'momentum': {
                    'indicators': ['MACD', 'RSI', 'Williams_R'],
                    'momentum_threshold': 0.6,
                    'timeframes': ['M15', 'H1', 'H4']
                },
                'scalping': {
                    'indicators': ['EMA', 'Stochastic', 'CCI'],
                    'quick_profit_target': 10,
                    'timeframes': ['M1', 'M5']
                },
                'swing_trading': {
                    'indicators': ['SMA', 'MACD', 'Fibonacci'],
                    'swing_duration': '1-5 days',
                    'timeframes': ['H4', 'D1', 'W1']
                }
            }
            
            # Strategy weights (will be learned and adapted)
            self.strategy_weights = {strategy: 1.0 for strategy in self.strategies}
            
            print(f"🧠 Configured {len(self.strategies)} trading strategies:")
            for i, strategy in enumerate(self.strategies, 1):
                print(f"   {i}. {strategy.replace('_', ' ').title()}")
                
        except Exception as e:
            print(f"❌ Error setting up strategies: {e}")
            # Fallback to basic strategies
            self.strategies = ['trend_following', 'mean_reversion', 'breakout']
    
    def setup_timeframes(self):
        """Setup multiple timeframes for analysis"""
        try:
            # Get timeframes from config
            all_timeframes = self.config.get('MULTI_STRATEGY_ANALYSIS', 'timeframes', 
                                           fallback='M15,H1,H4,D1').split(',')
            primary_timeframes = self.config.get('MULTI_STRATEGY_ANALYSIS', 'primary_timeframes', 
                                                fallback='M15,H1,H4,D1').split(',')
            scalping_timeframes = self.config.get('MULTI_STRATEGY_ANALYSIS', 'scalping_timeframes', 
                                                 fallback='M1,M5,M15').split(',')
            longterm_timeframes = self.config.get('MULTI_STRATEGY_ANALYSIS', 'longterm_timeframes', 
                                                 fallback='H4,D1,W1').split(',')
            
            # Clean timeframes
            self.all_timeframes = [tf.strip() for tf in all_timeframes]
            self.primary_timeframes = [tf.strip() for tf in primary_timeframes]
            self.scalping_timeframes = [tf.strip() for tf in scalping_timeframes]
            self.longterm_timeframes = [tf.strip() for tf in longterm_timeframes]
            
            # Timeframe mapping for MT5
            self.timeframe_mapping = {
                'M1': mt5.TIMEFRAME_M1 if MT5_AVAILABLE else 1,
                'M5': mt5.TIMEFRAME_M5 if MT5_AVAILABLE else 5,
                'M15': mt5.TIMEFRAME_M15 if MT5_AVAILABLE else 15,
                'M30': mt5.TIMEFRAME_M30 if MT5_AVAILABLE else 30,
                'H1': mt5.TIMEFRAME_H1 if MT5_AVAILABLE else 60,
                'H4': mt5.TIMEFRAME_H4 if MT5_AVAILABLE else 240,
                'D1': mt5.TIMEFRAME_D1 if MT5_AVAILABLE else 1440,
                'W1': mt5.TIMEFRAME_W1 if MT5_AVAILABLE else 10080,
                'MN1': mt5.TIMEFRAME_MN1 if MT5_AVAILABLE else 43200
            }
            
            print(f"⏰ Configured {len(self.all_timeframes)} timeframes for analysis:")
            for i, tf in enumerate(self.all_timeframes, 1):
                print(f"   {i}. {tf}")
                
        except Exception as e:
            print(f"❌ Error setting up timeframes: {e}")
            # Fallback to basic timeframes
            self.all_timeframes = ['M15', 'H1', 'H4', 'D1']
            self.primary_timeframes = self.all_timeframes
    
    def setup_learning_system(self):
        """Setup the self-learning system"""
        try:
            # Learning configuration
            self.continuous_learning = self.config.getboolean('SELF_LEARNING_SYSTEM', 'continuous_learning', fallback=True)
            self.learning_update_interval = int(self.config.get('SELF_LEARNING_SYSTEM', 'learning_update_interval', fallback='1800'))
            self.experience_memory_size = int(self.config.get('SELF_LEARNING_SYSTEM', 'experience_memory_size', fallback='10000'))
            
            # Experience memory for learning
            self.experience_memory = []
            self.successful_patterns = []
            self.failed_patterns = []
            
            # Strategy performance tracking
            self.strategy_success_rates = {strategy: {'wins': 0, 'losses': 0, 'total': 0} for strategy in self.strategies}
            self.currency_success_rates = {symbol: {'wins': 0, 'losses': 0, 'total': 0} for symbol in self.active_symbols}
            
            # Adaptive parameters
            self.adaptive_parameters = {
                'confidence_thresholds': {symbol: 0.65 for symbol in self.active_symbols},
                'risk_levels': {symbol: 0.02 for symbol in self.active_symbols},
                'strategy_preferences': {symbol: {strategy: 1.0 for strategy in self.strategies} for symbol in self.active_symbols}
            }
            
            print("🧠 Self-learning system initialized")
            print(f"   📚 Experience memory size: {self.experience_memory_size}")
            print(f"   🔄 Learning update interval: {self.learning_update_interval} seconds")
            
        except Exception as e:
            print(f"❌ Error setting up learning system: {e}")
            self.continuous_learning = True
            self.experience_memory = []
    
    def connect_to_mt5(self) -> bool:
        """Connect to MetaTrader 5"""
        try:
            if not MT5_AVAILABLE:
                print("❌ MetaTrader5 library not available")
                return False
            
            print("🔄 Connecting to MetaTrader 5...")
            
            # Initialize MT5
            if not mt5.initialize():
                print(f"❌ MT5 initialization failed: {mt5.last_error()}")
                return False
            
            # Get connection settings
            server = self.config.get('MT5_CONNECTION', 'server', fallback='MetaQuotes-Demo')
            login = int(self.config.get('MT5_CONNECTION', 'login', fallback='0'))
            password = self.config.get('MT5_CONNECTION', 'password', fallback='')
            
            # Login
            if not mt5.login(login, password, server):
                print(f"❌ MT5 login failed: {mt5.last_error()}")
                return False
            
            # Verify connection
            account_info = mt5.account_info()
            if account_info is None:
                print("❌ Failed to get account info")
                return False
            
            self.is_connected = True
            print("✅ Connected to MetaTrader 5 successfully!")
            print(f"   Account: {account_info.login}")
            print(f"   Server: {account_info.server}")
            print(f"   Balance: ${account_info.balance:.2f}")
            print(f"   Equity: ${account_info.equity:.2f}")
            
            # Verify symbols
            self.verify_symbols()
            
            return True
            
        except Exception as e:
            print(f"❌ Error connecting to MT5: {e}")
            return False
    
    def verify_symbols(self):
        """Verify that all configured symbols are available"""
        try:
            available_symbols = []
            unavailable_symbols = []
            
            for symbol in self.active_symbols:
                symbol_info = mt5.symbol_info(symbol)
                if symbol_info is not None:
                    # Enable symbol if not visible
                    if not symbol_info.visible:
                        mt5.symbol_select(symbol, True)
                    available_symbols.append(symbol)
                else:
                    unavailable_symbols.append(symbol)
            
            # Update active symbols to only include available ones
            self.active_symbols = available_symbols
            
            print(f"✅ Verified symbols: {len(available_symbols)} available")
            if unavailable_symbols:
                print(f"⚠️ Unavailable symbols: {unavailable_symbols}")
            
        except Exception as e:
            print(f"❌ Error verifying symbols: {e}")
    
    def get_multi_currency_data(self, timeframe: str, bars: int = 1000) -> Dict[str, pd.DataFrame]:
        """Get market data for all currencies and timeframe"""
        try:
            if not self.is_connected:
                print("❌ Not connected to MT5")
                return {}
            
            mt5_timeframe = self.timeframe_mapping.get(timeframe, mt5.TIMEFRAME_H1)
            all_data = {}
            
            for symbol in self.active_symbols:
                try:
                    # Get rates
                    rates = mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, bars)
                    
                    if rates is not None and len(rates) > 0:
                        # Convert to DataFrame
                        df = pd.DataFrame(rates)
                        df['time'] = pd.to_datetime(df['time'], unit='s')
                        df.set_index('time', inplace=True)
                        
                        # Add basic calculations
                        df['hl2'] = (df['high'] + df['low']) / 2
                        df['hlc3'] = (df['high'] + df['low'] + df['close']) / 3
                        df['ohlc4'] = (df['open'] + df['high'] + df['low'] + df['close']) / 4
                        
                        all_data[symbol] = df
                        
                except Exception as e:
                    print(f"⚠️ Error getting data for {symbol}: {e}")
                    continue
            
            return all_data
            
        except Exception as e:
            print(f"❌ Error getting multi-currency data: {e}")
            return {}

    def calculate_technical_indicators(self, df: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """Calculate comprehensive technical indicators for a currency pair"""
        try:
            if df is None or len(df) < 50:
                return df

            # Price data
            high = df['high'].values
            low = df['low'].values
            close = df['close'].values
            open_price = df['open'].values
            volume = df['tick_volume'].values

            # Moving Averages
            df['SMA_10'] = df['close'].rolling(window=10).mean()
            df['SMA_20'] = df['close'].rolling(window=20).mean()
            df['SMA_50'] = df['close'].rolling(window=50).mean()
            df['EMA_12'] = df['close'].ewm(span=12).mean()
            df['EMA_26'] = df['close'].ewm(span=26).mean()

            # MACD
            df['MACD'] = df['EMA_12'] - df['EMA_26']
            df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
            df['MACD_Histogram'] = df['MACD'] - df['MACD_Signal']

            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))

            # Bollinger Bands
            df['BB_Middle'] = df['close'].rolling(window=20).mean()
            bb_std = df['close'].rolling(window=20).std()
            df['BB_Upper'] = df['BB_Middle'] + (bb_std * 2)
            df['BB_Lower'] = df['BB_Middle'] - (bb_std * 2)
            df['BB_Width'] = df['BB_Upper'] - df['BB_Lower']
            df['BB_Position'] = (df['close'] - df['BB_Lower']) / df['BB_Width']

            # ATR (Average True Range)
            df['TR'] = np.maximum(
                df['high'] - df['low'],
                np.maximum(
                    abs(df['high'] - df['close'].shift(1)),
                    abs(df['low'] - df['close'].shift(1))
                )
            )
            df['ATR'] = df['TR'].rolling(window=14).mean()

            # Stochastic
            lowest_low = df['low'].rolling(window=14).min()
            highest_high = df['high'].rolling(window=14).max()
            df['Stoch_K'] = 100 * (df['close'] - lowest_low) / (highest_high - lowest_low)
            df['Stoch_D'] = df['Stoch_K'].rolling(window=3).mean()

            # Williams %R
            df['Williams_R'] = -100 * (highest_high - df['close']) / (highest_high - lowest_low)

            # CCI (Commodity Channel Index)
            typical_price = (df['high'] + df['low'] + df['close']) / 3
            sma_tp = typical_price.rolling(window=20).mean()
            mad = typical_price.rolling(window=20).apply(lambda x: np.mean(np.abs(x - x.mean())))
            df['CCI'] = (typical_price - sma_tp) / (0.015 * mad)

            # ADX (Average Directional Index)
            plus_dm = df['high'].diff()
            minus_dm = df['low'].diff() * -1
            plus_dm[plus_dm < 0] = 0
            minus_dm[minus_dm < 0] = 0

            tr_smooth = df['TR'].rolling(window=14).mean()
            plus_di = 100 * (plus_dm.rolling(window=14).mean() / tr_smooth)
            minus_di = 100 * (minus_dm.rolling(window=14).mean() / tr_smooth)

            dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
            df['ADX'] = dx.rolling(window=14).mean()
            df['Plus_DI'] = plus_di
            df['Minus_DI'] = minus_di

            # Volume indicators
            df['Volume_SMA'] = df['tick_volume'].rolling(window=20).mean()
            df['Volume_Ratio'] = df['tick_volume'] / df['Volume_SMA']

            # Price patterns
            df['Doji'] = abs(df['open'] - df['close']) <= (df['high'] - df['low']) * 0.1
            df['Hammer'] = (df['close'] > df['open']) & ((df['open'] - df['low']) > 2 * (df['close'] - df['open']))
            df['Shooting_Star'] = (df['open'] > df['close']) & ((df['high'] - df['open']) > 2 * (df['open'] - df['close']))

            # Support and Resistance levels
            df['Resistance'] = df['high'].rolling(window=20).max()
            df['Support'] = df['low'].rolling(window=20).min()
            df['Support_Resistance_Ratio'] = (df['close'] - df['Support']) / (df['Resistance'] - df['Support'])

            # Trend indicators
            df['Price_Above_SMA20'] = df['close'] > df['SMA_20']
            df['Price_Above_SMA50'] = df['close'] > df['SMA_50']
            df['SMA20_Above_SMA50'] = df['SMA_20'] > df['SMA_50']

            # Volatility
            df['Volatility'] = df['close'].pct_change().rolling(window=20).std() * np.sqrt(252)

            return df

        except Exception as e:
            print(f"❌ Error calculating indicators for {symbol}: {e}")
            return df

    def analyze_market_conditions(self, symbol: str, timeframe: str, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze current market conditions for a specific currency and timeframe"""
        try:
            if df is None or len(df) < 50:
                return {'condition': 'insufficient_data', 'confidence': 0.0}

            latest = df.iloc[-1]
            prev = df.iloc[-2] if len(df) > 1 else latest

            conditions = {
                'symbol': symbol,
                'timeframe': timeframe,
                'timestamp': datetime.now(),
                'price': latest['close'],
                'spread': 0.0,  # Will be updated with real spread
                'volatility': latest.get('Volatility', 0.0),
                'atr': latest.get('ATR', 0.0)
            }

            # Trend Analysis
            trend_score = 0
            if latest.get('Price_Above_SMA20', False):
                trend_score += 1
            if latest.get('Price_Above_SMA50', False):
                trend_score += 1
            if latest.get('SMA20_Above_SMA50', False):
                trend_score += 1
            if latest.get('MACD', 0) > latest.get('MACD_Signal', 0):
                trend_score += 1
            if latest.get('ADX', 0) > 25:
                trend_score += 1

            conditions['trend_strength'] = trend_score / 5.0
            conditions['trend_direction'] = 'bullish' if trend_score >= 3 else 'bearish' if trend_score <= 2 else 'sideways'

            # Momentum Analysis
            momentum_score = 0
            rsi = latest.get('RSI', 50)
            if 30 < rsi < 70:  # Not overbought/oversold
                momentum_score += 1
            if latest.get('MACD_Histogram', 0) > prev.get('MACD_Histogram', 0):  # Increasing momentum
                momentum_score += 1
            if latest.get('Stoch_K', 50) > latest.get('Stoch_D', 50):  # Stoch bullish
                momentum_score += 1

            conditions['momentum_strength'] = momentum_score / 3.0
            conditions['momentum_direction'] = 'positive' if momentum_score >= 2 else 'negative'

            # Volatility Analysis
            volatility_level = 'low'
            if latest.get('ATR', 0) > df['ATR'].rolling(window=50).mean().iloc[-1] * 1.5:
                volatility_level = 'high'
            elif latest.get('ATR', 0) > df['ATR'].rolling(window=50).mean().iloc[-1] * 1.2:
                volatility_level = 'medium'

            conditions['volatility_level'] = volatility_level

            # Support/Resistance Analysis
            sr_ratio = latest.get('Support_Resistance_Ratio', 0.5)
            if sr_ratio > 0.8:
                conditions['price_position'] = 'near_resistance'
            elif sr_ratio < 0.2:
                conditions['price_position'] = 'near_support'
            else:
                conditions['price_position'] = 'middle_range'

            # Overall market condition
            if conditions['trend_strength'] > 0.6 and conditions['momentum_strength'] > 0.6:
                conditions['overall_condition'] = 'strong_trend'
                conditions['confidence'] = 0.8
            elif conditions['volatility_level'] == 'high' and conditions['trend_strength'] > 0.4:
                conditions['overall_condition'] = 'breakout_potential'
                conditions['confidence'] = 0.7
            elif conditions['trend_strength'] < 0.4 and conditions['volatility_level'] == 'low':
                conditions['overall_condition'] = 'consolidation'
                conditions['confidence'] = 0.5
            else:
                conditions['overall_condition'] = 'mixed_signals'
                conditions['confidence'] = 0.4

            return conditions

        except Exception as e:
            print(f"❌ Error analyzing market conditions for {symbol}: {e}")
            return {'condition': 'error', 'confidence': 0.0}

    def apply_strategy_analysis(self, strategy: str, symbol: str, timeframe: str, df: pd.DataFrame,
                              market_conditions: Dict[str, Any]) -> Dict[str, Any]:
        """Apply specific strategy analysis to currency pair"""
        try:
            if df is None or len(df) < 50:
                return {'signal': 'no_signal', 'confidence': 0.0, 'reason': 'insufficient_data'}

            latest = df.iloc[-1]
            strategy_config = self.strategy_configs.get(strategy, {})

            # Get strategy-specific timeframes
            strategy_timeframes = strategy_config.get('timeframes', [timeframe])
            if timeframe not in strategy_timeframes:
                return {'signal': 'no_signal', 'confidence': 0.0, 'reason': 'wrong_timeframe'}

            signal_result = {'strategy': strategy, 'symbol': symbol, 'timeframe': timeframe, 'timestamp': datetime.now()}

            if strategy == 'trend_following':
                return self._analyze_trend_following(latest, market_conditions, signal_result)
            elif strategy == 'mean_reversion':
                return self._analyze_mean_reversion(latest, market_conditions, signal_result)
            elif strategy == 'breakout':
                return self._analyze_breakout(latest, market_conditions, signal_result)
            elif strategy == 'momentum':
                return self._analyze_momentum(latest, market_conditions, signal_result)
            elif strategy == 'scalping':
                return self._analyze_scalping(latest, market_conditions, signal_result)
            elif strategy == 'swing_trading':
                return self._analyze_swing_trading(latest, market_conditions, signal_result)
            else:
                return {'signal': 'no_signal', 'confidence': 0.0, 'reason': 'unknown_strategy'}

        except Exception as e:
            print(f"❌ Error in strategy analysis {strategy} for {symbol}: {e}")
            return {'signal': 'error', 'confidence': 0.0, 'reason': str(e)}

    def _analyze_trend_following(self, latest: pd.Series, market_conditions: Dict, result: Dict) -> Dict:
        """Trend following strategy analysis"""
        try:
            confidence = 0.0
            signal = 'no_signal'
            reasons = []

            # Check trend strength
            trend_strength = market_conditions.get('trend_strength', 0.0)
            if trend_strength > 0.7:
                confidence += 0.3
                reasons.append('strong_trend')
            elif trend_strength > 0.5:
                confidence += 0.2
                reasons.append('moderate_trend')

            # Check MACD
            macd = latest.get('MACD', 0)
            macd_signal = latest.get('MACD_Signal', 0)
            if macd > macd_signal and macd > 0:
                confidence += 0.2
                signal = 'buy'
                reasons.append('macd_bullish')
            elif macd < macd_signal and macd < 0:
                confidence += 0.2
                signal = 'sell'
                reasons.append('macd_bearish')

            # Check moving averages
            if latest.get('Price_Above_SMA20', False) and latest.get('SMA20_Above_SMA50', False):
                confidence += 0.2
                if signal == 'no_signal':
                    signal = 'buy'
                reasons.append('ma_alignment_bullish')
            elif not latest.get('Price_Above_SMA20', True) and not latest.get('SMA20_Above_SMA50', True):
                confidence += 0.2
                if signal == 'no_signal':
                    signal = 'sell'
                reasons.append('ma_alignment_bearish')

            # Check ADX for trend strength
            adx = latest.get('ADX', 0)
            if adx > 25:
                confidence += 0.1
                reasons.append('strong_directional_movement')

            # Minimum confidence threshold
            if confidence < 0.6:
                signal = 'no_signal'

            result.update({
                'signal': signal,
                'confidence': min(confidence, 1.0),
                'reasons': reasons,
                'entry_price': latest.get('close', 0),
                'stop_loss': self._calculate_stop_loss(latest, signal),
                'take_profit': self._calculate_take_profit(latest, signal)
            })

            return result

        except Exception as e:
            return {'signal': 'error', 'confidence': 0.0, 'reason': str(e)}

    def _analyze_mean_reversion(self, latest: pd.Series, market_conditions: Dict, result: Dict) -> Dict:
        """Mean reversion strategy analysis"""
        try:
            confidence = 0.0
            signal = 'no_signal'
            reasons = []

            # Check if market is in consolidation (good for mean reversion)
            if market_conditions.get('overall_condition') == 'consolidation':
                confidence += 0.2
                reasons.append('consolidation_market')

            # RSI analysis
            rsi = latest.get('RSI', 50)
            if rsi < 30:  # Oversold
                confidence += 0.3
                signal = 'buy'
                reasons.append('rsi_oversold')
            elif rsi > 70:  # Overbought
                confidence += 0.3
                signal = 'sell'
                reasons.append('rsi_overbought')
            elif 40 < rsi < 60:  # Neutral zone
                confidence += 0.1
                reasons.append('rsi_neutral')

            # Bollinger Bands analysis
            bb_position = latest.get('BB_Position', 0.5)
            if bb_position < 0.1:  # Near lower band
                confidence += 0.2
                if signal == 'no_signal':
                    signal = 'buy'
                reasons.append('bb_oversold')
            elif bb_position > 0.9:  # Near upper band
                confidence += 0.2
                if signal == 'no_signal':
                    signal = 'sell'
                reasons.append('bb_overbought')

            # Stochastic analysis
            stoch_k = latest.get('Stoch_K', 50)
            stoch_d = latest.get('Stoch_D', 50)
            if stoch_k < 20 and stoch_k > stoch_d:  # Oversold and turning up
                confidence += 0.2
                reasons.append('stoch_oversold_turning')
            elif stoch_k > 80 and stoch_k < stoch_d:  # Overbought and turning down
                confidence += 0.2
                reasons.append('stoch_overbought_turning')

            # Minimum confidence threshold
            if confidence < 0.5:
                signal = 'no_signal'

            result.update({
                'signal': signal,
                'confidence': min(confidence, 1.0),
                'reasons': reasons,
                'entry_price': latest.get('close', 0),
                'stop_loss': self._calculate_stop_loss(latest, signal),
                'take_profit': self._calculate_take_profit(latest, signal)
            })

            return result

        except Exception as e:
            return {'signal': 'error', 'confidence': 0.0, 'reason': str(e)}

    def _analyze_breakout(self, latest: pd.Series, market_conditions: Dict, result: Dict) -> Dict:
        """Breakout strategy analysis"""
        try:
            confidence = 0.0
            signal = 'no_signal'
            reasons = []

            # Check for high volatility (good for breakouts)
            if market_conditions.get('volatility_level') == 'high':
                confidence += 0.2
                reasons.append('high_volatility')
            elif market_conditions.get('volatility_level') == 'medium':
                confidence += 0.1
                reasons.append('medium_volatility')

            # Check price position relative to support/resistance
            price_position = market_conditions.get('price_position', 'middle_range')
            if price_position == 'near_resistance':
                # Potential upward breakout
                if latest.get('close', 0) > latest.get('Resistance', 0):
                    confidence += 0.3
                    signal = 'buy'
                    reasons.append('resistance_breakout')
            elif price_position == 'near_support':
                # Potential downward breakout
                if latest.get('close', 0) < latest.get('Support', 0):
                    confidence += 0.3
                    signal = 'sell'
                    reasons.append('support_breakdown')

            # Volume confirmation
            volume_ratio = latest.get('Volume_Ratio', 1.0)
            if volume_ratio > 1.5:  # High volume
                confidence += 0.2
                reasons.append('volume_confirmation')

            # ATR analysis for volatility
            atr = latest.get('ATR', 0)
            if atr > 0:
                # Check if current range is expanding
                current_range = latest.get('high', 0) - latest.get('low', 0)
                if current_range > atr * 1.2:
                    confidence += 0.1
                    reasons.append('expanding_range')

            # Bollinger Bands squeeze (low volatility before breakout)
            bb_width = latest.get('BB_Width', 0)
            if bb_width > 0:
                # This would need historical comparison, simplified here
                confidence += 0.1
                reasons.append('bb_analysis')

            # Minimum confidence threshold
            if confidence < 0.6:
                signal = 'no_signal'

            result.update({
                'signal': signal,
                'confidence': min(confidence, 1.0),
                'reasons': reasons,
                'entry_price': latest.get('close', 0),
                'stop_loss': self._calculate_stop_loss(latest, signal),
                'take_profit': self._calculate_take_profit(latest, signal)
            })

            return result

        except Exception as e:
            return {'signal': 'error', 'confidence': 0.0, 'reason': str(e)}

    def _analyze_momentum(self, latest: pd.Series, market_conditions: Dict, result: Dict) -> Dict:
        """Momentum strategy analysis"""
        try:
            confidence = 0.0
            signal = 'no_signal'
            reasons = []

            # MACD momentum
            macd = latest.get('MACD', 0)
            macd_signal = latest.get('MACD_Signal', 0)
            macd_hist = latest.get('MACD_Histogram', 0)

            if macd > macd_signal and macd_hist > 0:
                confidence += 0.3
                signal = 'buy'
                reasons.append('macd_momentum_bullish')
            elif macd < macd_signal and macd_hist < 0:
                confidence += 0.3
                signal = 'sell'
                reasons.append('macd_momentum_bearish')

            # RSI momentum
            rsi = latest.get('RSI', 50)
            if 50 < rsi < 70 and signal == 'buy':
                confidence += 0.2
                reasons.append('rsi_bullish_momentum')
            elif 30 < rsi < 50 and signal == 'sell':
                confidence += 0.2
                reasons.append('rsi_bearish_momentum')

            # Williams %R
            williams_r = latest.get('Williams_R', -50)
            if williams_r > -20 and signal == 'buy':
                confidence += 0.1
                reasons.append('williams_overbought_momentum')
            elif williams_r < -80 and signal == 'sell':
                confidence += 0.1
                reasons.append('williams_oversold_momentum')

            # Price momentum (simple price change)
            close = latest.get('close', 0)
            if close > 0:
                # This would need previous price, simplified here
                confidence += 0.1
                reasons.append('price_momentum')

            # Overall momentum strength
            momentum_strength = market_conditions.get('momentum_strength', 0.0)
            if momentum_strength > 0.6:
                confidence += 0.2
                reasons.append('strong_momentum')

            # Minimum confidence threshold
            if confidence < 0.6:
                signal = 'no_signal'

            result.update({
                'signal': signal,
                'confidence': min(confidence, 1.0),
                'reasons': reasons,
                'entry_price': latest.get('close', 0),
                'stop_loss': self._calculate_stop_loss(latest, signal),
                'take_profit': self._calculate_take_profit(latest, signal)
            })

            return result

        except Exception as e:
            return {'signal': 'error', 'confidence': 0.0, 'reason': str(e)}

    def _analyze_scalping(self, latest: pd.Series, market_conditions: Dict, result: Dict) -> Dict:
        """Scalping strategy analysis (for short-term trades)"""
        try:
            confidence = 0.0
            signal = 'no_signal'
            reasons = []

            # Scalping works best in trending markets with good volatility
            trend_strength = market_conditions.get('trend_strength', 0.0)
            if trend_strength > 0.5:
                confidence += 0.2
                reasons.append('trending_market')

            # EMA crossover for quick signals
            ema_12 = latest.get('EMA_12', 0)
            ema_26 = latest.get('EMA_26', 0)
            if ema_12 > ema_26:
                confidence += 0.2
                signal = 'buy'
                reasons.append('ema_bullish')
            elif ema_12 < ema_26:
                confidence += 0.2
                signal = 'sell'
                reasons.append('ema_bearish')

            # Stochastic for entry timing
            stoch_k = latest.get('Stoch_K', 50)
            stoch_d = latest.get('Stoch_D', 50)
            if stoch_k > stoch_d and stoch_k < 80:
                confidence += 0.2
                reasons.append('stoch_bullish_entry')
            elif stoch_k < stoch_d and stoch_k > 20:
                confidence += 0.2
                reasons.append('stoch_bearish_entry')

            # CCI for momentum
            cci = latest.get('CCI', 0)
            if -100 < cci < 100:
                confidence += 0.1
                reasons.append('cci_normal_range')

            # Quick profit target (smaller than other strategies)
            confidence *= 0.8  # Scalping has inherently lower confidence due to noise

            # Minimum confidence threshold (lower for scalping)
            if confidence < 0.4:
                signal = 'no_signal'

            result.update({
                'signal': signal,
                'confidence': min(confidence, 1.0),
                'reasons': reasons,
                'entry_price': latest.get('close', 0),
                'stop_loss': self._calculate_stop_loss(latest, signal, multiplier=0.5),  # Tighter stops
                'take_profit': self._calculate_take_profit(latest, signal, multiplier=0.5)  # Smaller targets
            })

            return result

        except Exception as e:
            return {'signal': 'error', 'confidence': 0.0, 'reason': str(e)}

    def _analyze_swing_trading(self, latest: pd.Series, market_conditions: Dict, result: Dict) -> Dict:
        """Swing trading strategy analysis (for medium-term trades)"""
        try:
            confidence = 0.0
            signal = 'no_signal'
            reasons = []

            # Swing trading works best with clear trends and good momentum
            trend_strength = market_conditions.get('trend_strength', 0.0)
            momentum_strength = market_conditions.get('momentum_strength', 0.0)

            if trend_strength > 0.6 and momentum_strength > 0.5:
                confidence += 0.3
                reasons.append('strong_trend_momentum')

            # SMA for trend direction
            sma_20 = latest.get('SMA_20', 0)
            sma_50 = latest.get('SMA_50', 0)
            close = latest.get('close', 0)

            if close > sma_20 > sma_50:
                confidence += 0.2
                signal = 'buy'
                reasons.append('bullish_sma_alignment')
            elif close < sma_20 < sma_50:
                confidence += 0.2
                signal = 'sell'
                reasons.append('bearish_sma_alignment')

            # MACD for momentum confirmation
            macd = latest.get('MACD', 0)
            macd_signal = latest.get('MACD_Signal', 0)
            if macd > macd_signal and signal == 'buy':
                confidence += 0.2
                reasons.append('macd_confirms_buy')
            elif macd < macd_signal and signal == 'sell':
                confidence += 0.2
                reasons.append('macd_confirms_sell')

            # RSI for entry timing (avoid extreme levels)
            rsi = latest.get('RSI', 50)
            if 40 < rsi < 60:
                confidence += 0.1
                reasons.append('rsi_neutral_good_entry')
            elif rsi < 40 and signal == 'buy':
                confidence += 0.1
                reasons.append('rsi_oversold_buy_opportunity')
            elif rsi > 60 and signal == 'sell':
                confidence += 0.1
                reasons.append('rsi_overbought_sell_opportunity')

            # Support/Resistance for entry points
            price_position = market_conditions.get('price_position', 'middle_range')
            if price_position == 'near_support' and signal == 'buy':
                confidence += 0.1
                reasons.append('buying_near_support')
            elif price_position == 'near_resistance' and signal == 'sell':
                confidence += 0.1
                reasons.append('selling_near_resistance')

            # Minimum confidence threshold
            if confidence < 0.6:
                signal = 'no_signal'

            result.update({
                'signal': signal,
                'confidence': min(confidence, 1.0),
                'reasons': reasons,
                'entry_price': latest.get('close', 0),
                'stop_loss': self._calculate_stop_loss(latest, signal, multiplier=1.5),  # Wider stops
                'take_profit': self._calculate_take_profit(latest, signal, multiplier=2.0)  # Larger targets
            })

            return result

        except Exception as e:
            return {'signal': 'error', 'confidence': 0.0, 'reason': str(e)}

    def _calculate_stop_loss(self, latest: pd.Series, signal: str, multiplier: float = 1.0) -> float:
        """Calculate stop loss based on ATR"""
        try:
            atr = latest.get('ATR', 0.001)
            close = latest.get('close', 0)

            if signal == 'buy':
                return close - (atr * 2.0 * multiplier)
            elif signal == 'sell':
                return close + (atr * 2.0 * multiplier)
            else:
                return close

        except Exception as e:
            return latest.get('close', 0)

    def _calculate_take_profit(self, latest: pd.Series, signal: str, multiplier: float = 1.0) -> float:
        """Calculate take profit based on ATR"""
        try:
            atr = latest.get('ATR', 0.001)
            close = latest.get('close', 0)

            if signal == 'buy':
                return close + (atr * 3.0 * multiplier)
            elif signal == 'sell':
                return close - (atr * 3.0 * multiplier)
            else:
                return close

        except Exception as e:
            return latest.get('close', 0)

    def run_comprehensive_analysis(self) -> Dict[str, List[Dict]]:
        """Run comprehensive analysis across all currencies, timeframes, and strategies"""
        try:
            if not self.is_connected:
                print("❌ Not connected to MT5")
                return {}

            print("🧠 Starting comprehensive multi-currency, multi-strategy analysis...")

            all_signals = {}
            analysis_count = 0

            # Analyze each currency
            for symbol in self.active_symbols:
                print(f"📊 Analyzing {symbol}...")
                symbol_signals = []

                # Analyze each primary timeframe
                for timeframe in self.primary_timeframes:
                    try:
                        # Get market data
                        data = self.get_multi_currency_data(timeframe, bars=200)
                        if symbol not in data or data[symbol] is None:
                            continue

                        df = data[symbol]

                        # Calculate technical indicators
                        df = self.calculate_technical_indicators(df, symbol)

                        # Analyze market conditions
                        market_conditions = self.analyze_market_conditions(symbol, timeframe, df)

                        # Apply each strategy
                        for strategy in self.strategies:
                            try:
                                # Get strategy weight for this symbol
                                strategy_weight = self.adaptive_parameters['strategy_preferences'][symbol].get(strategy, 1.0)

                                # Apply strategy analysis
                                signal_result = self.apply_strategy_analysis(strategy, symbol, timeframe, df, market_conditions)

                                # Apply adaptive weighting
                                if signal_result.get('confidence', 0) > 0:
                                    signal_result['confidence'] *= strategy_weight
                                    signal_result['strategy_weight'] = strategy_weight

                                # Add to results if signal is strong enough
                                min_confidence = self.adaptive_parameters['confidence_thresholds'].get(symbol, 0.65)
                                if signal_result.get('confidence', 0) >= min_confidence:
                                    symbol_signals.append(signal_result)
                                    analysis_count += 1

                            except Exception as e:
                                print(f"⚠️ Error in {strategy} analysis for {symbol} {timeframe}: {e}")
                                continue

                    except Exception as e:
                        print(f"⚠️ Error analyzing {symbol} {timeframe}: {e}")
                        continue

                # Store signals for this symbol
                if symbol_signals:
                    all_signals[symbol] = symbol_signals
                    print(f"✅ {symbol}: Found {len(symbol_signals)} potential signals")
                else:
                    print(f"⚠️ {symbol}: No signals found")

            print(f"🎯 Analysis complete: {analysis_count} total signals across {len(all_signals)} currencies")

            # Update market conditions for learning
            self.market_conditions = {symbol: signals for symbol, signals in all_signals.items()}

            return all_signals

        except Exception as e:
            print(f"❌ Error in comprehensive analysis: {e}")
            return {}

    def select_best_opportunities(self, all_signals: Dict[str, List[Dict]]) -> List[Dict]:
        """Select the best trading opportunities from all signals"""
        try:
            if not all_signals:
                return []

            # Flatten all signals
            all_opportunities = []
            for symbol, signals in all_signals.items():
                for signal in signals:
                    signal['symbol'] = symbol
                    all_opportunities.append(signal)

            # Sort by confidence (highest first)
            all_opportunities.sort(key=lambda x: x.get('confidence', 0), reverse=True)

            # Apply position limits
            selected_opportunities = []
            symbol_counts = {}
            total_positions = len(self.active_positions)

            for opportunity in all_opportunities:
                symbol = opportunity['symbol']

                # Check total position limit
                if total_positions + len(selected_opportunities) >= self.max_positions_total:
                    break

                # Check per-symbol limit
                symbol_count = symbol_counts.get(symbol, 0)
                current_symbol_positions = len([pos for pos in self.active_positions.values() if pos.get('symbol') == symbol])

                if symbol_count + current_symbol_positions < self.max_positions_per_symbol:
                    selected_opportunities.append(opportunity)
                    symbol_counts[symbol] = symbol_count + 1

                    # Limit total selections to prevent overtrading
                    if len(selected_opportunities) >= 5:  # Max 5 new positions per cycle
                        break

            return selected_opportunities

        except Exception as e:
            print(f"❌ Error selecting opportunities: {e}")
            return []

    def execute_trade(self, opportunity: Dict) -> bool:
        """Execute a trade based on the opportunity"""
        try:
            if not self.is_connected:
                print("❌ Cannot execute trade: Not connected to MT5")
                return False

            symbol = opportunity['symbol']
            signal = opportunity['signal']
            confidence = opportunity.get('confidence', 0)
            entry_price = opportunity.get('entry_price', 0)
            stop_loss = opportunity.get('stop_loss', 0)
            take_profit = opportunity.get('take_profit', 0)

            # Calculate position size based on risk and confidence
            lot_size = self.calculate_position_size(symbol, confidence, stop_loss, entry_price)

            if lot_size <= 0:
                print(f"⚠️ Invalid lot size for {symbol}: {lot_size}")
                return False

            # Prepare order request
            if signal == 'buy':
                order_type = mt5.ORDER_TYPE_BUY
                price = mt5.symbol_info_tick(symbol).ask
            elif signal == 'sell':
                order_type = mt5.ORDER_TYPE_SELL
                price = mt5.symbol_info_tick(symbol).bid
            else:
                print(f"⚠️ Invalid signal for {symbol}: {signal}")
                return False

            # Get symbol info for proper formatting
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                print(f"❌ Cannot get symbol info for {symbol}")
                return False

            # Format prices
            digits = symbol_info.digits
            point = symbol_info.point

            # Adjust stop loss and take profit
            if signal == 'buy':
                if stop_loss > 0:
                    stop_loss = max(stop_loss, price - 100 * point)  # Minimum distance
                if take_profit > 0:
                    take_profit = max(take_profit, price + 100 * point)
            else:
                if stop_loss > 0:
                    stop_loss = min(stop_loss, price + 100 * point)
                if take_profit > 0:
                    take_profit = min(take_profit, price - 100 * point)

            # Round prices
            price = round(price, digits)
            stop_loss = round(stop_loss, digits) if stop_loss > 0 else 0
            take_profit = round(take_profit, digits) if take_profit > 0 else 0

            # Create order request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": lot_size,
                "type": order_type,
                "price": price,
                "sl": stop_loss,
                "tp": take_profit,
                "deviation": 20,
                "magic": 123456,
                "comment": f"Multi-Strategy-{opportunity.get('strategy', 'unknown')}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            # Send order
            print(f"📤 Executing {signal.upper()} order for {symbol}:")
            print(f"   Volume: {lot_size}")
            print(f"   Price: {price}")
            print(f"   SL: {stop_loss}")
            print(f"   TP: {take_profit}")
            print(f"   Strategy: {opportunity.get('strategy', 'unknown')}")
            print(f"   Confidence: {confidence:.2%}")

            result = mt5.order_send(request)

            if result is None:
                print(f"❌ Order send failed: {mt5.last_error()}")
                return False

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                print(f"❌ Order failed: {result.retcode} - {result.comment}")
                return False

            # Store position info
            position_info = {
                'symbol': symbol,
                'signal': signal,
                'strategy': opportunity.get('strategy', 'unknown'),
                'timeframe': opportunity.get('timeframe', 'unknown'),
                'volume': lot_size,
                'entry_price': price,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'confidence': confidence,
                'entry_time': datetime.now(),
                'order_id': result.order,
                'deal_id': result.deal,
                'reasons': opportunity.get('reasons', [])
            }

            self.active_positions[result.order] = position_info

            print(f"✅ Trade executed successfully!")
            print(f"   Order ID: {result.order}")
            print(f"   Deal ID: {result.deal}")

            # Record for learning
            self.record_trade_execution(opportunity, position_info, True)

            return True

        except Exception as e:
            print(f"❌ Error executing trade: {e}")
            self.record_trade_execution(opportunity, {}, False)
            return False

    def calculate_position_size(self, symbol: str, confidence: float, stop_loss: float, entry_price: float) -> float:
        """Calculate position size based on risk management and confidence"""
        try:
            # Get account info
            account_info = mt5.account_info()
            if account_info is None:
                return 0.01

            balance = account_info.balance
            equity = account_info.equity

            # Get symbol info
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                return 0.01

            # Base risk per trade
            base_risk = self.adaptive_parameters['risk_levels'].get(symbol, 0.02)

            # Adjust risk based on confidence
            confidence_multiplier = min(confidence * 1.5, 2.0)  # Max 2x risk for high confidence
            adjusted_risk = base_risk * confidence_multiplier

            # Calculate risk amount in account currency
            risk_amount = balance * adjusted_risk

            # Calculate stop loss distance in pips
            if stop_loss > 0 and entry_price > 0:
                stop_distance = abs(entry_price - stop_loss)

                # Convert to account currency
                if symbol.endswith('USD'):
                    pip_value = symbol_info.trade_tick_value
                else:
                    pip_value = symbol_info.trade_tick_value

                if pip_value > 0 and stop_distance > 0:
                    # Calculate lot size
                    lot_size = risk_amount / (stop_distance / symbol_info.point * pip_value)

                    # Apply limits
                    min_lot = symbol_info.volume_min
                    max_lot = min(symbol_info.volume_max, balance / 1000)  # Conservative max

                    lot_size = max(min_lot, min(lot_size, max_lot))

                    # Round to step
                    step = symbol_info.volume_step
                    lot_size = round(lot_size / step) * step

                    return lot_size

            # Fallback to minimum lot size
            return symbol_info.volume_min if symbol_info else 0.01

        except Exception as e:
            print(f"❌ Error calculating position size: {e}")
            return 0.01

    def record_trade_execution(self, opportunity: Dict, position_info: Dict, success: bool):
        """Record trade execution for learning"""
        try:
            experience = {
                'timestamp': datetime.now(),
                'symbol': opportunity.get('symbol', ''),
                'strategy': opportunity.get('strategy', ''),
                'timeframe': opportunity.get('timeframe', ''),
                'signal': opportunity.get('signal', ''),
                'confidence': opportunity.get('confidence', 0),
                'reasons': opportunity.get('reasons', []),
                'success': success,
                'position_info': position_info
            }

            # Add to experience memory
            self.experience_memory.append(experience)

            # Limit memory size
            if len(self.experience_memory) > self.experience_memory_size:
                self.experience_memory = self.experience_memory[-self.experience_memory_size:]

            # Update strategy performance tracking
            strategy = opportunity.get('strategy', 'unknown')
            symbol = opportunity.get('symbol', 'unknown')

            if success:
                self.strategy_success_rates[strategy]['wins'] += 1
                self.currency_success_rates[symbol]['wins'] += 1
            else:
                self.strategy_success_rates[strategy]['losses'] += 1
                self.currency_success_rates[symbol]['losses'] += 1

            self.strategy_success_rates[strategy]['total'] += 1
            self.currency_success_rates[symbol]['total'] += 1

        except Exception as e:
            print(f"❌ Error recording trade execution: {e}")

    def update_learning_system(self):
        """Update the learning system based on recent experience"""
        try:
            if not self.continuous_learning or len(self.experience_memory) < 10:
                return

            print("🧠 Updating learning system...")

            # Analyze recent performance
            recent_experiences = self.experience_memory[-100:]  # Last 100 trades

            # Update strategy weights based on performance
            for strategy in self.strategies:
                strategy_experiences = [exp for exp in recent_experiences if exp.get('strategy') == strategy]

                if len(strategy_experiences) >= 5:  # Minimum sample size
                    success_rate = sum(1 for exp in strategy_experiences if exp.get('success', False)) / len(strategy_experiences)

                    # Update strategy weights for each symbol
                    for symbol in self.active_symbols:
                        symbol_strategy_experiences = [exp for exp in strategy_experiences if exp.get('symbol') == symbol]

                        if len(symbol_strategy_experiences) >= 3:
                            symbol_success_rate = sum(1 for exp in symbol_strategy_experiences if exp.get('success', False)) / len(symbol_strategy_experiences)

                            # Adjust strategy preference
                            current_weight = self.adaptive_parameters['strategy_preferences'][symbol][strategy]

                            if symbol_success_rate > 0.6:  # Good performance
                                new_weight = min(current_weight * 1.1, 2.0)
                            elif symbol_success_rate < 0.4:  # Poor performance
                                new_weight = max(current_weight * 0.9, 0.5)
                            else:
                                new_weight = current_weight

                            self.adaptive_parameters['strategy_preferences'][symbol][strategy] = new_weight

            # Update confidence thresholds based on overall performance
            for symbol in self.active_symbols:
                symbol_experiences = [exp for exp in recent_experiences if exp.get('symbol') == symbol]

                if len(symbol_experiences) >= 5:
                    success_rate = sum(1 for exp in symbol_experiences if exp.get('success', False)) / len(symbol_experiences)
                    current_threshold = self.adaptive_parameters['confidence_thresholds'][symbol]

                    if success_rate > 0.7:  # Very good performance - can lower threshold
                        new_threshold = max(current_threshold * 0.95, 0.5)
                    elif success_rate < 0.4:  # Poor performance - raise threshold
                        new_threshold = min(current_threshold * 1.05, 0.8)
                    else:
                        new_threshold = current_threshold

                    self.adaptive_parameters['confidence_thresholds'][symbol] = new_threshold

            # Update risk levels based on performance and drawdown
            account_info = mt5.account_info()
            if account_info:
                current_equity = account_info.equity
                current_balance = account_info.balance

                if current_equity < current_balance * 0.95:  # 5% drawdown
                    # Reduce risk
                    for symbol in self.active_symbols:
                        current_risk = self.adaptive_parameters['risk_levels'][symbol]
                        self.adaptive_parameters['risk_levels'][symbol] = max(current_risk * 0.8, 0.01)
                elif current_equity > current_balance * 1.1:  # 10% profit
                    # Can increase risk slightly
                    for symbol in self.active_symbols:
                        current_risk = self.adaptive_parameters['risk_levels'][symbol]
                        self.adaptive_parameters['risk_levels'][symbol] = min(current_risk * 1.1, 0.05)

            print("✅ Learning system updated")
            self._print_learning_summary()

        except Exception as e:
            print(f"❌ Error updating learning system: {e}")

    def _print_learning_summary(self):
        """Print a summary of the learning system state"""
        try:
            print("\n📊 Learning System Summary:")
            print("=" * 50)

            # Strategy performance
            print("🧠 Strategy Performance:")
            for strategy, stats in self.strategy_success_rates.items():
                if stats['total'] > 0:
                    win_rate = stats['wins'] / stats['total'] * 100
                    print(f"   {strategy.replace('_', ' ').title()}: {win_rate:.1f}% ({stats['wins']}/{stats['total']})")

            # Currency performance
            print("\n💱 Currency Performance:")
            for symbol, stats in self.currency_success_rates.items():
                if stats['total'] > 0:
                    win_rate = stats['wins'] / stats['total'] * 100
                    confidence_threshold = self.adaptive_parameters['confidence_thresholds'][symbol]
                    risk_level = self.adaptive_parameters['risk_levels'][symbol]
                    print(f"   {symbol}: {win_rate:.1f}% | Confidence: {confidence_threshold:.2f} | Risk: {risk_level:.2%}")

            print("=" * 50)

        except Exception as e:
            print(f"❌ Error printing learning summary: {e}")

    def run_intelligent_trading_cycle(self) -> bool:
        """Run one complete intelligent trading cycle"""
        try:
            print(f"\n🚀 Starting intelligent trading cycle at {datetime.now().strftime('%H:%M:%S')}")
            print("=" * 60)

            # Step 1: Comprehensive analysis
            all_signals = self.run_comprehensive_analysis()

            if not all_signals:
                print("⚠️ No trading signals found in this cycle")
                return False

            # Step 2: Select best opportunities
            opportunities = self.select_best_opportunities(all_signals)

            if not opportunities:
                print("⚠️ No suitable trading opportunities after filtering")
                return False

            print(f"🎯 Selected {len(opportunities)} trading opportunities:")
            for i, opp in enumerate(opportunities, 1):
                print(f"   {i}. {opp['symbol']} {opp['signal'].upper()} - {opp['strategy']} - {opp['confidence']:.2%}")

            # Step 3: Execute trades
            executed_trades = 0
            for opportunity in opportunities:
                if self.execute_trade(opportunity):
                    executed_trades += 1
                    time.sleep(1)  # Small delay between trades

            print(f"\n✅ Trading cycle complete: {executed_trades}/{len(opportunities)} trades executed")

            # Step 4: Update learning system
            if executed_trades > 0:
                self.update_learning_system()

            return executed_trades > 0

        except Exception as e:
            print(f"❌ Error in trading cycle: {e}")
            return False

    def run_continuous_trading(self, duration_minutes: int = 60):
        """Run continuous intelligent trading for specified duration"""
        try:
            if not self.is_connected:
                print("❌ Cannot start trading: Not connected to MT5")
                return False

            print(f"🚀 Starting continuous multi-currency intelligent trading for {duration_minutes} minutes...")

            self.is_trading = True
            start_time = datetime.now()
            end_time = start_time + timedelta(minutes=duration_minutes)
            cycle_count = 0
            successful_cycles = 0

            while self.is_trading and datetime.now() < end_time:
                try:
                    cycle_count += 1
                    print(f"\n🔄 Trading Cycle #{cycle_count}")

                    # Run trading cycle
                    if self.run_intelligent_trading_cycle():
                        successful_cycles += 1

                    # Update learning system periodically
                    if cycle_count % 5 == 0:  # Every 5 cycles
                        self.update_learning_system()

                    # Wait before next cycle (3 minutes)
                    print(f"⏳ Waiting 3 minutes before next cycle...")
                    for i in range(180):  # 180 seconds = 3 minutes
                        if not self.is_trading:
                            break
                        time.sleep(1)

                except KeyboardInterrupt:
                    print("\n⏸️ Trading interrupted by user")
                    break
                except Exception as e:
                    print(f"❌ Error in trading cycle {cycle_count}: {e}")
                    continue

            self.is_trading = False

            # Final summary
            elapsed_time = datetime.now() - start_time
            print(f"\n🏁 Continuous trading completed!")
            print(f"   Duration: {elapsed_time}")
            print(f"   Cycles: {cycle_count}")
            print(f"   Successful cycles: {successful_cycles}")
            print(f"   Success rate: {successful_cycles/cycle_count*100:.1f}%" if cycle_count > 0 else "   Success rate: 0%")

            # Final learning update
            self.update_learning_system()

            return True

        except Exception as e:
            print(f"❌ Error in continuous trading: {e}")
            self.is_trading = False
            return False

    def stop_trading(self):
        """Stop the trading system"""
        self.is_trading = False
        print("⏸️ Trading system stopped")

    def disconnect(self):
        """Disconnect from MetaTrader 5"""
        try:
            self.is_trading = False
            if MT5_AVAILABLE and self.is_connected:
                mt5.shutdown()
            self.is_connected = False
            print("✅ Disconnected from MetaTrader 5")
        except Exception as e:
            print(f"❌ Error disconnecting: {e}")

    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        try:
            status = {
                'connected': self.is_connected,
                'trading': self.is_trading,
                'start_time': self.system_start_time,
                'uptime': datetime.now() - self.system_start_time,
                'active_currencies': len(self.active_symbols),
                'active_strategies': len(self.strategies),
                'active_timeframes': len(self.primary_timeframes),
                'active_positions': len(self.active_positions),
                'experience_memory_size': len(self.experience_memory),
                'currencies': self.active_symbols,
                'strategies': self.strategies,
                'timeframes': self.primary_timeframes
            }

            if self.is_connected and MT5_AVAILABLE:
                account_info = mt5.account_info()
                if account_info:
                    status['account_info'] = {
                        'balance': account_info.balance,
                        'equity': account_info.equity,
                        'margin': account_info.margin,
                        'free_margin': account_info.margin_free,
                        'margin_level': account_info.margin_level,
                        'profit': account_info.profit
                    }

            return status

        except Exception as e:
            print(f"❌ Error getting system status: {e}")
            return {'error': str(e)}

def main():
    """Main function to run the multi-currency intelligent trading system"""
    try:
        print("🚀 Multi-Currency Intelligent Trading System V3")
        print("=" * 60)

        # Initialize system
        system = MultiCurrencyIntelligentSystem()

        # Connect to MT5
        if not system.connect_to_mt5():
            print("❌ Failed to connect to MetaTrader 5")
            return

        # Interactive menu
        while True:
            print("\n" + "=" * 60)
            print("📋 Multi-Currency Intelligent Trading System Menu:")
            print("1️⃣  Run single analysis cycle")
            print("2️⃣  Run continuous trading (30 minutes)")
            print("3️⃣  Run continuous trading (1 hour)")
            print("4️⃣  Run continuous trading (custom duration)")
            print("5️⃣  View system status")
            print("6️⃣  View learning summary")
            print("7️⃣  Update learning system")
            print("8️⃣  Stop trading")
            print("9️⃣  Disconnect and exit")
            print("=" * 60)

            choice = input("🎯 Enter your choice (1-9): ").strip()

            if choice == '1':
                system.run_intelligent_trading_cycle()
            elif choice == '2':
                system.run_continuous_trading(30)
            elif choice == '3':
                system.run_continuous_trading(60)
            elif choice == '4':
                try:
                    duration = int(input("⏰ Enter duration in minutes: "))
                    system.run_continuous_trading(duration)
                except ValueError:
                    print("❌ Invalid duration")
            elif choice == '5':
                status = system.get_system_status()
                print("\n📊 System Status:")
                for key, value in status.items():
                    print(f"   {key}: {value}")
            elif choice == '6':
                system._print_learning_summary()
            elif choice == '7':
                system.update_learning_system()
            elif choice == '8':
                system.stop_trading()
            elif choice == '9':
                system.disconnect()
                break
            else:
                print("❌ Invalid choice")

        print("👋 Thank you for using Multi-Currency Intelligent Trading System!")

    except KeyboardInterrupt:
        print("\n⏸️ System interrupted by user")
    except Exception as e:
        print(f"❌ System error: {e}")

if __name__ == "__main__":
    main()
