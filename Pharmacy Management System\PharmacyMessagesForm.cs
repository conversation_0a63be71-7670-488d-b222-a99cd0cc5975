using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy_Management_System
{
    public partial class PharmacyMessagesForm : Form
    {
        private UnifiedPharmacyFunction unifiedFunction = new UnifiedPharmacyFunction();
        private Timer refreshTimer;

        public PharmacyMessagesForm()
        {
            InitializeComponent();
            InitializeTimer();
            ApplyModernDesign();
        }

        private void PharmacyMessagesForm_Load(object sender, EventArgs e)
        {
            LoadMessages();
            LoadPharmacies();
        }

        private void InitializeTimer()
        {
            refreshTimer = new Timer();
            refreshTimer.Interval = 15000; // 15 ثانية
            refreshTimer.Tick += RefreshTimer_Tick;
            refreshTimer.Start();
        }

        private void RefreshTimer_Tick(object sender, EventArgs e)
        {
            LoadMessages();
        }

        private void ApplyModernDesign()
        {
            try
            {
                this.BackColor = Color.White;
                this.Font = new Font("Segoe UI", 10F);
                
                // تحسين مظهر الجدول
                FormatMessagesGrid();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في تطبيق التصميم: " + ex.Message);
            }
        }

        private void FormatMessagesGrid()
        {
            try
            {
                dataGridViewMessages.Font = new Font("Segoe UI", 10F);
                dataGridViewMessages.BackgroundColor = Color.White;
                dataGridViewMessages.GridColor = Color.FromArgb(224, 224, 224);
                dataGridViewMessages.BorderStyle = BorderStyle.None;
                dataGridViewMessages.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
                
                dataGridViewMessages.RowHeadersVisible = false;
                dataGridViewMessages.AllowUserToAddRows = false;
                dataGridViewMessages.AllowUserToDeleteRows = false;
                dataGridViewMessages.ReadOnly = true;
                dataGridViewMessages.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
                dataGridViewMessages.MultiSelect = false;
                
                dataGridViewMessages.RightToLeft = RightToLeft.Yes;
                dataGridViewMessages.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                dataGridViewMessages.DefaultCellStyle.ForeColor = Color.FromArgb(33, 33, 33);
                dataGridViewMessages.DefaultCellStyle.BackColor = Color.White;
                dataGridViewMessages.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 150, 136);
                dataGridViewMessages.DefaultCellStyle.SelectionForeColor = Color.White;
                
                dataGridViewMessages.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(0, 150, 136);
                dataGridViewMessages.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
                dataGridViewMessages.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
                dataGridViewMessages.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dataGridViewMessages.ColumnHeadersHeight = 40;
                dataGridViewMessages.EnableHeadersVisualStyles = false;
                
                dataGridViewMessages.RowTemplate.Height = 35;
                dataGridViewMessages.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في تنسيق جدول الرسائل: " + ex.Message);
            }
        }

        private async void LoadMessages()
        {
            try
            {
                lblStatus.Text = "جاري تحميل الرسائل...";
                lblStatus.ForeColor = Color.Orange;
                
                string query = @"
                    SELECT
                        pm.id,
                        pm.subject,
                        pm.message_content as messageContent,
                        pm.sent_date as sentDate,
                        pm.is_read as isRead,
                        p_sender.pharmacyName as senderPharmacyName,
                        p_receiver.pharmacyName as receiverPharmacyName,
                        CASE
                            WHEN pm.sender_pharmacy_id = @currentPharmacyId THEN 'مرسل'
                            ELSE 'مستقبل'
                        END as messageType
                    FROM pharmacy_messages pm
                    INNER JOIN pharmacies p_sender ON pm.sender_pharmacy_id = p_sender.id
                    INNER JOIN pharmacies p_receiver ON pm.receiver_pharmacy_id = p_receiver.id
                    WHERE pm.sender_pharmacy_id = @currentPharmacyId
                       OR pm.receiver_pharmacy_id = @currentPharmacyId
                    ORDER BY pm.sent_date DESC";

                var parameters = new Dictionary<string, object>
                {
                    {"@currentPharmacyId", SessionManager.CurrentPharmacyId}
                };

                DataSet messagesDataSet = await Task.Run(() => unifiedFunction.ExecuteQuery(query, parameters));
                DataTable messagesData = messagesDataSet?.Tables[0];
                
                if (messagesData != null && messagesData.Rows.Count > 0)
                {
                    dataGridViewMessages.DataSource = messagesData;
                    SetupMessagesGridColumns();
                    lblStatus.Text = "تم العثور على " + messagesData.Rows.Count + " رسالة";
                    lblStatus.ForeColor = Color.Green;
                }
                else
                {
                    dataGridViewMessages.DataSource = null;
                    lblStatus.Text = "لا توجد رسائل";
                    lblStatus.ForeColor = Color.Gray;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل الرسائل: " + ex.Message, "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "خطأ في تحميل الرسائل";
                lblStatus.ForeColor = Color.Red;
            }
        }

        private void SetupMessagesGridColumns()
        {
            try
            {
                if (dataGridViewMessages.Columns.Count > 0)
                {
                    if (dataGridViewMessages.Columns.Contains("id"))
                        dataGridViewMessages.Columns["id"].Visible = false;

                    if (dataGridViewMessages.Columns.Contains("subject"))
                        dataGridViewMessages.Columns["subject"].HeaderText = "الموضوع";
                    
                    if (dataGridViewMessages.Columns.Contains("messageContent"))
                        dataGridViewMessages.Columns["messageContent"].HeaderText = "محتوى الرسالة";
                    
                    if (dataGridViewMessages.Columns.Contains("sentDate"))
                    {
                        dataGridViewMessages.Columns["sentDate"].HeaderText = "تاريخ الإرسال";
                        dataGridViewMessages.Columns["sentDate"].DefaultCellStyle.Format = "dd/MM/yyyy HH:mm";
                    }
                    
                    if (dataGridViewMessages.Columns.Contains("isRead"))
                        dataGridViewMessages.Columns["isRead"].HeaderText = "مقروءة";
                    
                    if (dataGridViewMessages.Columns.Contains("senderPharmacyName"))
                        dataGridViewMessages.Columns["senderPharmacyName"].HeaderText = "الصيدلية المرسلة";
                    
                    if (dataGridViewMessages.Columns.Contains("receiverPharmacyName"))
                        dataGridViewMessages.Columns["receiverPharmacyName"].HeaderText = "الصيدلية المستقبلة";
                    
                    if (dataGridViewMessages.Columns.Contains("messageType"))
                        dataGridViewMessages.Columns["messageType"].HeaderText = "نوع الرسالة";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في إعداد أعمدة جدول الرسائل: " + ex.Message);
            }
        }

        private async void LoadPharmacies()
        {
            try
            {
                string query = @"
                    SELECT id, pharmacyName
                    FROM pharmacies
                    WHERE id != @currentPharmacyId AND isActive = 1
                    ORDER BY pharmacyName";

                var parameters = new Dictionary<string, object>
                {
                    {"@currentPharmacyId", SessionManager.CurrentPharmacyId}
                };

                DataSet pharmaciesDataSet = await Task.Run(() => unifiedFunction.ExecuteQuery(query, parameters));
                DataTable pharmaciesData = pharmaciesDataSet?.Tables[0];
                
                if (pharmaciesData != null)
                {
                    cmbPharmacies.DisplayMember = "pharmacyName";
                    cmbPharmacies.ValueMember = "id";
                    cmbPharmacies.DataSource = pharmaciesData;
                    cmbPharmacies.SelectedIndex = -1;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في تحميل الصيدليات: " + ex.Message);
            }
        }

        private async void btnSendMessage_Click(object sender, EventArgs e)
        {
            try
            {
                if (cmbPharmacies.SelectedValue == null)
                {
                    MessageBox.Show("يرجى اختيار صيدلية للإرسال إليها", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtSubject.Text))
                {
                    MessageBox.Show("يرجى إدخال موضوع الرسالة", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtMessage.Text))
                {
                    MessageBox.Show("يرجى إدخال محتوى الرسالة", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                string insertQuery = @"
                    INSERT INTO pharmacy_messages 
                    (sender_pharmacy_id, receiver_pharmacy_id, subject, message_content, sent_date, is_read)
                    VALUES (@senderPharmacyId, @receiverPharmacyId, @subject, @messageContent, GETDATE(), 0)";

                var parameters = new Dictionary<string, object>
                {
                    {"@senderPharmacyId", SessionManager.CurrentPharmacyId},
                    {"@receiverPharmacyId", cmbPharmacies.SelectedValue},
                    {"@subject", txtSubject.Text.Trim()},
                    {"@messageContent", txtMessage.Text.Trim()}
                };

                bool success = await Task.Run(() => unifiedFunction.ExecuteNonQuery(insertQuery, parameters));
                
                if (success)
                {
                    MessageBox.Show("تم إرسال الرسالة بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    // مسح الحقول
                    txtSubject.Clear();
                    txtMessage.Clear();
                    cmbPharmacies.SelectedIndex = -1;
                    
                    // تحديث قائمة الرسائل
                    LoadMessages();
                }
                else
                {
                    MessageBox.Show("فشل في إرسال الرسالة", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في إرسال الرسالة: " + ex.Message, "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadMessages();
        }

        private async void dataGridViewMessages_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0)
                {
                    var selectedRow = dataGridViewMessages.Rows[e.RowIndex];
                    int messageId = Convert.ToInt32(selectedRow.Cells["id"].Value);
                    bool isRead = Convert.ToBoolean(selectedRow.Cells["isRead"].Value);
                    
                    // تحديد الرسالة كمقروءة إذا لم تكن كذلك
                    if (!isRead)
                    {
                        string updateQuery = @"
                            UPDATE pharmacy_messages 
                            SET is_read = 1 
                            WHERE id = @messageId";

                        var parameters = new Dictionary<string, object>
                        {
                            {"@messageId", messageId}
                        };

                        await Task.Run(() => unifiedFunction.ExecuteNonQuery(updateQuery, parameters));
                        LoadMessages();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في تحديث حالة الرسالة: " + ex.Message);
            }
        }


    }
}
