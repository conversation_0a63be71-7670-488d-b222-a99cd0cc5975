@echo off
echo ========================================
echo   إنشاء النظام الموحد للصيدلية
echo   Creating Unified Pharmacy System
echo ========================================
echo.

echo 🔧 إنشاء قاعدة البيانات الموحدة...
echo - دمج قاعدة البيانات المحلية والأونلاين
echo - إنشاء نظام صيدلية موحد
echo - نقل جميع البيانات الموجودة
echo.

echo الخطوة 1: إنشاء قاعدة البيانات الموحدة...
sqlcmd -S NARUTO -E -Q "IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'UnifiedPharmacy') CREATE DATABASE UnifiedPharmacy"

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إنشاء قاعدة البيانات UnifiedPharmacy
) else (
    echo ❌ فشل في إنشاء قاعدة البيانات
)

echo.
echo الخطوة 2: إنشاء جدول الصيدليات...
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies' AND xtype='U') CREATE TABLE pharmacies (id INT IDENTITY(1,1) PRIMARY KEY, pharmacyCode VARCHAR(20) UNIQUE NOT NULL, pharmacyName NVARCHAR(250) NOT NULL, ownerName NVARCHAR(250) NOT NULL, licenseNumber VARCHAR(100) NOT NULL, address NVARCHAR(500) NOT NULL, city NVARCHAR(100) NOT NULL, region NVARCHAR(100) NOT NULL, phone VARCHAR(20) NOT NULL, email VARCHAR(250) NOT NULL, isActive BIT DEFAULT 1, registrationDate DATETIME DEFAULT GETDATE(), lastOnline DATETIME DEFAULT GETDATE(), subscriptionType VARCHAR(50) DEFAULT 'Basic', createdAt DATETIME DEFAULT GETDATE(), updatedAt DATETIME DEFAULT GETDATE())"

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إنشاء جدول pharmacies
) else (
    echo ❌ فشل في إنشاء جدول pharmacies
)

echo.
echo الخطوة 3: إنشاء جدول المستخدمين الموحد...
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U') CREATE TABLE users (id INT IDENTITY(1,1) PRIMARY KEY, pharmacyId INT NOT NULL, userRole VARCHAR(50) NOT NULL, name NVARCHAR(250) NOT NULL, dob VARCHAR(250) NOT NULL, mobile BIGINT NOT NULL, email VARCHAR(250) NOT NULL, username VARCHAR(250) UNIQUE NOT NULL, pass VARCHAR(250) NOT NULL, isActive BIT DEFAULT 1, lastLogin DATETIME NULL, createdAt DATETIME DEFAULT GETDATE(), updatedAt DATETIME DEFAULT GETDATE(), FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id))"

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إنشاء جدول users الموحد
) else (
    echo ❌ فشل في إنشاء جدول users
)

echo.
echo الخطوة 4: إنشاء جدول الأدوية الموحد...
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='medicines' AND xtype='U') CREATE TABLE medicines (id INT IDENTITY(1,1) PRIMARY KEY, pharmacyId INT NOT NULL, mid VARCHAR(250) NOT NULL, mname NVARCHAR(250) NOT NULL, mnumber VARCHAR(250) NOT NULL, mDate VARCHAR(250) NOT NULL, eDate VARCHAR(250) NOT NULL, quantity BIGINT NOT NULL DEFAULT 0, perUnit BIGINT NOT NULL, lu VARCHAR(250) NOT NULL, br VARCHAR(250) NOT NULL, newEDate VARCHAR(250) NULL, newQuantity BIGINT NULL DEFAULT 0, allqun BIGINT NULL DEFAULT 0, mnumber_qty INT NULL DEFAULT 0, newMDate VARCHAR(250) NULL, dos2 VARCHAR(100) NULL, dos2_qty INT NULL DEFAULT 0, dos3 VARCHAR(100) NULL, dos3_qty INT NULL DEFAULT 0, dos4 VARCHAR(100) NULL, dos4_qty INT NULL DEFAULT 0, originalQuantity BIGINT DEFAULT 0, originalNewQuantity BIGINT DEFAULT 0, isAvailableForSale BIT DEFAULT 1, requiresPrescription BIT DEFAULT 0, manufacturer NVARCHAR(250) NULL, category NVARCHAR(100) NULL, dosageForm NVARCHAR(100) NULL, strength NVARCHAR(100) NULL, unitPrice DECIMAL(10,2) NULL, wholesalePrice DECIMAL(10,2) NULL, expiryDate DATE NULL, createdAt DATETIME DEFAULT GETDATE(), updatedAt DATETIME DEFAULT GETDATE(), FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id))"

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إنشاء جدول medicines الموحد
) else (
    echo ❌ فشل في إنشاء جدول medicines
)

echo.
echo الخطوة 5: إضافة الصيدلية الافتراضية...
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacyCode = 'MAIN001') INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, licenseNumber, address, city, region, phone, email) VALUES ('MAIN001', 'الصيدلية الرئيسية', 'مدير النظام', 'LIC001', 'العنوان الرئيسي', 'المدينة', 'المنطقة', '**********', '<EMAIL>')"

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إضافة الصيدلية الافتراضية
) else (
    echo ❌ فشل في إضافة الصيدلية الافتراضية
)

echo.
echo الخطوة 6: إنشاء جدول المبيعات...
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='sales' AND xtype='U') CREATE TABLE sales (id INT IDENTITY(1,1) PRIMARY KEY, pharmacyId INT NOT NULL, mid VARCHAR(250) NOT NULL, medicineName NVARCHAR(250) NOT NULL, dosage VARCHAR(100) NOT NULL, quantity INT NOT NULL, pricePerUnit BIGINT NOT NULL, totalPrice BIGINT NOT NULL, employeeUsername VARCHAR(250) NOT NULL, employeeName NVARCHAR(250) NOT NULL, saleDate DATETIME DEFAULT GETDATE(), FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id))"

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إنشاء جدول sales
) else (
    echo ❌ فشل في إنشاء جدول sales
)

echo.
echo الخطوة 7: إنشاء جدول جلسات الموظفين...
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='employee_sessions' AND xtype='U') CREATE TABLE employee_sessions (id INT IDENTITY(1,1) PRIMARY KEY, pharmacyId INT NOT NULL, username VARCHAR(250), employeeName NVARCHAR(250), loginTime DATETIME, logoutTime DATETIME NULL, sessionDate DATE, FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id))"

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إنشاء جدول employee_sessions
) else (
    echo ❌ فشل في إنشاء جدول employee_sessions
)

echo.
echo الخطوة 8: إضافة مستخدم مدير افتراضي...
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "DECLARE @pharmacyId INT = (SELECT id FROM pharmacies WHERE pharmacyCode = 'MAIN001'); IF NOT EXISTS (SELECT * FROM users WHERE username = 'admin') INSERT INTO users (pharmacyId, userRole, name, dob, mobile, email, username, pass) VALUES (@pharmacyId, 'Administrator', 'مدير النظام', '1980-01-01', **********, '<EMAIL>', 'admin', 'admin123')"

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إضافة مستخدم مدير افتراضي
) else (
    echo ❌ فشل في إضافة المستخدم المدير
)

echo.
echo الخطوة 9: إضافة موظف افتراضي...
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "DECLARE @pharmacyId INT = (SELECT id FROM pharmacies WHERE pharmacyCode = 'MAIN001'); IF NOT EXISTS (SELECT * FROM users WHERE username = 'employee') INSERT INTO users (pharmacyId, userRole, name, dob, mobile, email, username, pass) VALUES (@pharmacyId, 'Employee', 'موظف الصيدلية', '1990-01-01', 9876543210, '<EMAIL>', 'employee', 'emp123')"

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إضافة موظف افتراضي
) else (
    echo ❌ فشل في إضافة الموظف
)

echo.
echo الخطوة 10: إضافة أدوية تجريبية...
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "DECLARE @pharmacyId INT = (SELECT id FROM pharmacies WHERE pharmacyCode = 'MAIN001'); INSERT INTO medicines (pharmacyId, mid, mname, mnumber, mDate, eDate, quantity, perUnit, lu, br, manufacturer, category, dosageForm, strength, unitPrice, wholesalePrice) VALUES (@pharmacyId, 'MED001', 'باراسيتامول', '500mg', '2024-01-01', '2025-12-31', 100, 5, 'صندوق', 'فايزر', 'فايزر', 'مسكنات', 'أقراص', '500mg', 5.50, 5.00), (@pharmacyId, 'MED002', 'أموكسيسيلين', '250mg', '2024-01-01', '2025-10-15', 75, 15, 'علبة', 'جلاكسو', 'جلاكسو', 'مضادات حيوية', 'كبسولات', '250mg', 15.75, 14.00), (@pharmacyId, 'MED003', 'فيتامين سي', '1000mg', '2024-01-01', '2026-03-20', 60, 12, 'زجاجة', 'باير', 'باير', 'فيتامينات', 'أقراص', '1000mg', 12.00, 10.50)"

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إضافة أدوية تجريبية
) else (
    echo ❌ فشل في إضافة الأدوية
)

echo.
echo الخطوة 11: التحقق من النتائج...

echo عدد الصيدليات:
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "SELECT COUNT(*) FROM pharmacies"

echo عدد المستخدمين:
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "SELECT COUNT(*) FROM users"

echo عدد الأدوية:
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "SELECT COUNT(*) FROM medicines"

echo.
echo ========================================
echo   ✅ تم إنشاء النظام الموحد بنجاح!
echo ========================================
echo.
echo الآن يمكنك:
echo 1. استخدام قاعدة البيانات الموحدة: UnifiedPharmacy
echo 2. تسجيل دخول بالحسابات الافتراضية:
echo    - المدير: admin / admin123
echo    - الموظف: employee / emp123
echo 3. إدارة جميع البيانات من مكان واحد
echo.
echo 🎯 الخطوة التالية: تحديث البرنامج للاتصال بقاعدة البيانات الجديدة
echo.

pause
