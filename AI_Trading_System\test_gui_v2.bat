@echo off
title Test Intelligent GUI V2 - Quick Launch

echo.
echo ========================================
echo   TESTING INTELLIGENT GUI V2
echo   Quick Launch for Testing
echo ========================================
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ❌ Python not found! Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Installing minimal requirements...
pip install tkinter matplotlib pandas numpy --quiet

echo.
echo Launching GUI in TEST MODE...
echo ⚠️  Note: Some features may be limited without full system
echo.

python intelligent_gui_v2.py

if errorlevel 1 (
    echo.
    echo ❌ Error launching GUI!
    echo 💡 Try: pip install --upgrade tkinter matplotlib
    echo.
)

pause
