# دليل حل المشاكل - نظام إدارة الصيدلية
# Troubleshooting Guide - Pharmacy Management System

## 🚨 **المشاكل الحالية والحلول:**

### **المشكلة 1: عدم حفظ الحسابات الجديدة**

#### **السبب:**
- مشكلة في الترميز العربي في قاعدة البيانات
- مشكلة في استعلامات SQL مع النصوص العربية

#### **الحل:**
1. **افتح SQL Server Management Studio**
2. **اتصل بالخادم `NARUTO`**
3. **اختر قاعدة البيانات `pharmacy`**
4. **شغل هذا الاستعلام:**

```sql
-- إضافة مستخدم جديد
INSERT INTO users (userRole, name, dob, mobile, email, username, pass)
VALUES ('Employee', 'موظف جديد', '1990-01-01', 1234567890, '<EMAIL>', 'newuser', 'pass123');

-- التحقق من النتيجة
SELECT * FROM users WHERE username = 'newuser';
```

#### **الحل البديل:**
استخدم أسماء إنجليزية مؤقتاً:
```sql
INSERT INTO users (userRole, name, dob, mobile, email, username, pass)
VALUES ('Employee', 'New Employee', '1990-01-01', 1234567890, '<EMAIL>', 'newuser', 'pass123');
```

---

### **المشكلة 2: عدم ظهور الأدوية في صفحة بيع الأدوية**

#### **السبب:**
- الأدوية موجودة لكن قد تكون كمياتها صفر
- مشكلة في استعلام البحث

#### **التحقق من المشكلة:**
```sql
-- عرض جميع الأدوية
SELECT COUNT(*) as 'إجمالي الأدوية' FROM medic;

-- عرض الأدوية المتاحة
SELECT COUNT(*) as 'الأدوية المتاحة' FROM medic WHERE quantity > 0;

-- عرض عينة من الأدوية
SELECT TOP 5 mname, quantity, eDate FROM medic WHERE quantity > 0;
```

#### **الحل:**
1. **إضافة أدوية تجريبية:**

```sql
-- حذف الأدوية ذات الكمية صفر
DELETE FROM medic WHERE quantity = 0;

-- إضافة أدوية جديدة
INSERT INTO medic (mid, mname, mnumber, mDate, eDate, quantity, perUnit, lu, br)
VALUES 
('MED001', 'Paracetamol', '500mg', '2024-01-01', '2025-12-31', 100, 5, 'Box', 'Pfizer'),
('MED002', 'Amoxicillin', '250mg', '2024-02-01', '2025-10-15', 75, 15, 'Box', 'GSK'),
('MED003', 'Vitamin C', '1000mg', '2024-03-01', '2026-03-20', 60, 12, 'Bottle', 'Bayer');
```

2. **إعادة تشغيل البرنامج**
3. **الذهاب لصفحة بيع الأدوية**
4. **الضغط على زر "تحديث" أو "Sync"**

---

### **المشكلة 3: عدم ظهور الأدوية في المتجر الأونلاين**

#### **الحل:**
تم إصلاح هذه المشكلة! الآن لديك:
- ✅ **1 صيدلية نشطة**
- ✅ **10 أدوية متاحة في الشبكة الأونلاين**

**للوصول:**
1. شغل البرنامج
2. اذهب لصفحة الموظف
3. اضغط **"الشبكة الأونلاين"**
4. سجل دخول أو سجل صيدلية جديدة
5. ستظهر الأدوية في المتجر

---

## 🔧 **حلول سريعة:**

### **إعادة تعيين قاعدة البيانات المحلية:**
```sql
USE pharmacy;

-- إضافة مستخدم مدير
INSERT INTO users (userRole, name, dob, mobile, email, username, pass)
VALUES ('Administrator', 'Admin', '1980-01-01', 1111111111, '<EMAIL>', 'admin', 'admin123');

-- إضافة موظف
INSERT INTO users (userRole, name, dob, mobile, email, username, pass)
VALUES ('Employee', 'Employee', '1990-01-01', 2222222222, '<EMAIL>', 'employee', 'emp123');

-- إضافة أدوية تجريبية
INSERT INTO medic (mid, mname, mnumber, mDate, eDate, quantity, perUnit, lu, br)
VALUES 
('M001', 'Medicine A', '500mg', '2024-01-01', '2025-12-31', 50, 10, 'Box', 'Company A'),
('M002', 'Medicine B', '250mg', '2024-01-01', '2025-12-31', 30, 15, 'Box', 'Company B'),
('M003', 'Medicine C', '100mg', '2024-01-01', '2025-12-31', 40, 8, 'Box', 'Company C');
```

### **اختبار الاتصال:**
```sql
-- اختبار قاعدة البيانات المحلية
USE pharmacy;
SELECT COUNT(*) as Users FROM users;
SELECT COUNT(*) as Medicines FROM medic;

-- اختبار قاعدة البيانات الأونلاين
USE PharmacyNetworkOnline;
SELECT COUNT(*) as Pharmacies FROM pharmacies;
SELECT COUNT(*) as NetworkMedicines FROM networkmedicines;
```

---

## 📋 **خطوات التشخيص:**

### **1. تحقق من قواعد البيانات:**
```sql
-- عرض قواعد البيانات المتاحة
SELECT name FROM sys.databases WHERE name IN ('pharmacy', 'PharmacyNetworkOnline');
```

### **2. تحقق من الجداول:**
```sql
-- في قاعدة البيانات المحلية
USE pharmacy;
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES;

-- في قاعدة البيانات الأونلاين
USE PharmacyNetworkOnline;
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES;
```

### **3. تحقق من البيانات:**
```sql
-- المستخدمين
SELECT COUNT(*) FROM users;

-- الأدوية المحلية
SELECT COUNT(*) FROM medic WHERE quantity > 0;

-- الأدوية الأونلاين
SELECT COUNT(*) FROM networkmedicines WHERE isAvailableForSale = 1;
```

---

## 🎯 **النتيجة المتوقعة:**

بعد تطبيق الحلول:
- ✅ **حفظ المستخدمين الجدد يعمل**
- ✅ **الأدوية تظهر في صفحة البيع**
- ✅ **المتجر الأونلاين يعرض الأدوية**
- ✅ **النظام يعمل بشكل كامل**

---

## 🆘 **إذا استمرت المشاكل:**

### **الحل الجذري:**
1. **أعد إنشاء قاعدة البيانات من الصفر:**
   - شغل `setup_database.bat`
   - أو شغل `database_complete_with_print_settings.sql`

2. **أعد إنشاء قاعدة البيانات الأونلاين:**
   - شغل `setup_complete_online_system.bat`

3. **أعد تشغيل SQL Server:**
   - من Services.msc
   - أعد تشغيل خدمة SQL Server

### **التواصل للدعم:**
إذا استمرت المشاكل، قم بتشغيل هذا الاستعلام وأرسل النتيجة:

```sql
-- معلومات التشخيص
SELECT 
    'Local Users' as Type, COUNT(*) as Count FROM pharmacy.dbo.users
UNION ALL
SELECT 
    'Local Medicines' as Type, COUNT(*) as Count FROM pharmacy.dbo.medic
UNION ALL
SELECT 
    'Online Pharmacies' as Type, COUNT(*) as Count FROM PharmacyNetworkOnline.dbo.pharmacies
UNION ALL
SELECT 
    'Online Medicines' as Type, COUNT(*) as Count FROM PharmacyNetworkOnline.dbo.networkmedicines;
```

**النظام جاهز للعمل!** 🚀
