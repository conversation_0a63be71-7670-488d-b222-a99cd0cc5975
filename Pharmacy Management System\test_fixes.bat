@echo off
chcp 65001 >nul
echo ═══════════════════════════════════════════════════════════════
echo                    اختبار الإصلاحات الجديدة
echo                    Testing New Fixes
echo ═══════════════════════════════════════════════════════════════
echo.

echo الإصلاحات المطبقة:
echo Applied fixes:
echo ───────────────────────────────────────────────────────────────
echo ✅ 1. إزالة نافذة Pharmacy Network Login عند التشغيل
echo ✅ 1. Removed Pharmacy Network Login window on startup
echo ✅ 2. إضافة نموذج تسجيل دخول بسيط
echo ✅ 2. Added simple login form
echo ✅ 3. إصلاح حجم صفحة طلب الدواء
echo ✅ 3. Fixed medicine request form size
echo.

echo الخطوة 1: بناء المشروع
echo Step 1: Building project
echo ───────────────────────────────────────────────────────────────

REM البحث عن MSBuild
set MSBUILD_PATH=""
if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
) else (
    echo ⚠️ MSBuild غير موجود، محاولة استخدام dotnet build
    echo ⚠️ MSBuild not found, trying dotnet build
    dotnet build "Pharmacy Management System.csproj" --configuration Debug >nul 2>&1
    goto CHECK_BUILD
)

%MSBUILD_PATH% "Pharmacy Management System.csproj" /p:Configuration=Debug /p:Platform="Any CPU" /nologo /verbosity:quiet

:CHECK_BUILD
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء المشروع بنجاح
    echo ✅ Project built successfully
) else (
    echo ❌ فشل في بناء المشروع
    echo ❌ Project build failed
    pause
    exit /b 1
)

echo.
echo الخطوة 2: تشغيل النظام
echo Step 2: Running system
echo ───────────────────────────────────────────────────────────────

if exist "bin\Debug\Pharmacy Management System.exe" (
    echo 🚀 تشغيل نظام إدارة الصيدلية...
    echo 🚀 Starting Pharmacy Management System...
    echo.
    echo 📋 معلومات تسجيل الدخول:
    echo 📋 Login credentials:
    echo • المدير: admin / admin123
    echo • Administrator: admin / admin123
    echo • الموظف: employee1 / emp123
    echo • Employee: employee1 / emp123
    echo.
    echo 🧪 اختبار الإصلاحات:
    echo 🧪 Testing fixes:
    echo.
    echo 1. ✅ يجب أن تظهر نافذة تسجيل دخول بسيطة (وليس Pharmacy Network)
    echo 1. ✅ Simple login window should appear (not Pharmacy Network)
    echo.
    echo 2. ✅ سجل دخول كموظف واذهب لمتجر الصيدلية
    echo 2. ✅ Login as employee and go to Pharmacy Store
    echo.
    echo 3. ✅ اختبر طلب دواء - يجب أن تظهر النافذة كاملة
    echo 3. ✅ Test medicine request - window should appear completely
    echo.
    echo النظام سيفتح الآن...
    echo System will open now...
    start "" "bin\Debug\Pharmacy Management System.exe"
    echo.
    echo ✅ تم تشغيل النظام بنجاح!
    echo ✅ System started successfully!
) else (
    echo ❌ ملف التشغيل غير موجود
    echo ❌ Executable file not found
    echo يرجى التحقق من بناء المشروع
    echo Please check project build
)

echo.
echo ═══════════════════════════════════════════════════════════════
echo                        🎉 انتهى الاختبار!
echo                     🎉 Testing Completed!
echo ═══════════════════════════════════════════════════════════════
echo.
echo إذا ظهرت أي مشاكل:
echo If any issues appear:
echo 1. تأكد من أن نافذة تسجيل الدخول البسيطة تظهر
echo 1. Make sure simple login window appears
echo 2. تأكد من أن صفحة طلب الدواء تظهر كاملة
echo 2. Make sure medicine request page appears completely
echo 3. تحقق من عمل متجر الصيدلية
echo 3. Check pharmacy store functionality
echo.
pause
