﻿using Guna.UI2.WinForms;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Drawing.Printing;
using System.Linq;
using System.Windows.Forms;

namespace Pharmacy_Management_System.PharmacistUC
{
    public partial class UC_P_MedicineValidityCheck : UserControl
    {
        Function fn = new Function();
        String query;
        PrintDocument printDocument = new PrintDocument();
        PrintPreviewDialog printPreviewDialog = new PrintPreviewDialog();

        // متغيرات الطباعة
        private int currentRow = 0;
        private int currentPage = 1;
        private int[] columnWidths;

        public UC_P_MedicineValidityCheck()
        {
            InitializeComponent();

            // الاشتراك في حدث تغيير اللغة
            // LanguageManager.LanguageChanged += OnLanguageChanged;
        }

        private void OnLanguageChanged(object sender, EventArgs e)
        {
            ApplyLanguage();
        }

        public void ApplyLanguage()
        {
            // ترجمة العناوين والأزرار
            if (label1 != null) label1.Text = "فحص صلاحية الأدوية";
            if (btnPrint != null) btnPrint.Text = "طباعة";

            // ترجمة أعمدة الجدول
            if (guna2DataGridView1.Columns.Count > 0)
            {
                foreach (DataGridViewColumn column in guna2DataGridView1.Columns)
                {
                    switch (column.Name.ToLower())
                    {
                        case "mid":
                            column.HeaderText = "رقم الدواء";
                            break;
                        case "mname":
                            column.HeaderText = "اسم الدواء";
                            break;
                        case "quantity":
                            column.HeaderText = "الكمية";
                            break;
                        case "edate":
                            column.HeaderText = "تاريخ الانتهاء";
                            break;
                        case "perunit":
                            column.HeaderText = "السعر للوحدة";
                            break;
                    }
                }
            }

            // الحفاظ على التصميم من اليسار لليمين في جميع اللغات
            this.RightToLeft = RightToLeft.No;
        }

        protected override void OnHandleDestroyed(EventArgs e)
        {
            // إلغاء الاشتراك في حدث تغيير اللغة
            LanguageManager.LanguageChanged -= OnLanguageChanged;
            base.OnHandleDestroyed(e);
        }

        private void txtCheck_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (txtCheck.SelectedIndex == 0)
            {
                query = "select * from medic where eDate >= getdate()";
                DataSet ds = fn.getData(query);
                guna2DataGridView1.DataSource = ds.Tables[0];
                setLabel.Text = "الأدوية الصالحة";
                setLabel.ForeColor = Color.Black;
            }
            else if (txtCheck.SelectedIndex == 1)
            {
                query = "select * from medic where eDate <= getdate()";
                DataSet ds = fn.getData(query);
                guna2DataGridView1.DataSource = ds.Tables[0];
                setLabel.Text = "الأدوية المنتهية الصلاحية";
                setLabel.ForeColor = Color.Red;
            }
            else if (txtCheck.SelectedIndex == 2)
            {
                query = "select * from medic";
                DataSet ds = fn.getData(query);
                guna2DataGridView1.DataSource = ds.Tables[0];
                setLabel.Text = "جميع الأدوية";
                setLabel.ForeColor = Color.Black;
            }
            else if (txtCheck.SelectedIndex == 3)
            {
                query = "SELECT * FROM medic WHERE eDate > GETDATE() AND eDate <= DATEADD(MONTH, 6, GETDATE())";
                DataSet ds = fn.getData(query);
                guna2DataGridView1.DataSource = ds.Tables[0];
                setLabel.Text = "الأدوية التي ستنتهي صلاحيتها قريباً";
                setLabel.ForeColor = Color.Black;
            }
            else if (txtCheck.SelectedIndex == 4)
            {
                query = "Select * From medic WHERE quantity <= 10";
                DataSet ds = fn.getData(query);
                guna2DataGridView1.DataSource = ds.Tables[0];
                setLabel.Text = "الأدوية القليلة الكمية";
                setLabel.ForeColor = Color.Black;
            }
        }

        private void UC_P_MedicineValidityCheck_Load(object sender, EventArgs e)
        {
            setLabel.Text = "";
            printDocument.PrintPage += PrintDocument_PrintPage;
        }

        private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            // الحصول على إعدادات الطباعة المحفوظة
            var settings = AdministratorUC.UC_PrintDesign.GetPrintSettingsForReport("صلاحية الأدوية");

            int startX = settings.MarginLeft;
            int startY = settings.MarginTop + 60;
            const int rowHeight = 35; // زيادة ارتفاع الصف
            const int padding = 15; // زيادة المسافة بين الأعمدة
            const int headerHeight = 45; // زيادة ارتفاع الرأس
            const int footerHeight = 35;

            // إعداد الخطوط المحسنة باستخدام الإعدادات المحفوظة
            Font dataFont = new Font("Arial", settings.TableFont, FontStyle.Regular);
            Font headerFont = new Font("Arial", settings.TableFont + 2, FontStyle.Bold);
            Font titleFont = new Font("Arial", settings.TitleFont, FontStyle.Bold);
            Font footerFont = new Font("Arial", 9, FontStyle.Italic);

            // 1. طباعة العنوان والتاريخ المحسن باستخدام الإعدادات المحفوظة
            string title = string.IsNullOrEmpty(setLabel.Text) ? settings.TitleText : setLabel.Text;
            SizeF titleSize = e.Graphics.MeasureString(title, titleFont);

            float titleX = startX; // افتراضي يسار
            if (settings.TitleAlignment == "وسط")
                titleX = (e.PageBounds.Width - titleSize.Width) / 2;
            else if (settings.TitleAlignment == "يمين")
                titleX = e.PageBounds.Width - titleSize.Width - settings.MarginRight;

            e.Graphics.DrawString(title, titleFont, Brushes.DarkBlue, titleX, settings.MarginTop);

            // خط تحت العنوان
            e.Graphics.DrawLine(new Pen(Color.DarkBlue, settings.BorderWidth), startX, settings.MarginTop + 35, e.PageBounds.Width - startX, settings.MarginTop + 35);

            // طباعة التاريخ إذا كان مفعلاً
            if (settings.ShowDateTime)
            {
                string printDate = $"تاريخ الطباعة: {DateTime.Now.ToString(settings.DateFormat)} - {DateTime.Now:HH:mm}";
                e.Graphics.DrawString(printDate, footerFont, Brushes.Black, new PointF(startX, settings.MarginTop + 40));
            }

            if (settings.ShowPageNumbers)
            {
                string pageNumber = $"صفحة {currentPage}";
                e.Graphics.DrawString(pageNumber, footerFont, Brushes.Black, new PointF(e.PageBounds.Width - 100, settings.MarginTop + 40));
            }

            // 2. تحديد الأعمدة المراد طباعتها
            var columnsToPrint = guna2DataGridView1.Columns.Cast<DataGridViewColumn>()
                .Select(c => c.Name).ToList();

            var visibleColumns = guna2DataGridView1.Columns
                .Cast<DataGridViewColumn>()
                .Where(c => columnsToPrint.Contains(c.Name))
                .ToList();

            // 3. حساب عرض الأعمدة تلقائياً
            columnWidths = CalculateColumnWidths(e, visibleColumns, headerFont, dataFont, padding);

            // 4. ضبط مقاسات الأعمدة لتناسب الصفحة
            AdjustColumnWidths(e, ref columnWidths, startX);

            // 5. رسم رؤوس الأعمدة
            DrawColumnHeaders(e, startX, ref startY, headerHeight, padding, headerFont, visibleColumns, columnWidths);

            // 6. رسم بيانات الجدول
            bool morePages = DrawDataRows(e, startX, ref startY, rowHeight, padding, dataFont, visibleColumns, columnWidths);

            // 7. طباعة تذييل الصفحة
            e.Graphics.DrawString($"Page {currentPage}", footerFont, Brushes.Gray,
                new PointF(startX, e.MarginBounds.Bottom - footerHeight));

            e.HasMorePages = morePages;
            if (!morePages)
            {
                currentRow = 0;
                currentPage = 1;
            }
            else
            {
                currentPage++;
            }
        }

        private int[] CalculateColumnWidths(PrintPageEventArgs e, List<DataGridViewColumn> columns, Font headerFont, Font dataFont, int padding)
        {
            int[] widths = new int[columns.Count];

            for (int i = 0; i < columns.Count; i++)
            {
                // حساب عرض رأس العمود
                float width = e.Graphics.MeasureString(columns[i].HeaderText, headerFont).Width;

                // حساب أقصى عرض للمحتوى في العمود
                foreach (DataGridViewRow row in guna2DataGridView1.Rows)
                {
                    if (!row.IsNewRow)
                    {
                        string text = row.Cells[columns[i].Name].Value?.ToString() ?? "";
                        float textWidth = e.Graphics.MeasureString(text, dataFont).Width;
                        if (textWidth > width)
                            width = textWidth;
                    }
                }

                // ضمان حد أدنى مناسب لكل عمود
                int minWidth = GetMinimumColumnWidth(columns[i].Name);
                widths[i] = Math.Max((int)Math.Ceiling(width) + 3 * padding, minWidth);
            }

            return widths;
        }

        private int GetMinimumColumnWidth(string columnName)
        {
            // تحديد الحد الأدنى لعرض كل عمود حسب نوعه لمنع التداخل
            switch (columnName.ToLower())
            {
                case "mid":
                    return 90;  // رقم الدواء
                case "mname":
                    return 180; // اسم الدواء - أطول عمود
                case "quantity":
                    return 80;  // الكمية
                case "edate":
                    return 120; // تاريخ الانتهاء
                case "perunit":
                    return 100; // السعر
                default:
                    return 80;  // افتراضي
            }
        }

        private void AdjustColumnWidths(PrintPageEventArgs e, ref int[] columnWidths, int startX)
        {
            int totalWidth = columnWidths.Sum();
            int availableWidth = e.MarginBounds.Width - (2 * startX) - (columnWidths.Length * 10); // مسافة إضافية بين الأعمدة

            if (totalWidth > availableWidth)
            {
                // تقليل عرض الأعمدة بنسبة متناسبة مع الحفاظ على الحد الأدنى
                float scale = (float)availableWidth / totalWidth;

                for (int i = 0; i < columnWidths.Length; i++)
                {
                    int originalWidth = columnWidths[i];
                    columnWidths[i] = (int)(originalWidth * scale);

                    // ضمان حد أدنى مناسب لكل نوع عمود
                    int minWidth = GetMinimumColumnWidth(GetColumnNameByIndex(i));
                    columnWidths[i] = Math.Max(columnWidths[i], minWidth / 2); // نصف الحد الأدنى كحد أقصى للضغط
                }

                // إعادة حساب العرض الإجمالي بعد التعديل
                totalWidth = columnWidths.Sum();

                // إذا كان العرض لا يزال كبيراً، استخدم توزيع ذكي
                if (totalWidth > availableWidth)
                {
                    // توزيع العرض المتاح بناءً على أهمية العمود
                    DistributeWidthIntelligently(ref columnWidths, availableWidth);
                }
            }
        }

        private string GetColumnNameByIndex(int index)
        {
            if (guna2DataGridView1.Columns.Count > index)
            {
                return guna2DataGridView1.Columns[index].Name;
            }
            return "unknown";
        }

        private void DistributeWidthIntelligently(ref int[] columnWidths, int availableWidth)
        {
            // توزيع ذكي للعرض بناءً على أهمية الأعمدة
            var priorities = new Dictionary<string, float>
            {
                {"mname", 0.35f},    // اسم الدواء - الأهم
                {"edate", 0.20f},    // تاريخ الانتهاء
                {"quantity", 0.15f}, // الكمية
                {"perunit", 0.15f},  // السعر
                {"mid", 0.15f}       // رقم الدواء
            };

            for (int i = 0; i < columnWidths.Length; i++)
            {
                string columnName = GetColumnNameByIndex(i).ToLower();
                float priority = priorities.ContainsKey(columnName) ? priorities[columnName] : 0.1f;

                columnWidths[i] = Math.Max((int)(availableWidth * priority), 50); // حد أدنى 50 بكسل
            }
        }

        private void DrawColumnHeaders(PrintPageEventArgs e, int startX, ref int startY, int headerHeight,
                                    int padding, Font headerFont, List<DataGridViewColumn> columns, int[] columnWidths)
        {
            int currentX = startX;

            for (int i = 0; i < columns.Count; i++)
            {
                Rectangle rect = new Rectangle(currentX, startY, columnWidths[i], headerHeight);

                // رسم خلفية رأس العمود
                e.Graphics.FillRectangle(Brushes.LightBlue, rect);
                e.Graphics.DrawRectangle(Pens.Black, rect);

                // رسم نص رأس العمود مع منع التداخل
                string headerText = columns[i].HeaderText;
                string displayText = TruncateText(e.Graphics, headerText, headerFont, columnWidths[i] - 10);

                SizeF textSize = e.Graphics.MeasureString(displayText, headerFont);
                float textX = rect.X + 5; // مسافة من اليسار
                float textY = rect.Y + (rect.Height - textSize.Height) / 2;

                // رسم النص مع تحديد منطقة القطع
                Rectangle clipRect = new Rectangle(rect.X + 2, rect.Y + 2, rect.Width - 4, rect.Height - 4);
                e.Graphics.SetClip(clipRect);
                e.Graphics.DrawString(displayText, headerFont, Brushes.Black, textX, textY);
                e.Graphics.ResetClip();

                currentX += columnWidths[i];
            }

            startY += headerHeight;
        }

        private bool DrawDataRows(PrintPageEventArgs e, int startX, ref int startY, int rowHeight,
                                int padding, Font dataFont, List<DataGridViewColumn> columns, int[] columnWidths)
        {
            while (currentRow < guna2DataGridView1.Rows.Count)
            {
                DataGridViewRow row = guna2DataGridView1.Rows[currentRow];
                if (row.IsNewRow)
                {
                    currentRow++;
                    continue;
                }

                // التحقق من وجود مساحة كافية لسطر جديد
                if (startY + rowHeight > e.MarginBounds.Bottom - 30) // ترك مساحة للتذييل
                {
                    return true;
                }

                int currentX = startX;

                for (int i = 0; i < columns.Count; i++)
                {
                    Rectangle rect = new Rectangle(currentX, startY, columnWidths[i], rowHeight);

                    // رسم خلفية الصف (متناوب)
                    e.Graphics.FillRectangle(currentRow % 2 == 0 ? Brushes.White : Brushes.LightGray, rect);
                    e.Graphics.DrawRectangle(Pens.LightGray, rect);

                    // رسم نص الخلية مع منع التداخل
                    string cellText = row.Cells[columns[i].Name].Value?.ToString() ?? "";

                    // قطع النص إذا كان أطول من عرض العمود
                    string displayText = TruncateText(e.Graphics, cellText, dataFont, columnWidths[i] - 15);

                    // تحديد منطقة القطع لمنع تجاوز النص حدود العمود
                    Rectangle clipRect = new Rectangle(rect.X + 2, rect.Y + 2, rect.Width - 4, rect.Height - 4);
                    e.Graphics.SetClip(clipRect);

                    // رسم النص مع محاذاة مناسبة
                    float textX = rect.X + 5; // مسافة من اليسار
                    float textY = rect.Y + (rect.Height - e.Graphics.MeasureString(displayText, dataFont).Height) / 2;

                    e.Graphics.DrawString(displayText, dataFont, Brushes.Black, textX, textY);
                    e.Graphics.ResetClip();

                    currentX += columnWidths[i];
                }

                startY += rowHeight;
                currentRow++;
            }

            return false;
        }

        private string TruncateText(Graphics graphics, string text, Font font, int maxWidth)
        {
            if (string.IsNullOrEmpty(text))
                return text;

            // قياس عرض النص الكامل
            SizeF textSize = graphics.MeasureString(text, font);

            if (textSize.Width <= maxWidth)
                return text;

            // إذا كان النص طويلاً، قم بقطعه وإضافة "..."
            string ellipsis = "...";
            SizeF ellipsisSize = graphics.MeasureString(ellipsis, font);
            int availableWidth = maxWidth - (int)ellipsisSize.Width;

            if (availableWidth <= 0)
                return ellipsis;

            // قطع النص تدريجياً حتى يصبح مناسباً
            for (int i = text.Length - 1; i > 0; i--)
            {
                string truncated = text.Substring(0, i);
                SizeF truncatedSize = graphics.MeasureString(truncated, font);

                if (truncatedSize.Width <= availableWidth)
                {
                    return truncated + ellipsis;
                }
            }

            return ellipsis;
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            currentRow = 0;
            currentPage = 1;
            printPreviewDialog.Document = printDocument;
            printPreviewDialog.ShowDialog();
        }
    }
}