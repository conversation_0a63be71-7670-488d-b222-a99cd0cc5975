-- إصلاح قاعدة البيانات - Database Fix
-- تشغيل هذا الملف في SQL Server Management Studio

-- إنشاء قاعدة البيانات إذا لم تكن موجودة
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'UnifiedPharmacy')
BEGIN
    CREATE DATABASE UnifiedPharmacy;
    PRINT 'تم إنشاء قاعدة البيانات UnifiedPharmacy';
END
ELSE
BEGIN
    PRINT 'قاعدة البيانات UnifiedPharmacy موجودة بالفعل';
END
GO

USE UnifiedPharmacy;
GO

-- إنشاء جدول الصيدليات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies' AND xtype='U')
BEGIN
    CREATE TABLE pharmacies (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyCode VARCHAR(20) UNIQUE NOT NULL,
        pharmacyName NVARCHAR(250) NOT NULL,
        ownerName NVARCHAR(250) NOT NULL,
        licenseNumber VARCHAR(100) NOT NULL,
        address NVARCHAR(500) NOT NULL,
        city NVARCHAR(100) NOT NULL,
        region NVARCHAR(100) NOT NULL,
        phone VARCHAR(20) NOT NULL,
        email VARCHAR(250) NOT NULL,
        isActive BIT DEFAULT 1,
        registrationDate DATETIME DEFAULT GETDATE()
    );
    PRINT 'تم إنشاء جدول pharmacies';
END

-- إنشاء جدول المستخدمين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
BEGIN
    CREATE TABLE users (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyId INT NOT NULL,
        userRole VARCHAR(50) NOT NULL,
        name NVARCHAR(250) NOT NULL,
        dob VARCHAR(250) NOT NULL,
        mobile BIGINT NOT NULL,
        email VARCHAR(250) NOT NULL,
        username VARCHAR(250) NOT NULL,
        pass VARCHAR(250) NOT NULL,
        isActive BIT DEFAULT 1,
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
    );
    PRINT 'تم إنشاء جدول users';
END

-- إنشاء جدول جلسات الموظفين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='employee_sessions' AND xtype='U')
BEGIN
    CREATE TABLE employee_sessions (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyId INT NOT NULL,
        username VARCHAR(250),
        employeeName NVARCHAR(250),
        loginTime DATETIME,
        logoutTime DATETIME NULL,
        sessionDate DATE,
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
    );
    PRINT 'تم إنشاء جدول employee_sessions';
END

-- إضافة صيدلية افتراضية
IF NOT EXISTS (SELECT * FROM pharmacies WHERE pharmacyCode = 'MAIN001')
BEGIN
    INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, licenseNumber, address, city, region, phone, email)
    VALUES ('MAIN001', N'الصيدلية الرئيسية', N'مدير النظام', 'LIC001', N'العنوان الرئيسي', N'المدينة', N'المنطقة', '**********', '<EMAIL>');
    PRINT 'تم إضافة الصيدلية الافتراضية';
END

-- إضافة مستخدمين افتراضيين
IF NOT EXISTS (SELECT * FROM users WHERE username = 'admin')
BEGIN
    INSERT INTO users (pharmacyId, userRole, name, dob, mobile, email, username, pass)
    VALUES (1, 'Administrator', N'مدير النظام', '1980-01-01', **********, '<EMAIL>', 'admin', 'admin123');
    PRINT 'تم إضافة المدير الافتراضي';
END

IF NOT EXISTS (SELECT * FROM users WHERE username = 'employee')
BEGIN
    INSERT INTO users (pharmacyId, userRole, name, dob, mobile, email, username, pass)
    VALUES (1, 'Employee', N'موظف الصيدلية', '1990-01-01', **********, '<EMAIL>', 'employee', 'emp123');
    PRINT 'تم إضافة الموظف الافتراضي';
END

-- التحقق من البيانات
PRINT '';
PRINT '========================================';
PRINT 'تم إعداد قاعدة البيانات بنجاح!';
PRINT '========================================';
PRINT '';

SELECT 'الصيدليات' as النوع, COUNT(*) as العدد FROM pharmacies WHERE isActive = 1
UNION ALL
SELECT 'المستخدمين' as النوع, COUNT(*) as العدد FROM users WHERE isActive = 1
UNION ALL
SELECT 'الجلسات' as النوع, COUNT(*) as العدد FROM employee_sessions;

PRINT '';
PRINT 'حسابات تسجيل الدخول المتاحة:';
PRINT 'المدير: admin / admin123';
PRINT 'الموظف: employee / emp123';
PRINT '';
PRINT 'يمكنك الآن تشغيل البرنامج!';
