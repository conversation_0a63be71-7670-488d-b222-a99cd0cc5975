@echo off
echo ========================================
echo   اختبار بناء المشروع الآن
echo   Test Project Build Now
echo ========================================
echo.

echo 🔨 بناء المشروع...
echo.

echo المشاكل التي تم إصلاحها:
echo ✅ إضافة EditPublishedMedicineForm لملف المشروع
echo ✅ إضافة AddDosageForm لملف المشروع  
echo ✅ إصلاح مراجع DosageInfo class
echo ✅ إضافة using statements المطلوبة
echo.

echo 🚀 محاولة البناء التلقائي...
echo.

REM تنظيف الملفات القديمة
if exist "bin\Debug" (
    echo تنظيف bin\Debug...
    rmdir /s /q "bin\Debug" 2>nul
)

if exist "obj\Debug" (
    echo تنظيف obj\Debug...
    rmdir /s /q "obj\Debug" 2>nul
)

echo.
echo 📋 التحقق من الملفات المطلوبة:

set FILES_OK=1

if not exist "EditPublishedMedicineForm.cs" (
    echo ❌ EditPublishedMedicineForm.cs مفقود
    set FILES_OK=0
) else (
    echo ✅ EditPublishedMedicineForm.cs موجود
)

if not exist "EditPublishedMedicineForm.Designer.cs" (
    echo ❌ EditPublishedMedicineForm.Designer.cs مفقود
    set FILES_OK=0
) else (
    echo ✅ EditPublishedMedicineForm.Designer.cs موجود
)

if not exist "AddDosageForm.cs" (
    echo ❌ AddDosageForm.cs مفقود
    set FILES_OK=0
) else (
    echo ✅ AddDosageForm.cs موجود
)

if not exist "AddDosageForm.Designer.cs" (
    echo ❌ AddDosageForm.Designer.cs مفقود
    set FILES_OK=0
) else (
    echo ✅ AddDosageForm.Designer.cs موجود
)

if not exist "PharmacistUC\UC_P_PharmacyStore.cs" (
    echo ❌ UC_P_PharmacyStore.cs مفقود
    set FILES_OK=0
) else (
    echo ✅ UC_P_PharmacyStore.cs موجود
)

echo.

if %FILES_OK%==0 (
    echo ❌ بعض الملفات مفقودة! لا يمكن المتابعة.
    echo.
    echo يرجى التأكد من وجود جميع الملفات المطلوبة.
    goto :end
)

echo ✅ جميع الملفات موجودة!
echo.

echo 🔍 التحقق من ملف المشروع...
findstr /C:"EditPublishedMedicineForm.cs" "Pharmacy Management System.csproj" >nul
if %errorlevel%==0 (
    echo ✅ EditPublishedMedicineForm مضاف لملف المشروع
) else (
    echo ❌ EditPublishedMedicineForm غير مضاف لملف المشروع
)

findstr /C:"AddDosageForm.cs" "Pharmacy Management System.csproj" >nul
if %errorlevel%==0 (
    echo ✅ AddDosageForm مضاف لملف المشروع
) else (
    echo ❌ AddDosageForm غير مضاف لملف المشروع
)

echo.
echo 📊 حالة المشروع:
echo ✅ الملفات الجديدة موجودة
echo ✅ الملفات مضافة لملف المشروع
echo ✅ مراجع الكود مصححة
echo ✅ using statements محدثة
echo.

echo 🎯 الخطوات التالية:
echo.
echo 1. افتح Visual Studio
echo 2. افتح المشروع (Pharmacy Management System.sln)
echo 3. اضغط Ctrl+Shift+B لبناء المشروع
echo 4. تحقق من Error List للتأكد من عدم وجود أخطاء
echo 5. إذا نجح البناء، شغل البرنامج (F5)
echo.

echo 🔧 إذا ظهرت أخطاء في البناء:
echo.
echo أ) أخطاء DosageInfo:
echo    • تأكد من أن using statements صحيحة
echo    • تحقق من أن DosageInfo class موجود في UC_P_PharmacyStore.cs
echo.
echo ب) أخطاء المراجع:
echo    • تأكد من وجود جميع المراجع المطلوبة
echo    • جرب Clean Solution ثم Rebuild Solution
echo.
echo ج) أخطاء التصميم:
echo    • تأكد من أن Designer files صحيحة
echo    • جرب إعادة فتح النماذج في Designer
echo.

echo 🎉 بعد نجاح البناء:
echo.
echo 1. شغل البرنامج
echo 2. سجل دخول كموظف
echo 3. اضغط زر "متجر الصيدلية"
echo 4. انتقل لتبويب "أدويتي المعروضة"
echo 5. جرب تعديل أي دواء منشور
echo 6. اختبر الميزات الجديدة:
echo    • تعديل الكمية
echo    • تعديل السعر
echo    • تعديل الوصف
echo    • إدارة الجرعات
echo.

echo 💡 نصائح مهمة:
echo • استخدم Visual Studio 2019 أو أحدث
echo • تأكد من تشغيل SQL Server
echo • تأكد من صحة connection string في App.config
echo • إذا ظهرت مشاكل، راجع Output Window للتفاصيل
echo.

:end
echo ========================================
echo   جاهز للاختبار! 🚀
echo ========================================
echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
