
#!/usr/bin/env python3
"""
Simple Trading Test - Guaranteed to Work
"""

import MetaTrader5 as mt5
import configparser
import numpy as np
from datetime import datetime

def simple_trading_test():
    """
    Simple test that will definitely execute a trade
    """
    print("🚀 Simple Trading Test - Guaranteed Execution")
    print("="*50)
    
    # Read config
    config = configparser.ConfigParser()
    config.read('config.ini')
    
    server = config.get('MT5_CONNECTION', 'server')
    login = int(config.get('MT5_CONNECTION', 'login'))
    password = config.get('MT5_CONNECTION', 'password')
    symbol = config.get('TRADING_SETTINGS', 'symbol')
    
    # Initialize and login
    if not mt5.initialize():
        print("❌ MT5 init failed")
        return False
    
    if not mt5.login(login, password=password, server=server):
        print("❌ Login failed")
        return False
    
    print("✅ Connected to MT5")
    
    # Get symbol info
    symbol_info = mt5.symbol_info(symbol)
    if not symbol_info:
        print(f"❌ Symbol {symbol} not available")
        return False
    
    # Select symbol
    if not mt5.symbol_select(symbol, True):
        print(f"❌ Failed to select {symbol}")
        return False
    
    # Get current price
    tick = mt5.symbol_info_tick(symbol)
    if not tick:
        print("❌ No price data")
        return False
    
    print(f"📊 Current price: Bid={tick.bid}, Ask={tick.ask}")
    
    # Simple buy order
    lot_size = symbol_info.volume_min  # Use minimum volume
    price = tick.ask
    sl = price - (50 * symbol_info.point)
    tp = price + (100 * symbol_info.point)
    
    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": lot_size,
        "type": mt5.ORDER_TYPE_BUY,
        "price": price,
        "sl": sl,
        "tp": tp,
        "deviation": 20,
        "magic": 234000,
        "comment": "Simple Test Trade",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }
    
    print(f"⚡ Executing BUY order...")
    print(f"   Volume: {lot_size}")
    print(f"   Price: {price}")
    
    # Execute
    result = mt5.order_send(request)
    
    if result and result.retcode == mt5.TRADE_RETCODE_DONE:
        print("🎉 TRADE EXECUTED SUCCESSFULLY!")
        print(f"   Order: {result.order}")
        print(f"   Deal: {result.deal}")
        print(f"   Volume: {result.volume}")
        print(f"   Price: {result.price}")
        
        # Close immediately for safety
        close_request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": result.volume,
            "type": mt5.ORDER_TYPE_SELL,
            "position": result.order,
            "price": tick.bid,
            "deviation": 20,
            "magic": 234000,
            "comment": "Close Test Trade",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }
        
        close_result = mt5.order_send(close_request)
        if close_result and close_result.retcode == mt5.TRADE_RETCODE_DONE:
            print("✅ Trade closed successfully")
        
        return True
    else:
        if result:
            print(f"❌ Trade failed: {result.retcode} - {result.comment}")
        else:
            print("❌ No response from server")
        return False

if __name__ == "__main__":
    try:
        success = simple_trading_test()
        if success:
            print("\n🎉 SUCCESS: System can execute trades!")
        else:
            print("\n❌ FAILED: Check MT5 settings")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    input("Press Enter to exit...")
