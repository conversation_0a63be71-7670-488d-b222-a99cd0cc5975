-- تحديث اسم جدول network_medicines إلى networkmedicines
-- Update table name from network_medicines to networkmedicines

USE PharmacyNetworkOnline;
GO

PRINT '========================================';
PRINT 'تحديث اسم جدول الأدوية الشبكة';
PRINT '========================================';

-- التحقق من وجود الجدول القديم
IF EXISTS (SELECT * FROM sysobjects WHERE name='network_medicines' AND xtype='U')
BEGIN
    PRINT 'تم العثور على الجدول القديم network_medicines';
    
    -- إنشاء الجدول الجديد بالاسم المحدث
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='networkmedicines' AND xtype='U')
    BEGIN
        PRINT 'إنشاء الجدول الجديد networkmedicines...';
        
        CREATE TABLE networkmedicines (
            id INT IDENTITY(1,1) PRIMARY KEY,
            pharmacyId INT NOT NULL,
            localMedicineId VARCHAR(250) NOT NULL,
            medicineName NVARCHAR(250) NOT NULL,
            genericName NVARCHAR(250) NULL,
            brandName NVARCHAR(250) NULL,
            manufacturer NVARCHAR(250) NOT NULL,
            category NVARCHAR(100) NULL,
            dosageForm VARCHAR(100) NULL,
            strength VARCHAR(100) NULL,
            availableQuantity INT NOT NULL,
            unitPrice DECIMAL(10, 2) NOT NULL,
            wholesalePrice DECIMAL(10, 2) NULL,
            manufacturingDate DATE NULL,
            expiryDate DATE NOT NULL,
            batchNumber VARCHAR(100) NULL,
            location NVARCHAR(100) NULL,
            isAvailableForSale BIT DEFAULT 1,
            minOrderQuantity INT DEFAULT 1,
            maxOrderQuantity INT NULL,
            description NVARCHAR(1000) NULL,
            sideEffects NVARCHAR(1000) NULL,
            contraindications NVARCHAR(1000) NULL,
            storageConditions NVARCHAR(500) NULL,
            imageUrl VARCHAR(500) NULL,
            createdAt DATETIME DEFAULT GETDATE(),
            updatedAt DATETIME DEFAULT GETDATE(),
            FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
        );
        
        PRINT 'تم إنشاء الجدول الجديد networkmedicines';
        
        -- نسخ البيانات من الجدول القديم إلى الجديد
        INSERT INTO networkmedicines (
            pharmacyId, localMedicineId, medicineName, genericName, brandName,
            manufacturer, category, dosageForm, strength, availableQuantity,
            unitPrice, wholesalePrice, manufacturingDate, expiryDate, batchNumber,
            location, isAvailableForSale, minOrderQuantity, maxOrderQuantity,
            description, sideEffects, contraindications, storageConditions,
            imageUrl, createdAt, updatedAt
        )
        SELECT 
            pharmacyId, localMedicineId, medicineName, genericName, brandName,
            manufacturer, category, dosageForm, strength, availableQuantity,
            unitPrice, wholesalePrice, manufacturingDate, expiryDate, batchNumber,
            location, isAvailableForSale, minOrderQuantity, maxOrderQuantity,
            description, sideEffects, contraindications, storageConditions,
            imageUrl, createdAt, updatedAt
        FROM network_medicines;
        
        PRINT 'تم نسخ البيانات من الجدول القديم';
        
        -- إنشاء الفهارس للجدول الجديد
        CREATE INDEX IX_networkmedicines_pharmacyId ON networkmedicines(pharmacyId);
        CREATE INDEX IX_networkmedicines_medicineName ON networkmedicines(medicineName);
        CREATE INDEX IX_networkmedicines_isAvailableForSale ON networkmedicines(isAvailableForSale);
        CREATE INDEX IX_networkmedicines_expiryDate ON networkmedicines(expiryDate);
        CREATE INDEX IX_networkmedicines_category ON networkmedicines(category);
        
        PRINT 'تم إنشاء الفهارس للجدول الجديد';
        
        -- حذف الجدول القديم (اختياري - يمكنك تعطيل هذا السطر للاحتفاظ بنسخة احتياطية)
        -- DROP TABLE network_medicines;
        -- PRINT 'تم حذف الجدول القديم';
        
    END
    ELSE
    BEGIN
        PRINT 'الجدول الجديد networkmedicines موجود بالفعل';
    END
END
ELSE
BEGIN
    PRINT 'الجدول القديم network_medicines غير موجود';
    
    -- إنشاء الجدول الجديد مباشرة
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='networkmedicines' AND xtype='U')
    BEGIN
        PRINT 'إنشاء الجدول الجديد networkmedicines...';
        
        CREATE TABLE networkmedicines (
            id INT IDENTITY(1,1) PRIMARY KEY,
            pharmacyId INT NOT NULL,
            localMedicineId VARCHAR(250) NOT NULL,
            medicineName NVARCHAR(250) NOT NULL,
            genericName NVARCHAR(250) NULL,
            brandName NVARCHAR(250) NULL,
            manufacturer NVARCHAR(250) NOT NULL,
            category NVARCHAR(100) NULL,
            dosageForm VARCHAR(100) NULL,
            strength VARCHAR(100) NULL,
            availableQuantity INT NOT NULL,
            unitPrice DECIMAL(10, 2) NOT NULL,
            wholesalePrice DECIMAL(10, 2) NULL,
            manufacturingDate DATE NULL,
            expiryDate DATE NOT NULL,
            batchNumber VARCHAR(100) NULL,
            location NVARCHAR(100) NULL,
            isAvailableForSale BIT DEFAULT 1,
            minOrderQuantity INT DEFAULT 1,
            maxOrderQuantity INT NULL,
            description NVARCHAR(1000) NULL,
            sideEffects NVARCHAR(1000) NULL,
            contraindications NVARCHAR(1000) NULL,
            storageConditions NVARCHAR(500) NULL,
            imageUrl VARCHAR(500) NULL,
            createdAt DATETIME DEFAULT GETDATE(),
            updatedAt DATETIME DEFAULT GETDATE(),
            FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
        );
        
        -- إنشاء الفهارس
        CREATE INDEX IX_networkmedicines_pharmacyId ON networkmedicines(pharmacyId);
        CREATE INDEX IX_networkmedicines_medicineName ON networkmedicines(medicineName);
        CREATE INDEX IX_networkmedicines_isAvailableForSale ON networkmedicines(isAvailableForSale);
        CREATE INDEX IX_networkmedicines_expiryDate ON networkmedicines(expiryDate);
        CREATE INDEX IX_networkmedicines_category ON networkmedicines(category);
        
        PRINT 'تم إنشاء الجدول والفهارس بنجاح';
    END
    ELSE
    BEGIN
        PRINT 'الجدول networkmedicines موجود بالفعل';
    END
END

PRINT '========================================';
PRINT 'انتهى تحديث اسم الجدول';
PRINT '========================================';

-- عرض معلومات الجدول الجديد
SELECT 
    'networkmedicines' as TableName, 
    COUNT(*) as RecordCount 
FROM networkmedicines;

PRINT 'تم التحديث بنجاح!';
