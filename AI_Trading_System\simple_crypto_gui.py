#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from datetime import datetime
import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from mt5_crypto_trading_system import MT5CryptoTradingSystem
except ImportError as e:
    print(f"خطأ في استيراد النظام: {e}")
    sys.exit(1)

class SimpleCryptoGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام تداول العملات الرقمية - MetaTrader 5")
        self.root.geometry("1000x700")
        self.root.configure(bg='#2d2d2d')
        
        # النظام
        self.trading_system = None
        self.is_connected = False
        self.is_trading = False
        
        # إعداد النمط
        self.setup_style()
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # بدء تحديث البيانات
        self.update_data()
        
    def setup_style(self):
        """إعداد النمط"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # ألوان داكنة
        style.configure('TFrame', background='#2d2d2d')
        style.configure('TLabel', background='#2d2d2d', foreground='white', font=('Arial', 10))
        style.configure('TButton', background='#404040', foreground='white', font=('Arial', 9))
        style.configure('TEntry', background='#404040', foreground='white')
        style.configure('TCombobox', background='#404040', foreground='white')
        style.configure('TLabelFrame', background='#2d2d2d', foreground='white')
        
        # أزرار ملونة
        style.configure('Success.TButton', background='#28a745', foreground='white')
        style.configure('Danger.TButton', background='#dc3545', foreground='white')
        style.configure('Warning.TButton', background='#ffc107', foreground='black')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # شريط العنوان
        title_label = ttk.Label(main_frame, text="نظام تداول العملات الرقمية الذكي", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # لوحة التحكم
        self.create_control_panel(main_frame)
        
        # لوحة المعلومات
        self.create_info_panel(main_frame)
        
        # لوحة السجل
        self.create_log_panel(main_frame)
        
    def create_control_panel(self, parent):
        """إنشاء لوحة التحكم"""
        control_frame = ttk.LabelFrame(parent, text="لوحة التحكم")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # الصف الأول - الاتصال
        row1 = ttk.Frame(control_frame)
        row1.pack(fill=tk.X, padx=10, pady=10)
        
        self.connect_btn = ttk.Button(row1, text="اتصال", command=self.connect_mt5,
                                     style='Success.TButton')
        self.connect_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.disconnect_btn = ttk.Button(row1, text="قطع الاتصال", command=self.disconnect_mt5,
                                        style='Danger.TButton', state='disabled')
        self.disconnect_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # حالة الاتصال
        self.status_label = ttk.Label(row1, text="غير متصل", foreground='red')
        self.status_label.pack(side=tk.LEFT, padx=(20, 0))
        
        # الصف الثاني - التداول
        row2 = ttk.Frame(control_frame)
        row2.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # اختيار العملة
        ttk.Label(row2, text="العملة:").pack(side=tk.LEFT)
        self.symbol_var = tk.StringVar(value="EURUSD")
        self.symbol_combo = ttk.Combobox(row2, textvariable=self.symbol_var, width=15)
        self.symbol_combo.pack(side=tk.LEFT, padx=(5, 20))
        
        # وضع التداول
        self.demo_var = tk.BooleanVar(value=True)
        self.demo_check = ttk.Checkbutton(row2, text="وضع تجريبي", variable=self.demo_var)
        self.demo_check.pack(side=tk.LEFT, padx=(0, 20))
        
        # أزرار التداول
        self.start_btn = ttk.Button(row2, text="بدء التداول", command=self.start_trading,
                                   style='Success.TButton', state='disabled')
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_btn = ttk.Button(row2, text="إيقاف التداول", command=self.stop_trading,
                                  style='Danger.TButton', state='disabled')
        self.stop_btn.pack(side=tk.LEFT)
        
    def create_info_panel(self, parent):
        """إنشاء لوحة المعلومات"""
        info_frame = ttk.LabelFrame(parent, text="معلومات الحساب")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # شبكة المعلومات
        grid = ttk.Frame(info_frame)
        grid.pack(fill=tk.X, padx=10, pady=10)
        
        # الرصيد
        ttk.Label(grid, text="الرصيد:").grid(row=0, column=0, sticky='w', padx=(0, 10))
        self.balance_label = ttk.Label(grid, text="$0.00", foreground='#28a745')
        self.balance_label.grid(row=0, column=1, sticky='w', padx=(0, 30))
        
        # الربح/الخسارة
        ttk.Label(grid, text="الربح/الخسارة:").grid(row=0, column=2, sticky='w', padx=(0, 10))
        self.profit_label = ttk.Label(grid, text="$0.00")
        self.profit_label.grid(row=0, column=3, sticky='w', padx=(0, 30))
        
        # عدد الصفقات
        ttk.Label(grid, text="الصفقات المفتوحة:").grid(row=1, column=0, sticky='w', padx=(0, 10))
        self.positions_label = ttk.Label(grid, text="0")
        self.positions_label.grid(row=1, column=1, sticky='w', padx=(0, 30))
        
        # آخر تحليل
        ttk.Label(grid, text="آخر تحليل:").grid(row=1, column=2, sticky='w', padx=(0, 10))
        self.analysis_label = ttk.Label(grid, text="لا يوجد")
        self.analysis_label.grid(row=1, column=3, sticky='w')
        
    def create_log_panel(self, parent):
        """إنشاء لوحة السجل"""
        log_frame = ttk.LabelFrame(parent, text="سجل الأحداث")
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, 
                                                 bg='#1e1e1e', fg='white', 
                                                 font=('Consolas', 9))
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
    def log_message(self, message):
        """إضافة رسالة للسجل"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, full_message)
        self.log_text.see(tk.END)
        
    def connect_mt5(self):
        """الاتصال بـ MT5"""
        try:
            self.log_message("محاولة الاتصال بـ MetaTrader 5...")
            self.trading_system = MT5CryptoTradingSystem()
            
            if self.trading_system.connect_mt5():
                self.is_connected = True
                self.status_label.config(text="متصل", foreground='green')
                self.connect_btn.config(state='disabled')
                self.disconnect_btn.config(state='normal')
                self.start_btn.config(state='normal')
                
                # تحديث قائمة العملات
                symbols = self.trading_system.crypto_symbols
                self.symbol_combo['values'] = symbols
                if symbols:
                    self.symbol_combo.set(symbols[0])
                
                self.log_message("تم الاتصال بنجاح!")
                self.update_account_info()
            else:
                self.log_message("فشل في الاتصال!")
                messagebox.showerror("خطأ", "فشل في الاتصال بـ MetaTrader 5")
                
        except Exception as e:
            self.log_message(f"خطأ في الاتصال: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في الاتصال: {str(e)}")
            
    def disconnect_mt5(self):
        """قطع الاتصال"""
        if self.trading_system:
            self.trading_system.disconnect_mt5()
            
        self.is_connected = False
        self.status_label.config(text="غير متصل", foreground='red')
        self.connect_btn.config(state='normal')
        self.disconnect_btn.config(state='disabled')
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='disabled')
        
        self.log_message("تم قطع الاتصال")
        
    def start_trading(self):
        """بدء التداول"""
        if not self.is_connected:
            messagebox.showwarning("تحذير", "يجب الاتصال أولاً!")
            return
            
        self.is_trading = True
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        
        # تحديث وضع التداول
        self.trading_system.demo_mode = self.demo_var.get()
        self.trading_system.current_symbol = self.symbol_var.get()
        
        mode_text = "تجريبي" if self.demo_var.get() else "حقيقي"
        self.log_message(f"بدء التداول - الوضع: {mode_text} - العملة: {self.symbol_var.get()}")
        
        # بدء التداول في خيط منفصل
        threading.Thread(target=self.trading_loop, daemon=True).start()
        
    def stop_trading(self):
        """إيقاف التداول"""
        self.is_trading = False
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.log_message("تم إيقاف التداول")
        
    def trading_loop(self):
        """حلقة التداول"""
        while self.is_trading and self.is_connected:
            try:
                symbol = self.symbol_var.get()
                self.log_message(f"تحليل {symbol}...")
                
                # تحليل السوق
                analysis = self.trading_system.analyze_crypto_market(symbol)
                
                if analysis and 'decision' in analysis:
                    decision = analysis['decision']
                    confidence = analysis.get('confidence', 0)
                    
                    self.log_message(f"القرار: {decision} - الثقة: {confidence:.1f}%")
                    
                    # تحديث واجهة التحليل
                    self.root.after(0, lambda: self.analysis_label.config(
                        text=f"{decision} ({confidence:.1f}%)"))
                    
                    # تنفيذ التداول إذا كانت الثقة عالية
                    if confidence > 60:
                        if self.trading_system.execute_crypto_trade(analysis):
                            self.log_message("تم تنفيذ الصفقة بنجاح!")
                        else:
                            self.log_message("فشل في تنفيذ الصفقة")
                
                # تحديث معلومات الحساب
                self.root.after(0, self.update_account_info)
                
                # انتظار قبل التحليل التالي
                time.sleep(30)
                
            except Exception as e:
                self.log_message(f"خطأ في التداول: {str(e)}")
                time.sleep(10)
                
    def update_account_info(self):
        """تحديث معلومات الحساب"""
        if not self.is_connected or not self.trading_system:
            return

        try:
            # استخدام دالة get_account_summary الموجودة
            account_summary = self.trading_system.get_account_summary()

            if 'error' not in account_summary:
                balance = account_summary.get('balance', 0)
                equity = account_summary.get('equity', 0)
                profit = equity - balance  # حساب الربح/الخسارة

                self.balance_label.config(text=f"${balance:.2f}")

                # تلوين الربح/الخسارة
                if profit > 0:
                    self.profit_label.config(text=f"+${profit:.2f}", foreground='#28a745')
                elif profit < 0:
                    self.profit_label.config(text=f"${profit:.2f}", foreground='#dc3545')
                else:
                    self.profit_label.config(text="$0.00", foreground='white')

                # عدد الصفقات
                open_positions = account_summary.get('open_positions', 0)
                self.positions_label.config(text=str(open_positions))

            else:
                self.log_message(f"خطأ في معلومات الحساب: {account_summary['error']}")

        except Exception as e:
            self.log_message(f"خطأ في تحديث المعلومات: {str(e)}")
            
    def update_data(self):
        """تحديث البيانات دورياً"""
        if self.is_connected:
            self.update_account_info()
            
        # جدولة التحديث التالي
        self.root.after(5000, self.update_data)
        
    def run(self):
        """تشغيل التطبيق"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log_message("تم إيقاف التطبيق")
        finally:
            if self.trading_system:
                self.trading_system.disconnect_mt5()

if __name__ == "__main__":
    try:
        app = SimpleCryptoGUI()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")
