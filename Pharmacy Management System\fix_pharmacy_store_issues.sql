-- إصلاح مشاكل متجر الأدوية
USE UnifiedPharmacy;

PRINT '=== إصلاح مشاكل متجر الأدوية ===';

-- 1. التحقق من وجود الجداول المطلوبة
PRINT '1. فحص الجداول المطلوبة...';

-- فحص جدول purchase_requests
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'purchase_requests')
BEGIN
    PRINT '✅ جدول purchase_requests موجود';
    SELECT COUNT(*) as 'عدد الطلبات' FROM purchase_requests;
END
ELSE
BEGIN
    PRINT '❌ جدول purchase_requests غير موجود - إنشاء الجدول...';
    CREATE TABLE purchase_requests (
        id int IDENTITY(1,1) PRIMARY KEY,
        buyer_pharmacy_id int NOT NULL,
        seller_pharmacy_id int NOT NULL,
        medicine_id int NOT NULL,
        requested_quantity int NOT NULL,
        offered_price decimal(10,2) DEFAULT 0,
        request_message nvarchar(500),
        status nvarchar(50) DEFAULT 'pending',
        request_date datetime DEFAULT GETDATE(),
        response_date datetime NULL,
        response_message nvarchar(500) NULL,
        FOREIGN KEY (buyer_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (seller_pharmacy_id) REFERENCES pharmacies(id),
        FOREIGN KEY (medicine_id) REFERENCES medic(id)
    );
    PRINT '✅ تم إنشاء جدول purchase_requests';
END

-- 2. التحقق من وجود بيانات تجريبية
PRINT '';
PRINT '2. فحص البيانات التجريبية...';

DECLARE @requestCount int;
SELECT @requestCount = COUNT(*) FROM purchase_requests;

IF @requestCount = 0
BEGIN
    PRINT 'إضافة بيانات تجريبية لطلبات الأدوية...';
    
    -- إضافة طلبات تجريبية
    INSERT INTO purchase_requests (buyer_pharmacy_id, seller_pharmacy_id, medicine_id, requested_quantity, offered_price, request_message, status)
    SELECT 
        2 as buyer_pharmacy_id,  -- الصيدلية الثانية تطلب
        1 as seller_pharmacy_id, -- من الصيدلية الأولى
        m.id as medicine_id,
        5 as requested_quantity,
        m.perUnit * 0.9 as offered_price, -- خصم 10%
        N'نحتاج هذا الدواء بشكل عاجل' as request_message,
        'pending' as status
    FROM medic m 
    WHERE m.pharmacy_id = 1 AND m.quantity > 5
    AND m.id IN (SELECT TOP 3 id FROM medic WHERE pharmacy_id = 1 ORDER BY id);
    
    PRINT '✅ تم إضافة بيانات تجريبية';
END
ELSE
BEGIN
    PRINT '✅ البيانات التجريبية موجودة';
END

-- 3. التحقق من صحة العلاقات
PRINT '';
PRINT '3. فحص العلاقات بين الجداول...';

-- فحص العلاقة مع جدول pharmacies
SELECT 
    'طلبات بدون صيدلية مشترية' as المشكلة,
    COUNT(*) as العدد
FROM purchase_requests pr
LEFT JOIN pharmacies p1 ON pr.buyer_pharmacy_id = p1.id
WHERE p1.id IS NULL

UNION ALL

SELECT 
    'طلبات بدون صيدلية بائعة' as المشكلة,
    COUNT(*) as العدد
FROM purchase_requests pr
LEFT JOIN pharmacies p2 ON pr.seller_pharmacy_id = p2.id
WHERE p2.id IS NULL

UNION ALL

SELECT 
    'طلبات بدون دواء' as المشكلة,
    COUNT(*) as العدد
FROM purchase_requests pr
LEFT JOIN medic m ON pr.medicine_id = m.id
WHERE m.id IS NULL;

-- 4. عرض ملخص البيانات
PRINT '';
PRINT '4. ملخص البيانات الحالية...';

SELECT 
    p_seller.pharmacyName as 'الصيدلية البائعة',
    COUNT(*) as 'عدد الطلبات'
FROM purchase_requests pr
INNER JOIN pharmacies p_seller ON pr.seller_pharmacy_id = p_seller.id
GROUP BY p_seller.id, p_seller.pharmacyName
ORDER BY COUNT(*) DESC;

-- 5. اختبار الاستعلام المستخدم في الكود
PRINT '';
PRINT '5. اختبار الاستعلام المستخدم في الكود...';

SELECT
    pr.id, 
    pr.requested_quantity as requestedQuantity,
    pr.offered_price as offeredPrice, 
    pr.request_date as requestDate,
    pr.status, 
    ISNULL(pr.response_message, '') as responseMessage,
    ISNULL(pr.request_message, '') as requestMessage,
    m.mname as medicineName, 
    ISNULL(m.mnumber, '') as medicineNumber,
    m.perUnit as originalPrice,
    p_buyer.pharmacyName as buyerPharmacyName,
    ISNULL(p_buyer.phone, '') as buyerPhone,
    ISNULL(p_buyer.city, '') as buyerCity
FROM purchase_requests pr
INNER JOIN medic m ON pr.medicine_id = m.id
INNER JOIN pharmacies p_buyer ON pr.buyer_pharmacy_id = p_buyer.id
WHERE pr.seller_pharmacy_id = 1
ORDER BY pr.request_date DESC;

PRINT '';
PRINT '=== انتهى الإصلاح ===';
PRINT 'يمكنك الآن تشغيل البرنامج واختبار صفحة متجر الأدوية';
