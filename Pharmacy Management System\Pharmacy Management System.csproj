﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>Pharmacy_Management_System</RootNamespace>
    <AssemblyName>Pharmacy Management System</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <LangVersion>Default</LangVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>C:\Users\<USER>\Desktop\New folder %283%29\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>2</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>A290568CF4C6CE5170D80DCC425D5382844A8FE0</ManifestCertificateThumbprint>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>Pharmacy Management System_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>true</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>true</SignManifests>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Guna.UI2, Version=2.0.4.7, Culture=neutral, PublicKeyToken=8b9d14aa5142e261, processorArchitecture=MSIL">
      <HintPath>..\packages\Guna.UI2.WinForms.2.0.4.7\lib\net472\Guna.UI2.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Design" />
    <Reference Include="System.Management" />
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms.DataVisualization" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AdministratorUC\data.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="AdministratorUC\data.Designer.cs">
      <DependentUpon>data.cs</DependentUpon>
    </Compile>
    <Compile Include="AdministratorUC\UC_AddUser.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="AdministratorUC\UC_AddUser.Designer.cs">
      <DependentUpon>UC_AddUser.cs</DependentUpon>
    </Compile>
    <Compile Include="AdministratorUC\UC_Dashbord.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="AdministratorUC\UC_Dashbord.Designer.cs">
      <DependentUpon>UC_Dashbord.cs</DependentUpon>
    </Compile>
    <Compile Include="AdministratorUC\UC_SalesReport.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="AdministratorUC\UC_SalesReport.Designer.cs">
      <DependentUpon>UC_SalesReport.cs</DependentUpon>
    </Compile>
    <Compile Include="AdministratorUC\UC_EditUser.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="AdministratorUC\UC_EditUser.Designer.cs">
      <DependentUpon>UC_EditUser.cs</DependentUpon>
    </Compile>
    <Compile Include="AdministratorUC\UC_EmployeeSessions.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="AdministratorUC\UC_EmployeeSessions.Designer.cs">
      <DependentUpon>UC_EmployeeSessions.cs</DependentUpon>
    </Compile>
    <Compile Include="AdministratorUC\UC_PrintDesign.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="AdministratorUC\UC_PrintDesign.Designer.cs">
      <DependentUpon>UC_PrintDesign.cs</DependentUpon>
    </Compile>
    <Compile Include="Adminstrator.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Adminstrator.Designer.cs">
      <DependentUpon>Adminstrator.cs</DependentUpon>
    </Compile>
    <Compile Include="DGVPrinter.cs" />
    <Compile Include="ModernTheme.cs" />
    <Compile Include="NotificationManager.cs" />
    <Compile Include="Function.cs" />
    <Compile Include="LanguageManager.cs" />
    <Compile Include="UnifiedFunction.cs" />
    <Compile Include="UnifiedPharmacyFunction.cs" />
    <Compile Include="PharmacyNetworkLoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SessionManager.cs" />
    <Compile Include="CreateAccountForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CreateAccountForm.Designer.cs">
      <DependentUpon>CreateAccountForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Pharmacist.Designer.cs">
      <DependentUpon>Pharmacist.cs</DependentUpon>
    </Compile>
    <Compile Include="PharmacistUC\UC__P_SellMedicine.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="PharmacistUC\UC__P_SellMedicine.Designer.cs">
      <DependentUpon>UC__P_SellMedicine.cs</DependentUpon>
    </Compile>
    <Compile Include="PharmacistUC\UC_P_AddMedicine.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="PharmacistUC\UC_P_AddMedicine.Designer.cs">
      <DependentUpon>UC_P_AddMedicine.cs</DependentUpon>
    </Compile>
    <Compile Include="PharmacistUC\UC_P_Dashbord.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="PharmacistUC\UC_P_Dashbord.Designer.cs">
      <DependentUpon>UC_P_Dashbord.cs</DependentUpon>
    </Compile>
    <Compile Include="PharmacistUC\UC_P_MedicineValidityCheck.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="PharmacistUC\UC_P_MedicineValidityCheck.Designer.cs">
      <DependentUpon>UC_P_MedicineValidityCheck.cs</DependentUpon>
    </Compile>
    <Compile Include="PharmacistUC\UC_P_UpdateMedicine.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="PharmacistUC\UC_P_UpdateMedicine.Designer.cs">
      <DependentUpon>UC_P_UpdateMedicine.cs</DependentUpon>
    </Compile>
    <Compile Include="PharmacistUC\UC_P_ViewMedicines.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="PharmacistUC\UC_P_ViewMedicines.Designer.cs">
      <DependentUpon>UC_P_ViewMedicines.cs</DependentUpon>
    </Compile>
    <Compile Include="PharmacistUC\sell_user.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="PharmacistUC\sell_user.Designer.cs">
      <DependentUpon>sell_user.cs</DependentUpon>
    </Compile>
    <Compile Include="PharmacistUC\UC_P_PharmacyStore.cs" />
    <Compile Include="PharmacistUC\UC_P_PharmacyStore.Designer.cs">
      <DependentUpon>UC_P_PharmacyStore.cs</DependentUpon>
    </Compile>

    <Compile Include="PublishMedicineForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PublishMedicineForm.Designer.cs">
      <DependentUpon>PublishMedicineForm.cs</DependentUpon>
    </Compile>
    <Compile Include="RequestMedicineForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RequestMedicineForm.Designer.cs">
      <DependentUpon>RequestMedicineForm.cs</DependentUpon>
    </Compile>
    <Compile Include="OnlineNetworkManager.cs" />
    <Compile Include="CreateOrderForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CreateOrderForm.Designer.cs">
      <DependentUpon>CreateOrderForm.cs</DependentUpon>
    </Compile>
    <Compile Include="EditPublishedMedicineForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="EditPublishedMedicineForm.Designer.cs">
      <DependentUpon>EditPublishedMedicineForm.cs</DependentUpon>
    </Compile>
    <Compile Include="AddDosageForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AddDosageForm.Designer.cs">
      <DependentUpon>AddDosageForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="PharmacyCodeLoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PharmacyCodeLoginForm.Designer.cs">
      <DependentUpon>PharmacyCodeLoginForm.cs</DependentUpon>
    </Compile>
    <Compile Include="PharmacyChatForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PharmacyChatForm.Designer.cs">
      <DependentUpon>PharmacyChatForm.cs</DependentUpon>
    </Compile>
    <Compile Include="PharmacyMessagesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PharmacyMessagesForm.Designer.cs">
      <DependentUpon>PharmacyMessagesForm.cs</DependentUpon>
    </Compile>
    <Compile Include="SimpleLoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SimpleLoginForm.Designer.cs">
      <DependentUpon>SimpleLoginForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="AdministratorUC\data.resx">
      <DependentUpon>data.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="AdministratorUC\UC_AddUser.resx">
      <DependentUpon>UC_AddUser.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="AdministratorUC\UC_Dashbord.resx">
      <DependentUpon>UC_Dashbord.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="AdministratorUC\UC_SalesReport.resx">
      <DependentUpon>UC_SalesReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="AdministratorUC\UC_EditUser.resx">
      <DependentUpon>UC_EditUser.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="AdministratorUC\UC_EmployeeSessions.resx">
      <DependentUpon>UC_EmployeeSessions.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="AdministratorUC\UC_PrintDesign.resx">
      <DependentUpon>UC_PrintDesign.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Adminstrator.resx">
      <DependentUpon>Adminstrator.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CreateAccountForm.resx">
      <DependentUpon>CreateAccountForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Pharmacist.resx">
      <DependentUpon>Pharmacist.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PharmacistUC\UC__P_SellMedicine.resx">
      <DependentUpon>UC__P_SellMedicine.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PharmacistUC\UC_P_AddMedicine.resx">
      <DependentUpon>UC_P_AddMedicine.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PharmacistUC\UC_P_Dashbord.resx">
      <DependentUpon>UC_P_Dashbord.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PharmacistUC\UC_P_MedicineValidityCheck.resx">
      <DependentUpon>UC_P_MedicineValidityCheck.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PharmacistUC\UC_P_UpdateMedicine.resx">
      <DependentUpon>UC_P_UpdateMedicine.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PharmacistUC\UC_P_ViewMedicines.resx">
      <DependentUpon>UC_P_ViewMedicines.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PharmacistUC\sell_user.resx">
      <DependentUpon>sell_user.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PharmacistUC\UC_P_PharmacyStore.resx">
      <DependentUpon>UC_P_PharmacyStore.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PharmacyNetworkLoginForm.resx">
      <DependentUpon>PharmacyNetworkLoginForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CreateOrderForm.resx">
      <DependentUpon>CreateOrderForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PublishMedicineForm.resx">
      <DependentUpon>PublishMedicineForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RequestMedicineForm.resx">
      <DependentUpon>RequestMedicineForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="PharmacyCodeLoginForm.resx">
      <DependentUpon>PharmacyCodeLoginForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SimpleLoginForm.resx">
      <DependentUpon>SimpleLoginForm.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="Pharmacy Management System_TemporaryKey.pfx" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Enter.png" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.7.2">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.7.2 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>