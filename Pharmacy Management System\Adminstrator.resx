﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnSalesReport.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAALGPC/xhBQAAAYRJREFUaEPt
        mYFNAzEMRW+EjsAIHaEjMAIb0A1ghG4Am9AN6AbtBmUD6o84ybKS2EkTBwk/6alCXJP7vfwqd12CIAj+
        FQfyw1HMN4Qt+UV+O3kkm3n8fc2B/6cmHWFzkDcSA+x+/srzSspJR9gU5IlcB7iSD2SJPYlAPcWJ3xUE
        a58PAD/JDekJwvBzqAqCk8UV4AOsYql5clcQfPL8zVIsIS+ag6zl1tTK34umILzcmpby96A6SKrcmh7l
        rwpSKrfm6PJXBcH+BQdI5dbjQqaOG9mX5rJz8CY+CAb1JoJwIkhHIggngnQkgnAiSEciCCeCNICNZ+qB
        3Jnk54Bdeuq44oM77yvyTvL5rGKXXrwv8g6CkzmRfE6LuCksMqMjuH2ueQSL23OVGUEA+sLnzYmlaGJW
        EIBHTnxuKZagmZlBQK78arkls4Pkyq+WWyKD4Pv6pbPPZAlZflO5JTLIKLUrvZbfXG6JVxBo+TGpGc8g
        2HpUr30ruQd3oxz2Q2cQBMFfY1luqmFCmgNjDd8AAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnupdate.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAALGPC/xhBQAAAYRJREFUaEPt
        mYFNAzEMRW+EjsAIHaEjMAIb0A1ghG4Am9AN6AbtBmUD6o84ybKS2EkTBwk/6alCXJP7vfwqd12CIAj+
        FQfyw1HMN4Qt+UV+O3kkm3n8fc2B/6cmHWFzkDcSA+x+/srzSspJR9gU5IlcB7iSD2SJPYlAPcWJ3xUE
        a58PAD/JDekJwvBzqAqCk8UV4AOsYql5clcQfPL8zVIsIS+ag6zl1tTK34umILzcmpby96A6SKrcmh7l
        rwpSKrfm6PJXBcH+BQdI5dbjQqaOG9mX5rJz8CY+CAb1JoJwIkhHIggngnQkgnAiSEciCCeCNICNZ+qB
        3Jnk54Bdeuq44oM77yvyTvL5rGKXXrwv8g6CkzmRfE6LuCksMqMjuH2ueQSL23OVGUEA+sLnzYmlaGJW
        EIBHTnxuKZagmZlBQK78arkls4Pkyq+WWyKD4Pv6pbPPZAlZflO5JTLIKLUrvZbfXG6JVxBo+TGpGc8g
        2HpUr30ruQd3oxz2Q2cQBMFfY1luqmFCmgNjDd8AAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnLogOut.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAALGPC/xhBQAAAWNJREFUaEPN
        msFNA0EUxbYkSqAz6AA6gw6gA+gA5t2iyBM8C/P5lnxZycO+JIdVwrHI0/Bt+NXE3MvDcInnIR3Wwceh
        5mNIh3Qw74yGDuikhuJOaijupIbiTmoo7qSG4k5qKO6khuJOaijupIbiTmoonplnn/vh58W13WoonpkR
        4W5YNUZD8cw8YGZEqBqjofiW1WM0FP9k5RgNxcaqMRqKrRVjNBSvuHuMhuJVd47RUHzGXWM0FJ91x5jj
        5erCzJ38xZjj9erCzJ1kSN4l+rtWDcVnzat/+dH67YioofiMO0ZEDcWr7hoRNRSvuHNE1FBs3T0iaig2
        VoyIGop/smpE1FB8y8oRUUPxzOoRUUPxzMsvHypGRA3FM/O7XsZUjYgaijupobiTGoo7qaG4kxqKO6mh
        uJMaijupobiTGoo7qaG4k5o8CNIBHXwfavJzGh3SwaV/cwoJsp4O+w9zL+OejuMbQNFq2eumuFkAAAAA
        SUVORK5CYII=
</value>
  </data>
  <data name="btnAddUser.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAALGPC/xhBQAAAmpJREFUaEPd
        mgtNBEEMhk8CEpCAA3AADsABOAAJOAAH4AAcgAMkgAPoR9JNmXTndbPb5f7kS8hed9LOs9NlN0DHwqVw
        m3Aq/Avh/JvwneFDuBA2qSPhRfAcn+NBOBM2I4IojUKOJ4E2wnUveA62QDChYlF7jvUQOs1uBM+pHh6F
        MD0LnlM9sJuF6VXwnOolTPSi51AvYXoXPId6CdPBTK2RgTC6YboTPKd6CN1+Sf48p3rgTArTleA51QNt
        hWnkGqGtMB1MICNTFNoK08GsETTidA89Q6y+BM/BGnh3M+Iw85ysIfQgTMXtznOyhk0VIFDPVhy65c6J
        SkjLWsGWO/8m1TIqpdGgY64F6mW2ZkbpiaoLvy3WESMCIQCKd947HtgOD6glEHo2Fdn0p6A27GgclCeC
        ir95ZrMK3hlWhqVXrBM12AqjzRJwsqaXsbEB7Z0d0Es9ZVPewRl7r+m5l9j6WtfI4ETLfJ5DR3Kfy5UG
        Q1vVteRzgZ3DOrMvpexX7XLSaZbNFoiSjzWja1lKaU2oXU60kW2PPbt1IbdQk2+pbUk6Kn+mKaMwegp5
        1Ow2aluS7n7TVCWIfT7gtGDPiTmpbUm0hd10To34gFNLKs8mR6rpuV00a5DKs8mRanq+5mjAElPr9/rc
        kjONYLHFvuRW61E6DJHalvRn+9WX1mSVA3FJaeGi9Im6JhA980IKGvScXpFHJI20VZ00jpZN40mHWsU7
        +v6wC1avdLcBpkjNHMfGplA1u98qojdtJQYn+Y8je87wN89sALwTPhKp6GXdAGrANmxN1IiAWMCcC3aU
        OLF5xm+Z6bfb/QA10bovw182ggAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="btnDashbord.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAALGPC/xhBQAAAZ9JREFUaEPt
        mO1NAzEQRFMCJVACJdAJlEQJdAAdQAfQQSiBDmBHyo5Wpzl7k/h8GPlJ70du1uZ88QeXw2QymUz25MP8
        GVTcO1EFI0lUOJJEhSNJVJj11rw3n8yv07XeEhVmXfJofpuqdiuJCtd8N3Gz/llxYz6bsd2WEhUuxbTB
        FHL8eolegyEqjH6aeMoRz2q8mrGvpXdmDdSoti5RoYtvYjkI4HkNtC2tmW4DidMp4nmGuKa2kKgQYmGv
        4TVZttzJiAohnuQaXpMF50zsu6VEhRCH3RpekwVTNPbdUqJCWCJTE8FDiX27XRZ7CVV/iV0GUvojOFtU
        mz0kKoSlxZ5l660XEhXCN/Na0Ifqu6VEhe7agZhhy50qSlTo4p1Y/YtSA22Opuoz2mWxu+cOBrXZHzS6
        DgTixjLTDDWZb6KlRIVrYvE+mPFJ4rDDtR4LW0lUOJJEhSNJVNhCvB3W3hBbSFTYQkdlLSUqdDPbYw3v
        61KabL//ZiDX6KispUSFLXw5qbKWEhWOJFHhSBIVjiT5S6+t54p7n0wmk8keHA6/LmJMDqs09tcAAAAA
        SUVORK5CYII=
</value>
  </data>
  <metadata name="guna2Elipse1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="guna2Elipse2.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>140, 17</value>
  </metadata>
  <metadata name="guna2Elipse3.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>263, 17</value>
  </metadata>
  <metadata name="guna2Elipse4.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>386, 17</value>
  </metadata>
  <data name="guna2Button1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAALGPC/xhBQAAAU5JREFUaEPt
        mcENwjAMRTsCo3UT2IhuAhvBBsF2XjigttAK1TX4STk4iev/SZGlpkv2Timll3GR4YHW7ZGyHnnI2R7n
        zxlJy5FkPQnlLuMk48DSJmg96mp9Zd3JSOK15pcTUy5o/SqjXJlaBsnKpifxitavMkphahnkrkv+MkhJ
        IwahK0hJIwahK0h5aiFszDfNuieEkcZ402Rxl0YaMvW+abKwayMNWZpumixEMTLdNJkPYURhSxrZBKSk
        EYPQFaSkEYPQFaSkEYPQFaSkEYPQFaSkEYPQFaSkEYPQFaSkEYPQFaSkkUaIb79sGTUS6ms8e0aNtPuR
        m4yjDI/7Ea2r9ZXZ+xH2jJ+azA912Z0BSZOwb/r1kzU9mfaabY3W/eimqm6f/x+FAB9/boTcXfwKSEkj
        jfAXpj9zhR2qac4iyWGa5lvkISGaZuJH1z0AAzX8OxUhtBgAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnExit.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAALGPC/xhBQAAAjpJREFUaEPt
        muFNAzEMhTsCo7ABbAAbwAawAWwAm8AGsAFsABvABuDvx0NWelITN7m4qE96QrrGdl7iOLlwmyP+MU6M
        F8Y744vxw/hTkOeQNrTFJg2ujE/GstO1xBYf08ColqP+arw3nhtPjSV4fmmkDW29Lb7wuRrojBdAh66N
        kTTBBttPoxdEjKF4MCrgu7FnQHzhU/6J1R2M3JuRAN/GW+Mo4FtiiNmtIJDrEsGILeV+bxBD6dZFjJ8J
        RHQbnQoQS6m2t5hZIoRSTAiPxpkiBGIrzehTE6ggGLKw11gTu0Af6EuzEO0TI6vTcLDrKqUOGpqN4bvr
        SHBcQATHjtmgSoXL7rMRIQiajfAeQmMM4cxyK9CHkBiO1xhlSCshJEbVir+Z0CxGLzoZq1WTGJXdDDv5
        EqrF0ABmRpWY3kIIJJ+jSIwt6Mde0MiNJDG2oB8zg1TSTCNiMbV05s+82HeKANnLb5UIkHlDrBYBsh5R
        mkQAGmEAqwwGIyRCyHSMD4sAerHCyWwgICRCUBk+6FddoLvXDLOyNzQrB30dBHRB92XMckFHX5ov6ICu
        TEmxmeWY2HpXCm/WVI2ZYojpy3AYOJolphSxd2zy04tZY80QQ+nURYTgZ4ZFd2McBXwTp7sIDxUAyOz0
        3DTxpVmAoQrVAgJqn5Eg/uEfGTlssPUC8L3qqYLN0guC+jzjzLi0lnjuP/Pwtvia+i7EQVOn5gixzXDa
        /gNp4j/PKGcL8hzShrZDFvIR87HZ/ALLSjmva4csFgAAAABJRU5ErkJggg==
</value>
  </data>
  <metadata name="guna2Elipse5.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>509, 17</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>38</value>
  </metadata>
</root>