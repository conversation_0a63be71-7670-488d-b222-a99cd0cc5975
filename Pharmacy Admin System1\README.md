# 🏥 نظام إدارة الصيدليات المركزي - الإصدار الجديد
## Central Pharmacy Management System - New Version

### 📍 **الموقع الجديد:**
```
C:\Users\<USER>\source\repos\Pharmacy Admin System1\
```

---

## 🚀 **تشغيل النظام - خطوات بسيطة:**

### **الطريقة الأولى - التشغيل التلقائي:**
1. **شغل الملف:** `run_system.bat`
2. **انتظر إعداد قاعدة البيانات**
3. **سيفتح Visual Studio تلقائياً**
4. **اضغط F5 لتشغيل البرنامج**

### **الطريقة الثانية - يدوياً:**
1. **افتح SQL Server Management Studio**
2. **شغل ملف:** `quick_setup.sql`
3. **افتح Visual Studio**
4. **افتح ملف:** `Pharmacy Admin System1.sln`
5. **اضغط F5 للتشغيل**

---

## 🔐 **بيانات تسجيل الدخول:**
```
اسم المستخدم: superadmin
كلمة المرور: admin2025
```

---

## 📊 **الملفات الأساسية:**

### **ملفات المشروع:**
- ✅ `Pharmacy Admin System1.sln` - ملف الحل
- ✅ `Pharmacy Admin System1.csproj` - ملف المشروع
- ✅ `Program.cs` - نقطة الدخول
- ✅ `App.config` - إعدادات الاتصال

### **ملفات الواجهات:**
- ✅ `AdminLoginForm.cs` - واجهة تسجيل الدخول
- ✅ `AdminLoginForm.Designer.cs` - تصميم واجهة الدخول
- ✅ `AdminLoginForm.resx` - موارد واجهة الدخول
- ✅ `MainAdminForm.cs` - الواجهة الرئيسية
- ✅ `MainAdminForm.Designer.cs` - تصميم الواجهة الرئيسية
- ✅ `MainAdminForm.resx` - موارد الواجهة الرئيسية

### **ملفات الإدارة:**
- ✅ `DatabaseManager.cs` - إدارة قاعدة البيانات

### **ملفات قاعدة البيانات:**
- ✅ `quick_setup.sql` - إعداد قاعدة البيانات
- ✅ `run_system.bat` - تشغيل النظام

### **مجلد Properties:**
- ✅ `Properties\AssemblyInfo.cs`
- ✅ `Properties\Resources.resx`
- ✅ `Properties\Resources.Designer.cs`
- ✅ `Properties\Settings.settings`
- ✅ `Properties\Settings.Designer.cs`

---

## 🎯 **الميزات المتاحة:**

### **✅ لوحة التحكم:**
- عرض إحصائيات الصيدليات
- عرض الإيرادات الشهرية
- عرض طلبات التسجيل المعلقة
- عرض الصيدليات النشطة

### **✅ القوائم الجانبية:**
- لوحة التحكم
- الصيدليات المسجلة
- طلبات التسجيل
- إدارة الاشتراكات
- النسخ الاحتياطية
- إدارة المستخدمين
- التقارير
- إعدادات النظام

### **✅ البيانات التجريبية:**
- **3 صيدليات تجريبية** جاهزة للاختبار
- **4 خطط اشتراك** (أساسي، قياسي، مميز، مؤسسي)
- **مدير عام افتراضي** للدخول

---

## 🔧 **المتطلبات التقنية:**

### **البرمجيات:**
- ✅ Windows 10/11
- ✅ SQL Server 2016+ (مشغل)
- ✅ .NET Framework 4.7.2
- ✅ Visual Studio 2019/2022

### **قواعد البيانات:**
- ✅ **PharmacyAdminSystem** - قاعدة الإدارة المنفصلة
- ✅ **UnifiedPharmacy** - قاعدة الصيدليات (للقراءة)

---

## 🎊 **البيانات التجريبية:**

### **الصيدليات:**
1. **صيدلية النهضة** (الرياض) - اشتراك مميز - نشطة
2. **صيدلية الشفاء** (جدة) - اشتراك قياسي - نشطة
3. **صيدلية الأمل** (الدمام) - اشتراك أساسي - معلقة

### **خطط الاشتراك:**
- **أساسي:** 99 ريال/شهر (3 مستخدمين، 500 دواء)
- **قياسي:** 199 ريال/شهر (5 مستخدمين، 1000 دواء)
- **مميز:** 299 ريال/شهر (10 مستخدمين، 5000 دواء)
- **مؤسسي:** 499 ريال/شهر (50 مستخدم، 50000 دواء)

---

## 🔍 **استكشاف الأخطاء:**

### **❌ مشكلة في قاعدة البيانات:**
```
الحل: تأكد من تشغيل SQL Server
شغل quick_setup.sql يدوياً
```

### **❌ مشكلة في Visual Studio:**
```
الحل: أعد بناء المشروع (Build > Rebuild Solution)
تأكد من .NET Framework 4.7.2
```

### **❌ مشكلة في تسجيل الدخول:**
```
الحل: استخدم البيانات الافتراضية
تأكد من تنفيذ quick_setup.sql بنجاح
```

---

## 🎯 **خطوات الاختبار:**

### **1. اختبار تسجيل الدخول:**
- شغل البرنامج
- أدخل: superadmin / admin2025
- تأكد من فتح الواجهة الرئيسية

### **2. اختبار لوحة التحكم:**
- تحقق من ظهور الإحصائيات
- تأكد من عرض البيانات التجريبية
- اختبر القوائم الجانبية

### **3. اختبار قاعدة البيانات:**
- تحقق من إنشاء PharmacyAdminSystem
- تأكد من وجود الجداول والبيانات
- اختبر الاتصال

---

## 📞 **الدعم:**

### **في حالة المشاكل:**
1. تأكد من تشغيل SQL Server
2. تحقق من صلاحيات قاعدة البيانات
3. أعد تشغيل run_system.bat
4. اختبر الاتصال يدوياً

### **الملفات المهمة:**
- `run_system.bat` - للتشغيل السريع
- `quick_setup.sql` - لإعداد قاعدة البيانات
- `App.config` - لإعدادات الاتصال

---

## 🚀 **البدء الآن:**

```bash
# 1. انتقل للمجلد
cd "C:\Users\<USER>\source\repos\Pharmacy Admin System1"

# 2. شغل النظام
run_system.bat

# 3. سجل دخول
superadmin / admin2025

# 4. استمتع بالإدارة!
```

---

**🎯 نظام إدارة الصيدليات المركزي - الإصدار الجديد والمحسن!**

**📁 الموقع:** `C:\Users\<USER>\source\repos\Pharmacy Admin System1\`

**🔐 الدخول:** superadmin / admin2025

**🚀 التشغيل:** run_system.bat

**✅ جاهز للاستخدام الفوري!**
