-- إصلاح مشكلة تسجيل الدخول - عمود pharmacyId
-- Fix Login Issue - pharmacyId Column

USE UnifiedPharmacy;
GO

PRINT '========================================';
PRINT '   إصلاح مشكلة تسجيل الدخول';
PRINT '   Fix Login Issue';
PRINT '========================================';

-- 1. التحقق من وجود جدول المستخدمين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
BEGIN
    PRINT 'خطأ: جدول المستخدمين غير موجود!';
    PRINT 'Error: Users table does not exist!';
    RETURN;
END

-- 2. إضافة عمود pharmacyId إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'pharmacyId')
BEGIN
    ALTER TABLE users ADD pharmacyId INT DEFAULT 1;
    PRINT '✅ تم إضافة عمود pharmacyId';
END
ELSE
BEGIN
    PRINT '✅ عمود pharmacyId موجود بالفعل';
END

-- 3. إضافة عمود isActive إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'isActive')
BEGIN
    ALTER TABLE users ADD isActive BIT DEFAULT 1;
    PRINT '✅ تم إضافة عمود isActive';
END
ELSE
BEGIN
    PRINT '✅ عمود isActive موجود بالفعل';
END

-- 4. التأكد من وجود جدول الصيدليات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies' AND xtype='U')
BEGIN
    CREATE TABLE pharmacies(
        id int identity(1,1) primary key,
        pharmacyCode varchar(50) unique not null,
        pharmacyName nvarchar(250) not null,
        ownerName nvarchar(250) not null,
        licenseNumber varchar(100) not null,
        address nvarchar(500),
        city nvarchar(100),
        region nvarchar(100),
        phone varchar(20),
        email varchar(250),
        isActive bit default 1,
        createdDate datetime default getdate(),
        lastOnline datetime,
        subscriptionType varchar(50) default 'Basic'
    );
    PRINT '✅ تم إنشاء جدول pharmacies';
END
ELSE
BEGIN
    PRINT '✅ جدول pharmacies موجود بالفعل';
END

-- 5. إضافة صيدلية افتراضية
IF NOT EXISTS (SELECT * FROM pharmacies WHERE id = 1)
BEGIN
    SET IDENTITY_INSERT pharmacies ON;
    INSERT INTO pharmacies (id, pharmacyCode, pharmacyName, ownerName, licenseNumber, address, city, region, phone, email, isActive) 
    VALUES (1, 'MAIN001', N'الصيدلية الرئيسية', N'المالك الرئيسي', 'LIC001', N'العنوان الرئيسي', N'الرياض', N'الرياض', '**********', '<EMAIL>', 1);
    SET IDENTITY_INSERT pharmacies OFF;
    PRINT '✅ تم إنشاء صيدلية افتراضية';
END
ELSE
BEGIN
    PRINT '✅ الصيدلية الافتراضية موجودة';
END

-- 6. تحديث المستخدمين الموجودين
UPDATE users SET pharmacyId = 1 WHERE pharmacyId IS NULL OR pharmacyId = 0;
UPDATE users SET isActive = 1 WHERE isActive IS NULL;
PRINT '✅ تم تحديث المستخدمين الموجودين';

-- 7. التأكد من وجود جدول جلسات الموظفين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='employee_sessions' AND xtype='U')
BEGIN
    CREATE TABLE employee_sessions(
        id int identity(1,1) primary key,
        pharmacyId int not null,
        userId int not null,
        username nvarchar(250) not null,
        employeeName nvarchar(250) not null,
        loginTime datetime not null,
        logoutTime datetime null,
        sessionDate date not null,
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
    );
    PRINT '✅ تم إنشاء جدول employee_sessions';
END
ELSE
BEGIN
    PRINT '✅ جدول employee_sessions موجود بالفعل';
END

-- 8. عرض النتائج النهائية
PRINT '';
PRINT '========================================';
PRINT '   النتائج النهائية';
PRINT '   Final Results';
PRINT '========================================';

SELECT COUNT(*) as 'عدد المستخدمين' FROM users;
SELECT COUNT(*) as 'عدد الصيدليات' FROM pharmacies;

PRINT '';
PRINT 'عينة من المستخدمين:';
SELECT TOP 3 id, username, pharmacyId, isActive FROM users;

PRINT '';
PRINT '✅ تم الانتهاء من إصلاح مشكلة تسجيل الدخول!';
PRINT '✅ Login issue fix completed!';
PRINT '';
PRINT 'الآن يمكنك تشغيل البرنامج وتسجيل الدخول بنجاح';
PRINT 'You can now run the program and login successfully';
