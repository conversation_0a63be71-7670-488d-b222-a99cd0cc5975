@echo off
echo ========================================
echo   إصلاح مشكلة تسجيل الدخول - pharmacyId
echo   Fix Login Issue - pharmacyId Column
echo ========================================

echo.
echo 1. التحقق من قاعدة البيانات UnifiedPharmacy...
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "SELECT name FROM sys.databases WHERE name = 'UnifiedPharmacy'"

if %errorlevel% neq 0 (
    echo خطأ: لا يمكن الاتصال بقاعدة البيانات UnifiedPharmacy
    echo Error: Cannot connect to UnifiedPharmacy database
    pause
    exit /b 1
)

echo.
echo 2. التحقق من هيكل جدول المستخدمين...
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' ORDER BY ORDINAL_POSITION"

echo.
echo 3. إضافة عمود pharmacyId إذا لم يكن موجوداً...
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'pharmacyId') BEGIN ALTER TABLE users ADD pharmacyId INT DEFAULT 1; PRINT 'تم إضافة عمود pharmacyId'; END ELSE PRINT 'عمود pharmacyId موجود بالفعل';"

echo.
echo 4. إضافة عمود isActive إذا لم يكن موجوداً...
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'isActive') BEGIN ALTER TABLE users ADD isActive BIT DEFAULT 1; PRINT 'تم إضافة عمود isActive'; END ELSE PRINT 'عمود isActive موجود بالفعل';"

echo.
echo 5. التأكد من وجود جدول الصيدليات...
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies' AND xtype='U') BEGIN CREATE TABLE pharmacies( id int identity(1,1) primary key, pharmacyCode varchar(50) unique not null, pharmacyName nvarchar(250) not null, ownerName nvarchar(250) not null, licenseNumber varchar(100) not null, address nvarchar(500), city nvarchar(100), region nvarchar(100), phone varchar(20), email varchar(250), isActive bit default 1, createdDate datetime default getdate(), lastOnline datetime, subscriptionType varchar(50) default 'Basic' ); PRINT 'تم إنشاء جدول pharmacies'; END ELSE PRINT 'جدول pharmacies موجود بالفعل';"

echo.
echo 6. إضافة صيدلية افتراضية...
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "IF NOT EXISTS (SELECT * FROM pharmacies WHERE id = 1) BEGIN INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, licenseNumber, address, city, region, phone, email, isActive) VALUES ('MAIN001', N'الصيدلية الرئيسية', N'المالك الرئيسي', 'LIC001', N'العنوان الرئيسي', N'الرياض', N'الرياض', '**********', '<EMAIL>', 1); PRINT 'تم إنشاء صيدلية افتراضية'; END ELSE PRINT 'الصيدلية الافتراضية موجودة';"

echo.
echo 7. تحديث المستخدمين الموجودين...
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "UPDATE users SET pharmacyId = 1 WHERE pharmacyId IS NULL OR pharmacyId = 0; UPDATE users SET isActive = 1 WHERE isActive IS NULL; PRINT 'تم تحديث المستخدمين الموجودين';"

echo.
echo 8. التحقق من النتائج النهائية...
sqlcmd -S NARUTO -E -d UnifiedPharmacy -Q "SELECT COUNT(*) as UserCount FROM users; SELECT COUNT(*) as PharmacyCount FROM pharmacies; SELECT TOP 3 id, username, pharmacyId, isActive FROM users;"

echo.
echo ========================================
echo   تم الانتهاء من إصلاح مشكلة تسجيل الدخول!
echo   Login issue fix completed!
echo ========================================
echo.
echo الآن يمكنك تشغيل البرنامج وتسجيل الدخول بنجاح
echo You can now run the program and login successfully
echo.
pause
