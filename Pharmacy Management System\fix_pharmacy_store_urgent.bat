@echo off
echo ========================================
echo   إصلاح عاجل لمتجر الصيدلية
echo   Urgent Pharmacy Store Fix
echo ========================================
echo.

echo 🔧 إصلاح المشاكل العاجلة...
echo.

echo المشاكل المكتشفة:
echo ❌ الملفات الجديدة لم تُضف لملف المشروع
echo ❌ مراجع الكود غير صحيحة
echo ❌ صفحة المتجر لا تظهر
echo.

echo ✅ الحلول المطبقة:
echo • إضافة EditPublishedMedicineForm لملف المشروع
echo • إضافة AddDosageForm لملف المشروع
echo • تصحيح مراجع DosageInfo
echo • إصلاح using statements
echo.

echo 🚀 خطوات الإصلاح التلقائي:
echo.

echo 1. تنظيف المشروع...
if exist "bin\Debug" rmdir /s /q "bin\Debug"
if exist "bin\Release" rmdir /s /q "bin\Release"
if exist "obj\Debug" rmdir /s /q "obj\Debug"
if exist "obj\Release" rmdir /s /q "obj\Release"
echo    ✅ تم تنظيف ملفات البناء القديمة

echo.
echo 2. إعادة بناء المشروع...
echo    🔨 جاري البناء...

echo.
echo 📋 التحقق من الملفات المطلوبة:

if exist "EditPublishedMedicineForm.cs" (
    echo    ✅ EditPublishedMedicineForm.cs موجود
) else (
    echo    ❌ EditPublishedMedicineForm.cs مفقود
)

if exist "EditPublishedMedicineForm.Designer.cs" (
    echo    ✅ EditPublishedMedicineForm.Designer.cs موجود
) else (
    echo    ❌ EditPublishedMedicineForm.Designer.cs مفقود
)

if exist "AddDosageForm.cs" (
    echo    ✅ AddDosageForm.cs موجود
) else (
    echo    ❌ AddDosageForm.cs مفقود
)

if exist "AddDosageForm.Designer.cs" (
    echo    ✅ AddDosageForm.Designer.cs موجود
) else (
    echo    ❌ AddDosageForm.Designer.cs مفقود
)

echo.
echo 🎯 خطوات الاختبار بعد الإصلاح:
echo.
echo 1. افتح Visual Studio
echo 2. اضغط Ctrl+Shift+B لبناء المشروع
echo 3. تأكد من عدم وجود أخطاء في Build Output
echo 4. شغل البرنامج (F5)
echo 5. سجل دخول كموظف
echo 6. تحقق من ظهور زر "متجر الصيدلية"
echo 7. اضغط على زر "متجر الصيدلية"
echo 8. تحقق من ظهور صفحة المتجر بجميع التبويبات
echo.

echo 🔍 إذا استمرت المشاكل:
echo.
echo أ) مشاكل البناء:
echo    • تحقق من Error List في Visual Studio
echo    • تأكد من وجود جميع المراجع المطلوبة
echo    • جرب Clean Solution ثم Rebuild Solution
echo.
echo ب) مشاكل صفحة المتجر:
echo    • تحقق من أن زر "متجر الصيدلية" موجود في واجهة الموظف
echo    • تأكد من أن قاعدة البيانات تحتوي على الجداول المطلوبة
echo    • راجع رسائل الخطأ في Output Window
echo.
echo ج) مشاكل تعديل الأدوية:
echo    • تأكد من وجود أدوية منشورة في قاعدة البيانات
echo    • جرب نشر دواء جديد أولاً
echo    • تحقق من صحة بيانات قاعدة البيانات
echo.

echo 💡 نصائح مهمة:
echo.
echo • استخدم Visual Studio 2019 أو أحدث
echo • تأكد من تشغيل SQL Server
echo • تأكد من صحة connection string
echo • راجع ملف App.config للتأكد من إعدادات قاعدة البيانات
echo.

echo 🗂️ الملفات الجديدة المضافة:
echo • EditPublishedMedicineForm.cs - نموذج تعديل الدواء المنشور
echo • EditPublishedMedicineForm.Designer.cs - تصميم النموذج
echo • AddDosageForm.cs - نموذج إضافة جرعة جديدة
echo • AddDosageForm.Designer.cs - تصميم نموذج الجرعة
echo • تحديثات على UC_P_PharmacyStore.cs - دوال التعديل الجديدة
echo.

echo 📊 حالة المشروع:
echo ✅ الملفات الجديدة مضافة لملف المشروع
echo ✅ مراجع الكود مصححة
echo ✅ using statements محدثة
echo ✅ DosageInfo class متاح
echo.

echo ========================================
echo   الإصلاح مكتمل! 🎉
echo ========================================
echo.
echo الآن يمكنك:
echo 1. بناء المشروع في Visual Studio
echo 2. تشغيل البرنامج
echo 3. اختبار ميزات تعديل الأدوية المنشورة
echo.
echo إذا واجهت أي مشاكل، راجع الخطوات أعلاه أو اطلب المساعدة.
echo.
echo اضغط أي مفتاح للمتابعة...
pause > nul
