-- فحص SessionManager وحالة تسجيل الدخول
USE UnifiedPharmacy;

PRINT '=== فحص حالة SessionManager وتسجيل الدخول ===';

-- فحص جدول المستخدمين
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'users')
BEGIN
    PRINT '✅ جدول users موجود';
    SELECT COUNT(*) as 'عدد المستخدمين' FROM users;
    SELECT TOP 5 id, name, userRole, pharmacyId FROM users;
END
ELSE
BEGIN
    PRINT '❌ جدول users غير موجود';
END

-- فحص جدول الصيدليات
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacies')
BEGIN
    PRINT '✅ جدول pharmacies موجود';
    SELECT COUNT(*) as 'عدد الصيدليات' FROM pharmacies;
    SELECT id, pharmacy_name, pharmacy_code FROM pharmacies;
END
ELSE
BEGIN
    PRINT '❌ جدول pharmacies غير موجود';
END

-- فحص جلسات الموظفين
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'employee_sessions')
BEGIN
    PRINT '✅ جدول employee_sessions موجود';
    SELECT COUNT(*) as 'عدد الجلسات' FROM employee_sessions;
    SELECT TOP 5 id, username, pharmacyId, loginTime, logoutTime FROM employee_sessions ORDER BY loginTime DESC;
END
ELSE
BEGIN
    PRINT '❌ جدول employee_sessions غير موجود';
END

-- فحص طلبات الأدوية حسب الصيدلية
PRINT '=== فحص طلبات الأدوية حسب الصيدلية ===';

SELECT 
    p.id as 'معرف الصيدلية',
    p.pharmacy_name as 'اسم الصيدلية',
    COUNT(pr.id) as 'عدد الطلبات المستلمة'
FROM pharmacies p
LEFT JOIN purchase_requests pr ON p.id = pr.seller_pharmacy_id
GROUP BY p.id, p.pharmacy_name
ORDER BY p.id;

-- عرض تفاصيل الطلبات
SELECT 
    pr.id as 'رقم الطلب',
    pm.medicine_name as 'اسم الدواء',
    p_buyer.pharmacy_name as 'الصيدلية الطالبة',
    p_seller.pharmacy_name as 'الصيدلية البائعة',
    pr.requested_quantity as 'الكمية',
    pr.status as 'الحالة',
    pr.request_date as 'تاريخ الطلب'
FROM purchase_requests pr
INNER JOIN published_medicines pm ON pr.published_medicine_id = pm.id
INNER JOIN pharmacies p_buyer ON pr.buyer_pharmacy_id = p_buyer.id
INNER JOIN pharmacies p_seller ON pr.seller_pharmacy_id = p_seller.id
ORDER BY pr.request_date DESC;

PRINT '=== انتهى الفحص ===';
