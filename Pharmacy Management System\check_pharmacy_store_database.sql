-- فحص حالة قاعدة البيانات لمتجر الصيدلية
USE UnifiedPharmacy;

PRINT '=== فحص حالة قاعدة البيانات لمتجر الصيدلية ===';

-- فحص وجود جدول الصيدليات
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacies')
BEGIN
    PRINT '✅ جدول pharmacies موجود';
    SELECT COUNT(*) as 'عدد الصيدليات' FROM pharmacies;
    SELECT TOP 5 id, pharmacy_name, city FROM pharmacies;
END
ELSE
BEGIN
    PRINT '❌ جدول pharmacies غير موجود';
END

-- فحص وجود جدول الأدوية المنشورة
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'published_medicines')
BEGIN
    PRINT '✅ جدول published_medicines موجود';
    SELECT COUNT(*) as 'عدد الأدوية المنشورة' FROM published_medicines;
    SELECT TOP 5 id, medicine_name, pharmacy_id, quantity FROM published_medicines;
END
ELSE
BEGIN
    PRINT '❌ جدول published_medicines غير موجود';
END

-- فحص وجود جدول طلبات الشراء
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'purchase_requests')
BEGIN
    PRINT '✅ جدول purchase_requests موجود';
    SELECT COUNT(*) as 'عدد طلبات الشراء' FROM purchase_requests;
    SELECT TOP 5 id, buyer_pharmacy_id, seller_pharmacy_id, status FROM purchase_requests;
END
ELSE
BEGIN
    PRINT '❌ جدول purchase_requests غير موجود';
END

-- فحص وجود جدول الرسائل
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'pharmacy_messages')
BEGIN
    PRINT '✅ جدول pharmacy_messages موجود';
    SELECT COUNT(*) as 'عدد الرسائل' FROM pharmacy_messages;
END
ELSE
BEGIN
    PRINT '❌ جدول pharmacy_messages غير موجود';
END

PRINT '=== انتهى الفحص ===';
