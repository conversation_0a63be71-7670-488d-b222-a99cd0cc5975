@echo off
echo ========================================
echo   إعداد النظام الأونلاين الكامل
echo   Complete Online System Setup
echo ========================================
echo.

echo 🚀 إعداد قاعدة البيانات الأونلاين الكاملة...
echo.

echo الخطوة 1: إنشاء قاعدة البيانات والجداول...
sqlcmd -S NARUTO -E -i setup_complete_online_database.sql

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في إنشاء قاعدة البيانات!
    goto :error
)

echo ✅ تم إنشاء قاعدة البيانات والجداول بنجاح
echo.

echo الخطوة 2: إنشاء الإجراءات المخزنة...
sqlcmd -S NARUTO -E -i create_missing_stored_procedures.sql

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في إنشاء الإجراءات المخزنة!
    goto :error
)

echo ✅ تم إنشاء الإجراءات المخزنة بنجاح
echo.

echo ========================================
echo   ✅ تم إعداد النظام الأونلاين بنجاح!
echo ========================================
echo.
echo الميزات المتاحة الآن:
echo - تسجيل صيدليات جديدة
echo - تسجيل دخول للشبكة الأونلاين
echo - البحث عن الأدوية في الصيدليات الأخرى
echo - إرسال طلبات شراء بين الصيدليات
echo - المحادثة بين الصيدليات
echo.
echo قواعد البيانات:
echo - المحلية: pharmacy (للعمليات اليومية)
echo - الأونلاين: PharmacyNetworkOnline (للشبكة)
echo.
echo يمكنك الآن:
echo 1. تشغيل البرنامج
echo 2. الذهاب لصفحة الموظف
echo 3. الضغط على "الشبكة الأونلاين"
echo 4. تسجيل صيدلية جديدة أو تسجيل الدخول
echo.
goto :end

:error
echo.
echo ========================================
echo   ❌ حدث خطأ أثناء الإعداد!
echo ========================================
echo.
echo تأكد من:
echo 1. تشغيل SQL Server
echo 2. صحة اسم الخادم (NARUTO)
echo 3. وجود صلاحيات الإدارة
echo 4. وجود ملفات SQL في نفس المجلد
echo.
echo الملفات المطلوبة:
echo - setup_complete_online_database.sql
echo - create_missing_stored_procedures.sql
echo.

:end
echo اضغط أي مفتاح للإغلاق...
pause > nul
