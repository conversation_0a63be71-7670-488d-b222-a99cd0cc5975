-- ملف SQL مبسط لإضافة الجداول والأعمدة المطلوبة
-- شغل كل استعلام منفصل

-- 1. إضافة عمود originalQuantity
ALTER TABLE medic ADD originalQuantity BIGINT;

-- 2. إضا<PERSON>ة عمود originalNewQuantity  
ALTER TABLE medic ADD originalNewQuantity BIGINT;

-- 3. تحديث القيم الافتراضية
UPDATE medic SET originalQuantity = 0 WHERE originalQuantity IS NULL;
UPDATE medic SET originalNewQuantity = 0 WHERE originalNewQuantity IS NULL;

-- 4. تحديث الكميات الأصلية
UPDATE medic SET originalQuantity = quantity;
UPDATE medic SET originalNewQuantity = newQuantity WHERE newQuantity IS NOT NULL;

-- 5. إنشاء جدول المبيعات
CREATE TABLE sales (
    id INT IDENTITY(1,1) PRIMARY KEY,
    mid VARCHAR(250),
    medicineName VARCHAR(250),
    dosage VARCHAR(100),
    quantity INT,
    pricePerUnit BIGINT,
    totalPrice BIGINT,
    employeeUsername VARCHAR(250),
    employeeName VARCHAR(250),
    saleDate DATETIME DEFAULT GETDATE()
);

-- 6. إنشاء جدول جلسات الموظفين
CREATE TABLE employee_sessions (
    id INT IDENTITY(1,1) PRIMARY KEY,
    username VARCHAR(250),
    employeeName VARCHAR(250),
    loginTime DATETIME,
    logoutTime DATETIME NULL,
    sessionDate DATE
);
