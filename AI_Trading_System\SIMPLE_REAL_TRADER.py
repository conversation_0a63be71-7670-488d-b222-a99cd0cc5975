#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام التداول الحقيقي المبسط - يفتح صفقات حقيقية
SIMPLE REAL TRADER - OPENS REAL TRADES
"""

import MetaTrader5 as mt5
import time
import random
from datetime import datetime
import configparser

class SimpleRealTrader:
    def __init__(self):
        self.connected = False
        self.account_info = None
        self.load_config()
        
    def load_config(self):
        """تحميل إعدادات الحساب"""
        config = configparser.ConfigParser()
        config.read('config.ini')
        
        self.login = int(config.get('MT5_CONNECTION', 'login', fallback='********'))
        self.password = config.get('MT5_CONNECTION', 'password', fallback='D!2qKdJy')
        self.server = config.get('MT5_CONNECTION', 'server', fallback='MetaQuotes-Demo')
        self.symbol = config.get('TRADING_SETTINGS', 'symbol', fallback='EURUSD')
        self.lot_size = float(config.get('TRADING_SETTINGS', 'lot_size', fallback='0.01'))
        
    def connect(self):
        """الاتصال بـ MetaTrader 5"""
        print("🔥 محاولة الاتصال الحقيقي بـ MetaTrader 5...")
        
        # تهيئة MT5
        if not mt5.initialize():
            print(f"❌ فشل في تهيئة MT5: {mt5.last_error()}")
            return False
            
        # تسجيل الدخول
        if not mt5.login(self.login, self.password, self.server):
            print(f"❌ فشل في تسجيل الدخول: {mt5.last_error()}")
            return False
            
        # الحصول على معلومات الحساب
        self.account_info = mt5.account_info()
        if not self.account_info:
            print("❌ لا يمكن الحصول على معلومات الحساب")
            return False
            
        # التحقق من صلاحيات التداول
        if not self.account_info.trade_allowed:
            print("❌ التداول غير مسموح في هذا الحساب")
            return False
            
        # التحقق من التداول الآلي
        terminal_info = mt5.terminal_info()
        if not terminal_info.trade_allowed:
            print("❌ التداول الآلي معطل")
            return False
            
        self.connected = True
        print("🎉 تم الاتصال الحقيقي بنجاح!")
        print(f"📊 حساب: {self.account_info.login}")
        print(f"💰 رصيد: ${self.account_info.balance:.2f}")
        print(f"🏢 شركة: {self.account_info.company}")
        
        return True
        
    def get_current_price(self):
        """الحصول على السعر الحالي"""
        tick = mt5.symbol_info_tick(self.symbol)
        if not tick:
            return None, None
        return tick.bid, tick.ask
        
    def open_buy_order(self):
        """فتح صفقة شراء حقيقية"""
        if not self.connected:
            print("❌ غير متصل بـ MT5")
            return False
            
        bid, ask = self.get_current_price()
        if not ask:
            print("❌ لا يمكن الحصول على السعر")
            return False
            
        # حساب وقف الخسارة وجني الربح
        sl = ask - 0.0050  # 50 نقطة وقف خسارة
        tp = ask + 0.0100  # 100 نقطة جني ربح
        
        # إعداد الطلب
        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": self.symbol,
            "volume": self.lot_size,
            "type": mt5.ORDER_TYPE_BUY,
            "price": ask,
            "sl": sl,
            "tp": tp,
            "deviation": 20,
            "magic": 123456,
            "comment": "Real Buy Order",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_FOK,
        }
        
        print(f"🔥 فتح صفقة شراء حقيقية...")
        print(f"   الرمز: {self.symbol}")
        print(f"   الحجم: {self.lot_size}")
        print(f"   السعر: {ask}")
        print(f"   وقف الخسارة: {sl}")
        print(f"   جني الربح: {tp}")
        
        # تنفيذ الطلب
        result = mt5.order_send(request)
        
        if result.retcode != mt5.TRADE_RETCODE_DONE:
            print(f"❌ فشل في فتح الصفقة: {result.retcode} - {result.comment}")
            return False
            
        print("🎉 تم فتح صفقة الشراء الحقيقية بنجاح!")
        print(f"   رقم الأمر: {result.order}")
        print(f"   رقم الصفقة: {result.deal}")
        print(f"   الحجم المنفذ: {result.volume}")
        print(f"   السعر المنفذ: {result.price}")
        
        return True
        
    def open_sell_order(self):
        """فتح صفقة بيع حقيقية"""
        if not self.connected:
            print("❌ غير متصل بـ MT5")
            return False
            
        bid, ask = self.get_current_price()
        if not bid:
            print("❌ لا يمكن الحصول على السعر")
            return False
            
        # حساب وقف الخسارة وجني الربح
        sl = bid + 0.0050  # 50 نقطة وقف خسارة
        tp = bid - 0.0100  # 100 نقطة جني ربح
        
        # إعداد الطلب
        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": self.symbol,
            "volume": self.lot_size,
            "type": mt5.ORDER_TYPE_SELL,
            "price": bid,
            "sl": sl,
            "tp": tp,
            "deviation": 20,
            "magic": 123456,
            "comment": "Real Sell Order",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_FOK,
        }
        
        print(f"🔥 فتح صفقة بيع حقيقية...")
        print(f"   الرمز: {self.symbol}")
        print(f"   الحجم: {self.lot_size}")
        print(f"   السعر: {bid}")
        print(f"   وقف الخسارة: {sl}")
        print(f"   جني الربح: {tp}")
        
        # تنفيذ الطلب
        result = mt5.order_send(request)
        
        if result.retcode != mt5.TRADE_RETCODE_DONE:
            print(f"❌ فشل في فتح الصفقة: {result.retcode} - {result.comment}")
            return False
            
        print("🎉 تم فتح صفقة البيع الحقيقية بنجاح!")
        print(f"   رقم الأمر: {result.order}")
        print(f"   رقم الصفقة: {result.deal}")
        print(f"   الحجم المنفذ: {result.volume}")
        print(f"   السعر المنفذ: {result.price}")
        
        return True
        
    def get_open_positions(self):
        """الحصول على الصفقات المفتوحة"""
        positions = mt5.positions_get(symbol=self.symbol)
        return positions if positions else []
        
    def start_trading(self):
        """بدء التداول الحقيقي"""
        if not self.connect():
            return False
            
        print("\n🚀 بدء التداول الحقيقي...")
        print("⚠️ سيتم فتح صفقات حقيقية على حسابك!")
        
        # فتح صفقة شراء واحدة للاختبار
        print("\n🔥 فتح صفقة شراء للاختبار...")
        success = self.open_buy_order()
        
        if success:
            print("✅ تم فتح الصفقة الحقيقية بنجاح!")
            print("📊 تحقق من MetaTrader 5 لرؤية الصفقة")
        else:
            print("❌ فشل في فتح الصفقة")
            
        return success

def main():
    """الدالة الرئيسية"""
    print("🔥 نظام التداول الحقيقي المبسط")
    print("=" * 50)
    
    # تحذير
    print("🚨 تحذير مهم:")
    print("💰 هذا النظام سيفتح صفقة حقيقية واحدة!")
    print("⚠️ تأكد من أن هذا ما تريده!")
    
    confirm = input("\nهل تريد المتابعة؟ (y/n): ")
    if confirm.lower() != 'y':
        print("تم الإلغاء.")
        return
    
    # إنشاء النظام
    trader = SimpleRealTrader()
    
    # بدء التداول
    trader.start_trading()

if __name__ == "__main__":
    main()
