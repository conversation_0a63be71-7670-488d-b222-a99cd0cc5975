# نظام إدارة الصيدليات المركزي - سكريپت التشغيل
# Central Pharmacy Admin System - Run Script

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "🚀 نظام إدارة الصيدليات المركزي" -ForegroundColor Green
Write-Host "🚀 Central Pharmacy Admin System" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# الخطوة 1: إعداد قاعدة البيانات
Write-Host "الخطوة 1: إعداد قاعدة البيانات..." -ForegroundColor Yellow
Write-Host "Step 1: Setting up database..." -ForegroundColor Yellow

try {
    # إنشاء قاعدة البيانات
    $createDbQuery = "USE master; IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'PharmacyAdminSystem') CREATE DATABASE PharmacyAdminSystem; PRINT 'Database ready'"
    sqlcmd -S NARUTO -E -Q $createDbQuery
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ قاعدة البيانات جاهزة" -ForegroundColor Green
        Write-Host "✅ Database ready" -ForegroundColor Green
    } else {
        throw "Database creation failed"
    }
} catch {
    Write-Host "❌ خطأ في إعداد قاعدة البيانات: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "❌ Database setup failed: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "اضغط Enter للمتابعة..."
    exit 1
}

Write-Host ""

# الخطوة 2: إنشاء الجداول
Write-Host "الخطوة 2: إنشاء الجداول..." -ForegroundColor Yellow
Write-Host "Step 2: Creating tables..." -ForegroundColor Yellow

try {
    sqlcmd -S NARUTO -E -i "quick_setup.sql"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ تم إنشاء الجداول بنجاح" -ForegroundColor Green
        Write-Host "✅ Tables created successfully" -ForegroundColor Green
    } else {
        Write-Host "⚠️ تحذير: قد تكون الجداول موجودة بالفعل" -ForegroundColor Yellow
        Write-Host "⚠️ Warning: Tables may already exist" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ خطأ في إنشاء الجداول: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "❌ Table creation failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# الخطوة 3: البحث عن Visual Studio
Write-Host "الخطوة 3: البحث عن Visual Studio..." -ForegroundColor Yellow
Write-Host "Step 3: Looking for Visual Studio..." -ForegroundColor Yellow

$vsPath = $null
$vsPaths = @(
    "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\devenv.exe",
    "C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\devenv.exe",
    "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\devenv.exe",
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\Common7\IDE\devenv.exe",
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\devenv.exe"
)

foreach ($path in $vsPaths) {
    if (Test-Path $path) {
        $vsPath = $path
        break
    }
}

if ($vsPath) {
    Write-Host "✅ تم العثور على Visual Studio: $vsPath" -ForegroundColor Green
    Write-Host "✅ Visual Studio found: $vsPath" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "الخطوة 4: فتح المشروع في Visual Studio..." -ForegroundColor Yellow
    Write-Host "Step 4: Opening project in Visual Studio..." -ForegroundColor Yellow
    
    try {
        Start-Process -FilePath $vsPath -ArgumentList "Pharmacy Admin System.sln"
        Write-Host "✅ تم فتح المشروع في Visual Studio" -ForegroundColor Green
        Write-Host "✅ Project opened in Visual Studio" -ForegroundColor Green
        Write-Host ""
        Write-Host "📋 الخطوات التالية:" -ForegroundColor Cyan
        Write-Host "📋 Next steps:" -ForegroundColor Cyan
        Write-Host "1. انتظر حتى يتم تحميل المشروع كاملاً" -ForegroundColor White
        Write-Host "   Wait for the project to load completely" -ForegroundColor Gray
        Write-Host "2. اضغط F5 أو Ctrl+F5 لتشغيل البرنامج" -ForegroundColor White
        Write-Host "   Press F5 or Ctrl+F5 to run the application" -ForegroundColor Gray
        Write-Host "3. سجل دخول بالبيانات التالية:" -ForegroundColor White
        Write-Host "   Login with the following credentials:" -ForegroundColor Gray
        Write-Host "   اسم المستخدم | Username: superadmin" -ForegroundColor Yellow
        Write-Host "   كلمة المرور | Password: admin2025" -ForegroundColor Yellow
    } catch {
        Write-Host "❌ خطأ في فتح Visual Studio: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "❌ Failed to open Visual Studio: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "❌ لم يتم العثور على Visual Studio" -ForegroundColor Red
    Write-Host "❌ Visual Studio not found" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 الحلول البديلة:" -ForegroundColor Cyan
    Write-Host "💡 Alternative solutions:" -ForegroundColor Cyan
    Write-Host "1. تثبيت Visual Studio Community (مجاني)" -ForegroundColor White
    Write-Host "   Install Visual Studio Community (free)" -ForegroundColor Gray
    Write-Host "2. فتح المشروع يدوياً:" -ForegroundColor White
    Write-Host "   Open project manually:" -ForegroundColor Gray
    Write-Host "   - ابحث عن Visual Studio في قائمة البرامج" -ForegroundColor Gray
    Write-Host "   - افتح ملف 'Pharmacy Admin System.sln'" -ForegroundColor Gray
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "🎯 معلومات مهمة:" -ForegroundColor Green
Write-Host "🎯 Important information:" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "📁 موقع المشروع | Project location:" -ForegroundColor White
Write-Host "   $(Get-Location)" -ForegroundColor Gray
Write-Host ""
Write-Host "🔐 بيانات تسجيل الدخول | Login credentials:" -ForegroundColor White
Write-Host "   اسم المستخدم | Username: superadmin" -ForegroundColor Yellow
Write-Host "   كلمة المرور | Password: admin2025" -ForegroundColor Yellow
Write-Host ""
Write-Host "🎯 الميزات المتاحة | Available features:" -ForegroundColor White
Write-Host "   ✅ إدارة الصيدليات | Pharmacy management" -ForegroundColor Gray
Write-Host "   ✅ إدارة الاشتراكات | Subscription management" -ForegroundColor Gray
Write-Host "   ✅ النسخ الاحتياطية | Backup management" -ForegroundColor Gray
Write-Host "   ✅ التقارير والإحصائيات | Reports and statistics" -ForegroundColor Gray
Write-Host "   ✅ إدارة المستخدمين | User management" -ForegroundColor Gray
Write-Host ""

Read-Host "اضغط Enter للإغلاق | Press Enter to close"
