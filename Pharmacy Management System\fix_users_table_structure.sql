-- إصلاح هيكل جدول المستخدمين
-- Fix Users Table Structure

USE pharmacy;

PRINT '========================================';
PRINT '   إصلاح هيكل جدول المستخدمين';
PRINT '   Fixing Users Table Structure';
PRINT '========================================';

-- 1. التحقق من الهيكل الحالي
PRINT '';
PRINT '1. الهيكل الحالي لجدول المستخدمين:';
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'users'
ORDER BY ORDINAL_POSITION;

-- 2. إضافة الأعمدة المفقودة
PRINT '';
PRINT '2. إضافة الأعمدة المفقودة...';

-- إض<PERSON><PERSON>ة pharmacyId إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'pharmacyId')
BEGIN
    ALTER TABLE users ADD pharmacyId INT DEFAULT 1;
    PRINT '✅ تم إضافة عمود pharmacyId';
END
ELSE
BEGIN
    PRINT '✅ عمود pharmacyId موجود بالفعل';
END

-- إضافة isActive إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'isActive')
BEGIN
    ALTER TABLE users ADD isActive BIT DEFAULT 1;
    PRINT '✅ تم إضافة عمود isActive';
END
ELSE
BEGIN
    PRINT '✅ عمود isActive موجود بالفعل';
END

-- 3. التأكد من وجود جدول الصيدليات
PRINT '';
PRINT '3. التحقق من جدول الصيدليات...';
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'pharmacies')
BEGIN
    PRINT 'إنشاء جدول الصيدليات...';
    CREATE TABLE pharmacies (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyName VARCHAR(255) NOT NULL,
        pharmacyCode VARCHAR(50) UNIQUE,
        ownerName VARCHAR(255),
        city VARCHAR(100),
        region VARCHAR(100),
        phone VARCHAR(20),
        email VARCHAR(255),
        address TEXT,
        licenseNumber VARCHAR(100),
        isActive BIT DEFAULT 1,
        createdAt DATETIME DEFAULT GETDATE(),
        lastOnline DATETIME
    );
    PRINT '✅ تم إنشاء جدول الصيدليات';
END
ELSE
BEGIN
    PRINT '✅ جدول الصيدليات موجود بالفعل';
END

-- 4. إضافة صيدلية افتراضية
PRINT '';
PRINT '4. إضافة صيدلية افتراضية...';
IF NOT EXISTS (SELECT * FROM pharmacies WHERE id = 1)
BEGIN
    SET IDENTITY_INSERT pharmacies ON;
    INSERT INTO pharmacies (id, pharmacyName, pharmacyCode, ownerName, city, region, isActive)
    VALUES (1, 'الصيدلية الرئيسية', 'MAIN001', 'المالك الرئيسي', 'الرياض', 'الرياض', 1);
    SET IDENTITY_INSERT pharmacies OFF;
    PRINT '✅ تم إنشاء الصيدلية الافتراضية';
END
ELSE
BEGIN
    PRINT '✅ الصيدلية الافتراضية موجودة بالفعل';
END

-- 5. تحديث المستخدمين الموجودين
PRINT '';
PRINT '5. تحديث المستخدمين الموجودين...';

-- تحديث pharmacyId للمستخدمين الذين لا يملكون واحد
UPDATE users 
SET pharmacyId = 1 
WHERE pharmacyId IS NULL OR pharmacyId = 0;

-- تحديث isActive للمستخدمين الموجودين
UPDATE users 
SET isActive = 1 
WHERE isActive IS NULL;

DECLARE @updatedUsers INT = @@ROWCOUNT;
PRINT '✅ تم تحديث ' + CAST(@updatedUsers AS VARCHAR(10)) + ' مستخدم';

-- 6. إنشاء مستخدم تجريبي للاختبار
PRINT '';
PRINT '6. إنشاء مستخدم تجريبي...';
IF NOT EXISTS (SELECT * FROM users WHERE username = 'testuser')
BEGIN
    INSERT INTO users (pharmacyId, userRole, name, dob, mobile, email, username, pass, isActive)
    VALUES (1, 'Administrator', 'مستخدم تجريبي', '1990-01-01', 1234567890, '<EMAIL>', 'testuser', 'testpass', 1);
    PRINT '✅ تم إنشاء مستخدم تجريبي: testuser / testpass';
END
ELSE
BEGIN
    PRINT '✅ المستخدم التجريبي موجود بالفعل';
END

-- 7. عرض النتيجة النهائية
PRINT '';
PRINT '7. الهيكل النهائي لجدول المستخدمين:';
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'users'
ORDER BY ORDINAL_POSITION;

PRINT '';
PRINT '8. جميع المستخدمين مع معلومات الصيدليات:';
SELECT 
    u.id,
    u.username,
    u.name,
    u.userRole,
    u.pharmacyId,
    p.pharmacyName,
    u.isActive
FROM users u
LEFT JOIN pharmacies p ON u.pharmacyId = p.id
ORDER BY u.id;

PRINT '';
PRINT '========================================';
PRINT '   ✅ تم إصلاح هيكل قاعدة البيانات!';
PRINT '========================================';
PRINT '';
PRINT 'الآن يمكنك:';
PRINT '1. إنشاء حسابات جديدة بنجاح';
PRINT '2. تسجيل الدخول بالحسابات الجديدة';
PRINT '3. اختبار تسجيل الدخول بـ: testuser / testpass';
PRINT '';
