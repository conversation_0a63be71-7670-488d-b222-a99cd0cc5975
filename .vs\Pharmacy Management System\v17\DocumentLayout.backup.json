{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\pharmacist.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\pharmacist.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|c:\\users\\<USER>\\source\\repos\\pharmacy management system\\pharmacy management system\\onlinenetworkmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\onlinenetworkmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\administratoruc\\uc_salesreport.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\administratoruc\\uc_salesreport.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\pharmacistuc\\uc_p_pharmacystore.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\pharmacistuc\\uc_p_pharmacystore.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\pharmacistuc\\uc_p_pharmacystore.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\pharmacistuc\\uc_p_pharmacystore.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\pharmacist.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\pharmacist.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\pharmacistuc\\uc_p_pharmacystore.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\pharmacistuc\\uc_p_pharmacystore.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\requestmedicineform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\requestmedicineform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\pharmacymessagesform.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\pharmacymessagesform.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\pharmacynetworkloginform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\pharmacynetworkloginform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\simpleloginform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\simpleloginform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\publishmedicineform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\publishmedicineform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\simpleloginform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\simpleloginform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\publishmedicineform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\publishmedicineform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\requestmedicineform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\requestmedicineform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\pharmacistuc\\uc_p_addmedicine.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\pharmacistuc\\uc_p_addmedicine.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\simpleloginform.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\simpleloginform.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\pharmacistuc\\uc_p_addmedicine.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\pharmacistuc\\uc_p_addmedicine.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\adminstrator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\adminstrator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\pharmacist.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\pharmacist.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\pharmacistuc\\uc_p_dashbord.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{DE2F26CA-2F8C-41C8-9D4B-FA4977030B1C}|Pharmacy Management System\\Pharmacy Management System.csproj|solutionrelative:pharmacy management system\\pharmacistuc\\uc_p_dashbord.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\pharmacy management system\\pharmacistuc\\uc_p_requestedmedicines.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:pharmacy management system\\pharmacistuc\\uc_p_requestedmedicines.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 8, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "OnlineNetworkManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\OnlineNetworkManager.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\OnlineNetworkManager.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\OnlineNetworkManager.cs", "RelativeToolTip": "Pharmacy Management System\\OnlineNetworkManager.cs", "ViewState": "AgIAADIBAAAAAAAAAAAAACABAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T05:32:23.54Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "UC_SalesReport.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\AdministratorUC\\UC_SalesReport.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\AdministratorUC\\UC_SalesReport.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\AdministratorUC\\UC_SalesReport.cs [Design]", "RelativeToolTip": "Pharmacy Management System\\AdministratorUC\\UC_SalesReport.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T05:29:00.287Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "UC_P_PharmacyStore.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PharmacistUC\\UC_P_PharmacyStore.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\PharmacistUC\\UC_P_PharmacyStore.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PharmacistUC\\UC_P_PharmacyStore.cs", "RelativeToolTip": "Pharmacy Management System\\PharmacistUC\\UC_P_PharmacyStore.cs", "ViewState": "AgIAAE4IAAAAAAAAAAAIwGAIAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T01:43:22.922Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "UC_P_PharmacyStore.Designer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PharmacistUC\\UC_P_PharmacyStore.Designer.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\PharmacistUC\\UC_P_PharmacyStore.Designer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PharmacistUC\\UC_P_PharmacyStore.Designer.cs", "RelativeToolTip": "Pharmacy Management System\\PharmacistUC\\UC_P_PharmacyStore.Designer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T20:29:58.762Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "PharmacyMessagesForm.Designer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PharmacyMessagesForm.Designer.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\PharmacyMessagesForm.Designer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PharmacyMessagesForm.Designer.cs", "RelativeToolTip": "Pharmacy Management System\\PharmacyMessagesForm.Designer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAOkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T19:10:28.338Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "UC_P_RequestedMedicines.Designer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PharmacistUC\\UC_P_RequestedMedicines.Designer.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\PharmacistUC\\UC_P_RequestedMedicines.Designer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PharmacistUC\\UC_P_RequestedMedicines.Designer.cs", "RelativeToolTip": "Pharmacy Management System\\PharmacistUC\\UC_P_RequestedMedicines.Designer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T19:09:23.66Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Pharmacist.Designer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\Pharmacist.Designer.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\Pharmacist.Designer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\Pharmacist.Designer.cs", "RelativeToolTip": "Pharmacy Management System\\Pharmacist.Designer.cs", "ViewState": "AgIAAOUAAAAAAAAAAAAAAHEBAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T19:06:16.516Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "PharmacyNetworkLoginForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PharmacyNetworkLoginForm.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\PharmacyNetworkLoginForm.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PharmacyNetworkLoginForm.cs [Design]", "RelativeToolTip": "Pharmacy Management System\\PharmacyNetworkLoginForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T05:41:25.555Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "PublishMedicineForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PublishMedicineForm.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\PublishMedicineForm.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PublishMedicineForm.cs [Design]", "RelativeToolTip": "Pharmacy Management System\\PublishMedicineForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T05:40:44.053Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "SimpleLoginForm.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\SimpleLoginForm.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\SimpleLoginForm.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\SimpleLoginForm.cs", "RelativeToolTip": "Pharmacy Management System\\SimpleLoginForm.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAWwCsAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T05:40:30.301Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "SimpleLoginForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\SimpleLoginForm.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\SimpleLoginForm.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\SimpleLoginForm.cs [Design]", "RelativeToolTip": "Pharmacy Management System\\SimpleLoginForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T05:40:24.08Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "SimpleLoginForm.Designer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\SimpleLoginForm.Designer.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\SimpleLoginForm.Designer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\SimpleLoginForm.Designer.cs", "RelativeToolTip": "Pharmacy Management System\\SimpleLoginForm.Designer.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAhwDAAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T05:39:30.206Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\Program.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\Program.cs", "RelativeToolTip": "Pharmacy Management System\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T05:29:03.703Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "RequestMedicineForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\RequestMedicineForm.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\RequestMedicineForm.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\RequestMedicineForm.cs [Design]", "RelativeToolTip": "Pharmacy Management System\\RequestMedicineForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T05:14:39.824Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "UC_P_PharmacyStore.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PharmacistUC\\UC_P_PharmacyStore.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\PharmacistUC\\UC_P_PharmacyStore.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PharmacistUC\\UC_P_PharmacyStore.cs [Design]", "RelativeToolTip": "Pharmacy Management System\\PharmacistUC\\UC_P_PharmacyStore.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T02:57:09.161Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "Pharmacist.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\Pharmacist.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\Pharmacist.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\Pharmacist.cs", "RelativeToolTip": "Pharmacy Management System\\Pharmacist.cs", "ViewState": "AgIAAHoAAAAAAAAAAAAmwIEAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T02:25:13.734Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "Pharmacist.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\Pharmacist.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\Pharmacist.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\Pharmacist.cs [Design]", "RelativeToolTip": "Pharmacy Management System\\Pharmacist.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T02:25:46.942Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "UC_P_AddMedicine.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PharmacistUC\\UC_P_AddMedicine.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\PharmacistUC\\UC_P_AddMedicine.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PharmacistUC\\UC_P_AddMedicine.cs", "RelativeToolTip": "Pharmacy Management System\\PharmacistUC\\UC_P_AddMedicine.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAAADUAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T02:25:33.164Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "UC_P_AddMedicine.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PharmacistUC\\UC_P_AddMedicine.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\PharmacistUC\\UC_P_AddMedicine.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PharmacistUC\\UC_P_AddMedicine.cs [Design]", "RelativeToolTip": "Pharmacy Management System\\PharmacistUC\\UC_P_AddMedicine.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T02:25:28.421Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "RequestMedicineForm.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\RequestMedicineForm.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\RequestMedicineForm.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\RequestMedicineForm.cs", "RelativeToolTip": "Pharmacy Management System\\RequestMedicineForm.cs", "ViewState": "AgIAAIAAAAAAAAAAAAAswJkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T02:07:02.238Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "PublishMedicineForm.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PublishMedicineForm.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\PublishMedicineForm.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PublishMedicineForm.cs", "RelativeToolTip": "Pharmacy Management System\\PublishMedicineForm.cs", "ViewState": "AgIAAH0AAAAAAAAAAAAswJUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T02:06:59.014Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "UC_P_Dashbord.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PharmacistUC\\UC_P_Dashbord.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\PharmacistUC\\UC_P_Dashbord.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\PharmacistUC\\UC_P_Dashbord.cs [Design]", "RelativeToolTip": "Pharmacy Management System\\PharmacistUC\\UC_P_Dashbord.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T01:24:25.913Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "Adminstrator.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\Adminstrator.cs", "RelativeDocumentMoniker": "Pharmacy Management System\\Adminstrator.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Pharmacy Management System\\Pharmacy Management System\\Adminstrator.cs [Design]", "RelativeToolTip": "Pharmacy Management System\\Adminstrator.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T00:51:21.246Z"}]}]}]}