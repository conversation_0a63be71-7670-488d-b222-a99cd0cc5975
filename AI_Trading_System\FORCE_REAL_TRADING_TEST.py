#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إجبار التداول الحقيقي
Force Real Trading Test
"""

import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_real_trading_forced():
    """اختبار إجبار التداول الحقيقي"""
    print("🔥 اختبار إجبار التداول الحقيقي...")
    print("=" * 50)
    
    try:
        from intelligent_trading_system_v2 import IntelligentTradingSystemV2
        
        # إنشاء النظام - يجب أن يبدأ بالوضع الحقيقي
        print("1️⃣ إنشاء النظام...")
        system = IntelligentTradingSystemV2()  # demo_mode=False بشكل افتراضي
        
        print(f"✅ تم إنشاء النظام")
        print(f"🔥 وضع المحاكاة: {system.demo_mode}")
        
        if system.demo_mode:
            print("❌ النظام في وضع المحاكاة!")
            print("💡 يجب أن يكون في الوضع الحقيقي")
            return False
        else:
            print("✅ النظام في الوضع الحقيقي!")
        
        # اختبار الاتصال
        print("\n2️⃣ اختبار الاتصال بـ MT5...")
        connected = system.connect_to_mt5()
        
        if connected:
            print("✅ تم الاتصال بـ MT5 بنجاح")
            print(f"🔥 وضع المحاكاة بعد الاتصال: {system.demo_mode}")
            
            if system.demo_mode:
                print("❌ النظام تحول إلى وضع المحاكاة بعد الاتصال!")
                return False
            else:
                print("✅ النظام لا يزال في الوضع الحقيقي!")
        else:
            print("❌ فشل في الاتصال بـ MT5")
            print("💡 تأكد من:")
            print("   - تشغيل MetaTrader 5")
            print("   - تسجيل الدخول إلى حسابك")
            print("   - تفعيل التداول الآلي")
            print("   - صحة بيانات config.ini")
            return False
        
        # اختبار قرار التداول
        print("\n3️⃣ اختبار قرار التداول...")
        decision = {
            'decision': 'buy',
            'confidence': 0.75,
            'position_size': 0.01,
            'stop_loss': 1.0950,
            'take_profit': 1.1050,
            'risk_amount': 10.0,
            'margin_required': 5.0
        }
        
        print("⚠️ تحذير: سيتم اختبار تنفيذ صفقة حقيقية!")
        print("⚠️ هذا قد يفتح صفقة فعلية في حسابك!")
        
        # للأمان، لن ننفذ الصفقة الحقيقية في الاختبار
        print("🛡️ للأمان: لن يتم تنفيذ الصفقة الحقيقية في الاختبار")
        print("✅ النظام جاهز لتنفيذ الصفقات الحقيقية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_gui_real_mode():
    """اختبار الواجهة في الوضع الحقيقي"""
    print("\n🖥️ اختبار الواجهة في الوضع الحقيقي...")
    print("=" * 50)
    
    try:
        # محاكاة إنشاء الواجهة
        print("1️⃣ محاكاة إنشاء الواجهة...")
        
        # متغيرات الواجهة
        demo_mode = False  # يجب أن تبدأ بالوضع الحقيقي
        force_real_trading = True
        
        print(f"✅ demo_mode: {demo_mode}")
        print(f"✅ force_real_trading: {force_real_trading}")
        
        if demo_mode:
            print("❌ الواجهة تبدأ بوضع المحاكاة!")
            return False
        else:
            print("✅ الواجهة تبدأ بالوضع الحقيقي!")
        
        # محاكاة إنشاء النظام التجاري
        print("\n2️⃣ محاكاة إنشاء النظام التجاري...")
        from intelligent_trading_system_v2 import IntelligentTradingSystemV2
        
        # يجب تمرير demo_mode=False
        trading_system = IntelligentTradingSystemV2(demo_mode=demo_mode)
        
        print(f"✅ تم إنشاء النظام التجاري")
        print(f"🔥 وضع النظام: {'محاكاة' if trading_system.demo_mode else 'حقيقي'}")
        
        if trading_system.demo_mode:
            print("❌ النظام التجاري في وضع المحاكاة!")
            return False
        else:
            print("✅ النظام التجاري في الوضع الحقيقي!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار إجبار التداول الحقيقي")
    print("🔥 FORCE REAL TRADING TEST")
    print("=" * 60)
    
    # اختبار النظام الأساسي
    system_result = test_real_trading_forced()
    
    # اختبار الواجهة
    gui_result = test_gui_real_mode()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"   🔥 النظام الأساسي: {'✅ حقيقي' if system_result else '❌ فشل'}")
    print(f"   🖥️ الواجهة: {'✅ حقيقي' if gui_result else '❌ فشل'}")
    
    if system_result and gui_result:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ النظام مُعد للتداول الحقيقي")
        print("🔥 سيتم تنفيذ صفقات فعلية في MetaTrader 5")
        print("\n💡 للتشغيل:")
        print("   🚀 run_intelligent_gui_v2.bat")
    else:
        print("\n⚠️ بعض الاختبارات فشلت")
        print("💡 تحقق من الأخطاء أعلاه")
        
    print("\n🚨 تذكير مهم:")
    print("💰 هذا النظام يتداول بأموال حقيقية!")
    print("⚠️ تأكد من إعداداتك قبل البدء")

if __name__ == "__main__":
    main()
