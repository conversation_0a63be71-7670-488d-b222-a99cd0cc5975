﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
// using static System.Windows.Forms.VisualStyles.VisualStyleElement.StartPanel; // تم تعطيلها للتوافق مع C# 5

namespace Pharmacy_Management_System.PharmacistUC
{
    public partial class sell_user : UserControl
    {
        Function fn = new Function();
        String query;
        string user1 = "";
        public sell_user( string username)
        {
            InitializeComponent();
            user.Text = username;
            user1 = username;

            // الاشتراك في حدث تغيير اللغة
            LanguageManager.LanguageChanged += OnLanguageChanged;
        }

        private void OnLanguageChanged(object sender, EventArgs e)
        {
            ApplyLanguage();
        }

        public void ApplyLanguage()
        {
            // تحديث النصوص حسب اللغة المختارة
            // هذه صفحة بسيطة تعرض اسم المستخدم فقط

            // الحفاظ على التصميم من اليسار لليمين في جميع اللغات
            this.RightToLeft = RightToLeft.No;
        }

        public string ID
        {
            set { user.Text = value; }
        }

        private void sell_user_Load(object sender, EventArgs e)
        {
            query = "select * from users where username ='" + user.Text + "'";
            DataSet ds = fn.getData(query);

            // تطبيق اللغة الحالية
            ApplyLanguage();
        }

        protected override void OnHandleDestroyed(EventArgs e)
        {
            // إلغاء الاشتراك في أحداث التغيير
            LanguageManager.LanguageChanged -= OnLanguageChanged;
            base.OnHandleDestroyed(e);
        }
    }
}
