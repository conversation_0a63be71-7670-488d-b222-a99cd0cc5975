@echo off
chcp 65001 > nul
title اختبار إصلاح التداول الحقيقي - Real Trading Fix Test

echo.
echo ========================================
echo 🚀 اختبار إصلاح التداول الحقيقي
echo ========================================
echo.

echo 🔥 الإصلاحات المطبقة:
echo    ✅ فصل وضع المحاكاة عن التداول الحقيقي
echo    ✅ إصلاح أوضاع التنفيذ (Filling Modes)
echo    ✅ تحسين معالجة الأخطاء
echo    ✅ تحديث الواجهة الرسومية
echo.

echo 💡 هذا الاختبار سيتحقق من:
echo    🧪 وضع المحاكاة
echo    🔥 وضع التداول الحقيقي
echo    🔄 تبديل الأوضاع
echo.

pause

echo 🔄 بدء الاختبار...
echo.

python test_real_trading_fix.py

echo.
echo ========================================
echo ✅ انتهى الاختبار
echo ========================================
echo.

echo 💡 إذا نجحت جميع الاختبارات:
echo    🚀 شغّل الواجهة: run_intelligent_gui_v2.bat
echo    🔥 اختر "وضع فعلي" للتداول الحقيقي
echo    🧪 أو اختر "وضع تجريبي" للمحاكاة الآمنة
echo.

pause
