@echo off
echo Building Pharmacy Management System with new login...

cd "Pharmacy Management System"

echo Cleaning previous build...
if exist "bin\Debug" rmdir /s /q "bin\Debug"
if exist "obj\Debug" rmdir /s /q "obj\Debug"

echo Building project...
"%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" "Pharmacy Management System.csproj" /p:Configuration=Debug /p:Platform="Any CPU"

if %ERRORLEVEL% EQU 0 (
    echo Build successful!
    echo Running application...
    "bin\Debug\Pharmacy Management System.exe"
) else (
    echo Build failed!
    pause
)
