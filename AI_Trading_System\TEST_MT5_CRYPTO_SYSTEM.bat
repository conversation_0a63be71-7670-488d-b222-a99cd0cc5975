@echo off
chcp 65001 > nul
title 🧪 اختبار نظام تداول العملات الرقمية - MetaTrader 5

echo.
echo ================================================================
echo 🧪 اختبار نظام تداول العملات الرقمية على MetaTrader 5
echo MT5 CRYPTOCURRENCY TRADING SYSTEM TEST
echo ================================================================
echo.

echo 🎯 هذا الاختبار سيفحص:
echo    ✅ الوظائف الأساسية للنظام
echo    🔌 الاتصال بـ MetaTrader 5
echo    📊 استرجاع بيانات العملات الرقمية
echo    📈 التحليل الفني والمؤشرات
echo    🛡️ التداول التجريبي
echo    🧠 نظام التعلم الآلي
echo.

echo 📋 متطلبات الاختبار:
echo    🖥️ MetaTrader 5 يعمل
echo    🔑 حساب تداول صحيح
echo    🌐 اتصال بالإنترنت
echo    📊 دعم العملات الرقمية من الوسيط
echo.

echo ⚠️ ملاحظات مهمة:
echo    🛡️ الاختبار يستخدم الوضع التجريبي فقط
echo    💰 لن يتم فتح صفقات حقيقية
echo    📊 قد يستغرق الاختبار 2-5 دقائق
echo    🔄 تأكد من تفعيل التداول الآلي في MT5
echo.

pause

echo 🔄 بدء الاختبار الشامل...
echo.

python test_mt5_crypto_system.py

echo.
echo ================================================================
echo ✅ انتهى الاختبار
echo ================================================================
echo.

echo 💡 تفسير النتائج:
echo    🟢 80%+ : النظام ممتاز وجاهز للاستخدام
echo    🟡 60-79% : النظام جيد مع مشاكل طفيفة
echo    🔴 أقل من 60% : يحتاج إصلاحات كبيرة
echo.

echo 🛠️ إذا فشل الاختبار:
echo    1️⃣ تأكد من تشغيل MetaTrader 5
echo    2️⃣ تحقق من بيانات الحساب في mt5_crypto_config.ini
echo    3️⃣ تأكد من دعم الوسيط للعملات الرقمية
echo    4️⃣ تحقق من تفعيل التداول الآلي
echo    5️⃣ تأكد من الاتصال بالإنترنت
echo.

echo 🚀 الخطوات التالية:
echo    ✅ إذا نجح الاختبار: شغّل RUN_MT5_CRYPTO_TRADING.bat
echo    🔧 إذا فشل الاختبار: أصلح المشاكل وأعد الاختبار
echo    📚 راجع دليل المستخدم للمزيد من التفاصيل
echo.

pause
