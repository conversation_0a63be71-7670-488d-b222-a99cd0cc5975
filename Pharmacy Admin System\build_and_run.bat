@echo off
chcp 65001 >nul
echo ========================================
echo 🚀 بناء وتشغيل نظام إدارة الصيدليات المركزي
echo 🚀 Build and Run Central Pharmacy Admin System
echo ========================================
echo.

echo الخطوة 1: إعداد قاعدة البيانات...
echo Step 1: Setting up database...
sqlcmd -S NARUTO -E -Q "USE master; IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'PharmacyAdminSystem') CREATE DATABASE PharmacyAdminSystem; PRINT 'Database created or exists'"

if %ERRORLEVEL% EQU 0 (
    echo ✅ قاعدة البيانات جاهزة
    echo ✅ Database ready
) else (
    echo ❌ خطأ في إعداد قاعدة البيانات
    echo ❌ Database setup failed
    pause
    exit /b 1
)

echo.
echo الخطوة 2: إنشاء الجداول...
echo Step 2: Creating tables...
sqlcmd -S NARUTO -E -i quick_setup.sql

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إنشاء الجداول بنجاح
    echo ✅ Tables created successfully
) else (
    echo ❌ خطأ في إنشاء الجداول
    echo ❌ Table creation failed
)

echo.
echo الخطوة 3: البحث عن MSBuild...
echo Step 3: Looking for MSBuild...

set MSBUILD_PATH=""
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "C:\Program Files\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
) else (
    echo ❌ لم يتم العثور على MSBuild
    echo ❌ MSBuild not found
    echo.
    echo 💡 يرجى فتح المشروع في Visual Studio يدوياً:
    echo 💡 Please open the project in Visual Studio manually:
    echo 1. افتح Visual Studio
    echo 2. افتح ملف "Pharmacy Admin System.sln"
    echo 3. اضغط F5 للتشغيل
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على MSBuild: %MSBUILD_PATH%
echo ✅ MSBuild found: %MSBUILD_PATH%

echo.
echo الخطوة 4: بناء المشروع...
echo Step 4: Building project...
%MSBUILD_PATH% "Pharmacy Admin System.csproj" /p:Configuration=Debug /p:Platform="Any CPU"

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء المشروع بنجاح
    echo ✅ Project built successfully
    
    echo.
    echo الخطوة 5: تشغيل البرنامج...
    echo Step 5: Running application...
    
    if exist "bin\Debug\Pharmacy Admin System.exe" (
        echo 🚀 تشغيل البرنامج...
        echo 🚀 Starting application...
        start "" "bin\Debug\Pharmacy Admin System.exe"
        echo.
        echo ========================================
        echo ✅ تم تشغيل البرنامج بنجاح!
        echo ✅ Application started successfully!
        echo ========================================
        echo.
        echo 🔐 بيانات تسجيل الدخول:
        echo 🔐 Login credentials:
        echo اسم المستخدم | Username: superadmin
        echo كلمة المرور | Password: admin2025
        echo.
    ) else (
        echo ❌ لم يتم العثور على الملف التنفيذي
        echo ❌ Executable file not found
    )
) else (
    echo ❌ فشل في بناء المشروع
    echo ❌ Project build failed
    echo.
    echo 💡 جرب فتح المشروع في Visual Studio:
    echo 💡 Try opening the project in Visual Studio:
    echo 1. افتح "Pharmacy Admin System.sln"
    echo 2. اضغط F5 للتشغيل
)

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
