-- Simple Query Test for Published Medicines
USE UnifiedPharmacy;
GO

PRINT 'Testing Published Medicines Query...';
PRINT '';

-- 1. Basic Data
PRINT '1. Basic Data:';
SELECT COUNT(*) as pharmacy_count FROM pharmacies;
SELECT COUNT(*) as published_count FROM published_medicines;
PRINT '';

-- 2. Test Main Query
PRINT '2. Testing Main Query:';
SELECT
    pm.id,
    pm.medicine_name as medicineName,
    pm.quantity_available as quantity,
    pm.expiry_date as expiryDate,
    pm.price_per_unit as pricePerUnit,
    pm.description,
    pm.published_date as publishDate,
    p.pharmacyName as pharmacyName,
    p.phone as pharmacyPhone,
    p.address as pharmacyAddress,
    p.city as pharmacyCity,
    DATEDIFF(day, GETDATE(), pm.expiry_date) as daysToExpiry
FROM published_medicines pm
INNER JOIN pharmacies p ON pm.pharmacy_id = p.id
WHERE pm.is_available = 1
AND pm.expiry_date > GETDATE()
ORDER BY pm.published_date DESC;

PRINT '';

-- 3. Test My Published Medicines
PRINT '3. Testing My Published Medicines:';
DECLARE @currentPharmacyId INT = 1;

SELECT
    id,
    medicine_name as medicineName,
    quantity_available as quantity,
    expiry_date as expiryDate,
    price_per_unit as pricePerUnit,
    description,
    published_date as publishDate,
    DATEDIFF(day, GETDATE(), expiry_date) as daysToExpiry
FROM published_medicines
WHERE pharmacy_id = @currentPharmacyId
AND is_available = 1
ORDER BY published_date DESC;

PRINT '';

-- 4. Add test medicine from second pharmacy if needed
IF NOT EXISTS (SELECT * FROM published_medicines WHERE pharmacy_id = 2)
BEGIN
    INSERT INTO published_medicines
    (pharmacy_id, medicine_name, medicine_number, quantity_available, expiry_date, price_per_unit, description, published_date, is_available)
    VALUES
    (2, 'Aspirin 500mg', 'ASP500', 10, '2026-12-31', 15.50, 'Pain reliever and fever reducer', GETDATE(), 1);
    
    PRINT 'Added test medicine from second pharmacy';
END
ELSE
BEGIN
    PRINT 'Medicines from second pharmacy already exist';
END

PRINT '';

-- 5. Final Results
PRINT '5. Final Results:';
SELECT 
    'Total' as Type,
    COUNT(*) as Count
FROM published_medicines
WHERE is_available = 1

UNION ALL

SELECT 
    'Pharmacy 1' as Type,
    COUNT(*) as Count
FROM published_medicines
WHERE is_available = 1 AND pharmacy_id = 1

UNION ALL

SELECT 
    'Pharmacy 2' as Type,
    COUNT(*) as Count
FROM published_medicines
WHERE is_available = 1 AND pharmacy_id = 2;

PRINT '';
PRINT 'Query testing completed!';
